# CLAUDE.md - ChronNote Frontend

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with the ChronNote frontend codebase.

## Development Environment Setup

```bash
# Install dependencies
pnpm install

# Start development (Electron)
pnpm dev

# Start web development
pnpm dev:web
```

## Essential Commands

### Development & Building
- `pnpm dev` - Start Electron development server with hot reload
- `pnpm build` - Build complete application (runs pre-build checks)
- `pnpm start` - Start built Electron application
- `pnpm dev:web` - Start web development server (Vite)
- `pnpm build:web` - Build web version only

### Platform-Specific Builds
- `pnpm build:win` - Build for Windows x64
- `pnpm build:mac` - Build for macOS ARM64 + Universal
- `pnpm build:linux` - Build for Linux
- `pnpm build:unpack` - Build without packaging

### Code Quality (Always run before commits)
- `pnpm lint` - ESLint with auto-fix
- `pnpm typecheck` - TypeScript checking for all processes
- `pnpm typecheck:node` - Type check main/preload processes only
- `pnpm typecheck:web` - Type check renderer process only
- `pnpm format` - Prettier formatting
- `pnpm checks` - Run all pre-build checks (lint + typecheck)

### Testing
- `pnpm vitest` - Run tests in watch mode
- `pnpm test:unit` - Run all tests once

## Code Style & Conventions

### TypeScript
- Use strict TypeScript with proper type definitions
- Prefer `interface` over `type` for object types
- Use `const assertions` for readonly arrays/objects
- Always type function parameters and return values

### Vue 3 Patterns
- Use Composition API with `<script setup>` syntax
- Use `defineProps` and `defineEmits` with TypeScript
- Prefer `ref()` over `reactive()` for primitive values
- Use `computed()` for derived state
- Use `watch()` or `watchEffect()` for side effects

### Component Organization
- Put UI components in `src/renderer/src/components/ui/`
- Put feature components in `src/renderer/src/components/{feature}/`
- Use PascalCase for component files and directory names
- Export components from index.ts files when multiple components in directory

### Styling
- Use Tailwind CSS classes primarily
- Custom CSS only when Tailwind is insufficient
- Use CSS modules or scoped styles for component-specific styles
- Follow mobile-first responsive design

## Architecture Understanding

### Multi-Platform Structure
This is a cross-platform application supporting:
- **Electron** (Primary) - Windows, macOS, Linux desktop
- **Web** - Browser-based version  
- **iOS** - Mobile via Capacitor (in development)

### Process Architecture (Electron)
- **Main Process** (`src/main/`) - Window management, file system, system APIs
- **Preload** (`src/preload/`) - Secure IPC bridge, exposes APIs to renderer
- **Renderer** (`src/renderer/`) - Vue 3 frontend application

### Key Directories
```
src/
├── main/           # Electron main process
├── preload/        # Electron preload scripts  
└── renderer/       # Vue 3 frontend app
    ├── src/
    │   ├── api/           # Platform-specific & unified APIs
    │   ├── components/    # Vue components
    │   ├── stores/        # Pinia state management
    │   ├── editor/        # TipTap editor extensions
    │   ├── utils/         # Utility functions
    │   └── views/         # Page components
    └── index.html
```

### Database Layer
- **Electron**: SQLite via `better-sqlite3`
- **Web**: IndexedDB via `Dexie`
- **Unified API**: `src/renderer/src/api/uniform/db.ts`
- Always use the unified database API for cross-platform compatibility

### State Management
- **Pinia** stores with TypeScript
- Persistent stores for user preferences and app state
- Store files in `src/renderer/src/stores/`

### Editor System
Built on **TipTap v3 Beta** with custom extensions:
- Rich text editing with collaborative features
- Mathematics support via TipTap Mathematics extension  
- Custom drag handle and selection plugins
- YJS integration for real-time collaboration
- PDF viewer integration with annotations

## Development Patterns

### API Development
- Platform-specific APIs in `src/renderer/src/api/{platform}/`
- Unified APIs in `src/renderer/src/api/uniform/` 
- Always implement both Electron and Web versions
- Use TypeScript interfaces for API contracts

### Component Development
- Start with `src/renderer/src/components/ui/` patterns
- Use Radix Vue for accessible components
- Implement proper TypeScript props interfaces
- Consider platform differences (Electron vs Web)

### Editor Extension Development
- Custom extensions in `src/renderer/src/editor/plugins/`
- Follow TipTap v3 patterns for nodes, marks, extensions
- Test extensions on both Electron and Web platforms
- Consider collaborative editing implications (YJS)

### Database Development
- Use unified database API exclusively
- SQL constants in `src/renderer/src/sqls/constants.ts`
- Database migrations in `src/renderer/src/utils/dbMigration.ts`
- Test database operations on both SQLite and IndexedDB

## Testing Guidelines

### Unit Testing (Vitest)
- Test utility functions thoroughly
- Mock platform-specific APIs 
- Test Pinia stores in isolation
- Use Vue Test Utils for component testing

### Integration Testing
- Test cross-platform API compatibility
- Test database operations with real data
- Test editor functionality with sample documents

### Performance Considerations
- YJS documents can become large - implement chunking/pagination
- PDF rendering is resource-intensive - use lazy loading
- Database queries should be optimized for large collections
- Use `defineAsyncComponent` for code splitting large features

## Common Pitfalls & Solutions

### Platform Differences
- File system APIs differ between Electron and Web
- Always test features on both platforms  
- Use unified APIs to abstract platform differences
- Handle permission differences (file access, etc.)

### Editor Issues
- TipTap v3 is beta - check for breaking changes
- YJS collaboration requires careful state management
- Mathematics rendering needs proper LaTeX handling
- Drag & drop behavior varies between platforms

### Build Issues
- Run `pnpm checks` before every commit
- TypeScript strict mode catches many issues early
- Electron builder requires specific configurations
- Web builds need different bundling than Electron

## Quick Reference

### Most Common Tasks
1. **Add new UI component**: Create in `components/ui/`, follow existing patterns
2. **Add new page**: Create in `views/`, add to router
3. **Add API endpoint**: Implement in both `api/electron/` and `api/web/`
4. **Add database table**: Update schema, create migration, update unified API
5. **Add editor feature**: Create extension in `editor/plugins/`

### Debug Commands
- **Electron DevTools**: F12 in development
- **Vue DevTools**: Install browser extension
- **Console Logs**: Use `console.log` in renderer, `electron-log` in main
- **Database**: Use SQLite browser for Electron, browser DevTools for Web

## Cross-Platform Compatibility

### File System Operations
Different platforms return different formats from `fs.stat()`. Always use the utilities in `src/renderer/src/utils/fsUtils.ts` for cross-platform compatibility:

```typescript
import { isDirectory, isFile, getFileType } from '@renderer/utils/fsUtils'

// ❌ Platform-specific (may fail)
if (stats.isDirectory()) { ... }

// ✅ Cross-platform compatible
if (isDirectory(stats)) { ... }
```

**Common Issues:**
- `isDirectory is not a function` - Different platforms may have `isDirectory` as boolean property or missing entirely
- **Solution**: Use `fsUtils.isDirectory()` which handles function, boolean, and bitwise detection methods

**Documentation**: See `docs/filesystem-compatibility.md` for detailed guide on cross-platform file system operations.

## Icon Configuration Management

### Intelligent Icon System Architecture
A zero-configuration, extensible icon management system with progressive enhancement:

```
Icon System Components:
├── Auto-discovery Scanner     # src/utils/iconScanner.ts
├── Configuration Manager      # src/config/iconConfig.ts  
├── Smart Search Engine       # src/search/iconSearchEngine.ts
├── Unified Service Layer     # src/services/iconService.ts
└── UI Integration           # components/Todo/IconPicker.vue
```

### System Features
- **🔍 Auto-discovery**: Zero-config filesystem scanning with hot updates
- **🌐 Multi-language**: Chinese, English, Pinyin search with fuzzy matching
- **⚡ Smart Search**: Multi-level matching with weighted scoring
- **📊 Progressive Config**: Level 0 (auto) → Level 3 (advanced features)
- **🔧 Extensible**: Plugin architecture for new categories and icons

### Quick Status Check
```bash
# Check icon configuration status
ls src/renderer/src/assets/svg/color/ | wc -l  # Baseline: 26 categories

# Run complete detection
for dir in src/renderer/src/assets/svg/color/*/; do 
  echo "$(basename "$dir"): $(ls "$dir" | wc -l | tr -d ' ') files"
done

# Check critical Chinese filename categories
ls src/renderer/src/assets/svg/color/{commercial,emojis,information,fruits}/
```

### Priority Tasks
🔴 **Critical**: Chinese filename translation (40 icons)
- `commercial/` (22) - 书籍资料.svg → commercial-books.svg
- `emojis/` (13) - 喜欢.svg → emoji-like.svg  
- `information/` (5) - 图表.svg → information-chart.svg
- `fruits/` (1) - 蓝莓.svg → fruits-blueberry.svg

🟡 **Important**: Chinese display names for English files (913 icons)
- High-frequency: `food/`, `transportation/`, `weather/`
- Large categories: `cinema/`, `graduation/`, `magic/` (50 each)

🟢 **Optional**: Advanced features (aliases, pinyin, tags, weights)

### Search Capabilities
```typescript
// Multi-level matching system
1. Exact Match (100 pts) - 精确匹配
2. Prefix Match (80 pts) - 前缀匹配  
3. Alias Match (70 pts) - 别名匹配
4. Pinyin Match (60 pts) - 拼音匹配 (chuizi → 锤子)
5. Fuzzy Match (40 pts) - 模糊匹配

// Smart ranking
- Weight-based result scoring
- Relevance calculation
- Popular icon promotion
- Category priority handling
```

### Configuration Levels
- **Level 0**: Auto-scan, zero configuration
- **Level 1**: Category translations (26 categories)
- **Level 2**: Icon enhancements (aliases, pinyin, tags)
- **Level 3**: Advanced search config (weights, learning)

### Icon Assets Structure
```
src/renderer/src/assets/svg/color/ (26 categories, 953+ icons)
├── commercial/      (22) ⚠️ Chinese filenames - Priority 1
├── emojis/         (13) ⚠️ Chinese filenames - Priority 1
├── information/     (5) ⚠️ Chinese filenames - Priority 1
├── fruits/          (1) ⚠️ Chinese filenames - Priority 1
├── food/           (14) ✅ English files, needs Chinese display
├── transportation/ (32) ✅ English files, needs Chinese display
├── weather/        (21) ✅ English files, needs Chinese display
└── ... (19 more categories with English filenames)
```

### Usage Examples
```typescript
// Initialize icon service
await iconService.initialize({
  autoScan: true
})

// Smart search with multi-language support
const results = searchIcons({
  query: '锤子',  // Searches: hammer, chuizi, tool, etc.
  category: 'tools',
  maxResults: 10
})
```

### Related Files
- 📊 **Status Tracking**: @docs/icon-configuration-status.md
- 🔍 **Detection Tools**: @docs/icon-check-template.md  
- 📁 **Icon Directory**: `src/renderer/src/assets/svg/color/`

### When Adding New Icons
1. **Auto-detection**: New icons/categories automatically discovered
2. **Zero config**: System works immediately with basic functionality
3. **Progressive enhancement**: Add translations and metadata as needed
4. **Weight optimization**: Configure search weights for better relevance