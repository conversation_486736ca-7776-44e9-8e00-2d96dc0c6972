#!/usr/bin/env python3
"""
Blender MCP Server
基于 https://github.com/ahujasid/blender-mcp 项目的MCP服务器
"""

import socket
import json
import asyncio
import logging
import tempfile
from dataclasses import dataclass
from contextlib import asynccontextmanager
from typing import AsyncIterator, Dict, Any, List
import os
from pathlib import Path
import base64
from urllib.parse import urlparse

# 检查并安装 mcp 依赖
try:
    from mcp.server.fastmcp import FastMCP, Context, Image
except ImportError:
    print("正在安装 mcp 依赖...")
    import subprocess
    import sys
    subprocess.check_call([sys.executable, "-m", "pip", "install", "mcp"])
    from mcp.server.fastmcp import FastMCP, Context, Image

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("BlenderMCPServer")

@dataclass
class BlenderConnection:
    host: str
    port: int
    sock: socket.socket = None
    
    def connect(self) -> bool:
        """连接到Blender插件的socket服务器"""
        if self.sock:
            return True
            
        try:
            self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.sock.connect((self.host, self.port))
            logger.info(f"已连接到Blender：{self.host}:{self.port}")
            return True
        except Exception as e:
            logger.error(f"连接Blender失败：{str(e)}")
            self.sock = None
            return False
    
    def disconnect(self):
        """断开与Blender插件的连接"""
        if self.sock:
            try:
                self.sock.close()
            except Exception as e:
                logger.error(f"断开Blender连接时出错：{str(e)}")
            finally:
                self.sock = None

    def receive_full_response(self, sock, buffer_size=8192):
        """接收完整响应，可能需要多次接收"""
        chunks = []
        sock.settimeout(15.0)  # 匹配插件的超时时间
        
        try:
            while True:
                try:
                    chunk = sock.recv(buffer_size)
                    if not chunk:
                        if not chunks:
                            raise Exception("连接在接收任何数据之前就关闭了")
                        break
                    
                    chunks.append(chunk)
                    
                    # 检查是否接收到完整的JSON对象
                    try:
                        data = b''.join(chunks)
                        json.loads(data.decode('utf-8'))
                        logger.info(f"接收到完整响应（{len(data)}字节）")
                        return data
                    except json.JSONDecodeError:
                        # JSON不完整，继续接收
                        continue
                except socket.timeout:
                    logger.warning("分块接收时socket超时")
                    break
                except (ConnectionError, BrokenPipeError, ConnectionResetError) as e:
                    logger.error(f"接收时socket连接错误：{str(e)}")
                    raise
        except socket.timeout:
            logger.warning("分块接收时socket超时")
        except Exception as e:
            logger.error(f"接收时出错：{str(e)}")
            raise
            
        # 如果到达这里，要么超时了，要么跳出了循环
        # 尝试使用已有的数据
        if chunks:
            data = b''.join(chunks)
            logger.info(f"接收完成后返回数据（{len(data)}字节）")
            try:
                # 尝试解析已有数据
                json.loads(data.decode('utf-8'))
                return data
            except json.JSONDecodeError:
                # 如果无法解析，说明不完整
                raise Exception("接收到不完整的JSON响应")
        else:
            raise Exception("未接收到数据")

    def send_command(self, command_type: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """向Blender发送命令并返回响应"""
        if not self.sock and not self.connect():
            raise ConnectionError("未连接到Blender")
        
        command = {
            "type": command_type,
            "params": params or {}
        }
        
        try:
            # 记录发送的命令
            logger.info(f"发送命令：{command_type}，参数：{params}")
            
            # 发送命令
            self.sock.sendall(json.dumps(command).encode('utf-8'))
            logger.info(f"命令已发送，等待响应...")
            
            # 设置接收超时
            self.sock.settimeout(15.0)
            
            # 接收响应
            response_data = self.receive_full_response(self.sock)
            logger.info(f"接收到{len(response_data)}字节数据")
            
            response = json.loads(response_data.decode('utf-8'))
            logger.info(f"响应解析完成，状态：{response.get('status', '未知')}")
            
            if response.get("status") == "error":
                logger.error(f"Blender错误：{response.get('message')}")
                raise Exception(response.get("message", "Blender未知错误"))
            
            return response.get("result", {})
        except socket.timeout:
            logger.error("等待Blender响应时socket超时")
            self.sock = None
            raise Exception("等待Blender响应超时 - 请尝试简化您的请求")
        except (ConnectionError, BrokenPipeError, ConnectionResetError) as e:
            logger.error(f"Socket连接错误：{str(e)}")
            self.sock = None
            raise Exception(f"与Blender的连接丢失：{str(e)}")
        except json.JSONDecodeError as e:
            logger.error(f"来自Blender的无效JSON响应：{str(e)}")
            if 'response_data' in locals() and response_data:
                logger.error(f"原始响应（前200字节）：{response_data[:200]}")
            raise Exception(f"来自Blender的无效响应：{str(e)}")
        except Exception as e:
            logger.error(f"与Blender通信时出错：{str(e)}")
            self.sock = None
            raise Exception(f"与Blender通信错误：{str(e)}")

@asynccontextmanager
async def server_lifespan(server: FastMCP) -> AsyncIterator[Dict[str, Any]]:
    """管理服务器启动和关闭生命周期"""
    try:
        logger.info("BlenderMCP服务器启动中")
        
        # 启动时尝试连接到Blender以验证其可用性
        try:
            blender = get_blender_connection()
            logger.info("启动时成功连接到Blender")
        except Exception as e:
            logger.warning(f"启动时无法连接到Blender：{str(e)}")
            logger.warning("请确保在使用Blender资源或工具之前Blender插件正在运行")
        
        yield {}
    finally:
        # 关闭时清理全局连接
        global _blender_connection
        if _blender_connection:
            logger.info("关闭时断开与Blender的连接")
            _blender_connection.disconnect()
            _blender_connection = None
        logger.info("BlenderMCP服务器已关闭")

# 创建带有生命周期支持的MCP服务器
mcp = FastMCP(
    "BlenderMCP",
    description="通过模型上下文协议进行Blender集成",
    lifespan=server_lifespan
)

# 资源的全局连接（因为资源无法访问上下文）
_blender_connection = None
_polyhaven_enabled = False

def get_blender_connection():
    """获取或创建持久的Blender连接"""
    global _blender_connection, _polyhaven_enabled
    
    # 如果已有连接，检查其是否仍然有效
    if _blender_connection is not None:
        try:
            # 首先通过发送ping命令检查PolyHaven是否启用
            result = _blender_connection.send_command("get_polyhaven_status")
            # 全局存储PolyHaven状态
            _polyhaven_enabled = result.get("enabled", False)
            return _blender_connection
        except Exception as e:
            # 连接已失效，关闭并创建新的
            logger.warning(f"现有连接不再有效：{str(e)}")
            try:
                _blender_connection.disconnect()
            except:
                pass
            _blender_connection = None
    
    # 如果需要，创建新连接
    if _blender_connection is None:
        _blender_connection = BlenderConnection(host="localhost", port=9876)
        if not _blender_connection.connect():
            logger.error("连接Blender失败")
            _blender_connection = None
            raise Exception("无法连接到Blender。请确保Blender插件正在运行。")
        logger.info("创建了新的持久Blender连接")
    
    return _blender_connection

@mcp.tool()
def get_scene_info(ctx: Context) -> str:
    """获取当前Blender场景的详细信息"""
    try:
        blender = get_blender_connection()
        result = blender.send_command("get_scene_info")
        
        # 直接返回Blender发送的JSON表示
        return json.dumps(result, indent=2, ensure_ascii=False)
    except Exception as e:
        logger.error(f"从Blender获取场景信息时出错：{str(e)}")
        return f"获取场景信息出错：{str(e)}"

@mcp.tool()
def get_object_info(ctx: Context, object_name: str) -> str:
    """
    获取Blender场景中特定物体的详细信息。
    
    参数：
    - object_name: 要获取信息的物体名称
    """
    try:
        blender = get_blender_connection()
        result = blender.send_command("get_object_info", {"name": object_name})
        
        # 直接返回Blender发送的JSON表示
        return json.dumps(result, indent=2, ensure_ascii=False)
    except Exception as e:
        logger.error(f"从Blender获取物体信息时出错：{str(e)}")
        return f"获取物体信息出错：{str(e)}"

@mcp.tool()
def get_viewport_screenshot(ctx: Context, max_size: int = 800) -> Image:
    """
    获取当前Blender 3D视口的截图。
    
    参数：
    - max_size: 最大尺寸像素（默认：800）
    
    返回截图作为图像。
    """
    try:
        blender = get_blender_connection()
        
        # 创建临时文件路径
        temp_dir = tempfile.gettempdir()
        temp_path = os.path.join(temp_dir, f"blender_screenshot_{os.getpid()}.png")
        
        result = blender.send_command("get_viewport_screenshot", {
            "max_size": max_size,
            "filepath": temp_path,
            "format": "png"
        })
        
        if "error" in result:
            raise Exception(result["error"])
        
        if not os.path.exists(temp_path):
            raise Exception("截图文件未创建")
        
        # 读取文件
        with open(temp_path, 'rb') as f:
            image_bytes = f.read()
        
        # 删除临时文件
        os.remove(temp_path)
        
        return Image(data=image_bytes, format="png")
        
    except Exception as e:
        logger.error(f"截图出错：{str(e)}")
        raise Exception(f"截图失败：{str(e)}")

@mcp.tool()
def execute_blender_code(ctx: Context, code: str) -> str:
    """
    在Blender中执行任意Python代码。确保逐步执行，将其分解为较小的块。
    
    参数：
    - code: 要执行的Python代码
    """
    try:
        blender = get_blender_connection()
        result = blender.send_command("execute_code", {"code": code})
        return f"代码执行成功：{result.get('result', '')}"
    except Exception as e:
        logger.error(f"执行代码出错：{str(e)}")
        return f"执行代码出错：{str(e)}"

@mcp.tool()
def get_polyhaven_categories(ctx: Context, asset_type: str = "hdris") -> str:
    """
    获取Polyhaven上特定资源类型的类别列表。
    
    参数：
    - asset_type: 要获取类别的资源类型（hdris、textures、models、all）
    """
    try:
        blender = get_blender_connection()
        if not _polyhaven_enabled:
            return "PolyHaven集成已禁用。请在BlenderMCP侧边栏中选择它，然后重新运行。"
        result = blender.send_command("get_polyhaven_categories", {"asset_type": asset_type})
        
        if "error" in result:
            return f"错误：{result['error']}"
        
        # 以更易读的方式格式化类别
        categories = result["categories"]
        formatted_output = f"{asset_type}的类别：\n\n"
        
        # 按数量排序（降序）
        sorted_categories = sorted(categories.items(), key=lambda x: x[1], reverse=True)
        
        for category, count in sorted_categories:
            formatted_output += f"- {category}: {count} 个资源\n"
        
        return formatted_output
    except Exception as e:
        logger.error(f"获取Polyhaven类别出错：{str(e)}")
        return f"获取Polyhaven类别出错：{str(e)}"

@mcp.tool()
def search_polyhaven_assets(
    ctx: Context,
    asset_type: str = "all",
    categories: str = None
) -> str:
    """
    在Polyhaven上搜索资源，可选过滤。
    
    参数：
    - asset_type: 要搜索的资源类型（hdris、textures、models、all）
    - categories: 可选的逗号分隔类别列表进行过滤
    
    返回匹配资源列表及基本信息。
    """
    try:
        blender = get_blender_connection()
        result = blender.send_command("search_polyhaven_assets", {
            "asset_type": asset_type,
            "categories": categories
        })
        
        if "error" in result:
            return f"错误：{result['error']}"
        
        # 以更易读的方式格式化资源
        assets = result["assets"]
        total_count = result["total_count"]
        returned_count = result["returned_count"]
        
        formatted_output = f"找到{total_count}个资源"
        if categories:
            formatted_output += f"在类别中：{categories}"
        formatted_output += f"\n显示{returned_count}个资源：\n\n"
        
        # 按下载数量（流行度）排序
        sorted_assets = sorted(assets.items(), key=lambda x: x[1].get("download_count", 0), reverse=True)
        
        for asset_id, asset_data in sorted_assets:
            formatted_output += f"- {asset_data.get('name', asset_id)}（ID：{asset_id}）\n"
            formatted_output += f"  类型：{['HDRI', '纹理', '模型'][asset_data.get('type', 0)]}\n"
            formatted_output += f"  类别：{', '.join(asset_data.get('categories', []))}\n"
            formatted_output += f"  下载：{asset_data.get('download_count', '未知')}\n\n"
        
        return formatted_output
    except Exception as e:
        logger.error(f"搜索Polyhaven资源出错：{str(e)}")
        return f"搜索Polyhaven资源出错：{str(e)}"

@mcp.tool()
def download_polyhaven_asset(
    ctx: Context,
    asset_id: str,
    asset_type: str,
    resolution: str = "1k",
    file_format: str = None
) -> str:
    """
    下载并导入Polyhaven资源到Blender。
    
    参数：
    - asset_id: 要下载的资源ID
    - asset_type: 资源类型（hdris、textures、models）
    - resolution: 要下载的分辨率（例如1k、2k、4k）
    - file_format: 可选文件格式（例如HDRIs用hdr、exr；纹理用jpg、png；模型用gltf、fbx）
    
    返回表示成功或失败的消息。
    """
    try:
        blender = get_blender_connection()
        result = blender.send_command("download_polyhaven_asset", {
            "asset_id": asset_id,
            "asset_type": asset_type,
            "resolution": resolution,
            "file_format": file_format
        })
        
        if "error" in result:
            return f"错误：{result['error']}"
        
        if result.get("success"):
            message = result.get("message", "资源下载并导入成功")
            
            # 根据资源类型添加额外信息
            if asset_type == "hdris":
                return f"{message}。HDRI已设置为世界环境。"
            elif asset_type == "textures":
                material_name = result.get("material", "")
                maps = "、".join(result.get("maps", []))
                return f"{message}。创建了材质'{material_name}'，包含贴图：{maps}。"
            elif asset_type == "models":
                return f"{message}。模型已导入到当前场景。"
            else:
                return message
        else:
            return f"下载资源失败：{result.get('message', '未知错误')}"
    except Exception as e:
        logger.error(f"下载Polyhaven资源出错：{str(e)}")
        return f"下载Polyhaven资源出错：{str(e)}"

@mcp.tool()
def set_texture(
    ctx: Context,
    object_name: str,
    texture_id: str
) -> str:
    """
    将之前下载的Polyhaven纹理应用到物体上。
    
    参数：
    - object_name: 要应用纹理的物体名称
    - texture_id: 要应用的Polyhaven纹理ID（必须先下载）
    
    返回表示成功或失败的消息。
    """
    try:
        blender = get_blender_connection()
        result = blender.send_command("set_texture", {
            "object_name": object_name,
            "texture_id": texture_id
        })
        
        if "error" in result:
            return f"错误：{result['error']}"
        
        if result.get("success"):
            material_name = result.get("material", "")
            maps = "、".join(result.get("maps", []))
            
            # 添加详细材质信息
            material_info = result.get("material_info", {})
            node_count = material_info.get("node_count", 0)
            has_nodes = material_info.get("has_nodes", False)
            texture_nodes = material_info.get("texture_nodes", [])
            
            output = f"成功将纹理'{texture_id}'应用到{object_name}。\n"
            output += f"使用材质'{material_name}'，包含贴图：{maps}。\n\n"
            output += f"材质包含节点：{has_nodes}\n"
            output += f"总节点数量：{node_count}\n\n"
            
            if texture_nodes:
                output += "纹理节点：\n"
                for node in texture_nodes:
                    output += f"- {node['name']} 使用图像：{node['image']}\n"
                    if node['connections']:
                        output += "  连接：\n"
                        for conn in node['connections']:
                            output += f"    {conn}\n"
            else:
                output += "材质中未找到纹理节点。\n"
            
            return output
        else:
            return f"应用纹理失败：{result.get('message', '未知错误')}"
    except Exception as e:
        logger.error(f"应用纹理出错：{str(e)}")
        return f"应用纹理出错：{str(e)}"

@mcp.tool()
def get_polyhaven_status(ctx: Context) -> str:
    """
    检查Blender中是否启用了PolyHaven集成。
    返回指示PolyHaven功能是否可用的消息。
    """
    try:
        blender = get_blender_connection()
        result = blender.send_command("get_polyhaven_status")
        enabled = result.get("enabled", False)
        message = result.get("message", "")
        if enabled:
            message += "PolyHaven在纹理方面很出色，纹理种类比Sketchfab更丰富。"
        return message
    except Exception as e:
        logger.error(f"检查PolyHaven状态出错：{str(e)}")
        return f"检查PolyHaven状态出错：{str(e)}"

@mcp.tool()
def get_hyper3d_status(ctx: Context) -> str:
    """
    检查Blender中是否启用了Hyper3D Rodin集成。
    返回指示Hyper3D Rodin功能是否可用的消息。

    不要在返回的消息中强调密钥类型，但要默默记住它。
    """
    try:
        blender = get_blender_connection()
        result = blender.send_command("get_hyper3d_status")
        enabled = result.get("enabled", False)
        message = result.get("message", "")
        if enabled:
            message += ""
        return message
    except Exception as e:
        logger.error(f"检查Hyper3D状态出错：{str(e)}")
        return f"检查Hyper3D状态出错：{str(e)}"

@mcp.tool()
def get_sketchfab_status(ctx: Context) -> str:
    """
    检查Blender中是否启用了Sketchfab集成。
    返回指示Sketchfab功能是否可用的消息。
    """
    try:
        blender = get_blender_connection()
        result = blender.send_command("get_sketchfab_status")
        enabled = result.get("enabled", False)
        message = result.get("message", "")
        if enabled:
            message += "Sketchfab在逼真模型方面很出色，模型种类比PolyHaven更丰富。"        
        return message
    except Exception as e:
        logger.error(f"检查Sketchfab状态出错：{str(e)}")
        return f"检查Sketchfab状态出错：{str(e)}"

@mcp.tool()
def search_sketchfab_models(
    ctx: Context,
    query: str,
    categories: str = None,
    count: int = 20,
    downloadable: bool = True
) -> str:
    """
    在Sketchfab上搜索模型，可选过滤。
    
    参数：
    - query: 要搜索的文本
    - categories: 可选的逗号分隔类别列表
    - count: 返回的最大结果数量（默认20）
    - downloadable: 是否只包含可下载的模型（默认True）
    
    返回匹配模型的格式化列表。
    """
    try:
        blender = get_blender_connection()
        logger.info(f"使用查询搜索Sketchfab模型：{query}，类别：{categories}，数量：{count}，可下载：{downloadable}")
        result = blender.send_command("search_sketchfab_models", {
            "query": query,
            "categories": categories,
            "count": count,
            "downloadable": downloadable
        })
        
        if "error" in result:
            logger.error(f"Sketchfab搜索错误：{result['error']}")
            return f"错误：{result['error']}"
        
        # 安全地获取结果，为None提供回退
        if result is None:
            logger.error("从Sketchfab搜索收到None结果")
            return "错误：从Sketchfab搜索收到无响应"
            
        # 格式化结果
        models = result.get("results", []) or []
        if not models:
            return f"未找到匹配'{query}'的模型"
            
        formatted_output = f"找到{len(models)}个匹配'{query}'的模型：\n\n"
        
        for model in models:
            if model is None:
                continue
                
            model_name = model.get("name", "未命名模型")
            model_uid = model.get("uid", "未知ID")
            formatted_output += f"- {model_name}（UID：{model_uid}）\n"
            
            # 安全获取用户信息
            user = model.get("user") or {}
            username = user.get("username", "未知作者") if isinstance(user, dict) else "未知作者"
            formatted_output += f"  作者：{username}\n"
            
            # 安全获取许可信息
            license_data = model.get("license") or {}
            license_label = license_data.get("label", "未知") if isinstance(license_data, dict) else "未知"
            formatted_output += f"  许可：{license_label}\n"
            
            # 添加面数和可下载状态
            face_count = model.get("faceCount", "未知")
            is_downloadable = "是" if model.get("isDownloadable") else "否"
            formatted_output += f"  面数：{face_count}\n"
            formatted_output += f"  可下载：{is_downloadable}\n\n"
        
        return formatted_output
    except Exception as e:
        logger.error(f"搜索Sketchfab模型出错：{str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return f"搜索Sketchfab模型出错：{str(e)}"

@mcp.tool()
def download_sketchfab_model(
    ctx: Context,
    uid: str
) -> str:
    """
    通过UID下载并导入Sketchfab模型。
    
    参数：
    - uid: Sketchfab模型的唯一标识符
    
    返回表示成功或失败的消息。
    模型必须是可下载的，且您必须拥有适当的访问权限。
    """
    try:
        blender = get_blender_connection()
        logger.info(f"尝试下载UID为{uid}的Sketchfab模型")
        
        result = blender.send_command("download_sketchfab_model", {
            "uid": uid
        })
        
        if result is None:
            logger.error("从Sketchfab下载收到None结果")
            return "错误：从Sketchfab下载请求收到无响应"
            
        if "error" in result:
            logger.error(f"Sketchfab下载错误：{result['error']}")
            return f"错误：{result['error']}"
        
        if result.get("success"):
            imported_objects = result.get("imported_objects", [])
            object_names = "、".join(imported_objects) if imported_objects else "无"
            return f"成功导入模型。创建的物体：{object_names}"
        else:
            return f"下载模型失败：{result.get('message', '未知错误')}"
    except Exception as e:
        logger.error(f"下载Sketchfab模型出错：{str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return f"下载Sketchfab模型出错：{str(e)}"

def _process_bbox(original_bbox: list[float] | list[int] | None) -> list[int] | None:
    if original_bbox is None:
        return None
    if all(isinstance(i, int) for i in original_bbox):
        return original_bbox
    if any(i<=0 for i in original_bbox):
        raise ValueError("数字范围错误：bbox必须大于零！")
    return [int(float(i) / max(original_bbox) * 100) for i in original_bbox] if original_bbox else None

@mcp.tool()
def generate_hyper3d_model_via_text(
    ctx: Context,
    text_prompt: str,
    bbox_condition: list[float]=None
) -> str:
    """
    通过描述所需资源使用Hyper3D生成3D资源，并将资源导入Blender。
    3D资源具有内置材质。
    生成的模型具有标准化尺寸，因此生成后重新缩放可能很有用。
    
    参数：
    - text_prompt: 所需模型的简短描述，使用**英文**。
    - bbox_condition: 可选。如果给出，必须是长度为3的浮点数列表。控制模型的[长度、宽度、高度]比例。

    返回表示成功或失败的消息。
    """
    try:
        blender = get_blender_connection()
        result = blender.send_command("create_rodin_job", {
            "text_prompt": text_prompt,
            "images": None,
            "bbox_condition": _process_bbox(bbox_condition),
        })
        succeed = result.get("submit_time", False)
        if succeed:
            return json.dumps({
                "task_uuid": result["uuid"],
                "subscription_key": result["jobs"]["subscription_key"],
            }, ensure_ascii=False)
        else:
            return json.dumps(result, ensure_ascii=False)
    except Exception as e:
        logger.error(f"生成Hyper3D任务出错：{str(e)}")
        return f"生成Hyper3D任务出错：{str(e)}"

@mcp.tool()
def generate_hyper3d_model_via_images(
    ctx: Context,
    input_image_paths: list[str]=None,
    input_image_urls: list[str]=None,
    bbox_condition: list[float]=None
) -> str:
    """
    通过提供所需资源的图像使用Hyper3D生成3D资源，并将生成的资源导入Blender。
    3D资源具有内置材质。
    生成的模型具有标准化尺寸，因此生成后重新缩放可能很有用。
    
    参数：
    - input_image_paths: 输入图像的**绝对**路径。即使只提供一张图像，也要将其包装成列表。如果Hyper3D Rodin处于MAIN_SITE模式则必需。
    - input_image_urls: 输入图像的URL。即使只提供一张图像，也要将其包装成列表。如果Hyper3D Rodin处于FAL_AI模式则必需。
    - bbox_condition: 可选。如果给出，必须是长度为3的整数列表。控制模型的[长度、宽度、高度]比例。

    一次只能给出{input_image_paths, input_image_urls}中的一个，取决于Hyper3D Rodin的当前模式。
    返回表示成功或失败的消息。
    """
    if input_image_paths is not None and input_image_urls is not None:
        return f"错误：给出了冲突的参数！"
    if input_image_paths is None and input_image_urls is None:
        return f"错误：未提供图像！"
    if input_image_paths is not None:
        if not all(os.path.exists(i) for i in input_image_paths):
            return "错误：不是所有图像路径都有效！"
        images = []
        for path in input_image_paths:
            with open(path, "rb") as f:
                images.append(
                    (Path(path).suffix, base64.b64encode(f.read()).decode("ascii"))
                )
    elif input_image_urls is not None:
        if not all(urlparse(i) for i in input_image_paths):
            return "错误：不是所有图像URL都有效！"
        images = input_image_urls.copy()
    try:
        blender = get_blender_connection()
        result = blender.send_command("create_rodin_job", {
            "text_prompt": None,
            "images": images,
            "bbox_condition": _process_bbox(bbox_condition),
        })
        succeed = result.get("submit_time", False)
        if succeed:
            return json.dumps({
                "task_uuid": result["uuid"],
                "subscription_key": result["jobs"]["subscription_key"],
            }, ensure_ascii=False)
        else:
            return json.dumps(result, ensure_ascii=False)
    except Exception as e:
        logger.error(f"生成Hyper3D任务出错：{str(e)}")
        return f"生成Hyper3D任务出错：{str(e)}"

@mcp.tool()
def poll_rodin_job_status(
    ctx: Context,
    subscription_key: str=None,
    request_id: str=None,
):
    """
    检查Hyper3D Rodin生成任务是否完成。

    对于Hyper3D Rodin模式MAIN_SITE：
        参数：
        - subscription_key: 生成模型步骤中提供的subscription_key。

        返回状态列表。如果所有状态都是"Done"，任务就完成了。
        如果出现"Failed"，生成过程失败。
        这是一个轮询API，所以只有在状态最终确定（"Done"或"Canceled"）时才继续。

    对于Hyper3D Rodin模式FAL_AI：
        参数：
        - request_id: 生成模型步骤中提供的request_id。

        返回生成任务状态。如果状态是"COMPLETED"，任务就完成了。
        如果状态是"IN_PROGRESS"，任务正在进行中。
        如果状态不是"COMPLETED"、"IN_PROGRESS"、"IN_QUEUE"中的任何一个，生成过程可能失败。
        这是一个轮询API，所以只有在状态最终确定（"COMPLETED"或某个失败状态）时才继续。
    """
    try:
        blender = get_blender_connection()
        kwargs = {}
        if subscription_key:
            kwargs = {
                "subscription_key": subscription_key,
            }
        elif request_id:
            kwargs = {
                "request_id": request_id,
            }
        result = blender.send_command("poll_rodin_job_status", kwargs)
        return result
    except Exception as e:
        logger.error(f"生成Hyper3D任务出错：{str(e)}")
        return f"生成Hyper3D任务出错：{str(e)}"

@mcp.tool()
def import_generated_asset(
    ctx: Context,
    name: str,
    task_uuid: str=None,
    request_id: str=None,
):
    """
    在生成任务完成后导入由Hyper3D Rodin生成的资源。

    参数：
    - name: 场景中物体的名称
    - task_uuid: 对于Hyper3D Rodin模式MAIN_SITE：生成模型步骤中提供的task_uuid。
    - request_id: 对于Hyper3D Rodin模式FAL_AI：生成模型步骤中提供的request_id。

    只根据Hyper3D Rodin模式给出{task_uuid, request_id}中的一个！
    返回资源是否成功导入。
    """
    try:
        blender = get_blender_connection()
        kwargs = {
            "name": name
        }
        if task_uuid:
            kwargs["task_uuid"] = task_uuid
        elif request_id:
            kwargs["request_id"] = request_id
        result = blender.send_command("import_generated_asset", kwargs)
        return result
    except Exception as e:
        logger.error(f"生成Hyper3D任务出错：{str(e)}")
        return f"生成Hyper3D任务出错：{str(e)}"

@mcp.prompt()
def asset_creation_strategy() -> str:
    """定义在Blender中创建资源的首选策略"""
    return """在Blender中创建3D内容时，始终先检查是否有集成可用：

    0. 在任何操作之前，始终通过get_scene_info()检查场景
    1. 首先使用以下工具验证以下集成是否启用：
        1. PolyHaven
            使用get_polyhaven_status()验证其状态
            如果PolyHaven已启用：
            - 对于物体/模型：使用download_polyhaven_asset()，asset_type="models"
            - 对于材质/纹理：使用download_polyhaven_asset()，asset_type="textures"
            - 对于环境照明：使用download_polyhaven_asset()，asset_type="hdris"
        2. Sketchfab
            Sketchfab在逼真模型方面很出色，模型种类比PolyHaven更丰富。
            使用get_sketchfab_status()验证其状态
            如果Sketchfab已启用：
            - 对于物体/模型：首先使用search_sketchfab_models()进行查询搜索
            - 然后使用download_sketchfab_model()和UID下载特定模型
            - 注意只能访问可下载的模型，且必须正确配置API密钥
            - Sketchfab的模型种类比PolyHaven更丰富，特别是对于特定主题
        3. Hyper3D(Rodin)
            Hyper3D Rodin擅长为单个物品生成3D模型。
            所以不要尝试：
            1. 一次性生成整个场景
            2. 使用Hyper3D生成地面
            3. 分别生成物品的各个部分然后再组合

            使用get_hyper3d_status()验证其状态
            如果Hyper3D已启用：
            - 对于物体/模型，执行以下步骤：
                1. 创建模型生成任务
                    - 如果提供了图像，使用generate_hyper3d_model_via_images()
                    - 如果使用文本提示生成3D资源，使用generate_hyper3d_model_via_text()
                    如果密钥类型是free_trial且返回余额不足错误，告诉用户免费试用密钥每天只能生成有限的模型，他们可以选择：
                    - 等待一天再试
                    - 去hyper3d.ai了解如何获取自己的API密钥
                    - 去fal.ai获取自己的私人API密钥
                2. 轮询状态
                    - 使用poll_rodin_job_status()检查生成任务是否完成或失败
                3. 导入资源
                    - 使用import_generated_asset()导入生成的GLB模型资源
                4. 导入资源后，始终检查导入网格的world_bounding_box，并调整网格的位置和大小
                    调整导入网格的位置、缩放、旋转，使网格在正确的位置。

                您可以通过运行python代码复制物体来重用之前生成的资源，而无需创建另一个生成任务。

    3. 始终检查每个物品的world_bounding_box，以确保：
        - 确保所有不应该重叠的物体都没有重叠。
        - 物品具有正确的空间关系。
    
    4. 推荐的资源来源优先级：
        - 对于特定的现有物体：首先尝试Sketchfab，然后是PolyHaven
        - 对于通用物体/家具：首先尝试PolyHaven，然后是Sketchfab
        - 对于图书馆中不可用的自定义或独特物品：使用Hyper3D Rodin
        - 对于环境照明：使用PolyHaven HDRIs
        - 对于材质/纹理：使用PolyHaven纹理

    只有在以下情况时才回退到脚本编写：
    - PolyHaven、Sketchfab和Hyper3D都被禁用
    - 明确请求简单的基元
    - 任何图书馆中都不存在合适的资源
    - Hyper3D Rodin生成所需资源失败
    - 任务特别需要基本材质/颜色
    """

# 主执行
def main():
    """运行MCP服务器"""
    mcp.run()

if __name__ == "__main__":
    main()
