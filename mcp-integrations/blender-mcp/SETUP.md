# Blender MCP 设置指南

## 快速开始

### 1. 安装MCP依赖

首先确保你安装了必要的Python依赖：

```bash
cd /Users/<USER>/projects/chronnote/chronnoteV6/mcp-integrations/blender-mcp
pip install -r requirements.txt
```

### 2. 安装Blender插件

1. 打开Blender
2. 进入 Edit > Preferences > Add-ons
3. 点击 "Install..." 并选择 `addon.py` 文件
4. 启用插件：勾选 "Interface: Blender MCP" 旁边的复选框

### 3. 配置Claude Desktop

将提供的配置添加到你的Claude Desktop配置文件中：

**macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`

复制 `claude_desktop_config.json` 的内容到你的配置文件中，或者手动添加：

```json
{
    "mcpServers": {
        "blender": {
            "command": "python",
            "args": [
                "/Users/<USER>/projects/chronnote/chronnoteV6/mcp-integrations/blender-mcp/server.py"
            ],
            "cwd": "/Users/<USER>/projects/chronnote/chronnoteV6/mcp-integrations/blender-mcp"
        }
    }
}
```

### 4. 使用方法

1. **启动Blender** 并确保插件已启用
2. **在Blender中**：
   - 按 `N` 键打开侧边栏（如果未显示）
   - 找到 "BlenderMCP" 标签
   - 可选：启用你想要的集成（Poly Haven、Sketchfab、Hyper3D）
   - 点击 "连接到MCP服务器"
3. **在Claude Desktop中**：
   - 重启Claude Desktop（如果已运行）
   - 你应该看到一个锤子图标，表示Blender MCP工具可用

## 可选集成

### Poly Haven (材质、HDRIs、模型)
- 无需API密钥
- 提供高质量的材质、环境贴图和一些模型
- 在Blender中勾选"使用Poly Haven资源"

### Sketchfab (3D模型)
- 需要API密钥
- 提供大量的3D模型
- 获取API密钥：
  1. 访问 [Sketchfab.com](https://sketchfab.com)
  2. 注册/登录账户
  3. 进入账户设置 > API设置
  4. 生成新的API密钥
  5. 在Blender插件中输入API密钥

### Hyper3D Rodin (AI生成3D模型)
- 提供免费试用密钥（每日有限制）
- 也可以使用自己的API密钥
- 在Blender中：
  - 勾选"使用Hyper3D Rodin 3D模型生成"
  - 点击"设置免费试用API密钥"（或输入你自己的密钥）

## 示例命令

在Claude中，你可以尝试以下命令：

- "获取当前Blender场景的信息"
- "创建一个立方体并将其设置为红色"
- "从Poly Haven下载一个木材纹理并应用到立方体上"
- "搜索Sketchfab上的汽车模型"
- "使用Hyper3D生成一个咖啡杯的3D模型"
- "获取Blender视口的截图"

## 故障排除

### 连接问题
- 确保Blender插件服务器正在运行
- 检查Claude Desktop配置是否正确
- 尝试重启Blender和Claude Desktop

### 权限问题
- 确保Python脚本有执行权限
- 检查文件路径是否正确

### API集成问题
- 验证API密钥是否正确
- 检查网络连接
- 查看Blender控制台的错误信息

## 高级配置

### 自定义端口
如果9876端口被占用，可以在Blender插件设置中更改端口号。

### 虚拟环境
建议在虚拟环境中运行：

```bash
python3 -m venv venv
source venv/bin/activate  # macOS/Linux
pip install -r requirements.txt
```

### 开发模式
如果要修改代码，可以直接编辑 `server.py` 和 `addon.py` 文件。重启相应组件以应用更改。

## 注意事项

- 始终保存你的Blender工作，特别是在使用代码执行功能时
- Poly Haven资源下载可能需要一些时间
- Hyper3D生成可能需要几分钟时间
- 某些Sketchfab模型可能需要特殊权限才能下载

## 支持

如果遇到问题，请检查：
1. Blender控制台的错误信息
2. Claude Desktop的日志
3. 确保所有依赖项都已正确安装
