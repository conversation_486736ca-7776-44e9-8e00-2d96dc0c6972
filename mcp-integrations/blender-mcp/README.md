# Blender MCP Integration

这是一个基于 [blender-mcp](https://github.com/ahujasid/blender-mcp) 项目的Blender MCP集成，允许Claude AI通过MCP协议直接与Blender交互。

## 功能特性

- **双向通信**: 通过基于Socket的服务器连接Claude AI和Blender
- **物体操作**: 在Blender中创建、修改和删除3D物体
- **材质控制**: 应用和修改材质与颜色
- **场景检查**: 获取当前Blender场景的详细信息
- **代码执行**: 从Claude运行任意Python代码
- **Poly Haven集成**: 下载HDRIs、纹理和模型
- **Sketchfab集成**: 搜索和下载3D模型
- **Hyper3D集成**: AI生成3D模型
- **视口截图**: 获取Blender视口截图

## 安装步骤

### 1. 安装依赖

确保你有Python 3.10或更新版本，以及uv包管理器：

**Mac用户:**
```bash
brew install uv
```

**Windows用户:**
```bash
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```

### 2. 安装Blender插件

1. 下载 `addon.py` 文件
2. 打开Blender
3. 进入 Edit > Preferences > Add-ons
4. 点击 "Install..." 并选择 `addon.py` 文件
5. 启用插件：勾选 "Interface: Blender MCP" 旁边的复选框

### 3. 配置Claude Desktop

编辑你的Claude Desktop配置文件 `claude_desktop_config.json`：

```json
{
    "mcpServers": {
        "blender": {
            "command": "uvx",
            "args": [
                "blender-mcp"
            ]
        }
    }
}
```

或者如果你想使用本地版本：

```json
{
    "mcpServers": {
        "blender": {
            "command": "python",
            "args": [
                "mcp-integrations/blender-mcp/server.py"
            ]
        }
    }
}
```

## 使用方法

### 启动连接

1. 在Blender中，进入3D视图侧边栏（按N键显示）
2. 找到 "BlenderMCP" 标签
3. 可选：启用Poly Haven复选框以使用其API资源
4. 点击 "Connect to Claude"
5. 确保MCP服务器在终端中运行

### 与Claude交互

配置完成后，在Claude中你会看到一个锤子图标，表示Blender MCP工具可用。

#### 示例命令

- "创建一个低多边形的地牢场景，有一条龙守护着一壶黄金"
- "使用Poly Haven的HDRIs、纹理和模型（如岩石和植被）创建海滩氛围"
- "给一张参考图片，在Blender中创建一个场景"
- "通过Hyper3D生成一个花园侏儒的3D模型"
- "让这辆车变成红色金属质感"
- "创建一个球体并将其放在立方体上方"
- "设置像工作室一样的照明"
- "将摄像机指向场景，并使其等距投影"

## 故障排除

- **连接问题**: 确保Blender插件服务器正在运行，MCP服务器已在Claude中配置
- **超时错误**: 尝试简化你的请求或将其分解为较小的步骤
- **Poly Haven集成**: Claude的行为有时不稳定
- **重新启动**: 如果仍有连接错误，请尝试重新启动Claude和Blender服务器

## 技术细节

### 通信协议

系统使用基于TCP套接字的简单JSON协议：

- **命令**作为带有`type`和可选`params`的JSON对象发送
- **响应**是带有`status`和`result`或`message`的JSON对象

## 限制和安全考虑

- `execute_blender_code`工具允许在Blender中运行任意Python代码，这可能很强大但也有潜在危险。在生产环境中请谨慎使用
- Poly Haven需要下载模型、纹理和HDRI图像。如果你不想使用它，请在Blender中的复选框中关闭它
- 复杂操作可能需要分解为较小的步骤

## 贡献

欢迎贡献！请随时提交Pull Request。

## 免责声明

这是第三方集成，不是由Blender制作的。
