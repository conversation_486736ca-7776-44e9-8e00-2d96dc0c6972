# 代码由Sid<PERSON>rth Ahuja创建：www.github.com/ahujasid © 2025
# 中文本地化版本

import bpy
import mathutils
import json
import threading
import socket
import time
import requests
import tempfile
import traceback
import os
import shutil
import zipfile
from bpy.props import StringProperty, IntProperty, BoolProperty, EnumProperty
import io
from contextlib import redirect_stdout, suppress

bl_info = {
    "name": "Blender MCP",
    "author": "BlenderMCP",
    "version": (1, 2),
    "blender": (3, 0, 0),
    "location": "View3D > Sidebar > BlenderMCP",
    "description": "通过MCP连接Blender到Claude",
    "category": "Interface",
}

RODIN_FREE_TRIAL_KEY = "k9TcfFoEhNd9cCPP2guHAHHHkctZHIRhZDywZ1euGUXwihbYLpOjQhofby80NJez"

class BlenderMCPServer:
    def __init__(self, host='localhost', port=9876):
        self.host = host
        self.port = port
        self.running = False
        self.socket = None
        self.server_thread = None
    
    def start(self):
        if self.running:
            print("服务器已在运行")
            return
            
        self.running = True
        
        try:
            # 创建socket
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.socket.bind((self.host, self.port))
            self.socket.listen(1)
            
            # 启动服务器线程
            self.server_thread = threading.Thread(target=self._server_loop)
            self.server_thread.daemon = True
            self.server_thread.start()
            
            print(f"BlenderMCP服务器已在 {self.host}:{self.port} 启动")
        except Exception as e:
            print(f"启动服务器失败：{str(e)}")
            self.stop()
            
    def stop(self):
        self.running = False
        
        # 关闭socket
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
        
        # 等待线程结束
        if self.server_thread:
            try:
                if self.server_thread.is_alive():
                    self.server_thread.join(timeout=1.0)
            except:
                pass
            self.server_thread = None
        
        print("BlenderMCP服务器已停止")
    
    def _server_loop(self):
        """在单独线程中的主服务器循环"""
        print("服务器线程已启动")
        self.socket.settimeout(1.0)  # 允许停止的超时
        
        while self.running:
            try:
                # 接受新连接
                try:
                    client, address = self.socket.accept()
                    print(f"已连接到客户端：{address}")
                    
                    # 在单独线程中处理客户端
                    client_thread = threading.Thread(
                        target=self._handle_client,
                        args=(client,)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                except socket.timeout:
                    # 只检查运行条件
                    continue
                except Exception as e:
                    print(f"接受连接时出错：{str(e)}")
                    time.sleep(0.5)
            except Exception as e:
                print(f"服务器循环出错：{str(e)}")
                if not self.running:
                    break
                time.sleep(0.5)
        
        print("服务器线程已停止")
    
    def _handle_client(self, client):
        """处理连接的客户端"""
        print("客户端处理器已启动")
        client.settimeout(None)  # 无超时
        buffer = b''
        
        try:
            while self.running:
                # 接收数据
                try:
                    data = client.recv(8192)
                    if not data:
                        print("客户端已断开连接")
                        break
                    
                    buffer += data
                    try:
                        # 尝试解析命令
                        command = json.loads(buffer.decode('utf-8'))
                        buffer = b''
                        
                        # 在Blender主线程中执行命令
                        def execute_wrapper():
                            try:
                                response = self.execute_command(command)
                                response_json = json.dumps(response, ensure_ascii=False)
                                try:
                                    client.sendall(response_json.encode('utf-8'))
                                except:
                                    print("发送响应失败 - 客户端已断开连接")
                            except Exception as e:
                                print(f"执行命令时出错：{str(e)}")
                                traceback.print_exc()
                                try:
                                    error_response = {
                                        "status": "error",
                                        "message": str(e)
                                    }
                                    client.sendall(json.dumps(error_response, ensure_ascii=False).encode('utf-8'))
                                except:
                                    pass
                            return None
                        
                        # 在主线程中安排执行
                        bpy.app.timers.register(execute_wrapper, first_interval=0.0)
                    except json.JSONDecodeError:
                        # 数据不完整，等待更多数据
                        pass
                except Exception as e:
                    print(f"接收数据时出错：{str(e)}")
                    break
        except Exception as e:
            print(f"客户端处理器出错：{str(e)}")
        finally:
            try:
                client.close()
            except:
                pass
            print("客户端处理器已停止")

    def execute_command(self, command):
        """在Blender主线程中执行命令"""
        try:            
            return self._execute_command_internal(command)
                
        except Exception as e:
            print(f"执行命令时出错：{str(e)}")
            traceback.print_exc()
            return {"status": "error", "message": str(e)}

    def _execute_command_internal(self, command):
        """内部命令执行，具有适当的上下文"""
        cmd_type = command.get("type")
        params = command.get("params", {})

        # 添加检查PolyHaven状态的处理器
        if cmd_type == "get_polyhaven_status":
            return {"status": "success", "result": self.get_polyhaven_status()}
        
        # 始终可用的基础处理器
        handlers = {
            "get_scene_info": self.get_scene_info,
            "get_object_info": self.get_object_info,
            "get_viewport_screenshot": self.get_viewport_screenshot,
            "execute_code": self.execute_code,
            "get_polyhaven_status": self.get_polyhaven_status,
            "get_hyper3d_status": self.get_hyper3d_status,
            "get_sketchfab_status": self.get_sketchfab_status,
        }
        
        # 仅在启用时添加Polyhaven处理器
        if bpy.context.scene.blendermcp_use_polyhaven:
            polyhaven_handlers = {
                "get_polyhaven_categories": self.get_polyhaven_categories,
                "search_polyhaven_assets": self.search_polyhaven_assets,
                "download_polyhaven_asset": self.download_polyhaven_asset,
                "set_texture": self.set_texture,
            }
            handlers.update(polyhaven_handlers)
        
        # 仅在启用时添加Hyper3d处理器
        if bpy.context.scene.blendermcp_use_hyper3d:
            hyper3d_handlers = {
                "create_rodin_job": self.create_rodin_job,
                "poll_rodin_job_status": self.poll_rodin_job_status,
                "import_generated_asset": self.import_generated_asset,
            }
            handlers.update(hyper3d_handlers)
            
        # 仅在启用时添加Sketchfab处理器
        if bpy.context.scene.blendermcp_use_sketchfab:
            sketchfab_handlers = {
                "search_sketchfab_models": self.search_sketchfab_models,
                "download_sketchfab_model": self.download_sketchfab_model,
            }
            handlers.update(sketchfab_handlers)

        handler = handlers.get(cmd_type)
        if handler:
            try:
                print(f"执行{cmd_type}的处理器")
                result = handler(**params)
                print(f"处理器执行完成")
                return {"status": "success", "result": result}
            except Exception as e:
                print(f"处理器出错：{str(e)}")
                traceback.print_exc()
                return {"status": "error", "message": str(e)}
        else:
            return {"status": "error", "message": f"未知命令类型：{cmd_type}"}

    def get_scene_info(self):
        """获取当前Blender场景的信息"""
        try:
            print("获取场景信息...")
            # 简化场景信息以减少数据大小
            scene_info = {
                "name": bpy.context.scene.name,
                "object_count": len(bpy.context.scene.objects),
                "objects": [],
                "materials_count": len(bpy.data.materials),
            }
            
            # 收集最少的物体信息（限制为前10个物体）
            for i, obj in enumerate(bpy.context.scene.objects):
                if i >= 10:  # 从20减少到10
                    break
                    
                obj_info = {
                    "name": obj.name,
                    "type": obj.type,
                    # 只包含基本位置数据
                    "location": [round(float(obj.location.x), 2), 
                                round(float(obj.location.y), 2), 
                                round(float(obj.location.z), 2)],
                }
                scene_info["objects"].append(obj_info)
            
            print(f"场景信息已收集：{len(scene_info['objects'])}个物体")
            return scene_info
        except Exception as e:
            print(f"get_scene_info出错：{str(e)}")
            traceback.print_exc()
            return {"error": str(e)}
    
    @staticmethod
    def _get_aabb(obj):
        """返回物体的世界空间轴对齐边界框（AABB）"""
        if obj.type != 'MESH':
            raise TypeError("物体必须是网格")

        # 获取本地空间中的边界框角点
        local_bbox_corners = [mathutils.Vector(corner) for corner in obj.bound_box]

        # 转换为世界坐标
        world_bbox_corners = [obj.matrix_world @ corner for corner in local_bbox_corners]

        # 计算轴对齐的最小/最大坐标
        min_corner = mathutils.Vector(map(min, zip(*world_bbox_corners)))
        max_corner = mathutils.Vector(map(max, zip(*world_bbox_corners)))

        return [
            [*min_corner], [*max_corner]
        ]

    def get_object_info(self, name):
        """获取特定物体的详细信息"""
        obj = bpy.data.objects.get(name)
        if not obj:
            raise ValueError(f"未找到物体：{name}")
        
        # 基本物体信息
        obj_info = {
            "name": obj.name,
            "type": obj.type,
            "location": [obj.location.x, obj.location.y, obj.location.z],
            "rotation": [obj.rotation_euler.x, obj.rotation_euler.y, obj.rotation_euler.z],
            "scale": [obj.scale.x, obj.scale.y, obj.scale.z],
            "visible": obj.visible_get(),
            "materials": [],
        }

        if obj.type == "MESH":
            bounding_box = self._get_aabb(obj)
            obj_info["world_bounding_box"] = bounding_box
        
        # 添加材质槽
        for slot in obj.material_slots:
            if slot.material:
                obj_info["materials"].append(slot.material.name)
        
        # 如果适用，添加网格数据
        if obj.type == 'MESH' and obj.data:
            mesh = obj.data
            obj_info["mesh"] = {
                "vertices": len(mesh.vertices),
                "edges": len(mesh.edges),
                "polygons": len(mesh.polygons),
            }
        
        return obj_info
    
    def get_viewport_screenshot(self, max_size=800, filepath=None, format="png"):
        """
        获取当前3D视口的截图并保存到指定路径。
        
        参数：
        - max_size: 图像最大尺寸的像素数
        - filepath: 保存截图文件的路径
        - format: 图像格式（png、jpg等）
        
        返回成功/错误状态
        """
        try:
            if not filepath:
                return {"error": "未提供文件路径"}
            
            # 查找活动的3D视口
            area = None
            for a in bpy.context.screen.areas:
                if a.type == 'VIEW_3D':
                    area = a
                    break
            
            if not area:
                return {"error": "未找到3D视口"}
            
            # 使用适当的上下文覆盖截图
            with bpy.context.temp_override(area=area):
                bpy.ops.screen.screenshot_area(filepath=filepath)
            
            # 如果需要，加载并调整大小
            img = bpy.data.images.load(filepath)
            width, height = img.size
            
            if max(width, height) > max_size:
                scale = max_size / max(width, height)
                new_width = int(width * scale)
                new_height = int(height * scale)
                img.scale(new_width, new_height)
                
                # 设置格式并保存
                img.file_format = format.upper()
                img.save()
                width, height = new_width, new_height
            
            # 清理Blender图像数据
            bpy.data.images.remove(img)
            
            return {
                "success": True,
                "width": width,
                "height": height,
                "filepath": filepath
            }
            
        except Exception as e:
            return {"error": str(e)}
    
    def execute_code(self, code):
        """执行任意Blender Python代码"""
        # 这很强大但可能有危险 - 请谨慎使用
        try:
            # 为执行创建本地命名空间
            namespace = {"bpy": bpy}

            # 在执行期间捕获stdout，并将其作为结果返回
            capture_buffer = io.StringIO()
            with redirect_stdout(capture_buffer):
                exec(code, namespace)
            
            captured_output = capture_buffer.getvalue()
            return {"executed": True, "result": captured_output}
        except Exception as e:
            raise Exception(f"代码执行错误：{str(e)}")

    def get_polyhaven_categories(self, asset_type):
        """从Polyhaven获取特定资源类型的类别"""
        try:
            if asset_type not in ["hdris", "textures", "models", "all"]:
                return {"error": f"无效的资源类型：{asset_type}。必须是以下之一：hdris、textures、models、all"}
                
            response = requests.get(f"https://api.polyhaven.com/categories/{asset_type}")
            if response.status_code == 200:
                return {"categories": response.json()}
            else:
                return {"error": f"API请求失败，状态码{response.status_code}"}
        except Exception as e:
            return {"error": str(e)}
    
    def search_polyhaven_assets(self, asset_type=None, categories=None):
        """从Polyhaven搜索资源，可选过滤"""
        try:
            url = "https://api.polyhaven.com/assets"
            params = {}
            
            if asset_type and asset_type != "all":
                if asset_type not in ["hdris", "textures", "models"]:
                    return {"error": f"无效的资源类型：{asset_type}。必须是以下之一：hdris、textures、models、all"}
                params["type"] = asset_type
                
            if categories:
                params["categories"] = categories
                
            response = requests.get(url, params=params)
            if response.status_code == 200:
                # 限制响应大小以避免Blender过载
                assets = response.json()
                # 只返回前20个资源以保持响应大小可管理
                limited_assets = {}
                for i, (key, value) in enumerate(assets.items()):
                    if i >= 20:  # 限制为20个资源
                        break
                    limited_assets[key] = value
                
                return {"assets": limited_assets, "total_count": len(assets), "returned_count": len(limited_assets)}
            else:
                return {"error": f"API请求失败，状态码{response.status_code}"}
        except Exception as e:
            return {"error": str(e)}
    
    def download_polyhaven_asset(self, asset_id, asset_type, resolution="1k", file_format=None):
        """下载并导入Polyhaven资源"""
        try:
            # 首先获取文件信息
            files_response = requests.get(f"https://api.polyhaven.com/files/{asset_id}")
            if files_response.status_code != 200:
                return {"error": f"获取资源文件失败：{files_response.status_code}"}
            
            files_data = files_response.json()
            
            # 处理不同的资源类型
            if asset_type == "hdris":
                # 对于HDRIs，下载.hdr或.exr文件
                if not file_format:
                    file_format = "hdr"  # HDRIs的默认格式
                
                if "hdri" in files_data and resolution in files_data["hdri"] and file_format in files_data["hdri"][resolution]:
                    file_info = files_data["hdri"][resolution][file_format]
                    file_url = file_info["url"]
                    
                    # 对于HDRIs，我们需要先保存到临时文件
                    # 因为Blender无法从内存中正确加载HDR数据
                    with tempfile.NamedTemporaryFile(suffix=f".{file_format}", delete=False) as tmp_file:
                        # 下载文件
                        response = requests.get(file_url)
                        if response.status_code != 200:
                            return {"error": f"下载HDRI失败：{response.status_code}"}
                        
                        tmp_file.write(response.content)
                        tmp_path = tmp_file.name
                    
                    try:
                        # 如果不存在，创建新世界
                        if not bpy.data.worlds:
                            bpy.data.worlds.new("World")
                        
                        world = bpy.data.worlds[0]
                        world.use_nodes = True
                        node_tree = world.node_tree
                        
                        # 清除现有节点
                        for node in node_tree.nodes:
                            node_tree.nodes.remove(node)
                        
                        # 创建节点
                        tex_coord = node_tree.nodes.new(type='ShaderNodeTexCoord')
                        tex_coord.location = (-800, 0)
                        
                        mapping = node_tree.nodes.new(type='ShaderNodeMapping')
                        mapping.location = (-600, 0)
                        
                        # 从临时文件加载图像
                        env_tex = node_tree.nodes.new(type='ShaderNodeTexEnvironment')
                        env_tex.location = (-400, 0)
                        env_tex.image = bpy.data.images.load(tmp_path)
                        
                        # 使用在所有Blender版本中都存在的颜色空间
                        if file_format.lower() == 'exr':
                            # 尝试为EXR文件使用Linear颜色空间
                            try:
                                env_tex.image.colorspace_settings.name = 'Linear'
                            except:
                                # 如果Linear不可用，回退到Non-Color
                                env_tex.image.colorspace_settings.name = 'Non-Color'
                        else:  # hdr
                            # 对于HDR文件，按顺序尝试这些选项
                            for color_space in ['Linear', 'Linear Rec.709', 'Non-Color']:
                                try:
                                    env_tex.image.colorspace_settings.name = color_space
                                    break  # 如果成功设置颜色空间就停止
                                except:
                                    continue
                        
                        background = node_tree.nodes.new(type='ShaderNodeBackground')
                        background.location = (-200, 0)
                        
                        output = node_tree.nodes.new(type='ShaderNodeOutputWorld')
                        output.location = (0, 0)
                        
                        # 连接节点
                        node_tree.links.new(tex_coord.outputs['Generated'], mapping.inputs['Vector'])
                        node_tree.links.new(mapping.outputs['Vector'], env_tex.inputs['Vector'])
                        node_tree.links.new(env_tex.outputs['Color'], background.inputs['Color'])
                        node_tree.links.new(background.outputs['Background'], output.inputs['Surface'])
                        
                        # 设置为活动世界
                        bpy.context.scene.world = world
                        
                        # 清理临时文件
                        try:
                            tempfile._cleanup()  # 这将清理所有临时文件
                        except:
                            pass
                        
                        return {
                            "success": True, 
                            "message": f"HDRI {asset_id} 导入成功",
                            "image_name": env_tex.image.name
                        }
                    except Exception as e:
                        return {"error": f"在Blender中设置HDRI失败：{str(e)}"}
                else:
                    return {"error": f"此HDRI不支持请求的分辨率或格式"}
                    
            elif asset_type == "textures":
                if not file_format:
                    file_format = "jpg"  # 纹理的默认格式
                
                downloaded_maps = {}
                
                try:
                    for map_type in files_data:
                        if map_type not in ["blend", "gltf"]:  # 跳过非纹理文件
                            if resolution in files_data[map_type] and file_format in files_data[map_type][resolution]:
                                file_info = files_data[map_type][resolution][file_format]
                                file_url = file_info["url"]
                                
                                # 像HDRIs一样使用NamedTemporaryFile
                                with tempfile.NamedTemporaryFile(suffix=f".{file_format}", delete=False) as tmp_file:
                                    # 下载文件
                                    response = requests.get(file_url)
                                    if response.status_code == 200:
                                        tmp_file.write(response.content)
                                        tmp_path = tmp_file.name
                                        
                                        # 从临时文件加载图像
                                        image = bpy.data.images.load(tmp_path)
                                        image.name = f"{asset_id}_{map_type}.{file_format}"
                                        
                                        # 将图像打包到.blend文件中
                                        image.pack()
                                        
                                        # 根据贴图类型设置颜色空间
                                        if map_type in ['color', 'diffuse', 'albedo']:
                                            try:
                                                image.colorspace_settings.name = 'sRGB'
                                            except:
                                                pass
                                        else:
                                            try:
                                                image.colorspace_settings.name = 'Non-Color'
                                            except:
                                                pass
                                        
                                        downloaded_maps[map_type] = image
                                        
                                        # 清理临时文件
                                        try:
                                            os.unlink(tmp_path)
                                        except:
                                            pass
                
                    if not downloaded_maps:
                        return {"error": f"未找到请求分辨率和格式的纹理贴图"}
                    
                    # 使用下载的纹理创建新材质
                    mat = bpy.data.materials.new(name=asset_id)
                    mat.use_nodes = True
                    nodes = mat.node_tree.nodes
                    links = mat.node_tree.links
                    
                    # 清除默认节点
                    for node in nodes:
                        nodes.remove(node)
                    
                    # 创建输出节点
                    output = nodes.new(type='ShaderNodeOutputMaterial')
                    output.location = (300, 0)
                    
                    # 创建Principled BSDF节点
                    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
                    principled.location = (0, 0)
                    links.new(principled.outputs[0], output.inputs[0])
                    
                    # 根据可用贴图添加纹理节点
                    tex_coord = nodes.new(type='ShaderNodeTexCoord')
                    tex_coord.location = (-800, 0)
                    
                    mapping = nodes.new(type='ShaderNodeMapping')
                    mapping.location = (-600, 0)
                    mapping.vector_type = 'TEXTURE'  # 从默认'POINT'改为'TEXTURE'
                    links.new(tex_coord.outputs['UV'], mapping.inputs['Vector'])
                    
                    # 纹理节点的位置偏移
                    x_pos = -400
                    y_pos = 300
                    
                    # 连接不同的纹理贴图
                    for map_type, image in downloaded_maps.items():
                        tex_node = nodes.new(type='ShaderNodeTexImage')
                        tex_node.location = (x_pos, y_pos)
                        tex_node.image = image
                        
                        # 根据贴图类型设置颜色空间
                        if map_type.lower() in ['color', 'diffuse', 'albedo']:
                            try:
                                tex_node.image.colorspace_settings.name = 'sRGB'
                            except:
                                pass  # 如果sRGB不可用则使用默认
                        else:
                            try:
                                tex_node.image.colorspace_settings.name = 'Non-Color'
                            except:
                                pass  # 如果Non-Color不可用则使用默认
                        
                        links.new(mapping.outputs['Vector'], tex_node.inputs['Vector'])
                        
                        # 连接到Principled BSDF上的适当输入
                        if map_type.lower() in ['color', 'diffuse', 'albedo']:
                            links.new(tex_node.outputs['Color'], principled.inputs['Base Color'])
                        elif map_type.lower() in ['roughness', 'rough']:
                            links.new(tex_node.outputs['Color'], principled.inputs['Roughness'])
                        elif map_type.lower() in ['metallic', 'metalness', 'metal']:
                            links.new(tex_node.outputs['Color'], principled.inputs['Metallic'])
                        elif map_type.lower() in ['normal', 'nor']:
                            # 添加法线贴图节点
                            normal_map = nodes.new(type='ShaderNodeNormalMap')
                            normal_map.location = (x_pos + 200, y_pos)
                            links.new(tex_node.outputs['Color'], normal_map.inputs['Color'])
                            links.new(normal_map.outputs['Normal'], principled.inputs['Normal'])
                        elif map_type in ['displacement', 'disp', 'height']:
                            # 添加置换节点
                            disp_node = nodes.new(type='ShaderNodeDisplacement')
                            disp_node.location = (x_pos + 200, y_pos - 200)
                            links.new(tex_node.outputs['Color'], disp_node.inputs['Height'])
                            links.new(disp_node.outputs['Displacement'], output.inputs['Displacement'])
                        
                        y_pos -= 250
                    
                    return {
                        "success": True, 
                        "message": f"纹理 {asset_id} 作为材质导入",
                        "material": mat.name,
                        "maps": list(downloaded_maps.keys())
                    }
                
                except Exception as e:
                    return {"error": f"处理纹理失败：{str(e)}"}
                
            elif asset_type == "models":
                # 对于模型，如果可用优先选择glTF格式
                if not file_format:
                    file_format = "gltf"  # 模型的默认格式
                
                if file_format in files_data and resolution in files_data[file_format]:
                    file_info = files_data[file_format][resolution][file_format]
                    file_url = file_info["url"]
                    
                    # 创建临时目录来存储模型及其依赖项
                    temp_dir = tempfile.mkdtemp()
                    main_file_path = ""
                    
                    try:
                        # 下载主模型文件
                        main_file_name = file_url.split("/")[-1]
                        main_file_path = os.path.join(temp_dir, main_file_name)
                        
                        response = requests.get(file_url)
                        if response.status_code != 200:
                            return {"error": f"下载模型失败：{response.status_code}"}
                        
                        with open(main_file_path, "wb") as f:
                            f.write(response.content)
                        
                        # 检查包含的文件并下载它们
                        if "include" in file_info and file_info["include"]:
                            for include_path, include_info in file_info["include"].items():
                                # 获取包含文件的URL - 这是修复
                                include_url = include_info["url"]
                                
                                # 为包含的文件创建目录结构
                                include_file_path = os.path.join(temp_dir, include_path)
                                os.makedirs(os.path.dirname(include_file_path), exist_ok=True)
                                
                                # 下载包含的文件
                                include_response = requests.get(include_url)
                                if include_response.status_code == 200:
                                    with open(include_file_path, "wb") as f:
                                        f.write(include_response.content)
                                else:
                                    print(f"下载包含文件失败：{include_path}")
                        
                        # 将模型导入Blender
                        if file_format == "gltf" or file_format == "glb":
                            bpy.ops.import_scene.gltf(filepath=main_file_path)
                        elif file_format == "fbx":
                            bpy.ops.import_scene.fbx(filepath=main_file_path)
                        elif file_format == "obj":
                            bpy.ops.import_scene.obj(filepath=main_file_path)
                        elif file_format == "blend":
                            # 对于blend文件，我们需要追加或链接
                            with bpy.data.libraries.load(main_file_path, link=False) as (data_from, data_to):
                                data_to.objects = data_from.objects
                            
                            # 将物体链接到场景
                            for obj in data_to.objects:
                                if obj is not None:
                                    bpy.context.collection.objects.link(obj)
                        else:
                            return {"error": f"不支持的模型格式：{file_format}"}
                        
                        # 获取导入物体的名称
                        imported_objects = [obj.name for obj in bpy.context.selected_objects]
                        
                        return {
                            "success": True, 
                            "message": f"模型 {asset_id} 导入成功",
                            "imported_objects": imported_objects
                        }
                    except Exception as e:
                        return {"error": f"导入模型失败：{str(e)}"}
                    finally:
                        # 清理临时目录
                        with suppress(Exception):
                            shutil.rmtree(temp_dir)
                else:
                    return {"error": f"此模型不支持请求的格式或分辨率"}
                
            else:
                return {"error": f"不支持的资源类型：{asset_type}"}
                
        except Exception as e:
            return {"error": f"下载资源失败：{str(e)}"}

    def set_texture(self, object_name, texture_id):
        """通过创建新材质将之前下载的Polyhaven纹理应用到物体上"""
        try:
            # 获取物体
            obj = bpy.data.objects.get(object_name)
            if not obj:
                return {"error": f"未找到物体：{object_name}"}
            
            # 确保物体可以接受材质
            if not hasattr(obj, 'data') or not hasattr(obj.data, 'materials'):
                return {"error": f"物体 {object_name} 无法接受材质"}
            
            # 查找与此纹理相关的所有图像并确保它们正确加载
            texture_images = {}
            for img in bpy.data.images:
                if img.name.startswith(texture_id + "_"):
                    # 从图像名称中提取贴图类型
                    map_type = img.name.split('_')[-1].split('.')[0]
                    
                    # 强制重新加载图像
                    img.reload()
                    
                    # 确保适当的颜色空间
                    if map_type.lower() in ['color', 'diffuse', 'albedo']:
                        try:
                            img.colorspace_settings.name = 'sRGB'
                        except:
                            pass
                    else:
                        try:
                            img.colorspace_settings.name = 'Non-Color'
                        except:
                            pass
                    
                    # 确保图像被打包
                    if not img.packed_file:
                        img.pack()
                    
                    texture_images[map_type] = img
                    print(f"加载纹理贴图：{map_type} - {img.name}")
                    
                    # 调试信息
                    print(f"图像尺寸：{img.size[0]}x{img.size[1]}")
                    print(f"颜色空间：{img.colorspace_settings.name}")
                    print(f"文件格式：{img.file_format}")
                    print(f"已打包：{bool(img.packed_file)}")

            if not texture_images:
                return {"error": f"未找到 {texture_id} 的纹理图像。请先下载纹理。"}
            
            # 创建新材质
            new_mat_name = f"{texture_id}_material_{object_name}"
            
            # 移除任何同名的现有材质以避免冲突
            existing_mat = bpy.data.materials.get(new_mat_name)
            if existing_mat:
                bpy.data.materials.remove(existing_mat)
            
            new_mat = bpy.data.materials.new(name=new_mat_name)
            new_mat.use_nodes = True
            
            # 设置材质节点
            nodes = new_mat.node_tree.nodes
            links = new_mat.node_tree.links
            
            # 清除默认节点
            nodes.clear()
            
            # 创建输出节点
            output = nodes.new(type='ShaderNodeOutputMaterial')
            output.location = (600, 0)
            
            # 创建Principled BSDF节点
            principled = nodes.new(type='ShaderNodeBsdfPrincipled')
            principled.location = (300, 0)
            links.new(principled.outputs[0], output.inputs[0])
            
            # 根据可用贴图添加纹理节点
            tex_coord = nodes.new(type='ShaderNodeTexCoord')
            tex_coord.location = (-800, 0)
            
            mapping = nodes.new(type='ShaderNodeMapping')
            mapping.location = (-600, 0)
            mapping.vector_type = 'TEXTURE'  # 从默认'POINT'改为'TEXTURE'
            links.new(tex_coord.outputs['UV'], mapping.inputs['Vector'])
            
            # 纹理节点的位置偏移
            x_pos = -400
            y_pos = 300
            
            # 连接不同的纹理贴图
            for map_type, image in texture_images.items():
                tex_node = nodes.new(type='ShaderNodeTexImage')
                tex_node.location = (x_pos, y_pos)
                tex_node.image = image
                
                # 根据贴图类型设置颜色空间
                if map_type.lower() in ['color', 'diffuse', 'albedo']:
                    try:
                        tex_node.image.colorspace_settings.name = 'sRGB'
                    except:
                        pass  # 如果sRGB不可用则使用默认
                else:
                    try:
                        tex_node.image.colorspace_settings.name = 'Non-Color'
                    except:
                        pass  # 如果Non-Color不可用则使用默认
                
                links.new(mapping.outputs['Vector'], tex_node.inputs['Vector'])
                
                # 连接到Principled BSDF上的适当输入
                if map_type.lower() in ['color', 'diffuse', 'albedo']:
                    links.new(tex_node.outputs['Color'], principled.inputs['Base Color'])
                elif map_type.lower() in ['roughness', 'rough']:
                    links.new(tex_node.outputs['Color'], principled.inputs['Roughness'])
                elif map_type.lower() in ['metallic', 'metalness', 'metal']:
                    links.new(tex_node.outputs['Color'], principled.inputs['Metallic'])
                elif map_type.lower() in ['normal', 'nor', 'dx', 'gl']:
                    # 添加法线贴图节点
                    normal_map = nodes.new(type='ShaderNodeNormalMap')
                    normal_map.location = (x_pos + 200, y_pos)
                    links.new(tex_node.outputs['Color'], normal_map.inputs['Color'])
                    links.new(normal_map.outputs['Normal'], principled.inputs['Normal'])
                elif map_type.lower() in ['displacement', 'disp', 'height']:
                    # 添加置换节点
                    disp_node = nodes.new(type='ShaderNodeDisplacement')
                    disp_node.location = (x_pos + 200, y_pos - 200)
                    disp_node.inputs['Scale'].default_value = 0.1  # 减少置换强度
                    links.new(tex_node.outputs['Color'], disp_node.inputs['Height'])
                    links.new(disp_node.outputs['Displacement'], output.inputs['Displacement'])
                
                y_pos -= 250
            
            # 第二遍：使用适当处理特殊情况连接节点
            texture_nodes = {}
            
            # 首先找到所有纹理节点并按贴图类型存储它们
            for node in nodes:
                if node.type == 'TEX_IMAGE' and node.image:
                    for map_type, image in texture_images.items():
                        if node.image == image:
                            texture_nodes[map_type] = node
                            break
            
            # 现在使用节点而不是图像连接一切
            # 处理基础色（漫反射）
            for map_name in ['color', 'diffuse', 'albedo']:
                if map_name in texture_nodes:
                    links.new(texture_nodes[map_name].outputs['Color'], principled.inputs['Base Color'])
                    print(f"连接 {map_name} 到 Base Color")
                    break
            
            # 处理粗糙度
            for map_name in ['roughness', 'rough']:
                if map_name in texture_nodes:
                    links.new(texture_nodes[map_name].outputs['Color'], principled.inputs['Roughness'])
                    print(f"连接 {map_name} 到 Roughness")
                    break
            
            # 处理金属度
            for map_name in ['metallic', 'metalness', 'metal']:
                if map_name in texture_nodes:
                    links.new(texture_nodes[map_name].outputs['Color'], principled.inputs['Metallic'])
                    print(f"连接 {map_name} 到 Metallic")
                    break
            
            # 处理法线贴图
            for map_name in ['gl', 'dx', 'nor']:
                if map_name in texture_nodes:
                    normal_map_node = nodes.new(type='ShaderNodeNormalMap')
                    normal_map_node.location = (100, 100)
                    links.new(texture_nodes[map_name].outputs['Color'], normal_map_node.inputs['Color'])
                    links.new(normal_map_node.outputs['Normal'], principled.inputs['Normal'])
                    print(f"连接 {map_name} 到 Normal")
                    break
            
            # 处理置换
            for map_name in ['displacement', 'disp', 'height']:
                if map_name in texture_nodes:
                    disp_node = nodes.new(type='ShaderNodeDisplacement')
                    disp_node.location = (300, -200)
                    disp_node.inputs['Scale'].default_value = 0.1  # 减少置换强度
                    links.new(texture_nodes[map_name].outputs['Color'], disp_node.inputs['Height'])
                    links.new(disp_node.outputs['Displacement'], output.inputs['Displacement'])
                    print(f"连接 {map_name} 到 Displacement")
                    break
            
            # 处理ARM纹理（环境光遮蔽、粗糙度、金属度）
            if 'arm' in texture_nodes:
                separate_rgb = nodes.new(type='ShaderNodeSeparateRGB')
                separate_rgb.location = (-200, -100)
                links.new(texture_nodes['arm'].outputs['Color'], separate_rgb.inputs['Image'])
                
                # 如果没有专用粗糙度贴图，连接粗糙度（G）
                if not any(map_name in texture_nodes for map_name in ['roughness', 'rough']):
                    links.new(separate_rgb.outputs['G'], principled.inputs['Roughness'])
                    print("连接 ARM.G 到 Roughness")
                
                # 如果没有专用金属度贴图，连接金属度（B）
                if not any(map_name in texture_nodes for map_name in ['metallic', 'metalness', 'metal']):
                    links.new(separate_rgb.outputs['B'], principled.inputs['Metallic'])
                    print("连接 ARM.B 到 Metallic")
                
                # 对于AO（R通道），如果有基础色则与其相乘
                base_color_node = None
                for map_name in ['color', 'diffuse', 'albedo']:
                    if map_name in texture_nodes:
                        base_color_node = texture_nodes[map_name]
                        break
                
                if base_color_node:
                    mix_node = nodes.new(type='ShaderNodeMixRGB')
                    mix_node.location = (100, 200)
                    mix_node.blend_type = 'MULTIPLY'
                    mix_node.inputs['Fac'].default_value = 0.8  # 80%影响
                    
                    # 断开到基础色的直接连接
                    for link in base_color_node.outputs['Color'].links:
                        if link.to_socket == principled.inputs['Base Color']:
                            links.remove(link)
                    
                    # 通过混合节点连接
                    links.new(base_color_node.outputs['Color'], mix_node.inputs[1])
                    links.new(separate_rgb.outputs['R'], mix_node.inputs[2])
                    links.new(mix_node.outputs['Color'], principled.inputs['Base Color'])
                    print("连接 ARM.R 到 AO 与 Base Color 混合")
            
            # 如果是单独的，处理AO（环境光遮蔽）
            if 'ao' in texture_nodes:
                base_color_node = None
                for map_name in ['color', 'diffuse', 'albedo']:
                    if map_name in texture_nodes:
                        base_color_node = texture_nodes[map_name]
                        break
                
                if base_color_node:
                    mix_node = nodes.new(type='ShaderNodeMixRGB')
                    mix_node.location = (100, 200)
                    mix_node.blend_type = 'MULTIPLY'
                    mix_node.inputs['Fac'].default_value = 0.8  # 80%影响
                    
                    # 断开到基础色的直接连接
                    for link in base_color_node.outputs['Color'].links:
                        if link.to_socket == principled.inputs['Base Color']:
                            links.remove(link)
                    
                    # 通过混合节点连接
                    links.new(base_color_node.outputs['Color'], mix_node.inputs[1])
                    links.new(texture_nodes['ao'].outputs['Color'], mix_node.inputs[2])
                    links.new(mix_node.outputs['Color'], principled.inputs['Base Color'])
                    print("连接 AO 到与 Base Color 混合")
            
            # 关键：确保从物体中清除所有现有材质
            while len(obj.data.materials) > 0:
                obj.data.materials.pop(index=0)
            
            # 将新材质分配给物体
            obj.data.materials.append(new_mat)
            
            # 关键：使物体活动并选择它
            bpy.context.view_layer.objects.active = obj
            obj.select_set(True)
            
            # 关键：强制Blender更新材质
            bpy.context.view_layer.update()
            
            # 获取纹理贴图列表
            texture_maps = list(texture_images.keys())
            
            # 获取用于调试的纹理节点信息
            material_info = {
                "name": new_mat.name,
                "has_nodes": new_mat.use_nodes,
                "node_count": len(new_mat.node_tree.nodes),
                "texture_nodes": []
            }
            
            for node in new_mat.node_tree.nodes:
                if node.type == 'TEX_IMAGE' and node.image:
                    connections = []
                    for output in node.outputs:
                        for link in output.links:
                            connections.append(f"{output.name} → {link.to_node.name}.{link.to_socket.name}")
                    
                    material_info["texture_nodes"].append({
                        "name": node.name,
                        "image": node.image.name,
                        "colorspace": node.image.colorspace_settings.name,
                        "connections": connections
                    })
            
            return {
                "success": True,
                "message": f"创建新材质并将纹理 {texture_id} 应用到 {object_name}",
                "material": new_mat.name,
                "maps": texture_maps,
                "material_info": material_info
            }
            
        except Exception as e:
            print(f"set_texture出错：{str(e)}")
            traceback.print_exc()
            return {"error": f"应用纹理失败：{str(e)}"}

    def get_polyhaven_status(self):
        """获取PolyHaven集成的当前状态"""
        enabled = bpy.context.scene.blendermcp_use_polyhaven
        if enabled:
            return {"enabled": True, "message": "PolyHaven集成已启用并准备使用。"}
        else:
            return {
                "enabled": False, 
                "message": """PolyHaven集成当前已禁用。要启用它：
                            1. 在3D视口中，在侧边栏中找到BlenderMCP面板（如果隐藏按N键）
                            2. 勾选'使用Poly Haven资源'复选框
                            3. 重新启动与Claude的连接"""
        }

    #region Hyper3D
    def get_hyper3d_status(self):
        """获取Hyper3D Rodin集成的当前状态"""
        enabled = bpy.context.scene.blendermcp_use_hyper3d
        if enabled:
            if not bpy.context.scene.blendermcp_hyper3d_api_key:
                return {
                    "enabled": False, 
                    "message": """Hyper3D Rodin集成当前已启用，但未给出API密钥。要启用它：
                                1. 在3D视口中，在侧边栏中找到BlenderMCP面板（如果隐藏按N键）
                                2. 保持'使用Hyper3D Rodin 3D模型生成'复选框选中
                                3. 选择正确的平台并填写API密钥
                                4. 重新启动与Claude的连接"""
                }
            mode = bpy.context.scene.blendermcp_hyper3d_mode
            message = f"Hyper3D Rodin集成已启用并准备使用。模式：{mode}。" + \
                f"密钥类型：{'private' if bpy.context.scene.blendermcp_hyper3d_api_key != RODIN_FREE_TRIAL_KEY else 'free_trial'}"
            return {
                "enabled": True,
                "message": message
            }
        else:
            return {
                "enabled": False, 
                "message": """Hyper3D Rodin集成当前已禁用。要启用它：
                            1. 在3D视口中，在侧边栏中找到BlenderMCP面板（如果隐藏按N键）
                            2. 勾选'使用Hyper3D Rodin 3D模型生成'复选框
                            3. 重新启动与Claude的连接"""
            }

    def create_rodin_job(self, *args, **kwargs):
        match bpy.context.scene.blendermcp_hyper3d_mode:
            case "MAIN_SITE":
                return self.create_rodin_job_main_site(*args, **kwargs)
            case "FAL_AI":
                return self.create_rodin_job_fal_ai(*args, **kwargs)
            case _:
                return f"错误：未知的Hyper3D Rodin模式！"

    def create_rodin_job_main_site(
            self,
            text_prompt: str=None,
            images: list[tuple[str, str]]=None,
            bbox_condition=None
        ):
        try:
            if images is None:
                images = []
            """调用Rodin API，获取作业uuid和订阅密钥"""
            files = [
                *[("images", (f"{i:04d}{img_suffix}", img)) for i, (img_suffix, img) in enumerate(images)],
                ("tier", (None, "Sketch")),
                ("mesh_mode", (None, "Raw")),
            ]
            if text_prompt:
                files.append(("prompt", (None, text_prompt)))
            if bbox_condition:
                files.append(("bbox_condition", (None, json.dumps(bbox_condition))))
            response = requests.post(
                "https://hyperhuman.deemos.com/api/v2/rodin",
                headers={
                    "Authorization": f"Bearer {bpy.context.scene.blendermcp_hyper3d_api_key}",
                },
                files=files
            )
            data = response.json()
            return data
        except Exception as e:
            return {"error": str(e)}
    
    def create_rodin_job_fal_ai(
            self,
            text_prompt: str=None,
            images: list[tuple[str, str]]=None,
            bbox_condition=None
        ):
        try:
            req_data = {
                "tier": "Sketch",
            }
            if images:
                req_data["input_image_urls"] = images
            if text_prompt:
                req_data["prompt"] = text_prompt
            if bbox_condition:
                req_data["bbox_condition"] = bbox_condition
            response = requests.post(
                "https://queue.fal.run/fal-ai/hyper3d/rodin",
                headers={
                    "Authorization": f"Key {bpy.context.scene.blendermcp_hyper3d_api_key}",
                    "Content-Type": "application/json",
                },
                json=req_data
            )
            data = response.json()
            return data
        except Exception as e:
            return {"error": str(e)}

    def poll_rodin_job_status(self, *args, **kwargs):
        match bpy.context.scene.blendermcp_hyper3d_mode:
            case "MAIN_SITE":
                return self.poll_rodin_job_status_main_site(*args, **kwargs)
            case "FAL_AI":
                return self.poll_rodin_job_status_fal_ai(*args, **kwargs)
            case _:
                return f"错误：未知的Hyper3D Rodin模式！"

    def poll_rodin_job_status_main_site(self, subscription_key: str):
        """调用作业状态API获取作业状态"""
        response = requests.post(
            "https://hyperhuman.deemos.com/api/v2/status",
            headers={
                "Authorization": f"Bearer {bpy.context.scene.blendermcp_hyper3d_api_key}",
            },
            json={
                "subscription_key": subscription_key,
            },
        )
        data = response.json()
        return {
            "status_list": [i["status"] for i in data["jobs"]]
        }
    
    def poll_rodin_job_status_fal_ai(self, request_id: str):
        """调用作业状态API获取作业状态"""
        response = requests.get(
            f"https://queue.fal.run/fal-ai/hyper3d/requests/{request_id}/status",
            headers={
                "Authorization": f"KEY {bpy.context.scene.blendermcp_hyper3d_api_key}",
            },
        )
        data = response.json()
        return data

    @staticmethod
    def _clean_imported_glb(filepath, mesh_name=None):
        # 导入前获取现有物体集合
        existing_objects = set(bpy.data.objects)

        # 导入GLB文件
        bpy.ops.import_scene.gltf(filepath=filepath)
        
        # 确保上下文已更新
        bpy.context.view_layer.update()
        
        # 获取所有导入的物体
        imported_objects = list(set(bpy.data.objects) - existing_objects)
        
        if not imported_objects:
            print("错误：未导入任何物体。")
            return
        
        # 识别网格物体
        mesh_obj = None
        
        if len(imported_objects) == 1 and imported_objects[0].type == 'MESH':
            mesh_obj = imported_objects[0]
            print("导入单个网格，无需清理。")
        else:
            if len(imported_objects) == 2:
                empty_objs = [i for i in imported_objects if i.type == "EMPTY"]
                if len(empty_objs) != 1:
                    print("错误：期望一个空节点和一个网格子节点或单个网格物体。")
                    return
                parent_obj = empty_objs.pop()
                if len(parent_obj.children) == 1:
                    potential_mesh = parent_obj.children[0]
                    if potential_mesh.type == 'MESH':
                        print("GLB结构确认：空节点和一个网格子节点。")
                        
                        # 从空节点取消父级网格
                        potential_mesh.parent = None
                        
                        # 移除空节点
                        bpy.data.objects.remove(parent_obj)
                        print("移除空节点，只保留网格。")
                        
                        mesh_obj = potential_mesh
                    else:
                        print("错误：子节点不是网格物体。")
                        return
                else:
                    print("错误：期望一个空节点和一个网格子节点或单个网格物体。")
                    return
            else:
                print("错误：期望一个空节点和一个网格子节点或单个网格物体。")
                return
        
        # 如果需要重命名网格
        try:
            if mesh_obj and mesh_obj.name is not None and mesh_name:
                mesh_obj.name = mesh_name
                if mesh_obj.data.name is not None:
                    mesh_obj.data.name = mesh_name
                print(f"网格重命名为：{mesh_name}")
        except Exception as e:
            print("重命名有问题，放弃重命名。")

        return mesh_obj

    def import_generated_asset(self, *args, **kwargs):
        match bpy.context.scene.blendermcp_hyper3d_mode:
            case "MAIN_SITE":
                return self.import_generated_asset_main_site(*args, **kwargs)
            case "FAL_AI":
                return self.import_generated_asset_fal_ai(*args, **kwargs)
            case _:
                return f"错误：未知的Hyper3D Rodin模式！"

    def import_generated_asset_main_site(self, task_uuid: str, name: str):
        """获取生成的资源，导入到blender"""
        response = requests.post(
            "https://hyperhuman.deemos.com/api/v2/download",
            headers={
                "Authorization": f"Bearer {bpy.context.scene.blendermcp_hyper3d_api_key}",
            },
            json={
                'task_uuid': task_uuid
            }
        )
        data_ = response.json()
        temp_file = None
        for i in data_["list"]:
            if i["name"].endswith(".glb"):
                temp_file = tempfile.NamedTemporaryFile(
                    delete=False,
                    prefix=task_uuid,
                    suffix=".glb",
                )
    
                try:
                    # 下载内容
                    response = requests.get(i["url"], stream=True)
                    response.raise_for_status()  # 为HTTP错误引发异常
                    
                    # 将内容写入临时文件
                    for chunk in response.iter_content(chunk_size=8192):
                        temp_file.write(chunk)
                        
                    # 关闭文件
                    temp_file.close()
                    
                except Exception as e:
                    # 如果有错误则清理文件
                    temp_file.close()
                    os.unlink(temp_file.name)
                    return {"succeed": False, "error": str(e)}
                
                break
        else:
            return {"succeed": False, "error": "生成失败。请首先确保任务的所有作业都已完成，然后稍后重试。"}

        try:
            obj = self._clean_imported_glb(
                filepath=temp_file.name,
                mesh_name=name
            )
            result = {
                "name": obj.name,
                "type": obj.type,
                "location": [obj.location.x, obj.location.y, obj.location.z],
                "rotation": [obj.rotation_euler.x, obj.rotation_euler.y, obj.rotation_euler.z],
                "scale": [obj.scale.x, obj.scale.y, obj.scale.z],
            }

            if obj.type == "MESH":
                bounding_box = self._get_aabb(obj)
                result["world_bounding_box"] = bounding_box
            
            return {
                "succeed": True, **result
            }
        except Exception as e:
            return {"succeed": False, "error": str(e)}
    
    def import_generated_asset_fal_ai(self, request_id: str, name: str):
        """获取生成的资源，导入到blender"""
        response = requests.get(
            f"https://queue.fal.run/fal-ai/hyper3d/requests/{request_id}",
            headers={
                "Authorization": f"Key {bpy.context.scene.blendermcp_hyper3d_api_key}",
            }
        )
        data_ = response.json()
        temp_file = None
        
        temp_file = tempfile.NamedTemporaryFile(
            delete=False,
            prefix=request_id,
            suffix=".glb",
        )

        try:
            # 下载内容
            response = requests.get(data_["model_mesh"]["url"], stream=True)
            response.raise_for_status()  # 为HTTP错误引发异常
            
            # 将内容写入临时文件
            for chunk in response.iter_content(chunk_size=8192):
                temp_file.write(chunk)
                
            # 关闭文件
            temp_file.close()
            
        except Exception as e:
            # 如果有错误则清理文件
            temp_file.close()
            os.unlink(temp_file.name)
            return {"succeed": False, "error": str(e)}

        try:
            obj = self._clean_imported_glb(
                filepath=temp_file.name,
                mesh_name=name
            )
            result = {
                "name": obj.name,
                "type": obj.type,
                "location": [obj.location.x, obj.location.y, obj.location.z],
                "rotation": [obj.rotation_euler.x, obj.rotation_euler.y, obj.rotation_euler.z],
                "scale": [obj.scale.x, obj.scale.y, obj.scale.z],
            }

            if obj.type == "MESH":
                bounding_box = self._get_aabb(obj)
                result["world_bounding_box"] = bounding_box
            
            return {
                "succeed": True, **result
            }
        except Exception as e:
            return {"succeed": False, "error": str(e)}
    #endregion

    #region Sketchfab API
    def get_sketchfab_status(self):
        """获取Sketchfab集成的当前状态"""
        enabled = bpy.context.scene.blendermcp_use_sketchfab
        api_key = bpy.context.scene.blendermcp_sketchfab_api_key
        
        # 如果存在则测试API密钥
        if api_key:
            try:
                headers = {
                    "Authorization": f"Token {api_key}"
                }
                
                response = requests.get(
                    "https://api.sketchfab.com/v3/me",
                    headers=headers,
                    timeout=30  # 添加30秒超时
                )
                
                if response.status_code == 200:
                    user_data = response.json()
                    username = user_data.get("username", "未知用户")
                    return {
                        "enabled": True, 
                        "message": f"Sketchfab集成已启用并准备使用。已登录为：{username}"
                    }
                else:
                    return {
                        "enabled": False, 
                        "message": f"Sketchfab API密钥似乎无效。状态码：{response.status_code}"
                    }
            except requests.exceptions.Timeout:
                return {
                    "enabled": False, 
                    "message": "连接Sketchfab API超时。请检查您的网络连接。"
                }
            except Exception as e:
                return {
                    "enabled": False, 
                    "message": f"测试Sketchfab API密钥时出错：{str(e)}"
                }
                
        if enabled and api_key:
            return {"enabled": True, "message": "Sketchfab集成已启用并准备使用。"}
        elif enabled and not api_key:
            return {
                "enabled": False, 
                "message": """Sketchfab集成当前已启用，但未给出API密钥。要启用它：
                            1. 在3D视口中，在侧边栏中找到BlenderMCP面板（如果隐藏按N键）
                            2. 保持'使用Sketchfab'复选框选中
                            3. 输入您的Sketchfab API密钥
                            4. 重新启动与Claude的连接"""
            }
        else:
            return {
                "enabled": False, 
                "message": """Sketchfab集成当前已禁用。要启用它：
                            1. 在3D视口中，在侧边栏中找到BlenderMCP面板（如果隐藏按N键）
                            2. 勾选'使用Sketchfab资源'复选框
                            3. 输入您的Sketchfab API密钥
                            4. 重新启动与Claude的连接"""
            }
    
    def search_sketchfab_models(self, query, categories=None, count=20, downloadable=True):
        """根据查询和可选过滤器在Sketchfab上搜索模型"""
        try:
            api_key = bpy.context.scene.blendermcp_sketchfab_api_key
            if not api_key:
                return {"error": "Sketchfab API密钥未配置"}
                
            # 使用Sketchfab API文档中的确切字段构建搜索参数
            params = {
                "type": "models",
                "q": query,
                "count": count,
                "downloadable": downloadable,
                "archives_flavours": False
            }
            
            if categories:
                params["categories"] = categories
                
            # 向Sketchfab搜索端点发出API请求
            # 根据Sketchfab API文档，API密钥认证的正确格式
            headers = {
                "Authorization": f"Token {api_key}"
            }
            
            # 使用API文档中指定的搜索端点
            response = requests.get(
                "https://api.sketchfab.com/v3/search",
                headers=headers,
                params=params,
                timeout=30  # 添加30秒超时
            )
            
            if response.status_code == 401:
                return {"error": "认证失败（401）。请检查您的API密钥。"}
                
            if response.status_code != 200:
                return {"error": f"API请求失败，状态码{response.status_code}"}
                
            response_data = response.json()
            
            # 响应结构的安全检查
            if response_data is None:
                return {"error": "从Sketchfab API收到空响应"}
                
            # 处理响应中可能缺少'results'
            results = response_data.get("results", [])
            if not isinstance(results, list):
                return {"error": f"Sketchfab API的意外响应格式：{response_data}"}
                
            return response_data
        
        except requests.exceptions.Timeout:
            return {"error": "请求超时。请检查您的网络连接。"}
        except json.JSONDecodeError as e:
            return {"error": f"来自Sketchfab API的无效JSON响应：{str(e)}"}
        except Exception as e:
            import traceback
            traceback.print_exc()
            return {"error": str(e)}

    def download_sketchfab_model(self, uid):
        """通过UID从Sketchfab下载模型"""
        try:
            api_key = bpy.context.scene.blendermcp_sketchfab_api_key
            if not api_key:
                return {"error": "Sketchfab API密钥未配置"}
                
            # 使用API密钥认证的正确授权头
            headers = {
                "Authorization": f"Token {api_key}"
            }
            
            # 使用文档中的确切端点请求下载URL
            download_endpoint = f"https://api.sketchfab.com/v3/models/{uid}/download"
            
            response = requests.get(
                download_endpoint,
                headers=headers,
                timeout=30  # 添加30秒超时
            )
            
            if response.status_code == 401:
                return {"error": "认证失败（401）。请检查您的API密钥。"}
                
            if response.status_code != 200:
                return {"error": f"下载请求失败，状态码{response.status_code}"}
                
            data = response.json()
            
            # None数据的安全检查
            if data is None:
                return {"error": "从Sketchfab API收到下载请求的空响应"}
                
            # 安全检查提取下载URL
            gltf_data = data.get("gltf")
            if not gltf_data:
                return {"error": "此模型没有gltf下载URL。响应：" + str(data)}
                
            download_url = gltf_data.get("url")
            if not download_url:
                return {"error": "此模型没有下载URL。确保模型可下载且您有访问权限。"}
                
            # 下载模型（已有超时）
            model_response = requests.get(download_url, timeout=60)  # 60秒超时
            
            if model_response.status_code != 200:
                return {"error": f"模型下载失败，状态码{model_response.status_code}"}
                
            # 保存到临时文件
            temp_dir = tempfile.mkdtemp()
            zip_file_path = os.path.join(temp_dir, f"{uid}.zip")
            
            with open(zip_file_path, "wb") as f:
                f.write(model_response.content)
                
            # 增强安全性提取zip文件
            with zipfile.ZipFile(zip_file_path, 'r') as zip_ref:
                # 更安全的zip slip预防
                for file_info in zip_ref.infolist():
                    # 获取文件路径
                    file_path = file_info.filename
                    
                    # 将目录分隔符转换为当前OS样式
                    # 这处理zip条目中的/和\
                    target_path = os.path.join(temp_dir, os.path.normpath(file_path))
                    
                    # 获取比较的绝对路径
                    abs_temp_dir = os.path.abspath(temp_dir)
                    abs_target_path = os.path.abspath(target_path)
                    
                    # 确保标准化路径不会转义目标目录
                    if not abs_target_path.startswith(abs_temp_dir):
                        with suppress(Exception):
                            shutil.rmtree(temp_dir)
                        return {"error": "安全问题：Zip包含路径遍历尝试的文件"}
                    
                    # 目录遍历的额外显式检查
                    if ".." in file_path:
                        with suppress(Exception):
                            shutil.rmtree(temp_dir)
                        return {"error": "安全问题：Zip包含目录遍历序列的文件"}
                
                # 如果所有文件都通过安全检查，提取它们
                zip_ref.extractall(temp_dir)
                
            # 查找主glTF文件
            gltf_files = [f for f in os.listdir(temp_dir) if f.endswith('.gltf') or f.endswith('.glb')]
            
            if not gltf_files:
                with suppress(Exception):
                    shutil.rmtree(temp_dir)
                return {"error": "下载的模型中未找到glTF文件"}
                
            main_file = os.path.join(temp_dir, gltf_files[0])
            
            # 导入模型
            bpy.ops.import_scene.gltf(filepath=main_file)
            
            # 获取导入物体的名称
            imported_objects = [obj.name for obj in bpy.context.selected_objects]
            
            # 清理临时文件
            with suppress(Exception):
                shutil.rmtree(temp_dir)
            
            return {
                "success": True,
                "message": "模型导入成功",
                "imported_objects": imported_objects
            }
        
        except requests.exceptions.Timeout:
            return {"error": "请求超时。请检查您的网络连接，并尝试一个更简单的模型。"}
        except json.JSONDecodeError as e:
            return {"error": f"来自Sketchfab API的无效JSON响应：{str(e)}"}
        except Exception as e:
            import traceback
            traceback.print_exc()
            return {"error": f"下载模型失败：{str(e)}"}
    #endregion

# Blender UI面板
class BLENDERMCP_PT_Panel(bpy.types.Panel):
    bl_label = "Blender MCP"
    bl_idname = "BLENDERMCP_PT_Panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'BlenderMCP'
    
    def draw(self, context):
        layout = self.layout
        scene = context.scene
        
        layout.prop(scene, "blendermcp_port")
        layout.prop(scene, "blendermcp_use_polyhaven", text="使用Poly Haven资源")

        layout.prop(scene, "blendermcp_use_hyper3d", text="使用Hyper3D Rodin 3D模型生成")
        if scene.blendermcp_use_hyper3d:
            layout.prop(scene, "blendermcp_hyper3d_mode", text="Rodin模式")
            layout.prop(scene, "blendermcp_hyper3d_api_key", text="API密钥")
            layout.operator("blendermcp.set_hyper3d_free_trial_api_key", text="设置免费试用API密钥")
        
        layout.prop(scene, "blendermcp_use_sketchfab", text="使用Sketchfab资源")
        if scene.blendermcp_use_sketchfab:
            layout.prop(scene, "blendermcp_sketchfab_api_key", text="API密钥")
        
        if not scene.blendermcp_server_running:
            layout.operator("blendermcp.start_server", text="连接到MCP服务器")
        else:
            layout.operator("blendermcp.stop_server", text="断开与MCP服务器的连接")
            layout.label(text=f"运行在端口 {scene.blendermcp_port}")

# 设置Hyper3D API密钥的操作器
class BLENDERMCP_OT_SetFreeTrialHyper3DAPIKey(bpy.types.Operator):
    bl_idname = "blendermcp.set_hyper3d_free_trial_api_key"
    bl_label = "设置免费试用API密钥"
    
    def execute(self, context):
        context.scene.blendermcp_hyper3d_api_key = RODIN_FREE_TRIAL_KEY
        context.scene.blendermcp_hyper3d_mode = 'MAIN_SITE'
        self.report({'INFO'}, "API密钥设置成功！")
        return {'FINISHED'}

# 启动服务器的操作器
class BLENDERMCP_OT_StartServer(bpy.types.Operator):
    bl_idname = "blendermcp.start_server"
    bl_label = "连接到Claude"
    bl_description = "启动BlenderMCP服务器以连接Claude"
    
    def execute(self, context):
        scene = context.scene
        
        # 创建新的服务器实例
        if not hasattr(bpy.types, "blendermcp_server") or not bpy.types.blendermcp_server:
            bpy.types.blendermcp_server = BlenderMCPServer(port=scene.blendermcp_port)
        
        # 启动服务器
        bpy.types.blendermcp_server.start()
        scene.blendermcp_server_running = True
        
        return {'FINISHED'}

# 停止服务器的操作器
class BLENDERMCP_OT_StopServer(bpy.types.Operator):
    bl_idname = "blendermcp.stop_server"
    bl_label = "停止与Claude的连接"
    bl_description = "停止与Claude的连接"
    
    def execute(self, context):
        scene = context.scene
        
        # 如果存在，停止服务器
        if hasattr(bpy.types, "blendermcp_server") and bpy.types.blendermcp_server:
            bpy.types.blendermcp_server.stop()
            del bpy.types.blendermcp_server
        
        scene.blendermcp_server_running = False
        
        return {'FINISHED'}

# 注册函数
def register():
    bpy.types.Scene.blendermcp_port = IntProperty(
        name="端口",
        description="BlenderMCP服务器的端口",
        default=9876,
        min=1024,
        max=65535
    )
    
    bpy.types.Scene.blendermcp_server_running = bpy.props.BoolProperty(
        name="服务器运行中",
        default=False
    )
    
    bpy.types.Scene.blendermcp_use_polyhaven = bpy.props.BoolProperty(
        name="使用Poly Haven",
        description="启用Poly Haven资源集成",
        default=False
    )

    bpy.types.Scene.blendermcp_use_hyper3d = bpy.props.BoolProperty(
        name="使用Hyper3D Rodin",
        description="启用Hyper3D Rodin生成集成",
        default=False
    )

    bpy.types.Scene.blendermcp_hyper3d_mode = bpy.props.EnumProperty(
        name="Rodin模式",
        description="选择用于调用Rodin API的平台",
        items=[
            ("MAIN_SITE", "hyper3d.ai", "hyper3d.ai"),
            ("FAL_AI", "fal.ai", "fal.ai"),
        ],
        default="MAIN_SITE"
    )

    bpy.types.Scene.blendermcp_hyper3d_api_key = bpy.props.StringProperty(
        name="Hyper3D API密钥",
        subtype="PASSWORD",
        description="Hyper3D提供的API密钥",
        default=""
    )
    
    bpy.types.Scene.blendermcp_use_sketchfab = bpy.props.BoolProperty(
        name="使用Sketchfab",
        description="启用Sketchfab资源集成",
        default=False
    )

    bpy.types.Scene.blendermcp_sketchfab_api_key = bpy.props.StringProperty(
        name="Sketchfab API密钥",
        subtype="PASSWORD",
        description="Sketchfab提供的API密钥",
        default=""
    )
    
    bpy.utils.register_class(BLENDERMCP_PT_Panel)
    bpy.utils.register_class(BLENDERMCP_OT_SetFreeTrialHyper3DAPIKey)
    bpy.utils.register_class(BLENDERMCP_OT_StartServer)
    bpy.utils.register_class(BLENDERMCP_OT_StopServer)
    
    print("BlenderMCP插件已注册")

def unregister():
    # 如果服务器正在运行，停止它
    if hasattr(bpy.types, "blendermcp_server") and bpy.types.blendermcp_server:
        bpy.types.blendermcp_server.stop()
        del bpy.types.blendermcp_server
    
    bpy.utils.unregister_class(BLENDERMCP_PT_Panel)
    bpy.utils.unregister_class(BLENDERMCP_OT_SetFreeTrialHyper3DAPIKey)
    bpy.utils.unregister_class(BLENDERMCP_OT_StartServer)
    bpy.utils.unregister_class(BLENDERMCP_OT_StopServer)
    
    del bpy.types.Scene.blendermcp_port
    del bpy.types.Scene.blendermcp_server_running
    del bpy.types.Scene.blendermcp_use_polyhaven
    del bpy.types.Scene.blendermcp_use_hyper3d
    del bpy.types.Scene.blendermcp_hyper3d_mode
    del bpy.types.Scene.blendermcp_hyper3d_api_key
    del bpy.types.Scene.blendermcp_use_sketchfab
    del bpy.types.Scene.blendermcp_sketchfab_api_key

    print("BlenderMCP插件已取消注册")

if __name__ == "__main__":
    register()
