import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { StorageTestHelper, TestUser } from './storageTestHelper'
import { MockFileSystemManager } from './mockFileSystem'

/**
 * 集成测试：完整的存储架构重构流程验证
 */
describe('存储架构重构集成测试', () => {
  let testHelper: StorageTestHelper
  let testUsers: TestUser[]
  let fs: MockFileSystemManager

  beforeEach(async () => {
    testHelper = new StorageTestHelper('/tmp/chronnote-integration-test')
    fs = new MockFileSystemManager()
    await testHelper.setupTestEnvironment()
    testUsers = testHelper.generateTestUsers(2)
  })

  afterEach(async () => {
    await testHelper.cleanupTestEnvironment()
  })

  describe('完整的迁移和重构流程', () => {
    it('应该能够完成从旧格式到新格式的完整迁移', async () => {
      const workspace = testHelper.getTestWorkspace()
      
      // 1. 创建旧格式的文件结构
      console.log('1. 创建旧格式文件结构...')
      const originalStructure = await testHelper.createLegacyFileStructure(testUsers)
      expect(originalStructure.databases).toHaveLength(2)
      expect(originalStructure.imgFiles.length).toBeGreaterThan(0)
      
      // 2. 验证旧格式文件确实存在
      console.log('2. 验证旧格式文件...')
      for (const user of testUsers) {
        const dbPath = `${workspace}/example/chron_${user.uuid}.db`
        const blockPath = `${workspace}/example/blocks_${user.uuid}.json`
        
        const dbExists = await fs.stat(dbPath)
        const blockExists = await fs.stat(blockPath)
        
        expect(dbExists.isDirectory()).toBe(false) // 应该是文件
        expect(blockExists.isDirectory()).toBe(false)
      }
      
      // 3. 执行迁移到新格式
      console.log('3. 执行迁移...')
      const usersPath = `${workspace}/users`
      await fs.mkdir(usersPath)
      
      for (const user of testUsers) {
        const userPath = `${usersPath}/${user.uuid}`
        await fs.mkdir(userPath)
        await fs.mkdir(`${userPath}/img`)
        await fs.mkdir(`${userPath}/pdf`)
        
        // 迁移数据库文件
        const oldDbPath = `${workspace}/example/chron_${user.uuid}.db`
        const newDbPath = `${userPath}/chron.db`
        await fs.copy(oldDbPath, newDbPath)
        
        // 迁移块文件
        const oldBlockPath = `${workspace}/example/blocks_${user.uuid}.json`
        const newBlockPath = `${userPath}/blocks.json`
        await fs.copy(oldBlockPath, newBlockPath)
        
        // 复制共享资源
        const oldImgPath = `${workspace}/example/img`
        const oldPdfPath = `${workspace}/example/pdf`
        const newImgPath = `${userPath}/img`
        const newPdfPath = `${userPath}/pdf`
        
        try {
          await fs.copy(oldImgPath, newImgPath)
        } catch (error) {
          console.log('img目录复制失败或不存在，这是正常的')
        }
        
        try {
          await fs.copy(oldPdfPath, newPdfPath)
        } catch (error) {
          console.log('pdf目录复制失败或不存在，这是正常的')
        }
      }
      
      // 4. 验证新格式结构
      console.log('4. 验证新格式结构...')
      const isValidNewStructure = await testHelper.validateNewFileStructure(testUsers)
      expect(isValidNewStructure).toBe(true)
      
      // 5. 验证每个用户的独立性
      console.log('5. 验证用户独立性...')
      for (const user of testUsers) {
        const userPath = `${usersPath}/${user.uuid}`
        
        // 验证用户目录存在
        const userStat = await fs.stat(userPath)
        expect(userStat.isDirectory()).toBe(true)
        
        // 验证核心文件存在
        const dbStat = await fs.stat(`${userPath}/chron.db`)
        const blockStat = await fs.stat(`${userPath}/blocks.json`)
        expect(dbStat.isDirectory()).toBe(false)
        expect(blockStat.isDirectory()).toBe(false)
        
        // 验证资源目录存在
        const imgStat = await fs.stat(`${userPath}/img`)
        const pdfStat = await fs.stat(`${userPath}/pdf`)
        expect(imgStat.isDirectory()).toBe(true)
        expect(pdfStat.isDirectory()).toBe(true)
      }
      
      // 6. 验证文件内容完整性
      console.log('6. 验证文件内容...')
      for (const user of testUsers) {
        const userPath = `${usersPath}/${user.uuid}`
        
        // 验证数据库文件内容
        const dbContent = await fs.readFile(`${userPath}/chron.db`)
        expect(dbContent).toContain(`Test database for user ${user.uuid}`)
        
        // 验证块文件内容
        const blockContent = await fs.readJson(`${userPath}/blocks.json`)
        expect(blockContent.userId).toBe(user.uuid)
      }
      
      console.log('✅ 完整迁移流程验证成功!')
    })

    it('应该验证新架构的路径设计', () => {
      // Mock新的配置助手
      class MockNewConfig {
        workspacePath: string
        
        constructor(workspacePath: string) {
          this.workspacePath = workspacePath
        }
        
        getUserWorkspace(userId: string) {
          return `${this.workspacePath}/users/${userId}`
        }
        
        getUserDbPath(userId: string) {
          return `${this.getUserWorkspace(userId)}/chron`
        }
        
        getUserImgPath(userId: string) {
          return `${this.getUserWorkspace(userId)}/img`
        }
        
        getUserPdfPath(userId: string) {
          return `${this.getUserWorkspace(userId)}/pdf`
        }
      }
      
      const config = new MockNewConfig(testHelper.getTestWorkspace())
      
      // 验证路径生成逻辑
      for (const user of testUsers) {
        const userWorkspace = config.getUserWorkspace(user.uuid)
        const userDbPath = config.getUserDbPath(user.uuid)
        const userImgPath = config.getUserImgPath(user.uuid)
        const userPdfPath = config.getUserPdfPath(user.uuid)
        
        // 验证路径格式正确
        expect(userWorkspace).toMatch(/\/users\/[0-9a-f-]{36}$/)
        expect(userDbPath).toBe(`${userWorkspace}/chron`)
        expect(userImgPath).toBe(`${userWorkspace}/img`)
        expect(userPdfPath).toBe(`${userWorkspace}/pdf`)
        
        // 验证不同用户的路径不同
        const otherUser = testUsers.find(u => u.uuid !== user.uuid)!
        const otherWorkspace = config.getUserWorkspace(otherUser.uuid)
        expect(userWorkspace).not.toBe(otherWorkspace)
      }
    })

    it('应该验证新架构的优势', async () => {
      const workspace = testHelper.getTestWorkspace()
      
      // 创建模拟的新架构
      const usersPath = `${workspace}/users`
      await fs.mkdir(usersPath)
      
      for (const user of testUsers) {
        const userPath = `${usersPath}/${user.uuid}`
        await fs.mkdir(userPath)
        await fs.mkdir(`${userPath}/img`)
        await fs.mkdir(`${userPath}/pdf`)
        
        // 创建简化命名的文件
        await fs.writeFile(`${userPath}/chron.db`, `DB for ${user.uuid}`)
        await fs.writeFile(`${userPath}/blocks.json`, `{"userId": "${user.uuid}"}`)
        
        // 创建用户专属资源
        await fs.writeFile(`${userPath}/img/avatar.jpg`, `Avatar for ${user.uuid}`)
        await fs.writeFile(`${userPath}/pdf/document.pdf`, `Document for ${user.uuid}`)
      }
      
      // 验证新架构的优势
      
      // 1. 文件命名简洁（无冗余userId）
      for (const user of testUsers) {
        const userPath = `${usersPath}/${user.uuid}`
        
        // 检查文件存在且命名简洁
        const dbExists = await fs.stat(`${userPath}/chron.db`)
        const blockExists = await fs.stat(`${userPath}/blocks.json`)
        expect(dbExists.isDirectory()).toBe(false)
        expect(blockExists.isDirectory()).toBe(false)
        
        // 文件名不包含冗余的userId
        const files = await fs.readdir(userPath)
        expect(files).toContain('chron.db')
        expect(files).toContain('blocks.json')
        expect(files).not.toContain(`chron_${user.uuid}.db`)
        expect(files).not.toContain(`blocks_${user.uuid}.json`)
      }
      
      // 2. 完全的用户隔离
      for (let i = 0; i < testUsers.length; i++) {
        const user = testUsers[i]
        const userPath = `${usersPath}/${user.uuid}`
        
        // 每个用户有独立的资源
        const userImgContent = await fs.readFile(`${userPath}/img/avatar.jpg`)
        const userPdfContent = await fs.readFile(`${userPath}/pdf/document.pdf`)
        
        expect(userImgContent).toContain(user.uuid)
        expect(userPdfContent).toContain(user.uuid)
        
        // 其他用户无法访问此用户的文件
        for (let j = 0; j < testUsers.length; j++) {
          if (i !== j) {
            const otherUser = testUsers[j]
            const otherUserPath = `${usersPath}/${otherUser.uuid}`
            
            // 验证路径不同
            expect(userPath).not.toBe(otherUserPath)
          }
        }
      }
      
      // 3. 目录结构清晰
      const usersList = await fs.readdir(usersPath)
      expect(usersList).toHaveLength(2)
      
      for (const userId of usersList) {
        const userPath = `${usersPath}/${userId}`
        const userFiles = await fs.readdir(userPath)
        
        // 每个用户目录有标准结构
        expect(userFiles).toContain('img')
        expect(userFiles).toContain('pdf')
        expect(userFiles).toContain('chron.db')
        expect(userFiles).toContain('blocks.json')
      }
      
      console.log('✅ 新架构优势验证成功!')
    })
  })
})