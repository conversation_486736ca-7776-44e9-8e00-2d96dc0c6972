import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { StorageTestHelper, TestUser } from './storageTestHelper'
import { MockFileSystemManager } from './mockFileSystem'

/**
 * Mock配置助手，模拟新的用户专属路径功能
 */
class MockConfigHelper {
  workspacePath: string

  constructor(workspacePath: string) {
    this.workspacePath = workspacePath
  }

  getUserWorkspace(userId: string): string {
    if (!userId) {
      throw new Error('用户ID不能为空')
    }
    return `${this.workspacePath}/users/${userId}`
  }

  getUserDbPath(userId: string): string {
    return `${this.getUserWorkspace(userId)}/chron`
  }

  getUserImgPath(userId: string): string {
    return `${this.getUserWorkspace(userId)}/img`
  }

  getUserPdfPath(userId: string): string {
    return `${this.getUserWorkspace(userId)}/pdf`
  }

  async createUserDirectories(userId: string): Promise<void> {
    const fs = new MockFileSystemManager()
    const userWorkspace = this.getUserWorkspace(userId)
    const imgPath = this.getUserImgPath(userId)
    const pdfPath = this.getUserPdfPath(userId)

    await fs.mkdir(userWorkspace)
    await fs.mkdir(imgPath)
    await fs.mkdir(pdfPath)
  }

  async checkUserDirectories(userId: string): Promise<boolean> {
    try {
      const fs = new MockFileSystemManager()
      const userWorkspace = this.getUserWorkspace(userId)
      const imgPath = this.getUserImgPath(userId)
      const pdfPath = this.getUserPdfPath(userId)

      const workspaceStat = await fs.stat(userWorkspace)
      const imgStat = await fs.stat(imgPath)
      const pdfStat = await fs.stat(pdfPath)

      return workspaceStat.isDirectory() && imgStat.isDirectory() && pdfStat.isDirectory()
    } catch (error) {
      return false
    }
  }
}

describe('配置重构测试', () => {
  let testHelper: StorageTestHelper
  let testUsers: TestUser[]
  let config: MockConfigHelper

  beforeEach(async () => {
    testHelper = new StorageTestHelper()
    await testHelper.setupTestEnvironment()
    
    config = new MockConfigHelper(testHelper.getTestWorkspace())
    testUsers = testHelper.generateTestUsers(2)
  })

  afterEach(async () => {
    await testHelper.cleanupTestEnvironment()
  })

  describe('3.1 路径配置重构', () => {
    it('应该生成正确的用户专属路径', () => {
      const userId = testUsers[0].uuid
      
      const userWorkspace = config.getUserWorkspace(userId)
      const userDbPath = config.getUserDbPath(userId)
      const userImgPath = config.getUserImgPath(userId)
      const userPdfPath = config.getUserPdfPath(userId)

      expect(userWorkspace).toContain(`/users/${userId}`)
      expect(userDbPath).toBe(`${userWorkspace}/chron`)
      expect(userImgPath).toBe(`${userWorkspace}/img`)
      expect(userPdfPath).toBe(`${userWorkspace}/pdf`)
    })

    it('应该在用户ID为空时抛出错误', () => {
      expect(() => config.getUserWorkspace('')).toThrow('用户ID不能为空')
      expect(() => config.getUserWorkspace(null as any)).toThrow('用户ID不能为空')
      expect(() => config.getUserWorkspace(undefined as any)).toThrow('用户ID不能为空')
    })

    it('应该为不同用户生成不同的路径', () => {
      const user1 = testUsers[0]
      const user2 = testUsers[1]
      
      const workspace1 = config.getUserWorkspace(user1.uuid)
      const workspace2 = config.getUserWorkspace(user2.uuid)
      
      expect(workspace1).not.toBe(workspace2)
      expect(workspace1).toContain(user1.uuid)
      expect(workspace2).toContain(user2.uuid)
    })
  })

  describe('3.2 用户目录创建和验证', () => {
    it('应该能够创建完整的用户目录结构', async () => {
      const userId = testUsers[0].uuid
      
      // 初始状态：目录不存在
      const existsBefore = await config.checkUserDirectories(userId)
      expect(existsBefore).toBe(false)
      
      // 创建目录
      await config.createUserDirectories(userId)
      
      // 验证目录已创建
      const existsAfter = await config.checkUserDirectories(userId)
      expect(existsAfter).toBe(true)
    })

    it('应该为每个用户创建独立的目录结构', async () => {
      // 为两个用户创建目录
      await config.createUserDirectories(testUsers[0].uuid)
      await config.createUserDirectories(testUsers[1].uuid)
      
      // 验证两个用户的目录都存在且独立
      const user1Exists = await config.checkUserDirectories(testUsers[0].uuid)
      const user2Exists = await config.checkUserDirectories(testUsers[1].uuid)
      
      expect(user1Exists).toBe(true)
      expect(user2Exists).toBe(true)
      
      // 验证目录路径确实不同
      const workspace1 = config.getUserWorkspace(testUsers[0].uuid)
      const workspace2 = config.getUserWorkspace(testUsers[1].uuid)
      expect(workspace1).not.toBe(workspace2)
    })

    it('应该能够检测部分缺失的目录结构', async () => {
      const userId = testUsers[0].uuid
      const fs = new MockFileSystemManager()
      
      // 只创建用户工作空间，但不创建img和pdf目录
      const userWorkspace = config.getUserWorkspace(userId)
      await fs.mkdir(userWorkspace)
      
      // 检查应该返回false，因为img和pdf目录缺失
      const exists = await config.checkUserDirectories(userId)
      expect(exists).toBe(false)
    })
  })

  describe('3.3 数据库路径验证', () => {
    it('应该生成不带扩展名的数据库路径', () => {
      const userId = testUsers[0].uuid
      const dbPath = config.getUserDbPath(userId)
      
      // 路径应该不包含.db扩展名（由DatabaseManager添加）
      expect(dbPath).not.toContain('.db')
      expect(dbPath).toContain('/chron')
      expect(dbPath).toContain(userId)
    })

    it('应该生成唯一的数据库路径', () => {
      const dbPath1 = config.getUserDbPath(testUsers[0].uuid)
      const dbPath2 = config.getUserDbPath(testUsers[1].uuid)
      
      expect(dbPath1).not.toBe(dbPath2)
      expect(dbPath1).toContain('chron')
      expect(dbPath2).toContain('chron')
    })
  })

  describe('3.4 资源路径验证', () => {
    it('应该为同一用户的不同资源生成正确的路径', () => {
      const userId = testUsers[0].uuid
      const userWorkspace = config.getUserWorkspace(userId)
      const imgPath = config.getUserImgPath(userId)
      const pdfPath = config.getUserPdfPath(userId)
      
      expect(imgPath).toBe(`${userWorkspace}/img`)
      expect(pdfPath).toBe(`${userWorkspace}/pdf`)
      expect(imgPath).not.toBe(pdfPath)
    })

    it('应该为不同用户的同类资源生成不同路径', () => {
      const imgPath1 = config.getUserImgPath(testUsers[0].uuid)
      const imgPath2 = config.getUserImgPath(testUsers[1].uuid)
      const pdfPath1 = config.getUserPdfPath(testUsers[0].uuid)
      const pdfPath2 = config.getUserPdfPath(testUsers[1].uuid)
      
      expect(imgPath1).not.toBe(imgPath2)
      expect(pdfPath1).not.toBe(pdfPath2)
      expect(imgPath1).toContain(testUsers[0].uuid)
      expect(imgPath2).toContain(testUsers[1].uuid)
    })
  })

  describe('3.5 新旧路径对比验证', () => {
    it('应该体现新架构相比旧架构的改进', () => {
      const userId = testUsers[0].uuid
      
      // 旧架构路径（带用户ID冗余）
      const oldDbPath = `${config.workspacePath}/example/chron_${userId}.db`
      const oldBlockPath = `${config.workspacePath}/example/blocks_${userId}.json`
      const oldImgPath = `${config.workspacePath}/example/img`
      const oldPdfPath = `${config.workspacePath}/example/pdf`
      
      // 新架构路径（用户隔离）
      const newDbPath = `${config.getUserDbPath(userId)}.db`
      const newBlockPath = `${config.getUserWorkspace(userId)}/blocks.json`
      const newImgPath = config.getUserImgPath(userId)
      const newPdfPath = config.getUserPdfPath(userId)
      
      // 验证新架构的优势
      // 1. 文件名更简洁（无冗余userId）
      expect(newDbPath).toContain('/chron.db')
      expect(newDbPath).not.toContain(`chron_${userId}.db`)
      
      // 2. 用户隔离更明确
      expect(newImgPath).toContain(`/users/${userId}/img`)
      expect(newPdfPath).toContain(`/users/${userId}/pdf`)
      expect(oldImgPath).not.toContain(userId)
      expect(oldPdfPath).not.toContain(userId)
      
      // 3. 目录结构更清晰
      expect(newDbPath).toContain('/users/')
      expect(newBlockPath).toContain('/users/')
    })
  })
})