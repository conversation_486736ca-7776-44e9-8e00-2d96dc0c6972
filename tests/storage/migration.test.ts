import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { StorageTestHelper, TestUser, TestFileStructure } from './storageTestHelper'
import { MockFileSystemManager } from './mockFileSystem'

/**
 * Mock迁移服务，使用测试文件系统
 */
class MockMigrationService {
  private fs: MockFileSystemManager
  private testWorkspace: string

  constructor(testWorkspace: string) {
    this.fs = new MockFileSystemManager()
    this.testWorkspace = testWorkspace
  }

  /**
   * 扫描现有存储结构
   */
  async scanExistingStructure(): Promise<{ users: string[], needsMigration: boolean }> {
    const examplePath = `${this.testWorkspace}/example`
    
    try {
      const files = await this.fs.readdir(examplePath)
      
      // 识别数据库文件和用户ID
      const dbFiles = files.filter(file => file.startsWith('chron_') && file.endsWith('.db'))
      const userIds = dbFiles.map(file => file.replace('chron_', '').replace('.db', ''))
      
      // 验证对应的blocks文件存在
      const blockFiles = files.filter(file => file.startsWith('blocks_') && file.endsWith('.json'))
      const blockUserIds = blockFiles.map(file => file.replace('blocks_', '').replace('.json', ''))
      
      // 确保数据库文件和块文件一一对应
      const validUserIds = userIds.filter(userId => blockUserIds.includes(userId))
      
      return { users: validUserIds, needsMigration: validUserIds.length > 0 }
    } catch (error) {
      return { users: [], needsMigration: false }
    }
  }

  /**
   * 创建迁移前备份
   */
  async createPreMigrationBackup(): Promise<string> {
    const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\..+/, '')
    const backupPath = `${this.testWorkspace}/migration_backup_${timestamp}`
    
    await this.fs.mkdir(backupPath)
    
    // 备份整个example目录
    const examplePath = `${this.testWorkspace}/example`
    await this.fs.copy(examplePath, `${backupPath}/example`)
    
    return backupPath
  }

  /**
   * 执行保守的文件迁移策略
   */
  async performConservativeMigration(userIds: string[]): Promise<void> {
    const examplePath = `${this.testWorkspace}/example`
    const usersPath = `${this.testWorkspace}/users`
    
    // 创建users根目录
    await this.fs.mkdir(usersPath)

    for (const userId of userIds) {
      const userPath = `${usersPath}/${userId}`
      
      // 1. 创建用户目录结构
      await this.fs.mkdir(userPath)
      await this.fs.mkdir(`${userPath}/img`)
      await this.fs.mkdir(`${userPath}/pdf`)

      // 2. 移动数据库文件
      const oldDbPath = `${examplePath}/chron_${userId}.db`
      const newDbPath = `${userPath}/chron.db`
      
      try {
        await this.fs.copy(oldDbPath, newDbPath)
        await this.fs.remove(oldDbPath)
      } catch (error) {
        console.warn(`移动数据库文件失败 ${userId}: ${error}`)
      }

      // 3. 移动块数据文件
      const oldBlockPath = `${examplePath}/blocks_${userId}.json`
      const newBlockPath = `${userPath}/blocks.json`
      
      try {
        await this.fs.copy(oldBlockPath, newBlockPath)
        await this.fs.remove(oldBlockPath)
      } catch (error) {
        console.warn(`移动块数据文件失败 ${userId}: ${error}`)
      }

      // 4. 保守策略：复制所有img文件给每个用户
      const imgPath = `${examplePath}/img`
      try {
        await this.fs.copy(imgPath, `${userPath}/img`)
      } catch (error) {
        // img目录可能不存在，这是正常的
      }

      // 5. 保守策略：复制所有pdf文件给每个用户  
      const pdfPath = `${examplePath}/pdf`
      try {
        await this.fs.copy(pdfPath, `${userPath}/pdf`)
      } catch (error) {
        // pdf目录可能不存在，这是正常的
      }
    }

    // 清理旧的共享目录
    try {
      await this.fs.remove(`${examplePath}/img`)
      await this.fs.remove(`${examplePath}/pdf`)
    } catch (error) {
      // 某些目录可能不存在，这是正常的
    }
  }

  /**
   * 验证迁移结果
   */
  async verifyMigration(userIds: string[]): Promise<boolean> {
    const usersPath = `${this.testWorkspace}/users`
    
    for (const userId of userIds) {
      const userPath = `${usersPath}/${userId}`
      
      // 验证用户目录存在
      try {
        const userDir = await this.fs.stat(userPath)
        if (!userDir.isDirectory()) {
          return false
        }
      } catch (error) {
        return false
      }

      // 验证核心文件存在
      try {
        await this.fs.stat(`${userPath}/chron.db`)
        await this.fs.stat(`${userPath}/blocks.json`)
      } catch (error) {
        return false
      }

      // 验证资源目录存在
      try {
        const imgDir = await this.fs.stat(`${userPath}/img`)
        const pdfDir = await this.fs.stat(`${userPath}/pdf`)
        
        if (!imgDir.isDirectory() || !pdfDir.isDirectory()) {
          return false
        }
      } catch (error) {
        return false
      }
    }

    return true
  }
}

describe('数据迁移测试', () => {
  let testHelper: StorageTestHelper
  let migrationService: MockMigrationService
  let testUsers: TestUser[]
  let originalStructure: TestFileStructure

  beforeEach(async () => {
    testHelper = new StorageTestHelper()
    await testHelper.setupTestEnvironment()
    
    migrationService = new MockMigrationService(testHelper.getTestWorkspace())
    
    // 生成2个测试用户
    testUsers = testHelper.generateTestUsers(2)
    originalStructure = await testHelper.createLegacyFileStructure(testUsers)
  })

  afterEach(async () => {
    await testHelper.cleanupTestEnvironment()
  })

  describe('2.1 用户识别和数据分析', () => {
    it('应该正确识别多个用户', async () => {
      const scanResult = await migrationService.scanExistingStructure()
      
      expect(scanResult.needsMigration).toBe(true)
      expect(scanResult.users).toHaveLength(2)
      expect(scanResult.users).toContain(testUsers[0].uuid)
      expect(scanResult.users).toContain(testUsers[1].uuid)
    })

    it('应该在没有用户数据时返回无需迁移', async () => {
      // 创建一个空的测试环境
      const emptyHelper = new StorageTestHelper('/tmp/chronnote-test-empty')
      await emptyHelper.setupTestEnvironment()
      
      const emptyMigrationService = new MockMigrationService(emptyHelper.getTestWorkspace())
      const scanResult = await emptyMigrationService.scanExistingStructure()
      
      expect(scanResult.needsMigration).toBe(false)
      expect(scanResult.users).toHaveLength(0)
      
      await emptyHelper.cleanupTestEnvironment()
    })
  })

  describe('2.2 迁移前备份', () => {
    it('应该创建完整的备份', async () => {
      const backupPath = await migrationService.createPreMigrationBackup()
      
      expect(backupPath).toContain('migration_backup_')
      
      // 验证备份目录存在
      const fs = new (testHelper as any).fs.constructor()
      const backupStat = await fs.stat(backupPath)
      expect(backupStat.isDirectory()).toBe(true)
      
      // 验证备份内容存在
      const backupExamplePath = `${backupPath}/example`
      const backupExampleStat = await fs.stat(backupExamplePath)
      expect(backupExampleStat.isDirectory()).toBe(true)
    })
  })

  describe('2.3 保守的文件迁移策略', () => {
    it('应该为每个用户创建独立目录', async () => {
      const scanResult = await migrationService.scanExistingStructure()
      await migrationService.performConservativeMigration(scanResult.users)
      
      const workspace = testHelper.getTestWorkspace()
      const fs = new (testHelper as any).fs.constructor()
      
      // 验证users目录存在
      const usersPath = `${workspace}/users`
      const usersStat = await fs.stat(usersPath)
      expect(usersStat.isDirectory()).toBe(true)
      
      // 验证每个用户目录存在
      for (const user of testUsers) {
        const userPath = `${usersPath}/${user.uuid}`
        const userStat = await fs.stat(userPath)
        expect(userStat.isDirectory()).toBe(true)
      }
    })

    it('应该正确移动数据库和块文件', async () => {
      const scanResult = await migrationService.scanExistingStructure()
      await migrationService.performConservativeMigration(scanResult.users)
      
      const workspace = testHelper.getTestWorkspace()
      const fs = new (testHelper as any).fs.constructor()
      
      for (const user of testUsers) {
        const userPath = `${workspace}/users/${user.uuid}`
        
        // 验证新文件存在
        await fs.stat(`${userPath}/chron.db`)
        await fs.stat(`${userPath}/blocks.json`)
        
        // 验证旧文件已删除
        try {
          await fs.stat(`${workspace}/example/chron_${user.uuid}.db`)
          throw new Error('旧数据库文件应该已被删除')
        } catch (error) {
          // 这是期望的行为
        }
        
        try {
          await fs.stat(`${workspace}/example/blocks_${user.uuid}.json`)
          throw new Error('旧块文件应该已被删除')
        } catch (error) {
          // 这是期望的行为
        }
      }
    })

    it('应该为每个用户复制所有共享文件', async () => {
      const scanResult = await migrationService.scanExistingStructure()
      await migrationService.performConservativeMigration(scanResult.users)
      
      const workspace = testHelper.getTestWorkspace()
      const fs = new (testHelper as any).fs.constructor()
      
      for (const user of testUsers) {
        const userPath = `${workspace}/users/${user.uuid}`
        
        // 验证每个用户都有img和pdf目录
        const imgStat = await fs.stat(`${userPath}/img`)
        const pdfStat = await fs.stat(`${userPath}/pdf`)
        expect(imgStat.isDirectory()).toBe(true)
        expect(pdfStat.isDirectory()).toBe(true)
        
        // 验证文件被复制
        const imgFiles = await fs.readdir(`${userPath}/img`)
        const pdfFiles = await fs.readdir(`${userPath}/pdf`)
        
        expect(imgFiles.length).toBe(originalStructure.imgFiles.length)
        expect(pdfFiles.length).toBe(originalStructure.pdfFiles.length)
        
        // 验证文件内容
        for (const imgFile of originalStructure.imgFiles) {
          const content = await fs.readFile(`${userPath}/img/${imgFile}`)
          expect(content).toContain(`Mock image content for ${imgFile}`)
        }
      }
    })
  })

  describe('2.4 迁移验证', () => {
    it('应该验证迁移的完整性', async () => {
      const scanResult = await migrationService.scanExistingStructure()
      await migrationService.performConservativeMigration(scanResult.users)
      
      const verificationResult = await migrationService.verifyMigration(scanResult.users)
      expect(verificationResult).toBe(true)
    })

    it('应该使用测试助手验证新文件结构', async () => {
      const scanResult = await migrationService.scanExistingStructure()
      await migrationService.performConservativeMigration(scanResult.users)
      
      const isValid = await testHelper.validateNewFileStructure(testUsers)
      expect(isValid).toBe(true)
    })

    it('应该验证文件内容的完整性', async () => {
      const scanResult = await migrationService.scanExistingStructure()
      await migrationService.performConservativeMigration(scanResult.users)
      
      const integrityValid = await testHelper.validateFileIntegrity(originalStructure, testUsers)
      expect(integrityValid).toBe(true)
    })
  })

  describe('保守策略的存储空间计算', () => {
    it('应该正确计算存储空间增长', async () => {
      // 在迁移前计算存储空间
      const storageBefore = await testHelper.calculateStorageUsage()
      expect(storageBefore.before).toBeGreaterThan(0) // example目录应该有内容
      expect(storageBefore.after).toBe(0) // users目录还不存在
      
      const scanResult = await migrationService.scanExistingStructure()
      await migrationService.performConservativeMigration(scanResult.users)
      
      // 在迁移后计算存储空间
      const storageAfter = await testHelper.calculateStorageUsage()
      
      // 验证users目录现在有内容
      expect(storageAfter.after).toBeGreaterThan(0)
      
      // 计算实际的增长比例
      const actualRatio = storageAfter.after / storageBefore.before
      
      // 对于2个用户，共享文件的部分应该增加
      // (数据库文件+块文件保持总量不变，但img+pdf文件翻倍)
      // 实际测试中约为1.37，说明共享文件占总存储的约27%
      expect(actualRatio).toBeGreaterThan(1.2)  // 至少增长20%
      expect(actualRatio).toBeLessThan(2.5)     // 不会翻倍太多
    })
  })
})