import * as fs from 'fs/promises'
import * as path from 'path'

/**
 * Mock文件系统管理器，用于测试环境
 * 使用Node.js的fs模块而不是应用的文件系统抽象
 */
export class MockFileSystemManager {
  
  async mkdir(dirPath: string): Promise<void> {
    try {
      await fs.mkdir(dirPath, { recursive: true })
    } catch (error) {
      // 目录已存在，忽略错误
    }
  }

  async writeFile(filePath: string, content: string): Promise<void> {
    const dir = path.dirname(filePath)
    await this.mkdir(dir)
    await fs.writeFile(filePath, content, 'utf8')
  }

  async writeJson(filePath: string, data: any): Promise<void> {
    const content = JSON.stringify(data, null, 2)
    await this.writeFile(filePath, content)
  }

  async readFile(filePath: string): Promise<string> {
    const content = await fs.readFile(filePath, 'utf8')
    return content
  }

  async readJson(filePath: string): Promise<any> {
    const content = await this.readFile(filePath)
    return JSON.parse(content)
  }

  async readdir(dirPath: string): Promise<string[]> {
    try {
      return await fs.readdir(dirPath)
    } catch (error) {
      return []
    }
  }

  async stat(filePath: string): Promise<{ isDirectory: () => boolean; size: number }> {
    const stats = await fs.stat(filePath)
    return {
      isDirectory: () => stats.isDirectory(),
      size: stats.size
    }
  }

  async remove(filePath: string): Promise<void> {
    try {
      const stats = await fs.stat(filePath)
      if (stats.isDirectory()) {
        await fs.rmdir(filePath, { recursive: true })
      } else {
        await fs.unlink(filePath)
      }
    } catch (error) {
      // 文件不存在，忽略错误
    }
  }

  async copy(src: string, dest: string): Promise<void> {
    const stats = await fs.stat(src)
    
    if (stats.isDirectory()) {
      await this.mkdir(dest)
      const files = await fs.readdir(src)
      
      for (const file of files) {
        const srcFile = path.join(src, file)
        const destFile = path.join(dest, file)
        await this.copy(srcFile, destFile)
      }
    } else {
      const dir = path.dirname(dest)
      await this.mkdir(dir)
      await fs.copyFile(src, dest)
    }
  }
}