import { v4 as uuidv4 } from 'uuid'
import { MockFileSystemManager } from './mockFileSystem'

export interface TestUser {
  uuid: string
  email: string
}

export interface TestFileStructure {
  databases: string[]    // chron_{userId}.db files
  blocks: string[]       // blocks_{userId}.json files  
  imgFiles: string[]     // files in img/ directory
  pdfFiles: string[]     // files in pdf/ directory
}

/**
 * 测试用的文件系统助手类
 * 用于创建、清理和验证测试环境
 */
export class StorageTestHelper {
  private fs: MockFileSystemManager
  private testWorkspace: string
  private originalWorkspace: string | null = null

  constructor(testWorkspace: string = '/tmp/chronnote-test') {
    this.fs = new MockFileSystemManager()
    this.testWorkspace = testWorkspace
  }

  /**
   * 设置测试环境
   */
  async setupTestEnvironment(): Promise<void> {
    // 清理可能存在的测试目录
    try {
      await this.fs.remove(this.testWorkspace)
    } catch (error) {
      // 目录不存在，忽略错误
    }

    // 创建测试工作空间
    await this.fs.mkdir(this.testWorkspace)
    await this.fs.mkdir(`${this.testWorkspace}/example`)
    await this.fs.mkdir(`${this.testWorkspace}/example/img`)
    await this.fs.mkdir(`${this.testWorkspace}/example/pdf`)
  }

  /**
   * 清理测试环境
   */
  async cleanupTestEnvironment(): Promise<void> {
    try {
      await this.fs.remove(this.testWorkspace)
    } catch (error) {
      console.warn('Failed to cleanup test environment:', error)
    }
  }

  /**
   * 生成测试用户
   */
  generateTestUsers(count: number = 2): TestUser[] {
    const users: TestUser[] = []
    for (let i = 0; i < count; i++) {
      users.push({
        uuid: uuidv4(),
        email: `testuser${i + 1}@example.com`
      })
    }
    return users
  }

  /**
   * 创建旧格式的测试文件结构
   */
  async createLegacyFileStructure(users: TestUser[]): Promise<TestFileStructure> {
    const structure: TestFileStructure = {
      databases: [],
      blocks: [],
      imgFiles: ['test-image-1.jpg', 'test-image-2.png', 'avatar.jpg'],
      pdfFiles: ['document1.pdf', 'manual.pdf', 'report.pdf']
    }

    const examplePath = `${this.testWorkspace}/example`

    // 创建每个用户的数据库和块文件
    for (const user of users) {
      const dbFile = `chron_${user.uuid}.db`
      const blockFile = `blocks_${user.uuid}.json`
      
      // 创建虚拟数据库文件（实际上是文本文件，用于测试）
      await this.fs.writeFile(`${examplePath}/${dbFile}`, `-- Test database for user ${user.uuid}`)
      
      // 创建虚拟块文件
      await this.fs.writeJson(`${examplePath}/${blockFile}`, {
        userId: user.uuid,
        blocks: [],
        createdAt: new Date().toISOString()
      })

      structure.databases.push(dbFile)
      structure.blocks.push(blockFile)
    }

    // 创建共享的图片文件
    for (const imgFile of structure.imgFiles) {
      await this.fs.writeFile(
        `${examplePath}/img/${imgFile}`, 
        `Mock image content for ${imgFile}`
      )
    }

    // 创建共享的PDF文件
    for (const pdfFile of structure.pdfFiles) {
      await this.fs.writeFile(
        `${examplePath}/pdf/${pdfFile}`, 
        `Mock PDF content for ${pdfFile}`
      )
    }

    return structure
  }

  /**
   * 验证新格式的目录结构
   */
  async validateNewFileStructure(users: TestUser[]): Promise<boolean> {
    try {
      const usersPath = `${this.testWorkspace}/users`
      
      // 验证users目录存在
      const usersDir = await this.fs.stat(usersPath)
      if (!usersDir.isDirectory()) {
        throw new Error('Users directory should exist and be a directory')
      }

      // 验证每个用户的目录结构
      for (const user of users) {
        const userPath = `${usersPath}/${user.uuid}`
        
        // 验证用户目录存在
        const userDir = await this.fs.stat(userPath)
        if (!userDir.isDirectory()) {
          throw new Error(`User directory should exist: ${userPath}`)
        }

        // 验证核心文件存在
        await this.fs.stat(`${userPath}/chron.db`)
        await this.fs.stat(`${userPath}/blocks.json`)

        // 验证资源目录存在
        const imgDir = await this.fs.stat(`${userPath}/img`)
        const pdfDir = await this.fs.stat(`${userPath}/pdf`)
        
        if (!imgDir.isDirectory() || !pdfDir.isDirectory()) {
          throw new Error(`Resource directories should exist for user: ${user.uuid}`)
        }

        // 验证资源文件被复制到每个用户目录
        const imgFiles = await this.fs.readdir(`${userPath}/img`)
        const pdfFiles = await this.fs.readdir(`${userPath}/pdf`)
        
        if (imgFiles.length === 0 || pdfFiles.length === 0) {
          throw new Error(`Resource files should be copied to user directory: ${user.uuid}`)
        }
      }

      return true
    } catch (error) {
      console.error('New file structure validation failed:', error)
      return false
    }
  }

  /**
   * 验证文件内容的完整性
   */
  async validateFileIntegrity(originalStructure: TestFileStructure, users: TestUser[]): Promise<boolean> {
    try {
      const usersPath = `${this.testWorkspace}/users`

      for (const user of users) {
        const userPath = `${usersPath}/${user.uuid}`

        // 验证数据库文件内容
        const dbContent = await this.fs.readFile(`${userPath}/chron.db`)
        if (!dbContent.includes(`Test database for user ${user.uuid}`)) {
          throw new Error(`Database content validation failed for user: ${user.uuid}`)
        }

        // 验证块文件内容
        const blockContent = await this.fs.readJson(`${userPath}/blocks.json`)
        if (blockContent.userId !== user.uuid) {
          throw new Error(`Block file content validation failed for user: ${user.uuid}`)
        }

        // 验证图片文件内容
        for (const imgFile of originalStructure.imgFiles) {
          const content = await this.fs.readFile(`${userPath}/img/${imgFile}`)
          if (!content.includes(`Mock image content for ${imgFile}`)) {
            throw new Error(`Image file validation failed: ${imgFile} for user: ${user.uuid}`)
          }
        }

        // 验证PDF文件内容
        for (const pdfFile of originalStructure.pdfFiles) {
          const content = await this.fs.readFile(`${userPath}/pdf/${pdfFile}`)
          if (!content.includes(`Mock PDF content for ${pdfFile}`)) {
            throw new Error(`PDF file validation failed: ${pdfFile} for user: ${user.uuid}`)
          }
        }
      }

      return true
    } catch (error) {
      console.error('File integrity validation failed:', error)
      return false
    }
  }

  /**
   * 计算迁移前后的存储空间使用
   */
  async calculateStorageUsage(): Promise<{ before: number, after: number, ratio: number }> {
    try {
      const beforePath = `${this.testWorkspace}/example`
      const afterPath = `${this.testWorkspace}/users`

      const beforeSize = await this.calculateDirectorySize(beforePath)
      const afterSize = await this.calculateDirectorySize(afterPath)

      return {
        before: beforeSize,
        after: afterSize,
        ratio: beforeSize > 0 ? afterSize / beforeSize : 0
      }
    } catch (error) {
      console.error('Storage calculation failed:', error)
      return { before: 0, after: 0, ratio: 0 }
    }
  }

  /**
   * 递归计算目录大小（简化版本，仅用于测试）
   */
  private async calculateDirectorySize(dirPath: string): Promise<number> {
    try {
      const files = await this.fs.readdir(dirPath)
      let totalSize = 0

      for (const file of files) {
        const filePath = `${dirPath}/${file}`
        const stats = await this.fs.stat(filePath)
        
        if (stats.isDirectory()) {
          totalSize += await this.calculateDirectorySize(filePath)
        } else {
          totalSize += stats.size || 0
        }
      }

      return totalSize
    } catch (error) {
      return 0
    }
  }

  /**
   * 获取测试工作空间路径
   */
  getTestWorkspace(): string {
    return this.testWorkspace
  }
}