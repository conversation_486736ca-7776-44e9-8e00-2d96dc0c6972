export interface Issue {
  id: string
  title: string
  type?: IssueType
  status: IssueStatus
  assignee: string
  date: string
  avatar: string
  priority?: IssuePriority
}

export enum IssueType {
  FEATURE = 'Feature',
  IMPROVEMENT = 'Improvement',
  BUG = 'Bug',
  TASK = 'Task'
}

export enum IssueStatus {
  TODO = 'Todo',
  IN_PROGRESS = 'In Progress',
  DONE = 'Done',
  CANCELLED = 'Cancelled'
}

export enum IssuePriority {
  LOW = 'Low',
  MEDIUM = 'Medium',
  HIGH = 'High',
  URGENT = 'Urgent'
}

export const ISSUE_TYPE_STYLES = {
  [IssueType.FEATURE]: 'bg-purple-100 text-purple-800',
  [IssueType.IMPROVEMENT]: 'bg-blue-100 text-blue-800',
  [IssueType.BUG]: 'bg-red-100 text-red-800',
  [IssueType.TASK]: 'bg-gray-100 text-gray-800'
}

export const MAIN_HEADER_TABS = [
  {
    id: 'all-issues',
    label: 'All issues',
    icon: 'Circle'
  },
  {
    id: 'active',
    label: 'Active',
    icon: 'Circle'
  },
  {
    id: 'backlog',
    label: 'Backlog',
    icon: 'RotateCcw'
  }
]

export const SAMPLE_ISSUES: Issue[] = [
  {
    id: 'CHR-13',
    title: '球状笔记',
    type: IssueType.FEATURE,
    status: IssueStatus.TODO,
    assignee: 'Chron编辑器优化',
    date: 'May 30',
    avatar: '🎯'
  },
  {
    id: 'CHR-12',
    title: 'Bubble Menu 手势优化',
    type: IssueType.IMPROVEMENT,
    status: IssueStatus.TODO,
    assignee: 'Chron编辑器优化',
    date: 'May 30',
    avatar: '🟢'
  },
  {
    id: 'CHR-11',
    title: '安装包瘦下载',
    type: IssueType.IMPROVEMENT,
    status: IssueStatus.TODO,
    assignee: '',
    date: 'May 30',
    avatar: '🎯'
  },
  {
    id: 'CHR-27',
    title: 'Dify移植Coze',
    status: IssueStatus.TODO,
    assignee: '',
    date: 'May 31',
    avatar: '🎯'
  },
  {
    id: 'CHR-26',
    title: '命令面板中增加逻辑修改',
    status: IssueStatus.TODO,
    assignee: '',
    date: 'May 31',
    avatar: '🎯'
  },
  {
    id: 'CHR-25',
    title: 'Chron编辑器目录',
    status: IssueStatus.TODO,
    assignee: '',
    date: 'May 31',
    avatar: '🎯'
  },
  {
    id: 'CHR-24',
    title: 'Tavily Search API设计',
    status: IssueStatus.TODO,
    assignee: '',
    date: 'May 31',
    avatar: '🎯'
  },
  {
    id: 'CHR-23',
    title: 'Todo样式全面优化',
    status: IssueStatus.TODO,
    assignee: '',
    date: 'May 31',
    avatar: '🎯'
  },
  {
    id: 'CHR-22',
    title: '全局面板回车创建了个个习惯版',
    status: IssueStatus.TODO,
    assignee: '',
    date: 'May 31',
    avatar: '🎯'
  },
  {
    id: 'CHR-21',
    title: '标签功能',
    status: IssueStatus.TODO,
    assignee: '',
    date: 'May 31',
    avatar: '🎯'
  },
  {
    id: 'CHR-20',
    title: 'markdown样式',
    status: IssueStatus.TODO,
    assignee: '',
    date: 'May 31',
    avatar: '🎯'
  },
  {
    id: 'CHR-19',
    title: 'Chron 引擎位置',
    status: IssueStatus.TODO,
    assignee: '',
    date: 'May 31',
    avatar: '🎯'
  },
  {
    id: 'CHR-18',
    title: '编辑器初始化不要Focus',
    status: IssueStatus.TODO,
    assignee: '',
    date: 'May 31',
    avatar: '🎯'
  },
  {
    id: 'CHR-17',
    title: '新建PDF 没有Focus',
    status: IssueStatus.TODO,
    assignee: '',
    date: 'May 31',
    avatar: '🎯'
  },
  {
    id: 'CHR-16',
    title: '多屏联动的答案效',
    type: IssueType.BUG,
    status: IssueStatus.TODO,
    assignee: '',
    date: 'May 30',
    avatar: '🔴'
  },
  {
    id: 'CHR-15',
    title: '分层系统升级',
    type: IssueType.IMPROVEMENT,
    status: IssueStatus.TODO,
    assignee: '',
    date: 'May 30',
    avatar: '🟢'
  },
  {
    id: 'CHR-14',
    title: '图标库完善',
    type: IssueType.IMPROVEMENT,
    status: IssueStatus.TODO,
    assignee: '',
    date: 'May 30',
    avatar: '🎯'
  },
  {
    id: 'CHR-10',
    title: 'Chronnote Features系列',
    status: IssueStatus.TODO,
    assignee: 'Chronnote官方',
    date: 'May 30',
    avatar: '🟢'
  }
]
