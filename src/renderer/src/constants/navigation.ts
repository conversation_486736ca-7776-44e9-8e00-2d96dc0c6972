export interface NavigationItem {
  id: string
  label: string
  icon: string
  path?: string
  badge?: string | number
  children?: NavigationItem[]
  draggable?: boolean
}

export interface NavigationSection {
  id: string
  title: string
  items: NavigationItem[]
  collapsible?: boolean
  collapsed?: boolean
}

export const MAIN_NAVIGATION: NavigationItem[] = [
  {
    id: 'mail',
    label: '收件箱',
    icon: 'Mail'
  },
  {
    id: 'trash',
    label: '回收站',
    icon: 'Trash2'
  },
  {
    id: 'chronengine',
    label: 'Chron Engine',
    icon: 'Cog',
    draggable: true
  },
]

export const WORKSPACE_NAVIGATION: NavigationSection = {
  id: 'workspace',
  title: 'Workspace',
  collapsible: true,
  items: [
    {
      id: 'projects',
      label: 'Projects',
      icon: 'Folder',
      path: '/projects'
    },
    {
      id: 'views',
      label: 'Views',
      icon: 'Eye',
      path: '/views'
    },
    {
      id: 'more',
      label: 'More',
      icon: 'MoreHorizontal',
      path: '/more'
    }
  ]
}

export const TEAMS_NAVIGATION: NavigationSection = {
  id: 'teams',
  title: 'Your teams',
  collapsible: true,
  items: [
    {
      id: 'chronnote-team',
      label: 'Chronnote',
      icon: 'Circle',
      path: '/teams/chronnote',
      children: [
        {
          id: 'team-issues',
          label: 'Issues',
          icon: 'Circle',
          path: '/teams/chronnote/issues'
        },
        {
          id: 'team-projects',
          label: 'Projects',
          icon: 'Folder',
          path: '/teams/chronnote/projects'
        },
        {
          id: 'team-views',
          label: 'Views',
          icon: 'Eye',
          path: '/teams/chronnote/views'
        }
      ]
    }
  ]
}

export const TRY_NAVIGATION: NavigationSection = {
  id: 'try',
  title: 'Try',
  collapsible: true,
  items: [
    {
      id: 'import-issues',
      label: 'Import issues',
      icon: 'Plus',
      path: '/import-issues'
    },
    {
      id: 'invite-people',
      label: 'Invite people',
      icon: 'Plus',
      path: '/invite-people'
    },
    {
      id: 'link-github',
      label: 'Link GitHub',
      icon: 'Github',
      path: '/link-github'
    }
  ]
}

export const APP_CONFIG = {
  name: 'Chronnote',
  logo: 'CH',
  logoColor: 'bg-pink-500'
}

export const FOOTER_CONFIG = {
  plan: 'Free plan',
  helpIcon: 'HelpCircle'
}
