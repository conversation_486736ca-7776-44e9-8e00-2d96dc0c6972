/**
 * ChronEngine组件相关常量配置
 */

// Coze API配置
export const COZE_CONFIG = {
  BOT_ID: '7505382249266233370',
  DEFAULT_WORKFLOW_ID: '',
  API_BASE_URL: '',
} as const

// 打字机效果配置
export const TYPEWRITER_CONFIG = {
  DEFAULT_INTERVAL: 10, // ms
  FAST_INTERVAL: 5,
  SLOW_INTERVAL: 20,
} as const

// 滚动配置
export const SCROLL_CONFIG = {
  THRESHOLD: 50, // px
  SMOOTH_BEHAVIOR: 'smooth',
  AUTO_SCROLL_DELAY: 100, // ms
} as const

// UI尺寸配置
export const UI_CONFIG = {
  MAX_MESSAGE_HEIGHT: '10rem',
  CONTAINER_HEIGHT_CALC: 'calc(100%-1.5rem)',
  AI_CONVERSATION_HEIGHT: 'calc(100% - 120px)',
  INPUT_MIN_HEIGHT: '2.75rem',
  INPUT_MAX_HEIGHT: '10rem',
  EMPTY_STATE_HEIGHT: 'calc(100vh - 200px)',
} as const

// 网格样式配置
export const GRID_CONFIG = {
  SIZE: '40px 40px',
  OPACITY: 0.03,
} as const

// 动画配置
export const ANIMATION_CONFIG = {
  FADE_DURATION: '0.3s',
  TYPING_ANIMATION_DURATION: '1.5s',
  TRANSITION_DURATION: '0.2s',
  HOVER_TRANSITION: '0.3s',
} as const

// 默认AI建议
export const AI_SUGGESTIONS = [
  '总结一下这个文档的内容',
  '帮我润色这段文字', 
  '这段文字的主要观点是什么？',
  '如何提高我的笔记效率？',
  '推荐一些笔记整理的方法'
] as const

// 消息模板
export const MESSAGE_TEMPLATES = {
  THINKING: '正在思考中...',
  GENERATING: '正在生成回答...',
  TOOL_CALL_SUCCESS: '\n\n*工具调用成功*',
  TOOL_CALL_FAILED: '\n\n*工具调用失败: {error}*',
  TOOL_PROCESSING_FAILED: '\n\n*工具调用处理失败: {error}*',
  ERROR_GENERIC: '抱歉，发生了错误: {error}',
  ERROR_NETWORK: '抱歉，处理您的请求时出现了错误。请检查网络连接或稍后再试。',
  ERROR_EMPTY_RESPONSE: '抱歉，我无法生成回答。请尝试重新提问。',
  ANALYSIS_IMAGE: '请分析这张图片',
} as const

// 图片处理配置
export const IMAGE_CONFIG = {
  DEFAULT_WIDTH: '400px',
  SUPPORTED_PROTOCOLS: ['http', 'https', 'chron://'],
  PLACEHOLDER_TEXT: '[图片]',
} as const

// 事件名称
export const EVENTS = {
  CHRONENGINE_ADD_IMAGE: 'chronengine:add-image',
  CHRONENGINE_ADD_TEXT: 'chronengine:add-text', 
  COZE_IMAGE_UPLOADED: 'coze-image-uploaded',
} as const

// Toast配置
export const TOAST_CONFIG = {
  DEFAULT_DURATION: 2000,
  SUCCESS_TITLE: '提示',
  ERROR_TITLE: '错误',
  COPY_MESSAGE: '已复制到剪贴板',
  SAVE_MESSAGE: '已保存到笔记',
  SHARE_MESSAGE: '已复制到剪贴板，可以分享给他人',
  SAVE_TO_NEW_NOTE: '已保存到新笔记',
} as const

// 工具调用相关
export const TOOL_CALLING = {
  THINKING_START_TAG: '<thinking>',
  THINKING_END_TAG: '</thinking>',
  STEP_START_TAG: '<step>',
  STEP_END_TAG: '</step>',
} as const

// CSS类名
export const CSS_CLASSES = {
  CHRON_ENGINE_CONTAINER: 'chron-engine-container',
  AI_CONVERSATION_CONTAINER: 'ai-conversation-container',
  MESSAGE_CONTAINER: 'message-container',
  USER_MESSAGE_CONTAINER: 'user-message-container',
  AI_MESSAGE_CONTAINER: 'ai-message-container',
  INPUT_CONTAINER: 'input-container',
  CHAT_INPUT_WRAPPER: 'chat-input-wrapper',
  CHAT_ACTIONS: 'chat-actions',
  TYPING_INDICATOR: 'typing-indicator',
  ANIMATE_FADE_IN: 'animate-fade-in',
  BG_GRID_PATTERN: 'bg-grid-pattern',
} as const

// 快捷键配置
export const KEYBOARD = {
  ENTER: 'Enter',
  SHIFT: 'Shift',
} as const

// 默认编辑器内容
export const DEFAULT_EDITOR_CONTENT = {
  type: 'doc',
  content: [
    {
      type: 'paragraph',
      content: []
    }
  ]
} as const