<template>
  <div class="h-full flex flex-col">
    <!-- 回收站头部 -->
    <div class="flex items-center justify-between py-3 px-6 border-b border-base-border">
      <div class="flex items-center gap-3">
        <IconButton
          icon="ArrowLeft"
          variant="ghost"
          size="sm"
          @click="goToHome"
          class="text-base-content/70 hover:text-base-content"
        />
        <div class="flex items-center gap-2">
          <Trash2 class="h-4 w-4 text-base-content/70" />
          <h1 class="font-medium text-title uppercase tracking-wide">回收站</h1>
        </div>
      </div>
      <HeaderActions
        v-if="!trashStore.isEmpty"
        :menu-actions="headerMenuActions"
        :show-split-controls="false"
        :show-close-button="false"
        @action="handleHeaderAction"
      />
    </div>

    <!-- 回收站内容 -->
    <div class="flex-1 overflow-hidden">
      <!-- 空状态 -->
      <div v-if="trashStore.isEmpty" class="flex flex-col items-center pt-32 h-full">
        <div class="rounded-full p-8 inline-flex items-center justify-center">
          <!-- <Trash2 class="h-16 w-16 text-base-content/30" /> -->
          <img src="../assets/garbage.svg" alt="garbage" class="h-16 w-16 text-base-content/30" />
        </div>
        <h2 class="font-medium text-title mb-2">回收站为空</h2>
        <p class="text-subtitle text-center max-w-lg">
          删除的项目会出现在这里，您可以选择恢复或永久删除它们
        </p>
      </div>

      <!-- 回收站项目列表 -->
      <div v-else class="h-full overflow-y-auto p-6">
        <div class="space-y-3 max-w-4xl mx-auto">
          <div
            v-for="item in trashStore.sortedTrashItems"
            :key="item.uuid"
            class="flex items-center justify-between p-2.5 rounded-lg border border-base-border transition-colors hover:bg-base-50"
          >
            <div class="flex items-center gap-2.5 flex-1 min-w-0">
              <!-- 项目图标 -->
              <div class="flex-shrink-0 p-1 bg-base-100 rounded">
                <component
                  :is="getTypeIcon(item.originalNode.type)"
                  class="h-3.5 w-3.5 text-base-content/70"
                />
              </div>

              <!-- 项目信息 -->
              <div class="flex-1 min-w-0 select-none">
                <div class="flex items-center gap-2 mb-0.5">
                  <h3 class="text-sm font-medium text-title truncate">
                    {{ item.originalNode.title }}
                  </h3>
                  <!-- 显示子节点数量 -->
                  <span
                    v-if="getChildrenCount(item.uuid) > 0"
                    class="text-xs px-1.5 py-0.5 bg-base-150 text-base-content/70 rounded"
                  >
                    +{{ getChildrenCount(item.uuid) }}
                  </span>
                  <!-- 显示父组件信息 -->
                  <span
                    v-if="getParentInfo(item)"
                    :class="getParentInfoClass(item)"
                    class="text-xs px-1.5 py-0.5 rounded"
                  >
                    ← {{ getParentInfo(item) }}
                  </span>
                </div>
                <p class="text-xs text-subtitle">
                  删除于 {{ formatDeletedTime(item.deletedAt) }}
                  <span v-if="getChildrenCount(item.uuid) > 0" class="ml-2">
                    (包含 {{ getChildrenCount(item.uuid) }} 个子项目)
                  </span>
                  <span v-if="getParentInfo(item)" class="ml-2">
                    (父项目: {{ getParentInfo(item) }})
                  </span>
                </p>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex items-center gap-1 flex-shrink-0">
              <IconButton
                icon="RotateCcw"
                variant="ghost"
                size="sm"
                @click="restoreItem(item.uuid)"
                class="text-success hover:bg-success/10 hover:text-success"
              />
              <IconButton
                icon="Trash2"
                variant="ghost"
                size="sm"
                @click="confirmPermanentDelete(item)"
                class="text-error hover:bg-error/10 hover:text-error"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 清空回收站确认对话框 -->
    <ConfirmDialog
      v-model:open="showClearDialog"
      title="清空回收站"
      description="您确定要清空回收站吗？这将永久删除所有项目，此操作无法撤销。"
      confirm-text="清空"
      cancel-text="取消"
      confirm-icon="Trash2"
      cancel-icon="X"
      variant="danger"
      @confirm="handleClearTrash"
      @cancel="showClearDialog = false"
    />

    <!-- 永久删除确认对话框 -->
    <ConfirmDialog
      v-model:open="showDeleteDialog"
      title="永久删除"
      :description="getDeleteConfirmMessage()"
      confirm-text="永久删除"
      cancel-text="取消"
      confirm-icon="Trash2"
      cancel-icon="X"
      variant="danger"
      @confirm="handlePermanentDelete"
      @cancel="showDeleteDialog = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Trash2, FileTextIcon, ListTodo, FileIcon } from 'lucide-vue-next'
import { useTrashStore, type TrashItem } from '../stores/trashStore'
import { useGlobalStore } from '../stores/globalStore'
import { useNodeStore } from '../stores/nodeStore'
import { showToast } from '../utils/toast'
import ConfirmDialog from '../components/ui/ConfirmDialog.vue'
import { IconButton, HeaderActions } from '../components/ui/v2'

import type { ActionItem } from '../components/ui/v2/header-actions/HeaderActions.vue'

const trashStore = useTrashStore()
const globalStore = useGlobalStore()
const nodeStore = useNodeStore()

// 对话框状态
const showClearDialog = ref(false)
const showDeleteDialog = ref(false)
const selectedItem = ref<TrashItem | null>(null)

// 头部操作配置
const headerMenuActions: ActionItem[] = [
  {
    id: 'clear-trash',
    label: '清空回收站',
    icon: Trash2,
    action: () => {
      showClearDialog.value = true
    }
  }
]

// 返回主页
const goToHome = () => {
  globalStore.isTrashOpen = false
}

// 处理头部操作
const handleHeaderAction = (action: ActionItem) => {
  if (action.action) {
    action.action()
  }
}

// 获取类型图标
const getTypeIcon = (type: string) => {
  switch (type) {
    case 'note':
      return FileTextIcon
    case 'todo':
      return ListTodo
    case 'pdf':
      return FileIcon
    default:
      return FileIcon
  }
}

// 格式化删除时间
const formatDeletedTime = (timestamp: number) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffDays === 0) {
    return '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } else if (diffDays === 1) {
    return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } else if (diffDays < 7) {
    return `${diffDays} 天前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}

// 获取子节点数量
const getChildrenCount = (uuid: string) => {
  return trashStore.getChildrenCountInTrash(uuid)
}

// 获取父组件信息
const getParentInfo = (item: TrashItem) => {
  if (!item.originalNode.parent) return null

  const parentUuid = item.originalNode.parent

  // 首先检查父节点是否也在回收站中
  const parentInTrash = trashStore.getTrashItem(parentUuid)
  if (parentInTrash) {
    return parentInTrash.originalNode.title
  }

  // 如果父节点不在回收站中，检查是否在内存中
  const parentInMemory = nodeStore.getNodeById(parentUuid)
  if (parentInMemory) {
    return parentInMemory.title
  }

  // 如果都找不到，显示已删除
  return '已删除'
}

// 获取父组件信息的样式类
const getParentInfoClass = (item: TrashItem) => {
  if (!item.originalNode.parent) return ''

  const parentUuid = item.originalNode.parent

  // 检查父节点状态并返回对应样式
  const parentInTrash = trashStore.getTrashItem(parentUuid)
  if (parentInTrash) {
    // 父节点也在回收站中 - 橙色警告
    return 'bg-warning/20 text-warning'
  }

  const parentInMemory = nodeStore.getNodeById(parentUuid)
  if (parentInMemory) {
    // 父节点存在且正常 - 蓝色信息
    return 'bg-info/20 text-info'
  }

  // 父节点已删除 - 红色错误
  return 'bg-error/20 text-error'
}

// 获取删除确认消息
const getDeleteConfirmMessage = () => {
  if (!selectedItem.value) return ''

  const childrenCount = getChildrenCount(selectedItem.value.uuid)
  const title = selectedItem.value.originalNode.title

  if (childrenCount > 0) {
    return `您确定要永久删除 '${title}' 及其 ${childrenCount} 个子项目吗？此操作无法撤销。`
  } else {
    return `您确定要永久删除 '${title}' 吗？此操作无法撤销。`
  }
}

// 恢复项目
const restoreItem = async (uuid: string) => {
  try {
    const childrenCount = getChildrenCount(uuid)
    const restoredItem = await trashStore.restoreItem(uuid)
    if (restoredItem) {
      const message =
        childrenCount > 0
          ? `已恢复 '${restoredItem.originalNode.title}' 及其 ${childrenCount} 个子项目`
          : `已恢复 '${restoredItem.originalNode.title}'`
      showToast('成功', message, 2000)
    }
  } catch (error) {
    console.error('恢复项目失败:', error)
    showToast('错误', '恢复项目失败', 3000)
  }
}

// 确认永久删除
const confirmPermanentDelete = (item: TrashItem) => {
  selectedItem.value = item
  showDeleteDialog.value = true
}

// 处理永久删除
const handlePermanentDelete = async () => {
  if (!selectedItem.value) return

  try {
    const childrenCount = getChildrenCount(selectedItem.value.uuid)
    const title = selectedItem.value.originalNode.title
    const success = await trashStore.permanentlyDelete(selectedItem.value.uuid)
    if (success) {
      const message =
        childrenCount > 0
          ? `已永久删除 '${title}' 及其 ${childrenCount} 个子项目`
          : `已永久删除 '${title}'`
      showToast('成功', message, 2000)
    }
  } catch (error) {
    console.error('永久删除失败:', error)
    showToast('错误', '永久删除失败', 3000)
  } finally {
    selectedItem.value = null
    showDeleteDialog.value = false
  }
}

// 处理清空回收站
const handleClearTrash = async () => {
  try {
    await trashStore.clearTrash()
    showToast('成功', '回收站已清空', 2000)
  } catch (error) {
    console.error('清空回收站失败:', error)
    showToast('错误', '清空回收站失败', 3000)
  } finally {
    showClearDialog.value = false
  }
}
</script>
