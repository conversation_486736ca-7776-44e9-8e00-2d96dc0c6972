/**
 * 图标配置文件
 * 自动生成于: 2025-07-13T07:52:44.743Z
 * 总计: 26 个分类, 858 个图标
 *
 * 优化建议：
 * - 此文件包含大量静态数据（7000+行）
 * - 考虑只保留核心分类配置和高频图标的增强配置
 * - 大部分图标元数据可以通过动态扫描生成
 * - 可以将静态配置拆分为多个文件或使用数据库存储
 */

export interface IconCategory {
  id: string
  labelZh: string
  labelEn: string
  iconCount: number
}

export interface IconEnhancement {
  id: string
  category: string
  filename: string
  displayName: string
  aliases: string[]
  tags: string[]
  // 新增字段，与IconMetadata保持一致
  weight?: number
  pinyin?: string
  pinyinShort?: string
  type?: 'local' | 'preset' | 'generated'
  autoGenerated?: boolean
  lastUpdated?: string
}

// 分类配置
export const iconCategories: IconCategory[] = [
  {
    id: 'Playground',
    labelZh: '游乐场',
    labelEn: 'Playground',
    iconCount: 50
  },
  {
    id: 'SweetCandy',
    labelZh: '糖果甜品',
    labelEn: 'Sweet & Candy',
    iconCount: 50
  },
  {
    id: 'alphhabetAndNumbers',
    labelZh: '字母数字',
    labelEn: 'Alphabet & Numbers',
    iconCount: 50
  },
  {
    id: 'bubbleTea',
    labelZh: '奶茶饮品',
    labelEn: 'Bubble Tea',
    iconCount: 30
  },
  {
    id: 'cinema',
    labelZh: '电影院',
    labelEn: 'Cinema',
    iconCount: 50
  },
  {
    id: 'city',
    labelZh: '城市建筑',
    labelEn: 'City',
    iconCount: 50
  },
  {
    id: 'commercial',
    labelZh: '商业金融',
    labelEn: 'Business',
    iconCount: 22
  },
  {
    id: 'emojis',
    labelZh: '表情符号',
    labelEn: 'Emojis',
    iconCount: 13
  },
  {
    id: 'food',
    labelZh: '食物',
    labelEn: 'Food',
    iconCount: 14
  },
  {
    id: 'fruits',
    labelZh: '水果',
    labelEn: 'Fruits',
    iconCount: 1
  },
  {
    id: 'furniture',
    labelZh: '家具',
    labelEn: 'Furniture',
    iconCount: 41
  },
  {
    id: 'graduation',
    labelZh: '毕业典礼',
    labelEn: 'Graduation',
    iconCount: 50
  },
  {
    id: 'halloween',
    labelZh: '万圣节',
    labelEn: 'Halloween',
    iconCount: 30
  },
  {
    id: 'herbs',
    labelZh: '香草香料',
    labelEn: 'Herbs',
    iconCount: 40
  },
  {
    id: 'history',
    labelZh: '历史文物',
    labelEn: 'History',
    iconCount: 50
  },
  {
    id: 'information',
    labelZh: '信息图表',
    labelEn: 'Information',
    iconCount: 5
  },
  {
    id: 'insect',
    labelZh: '昆虫',
    labelEn: 'Insects',
    iconCount: 30
  },
  {
    id: 'languageLearning',
    labelZh: '语言学习',
    labelEn: 'Language Learning',
    iconCount: 50
  },
  {
    id: 'magic',
    labelZh: '魔法奇幻',
    labelEn: 'Magic',
    iconCount: 50
  },
  {
    id: 'music',
    labelZh: '音乐',
    labelEn: 'Music',
    iconCount: 17
  },
  {
    id: 'nature',
    labelZh: '自然',
    labelEn: 'Nature',
    iconCount: 30
  },
  {
    id: 'ocean',
    labelZh: '海洋',
    labelEn: 'Ocean',
    iconCount: 50
  },
  {
    id: 'plants',
    labelZh: '植物',
    labelEn: 'Plants',
    iconCount: 18
  },
  {
    id: 'study',
    labelZh: '学习',
    labelEn: 'Study',
    iconCount: 14
  },
  {
    id: 'transportation',
    labelZh: '交通工具',
    labelEn: 'Transportation',
    iconCount: 32
  },
  {
    id: 'weather',
    labelZh: '天气',
    labelEn: 'Weather',
    iconCount: 21
  }
]

// 图标增强配置
export const iconEnhancements: IconEnhancement[] = [
  {
    id: 'Playground-001-ladder',
    category: 'Playground',
    filename: '001-ladder.svg',
    displayName: 'ladder',
    aliases: ['001-ladder', 'ladder', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-002-puzzle',
    category: 'Playground',
    filename: '002-puzzle.svg',
    displayName: 'puzzle',
    aliases: ['002-puzzle', 'puzzle', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-003-ladder',
    category: 'Playground',
    filename: '003-ladder.svg',
    displayName: 'ladder',
    aliases: ['003-ladder', 'ladder', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-004-panel',
    category: 'Playground',
    filename: '004-panel.svg',
    displayName: 'panel',
    aliases: ['004-panel', 'panel', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-005-pull up bar',
    category: 'Playground',
    filename: '005-pull up bar.svg',
    displayName: 'pull up bar',
    aliases: ['005-pull up bar', 'pull up bar', 'pullupbar', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-006-slide',
    category: 'Playground',
    filename: '006-slide.svg',
    displayName: 'slide',
    aliases: ['006-slide', 'slide', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-007-castle',
    category: 'Playground',
    filename: '007-castle.svg',
    displayName: 'castle',
    aliases: ['007-castle', 'castle', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-008-bridge',
    category: 'Playground',
    filename: '008-bridge.svg',
    displayName: 'bridge',
    aliases: ['008-bridge', 'bridge', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-009-seesaw',
    category: 'Playground',
    filename: '009-seesaw.svg',
    displayName: 'seesaw',
    aliases: ['009-seesaw', 'seesaw', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-010-tires',
    category: 'Playground',
    filename: '010-tires.svg',
    displayName: 'tires',
    aliases: ['010-tires', 'tires', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-011-panel',
    category: 'Playground',
    filename: '011-panel.svg',
    displayName: 'panel',
    aliases: ['011-panel', 'panel', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-012-pyramid',
    category: 'Playground',
    filename: '012-pyramid.svg',
    displayName: 'pyramid',
    aliases: ['012-pyramid', 'pyramid', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-013-sandbox',
    category: 'Playground',
    filename: '013-sandbox.svg',
    displayName: 'sandbox',
    aliases: ['013-sandbox', 'sandbox', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-014-ball pool',
    category: 'Playground',
    filename: '014-ball pool.svg',
    displayName: 'ball pool',
    aliases: ['014-ball pool', 'ball pool', 'ballpool', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-015-ball',
    category: 'Playground',
    filename: '015-ball.svg',
    displayName: 'ball',
    aliases: ['015-ball', 'ball', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-016-tunnel',
    category: 'Playground',
    filename: '016-tunnel.svg',
    displayName: 'tunnel',
    aliases: ['016-tunnel', 'tunnel', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-017-train',
    category: 'Playground',
    filename: '017-train.svg',
    displayName: 'train',
    aliases: [
      '017-train',
      'train',
      '游乐场',
      'playground',
      '火车',
      '列车',
      'railway',
      '铁路',
      'rail',
      '下雨',
      '雨天',
      'rain',
      'rainy',
      '降雨',
      'wet'
    ],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-018-park',
    category: 'Playground',
    filename: '018-park.svg',
    displayName: 'park',
    aliases: ['018-park', 'park', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-019-fence',
    category: 'Playground',
    filename: '019-fence.svg',
    displayName: 'fence',
    aliases: ['019-fence', 'fence', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-020-fountain',
    category: 'Playground',
    filename: '020-fountain.svg',
    displayName: 'fountain',
    aliases: ['020-fountain', 'fountain', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-021-rubbish',
    category: 'Playground',
    filename: '021-rubbish.svg',
    displayName: 'rubbish',
    aliases: ['021-rubbish', 'rubbish', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-022-trampolin',
    category: 'Playground',
    filename: '022-trampolin.svg',
    displayName: 'trampolin',
    aliases: ['022-trampolin', 'trampolin', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-023-kiosk',
    category: 'Playground',
    filename: '023-kiosk.svg',
    displayName: 'kiosk',
    aliases: ['023-kiosk', 'kiosk', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-024-kiosk',
    category: 'Playground',
    filename: '024-kiosk.svg',
    displayName: 'kiosk',
    aliases: ['024-kiosk', 'kiosk', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-025-basketball',
    category: 'Playground',
    filename: '025-basketball.svg',
    displayName: 'basketball',
    aliases: ['025-basketball', 'basketball', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-026-playground',
    category: 'Playground',
    filename: '026-playground.svg',
    displayName: 'playground',
    aliases: ['026-playground', 'playground', '游乐场'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-027-ping pong',
    category: 'Playground',
    filename: '027-ping pong.svg',
    displayName: 'ping pong',
    aliases: ['027-ping pong', 'ping pong', 'pingpong', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-028-net',
    category: 'Playground',
    filename: '028-net.svg',
    displayName: 'net',
    aliases: ['028-net', 'net', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-029-zip line',
    category: 'Playground',
    filename: '029-zip line.svg',
    displayName: 'zip line',
    aliases: ['029-zip line', 'zip line', 'zipline', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-030-inflatable',
    category: 'Playground',
    filename: '030-inflatable.svg',
    displayName: 'inflatable',
    aliases: ['030-inflatable', 'inflatable', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-031-playground',
    category: 'Playground',
    filename: '031-playground.svg',
    displayName: 'playground',
    aliases: ['031-playground', 'playground', '游乐场'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-032-climbing wall',
    category: 'Playground',
    filename: '032-climbing wall.svg',
    displayName: 'climbing wall',
    aliases: ['032-climbing wall', 'climbing wall', 'climbingwall', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-033-swing',
    category: 'Playground',
    filename: '033-swing.svg',
    displayName: 'swing',
    aliases: ['033-swing', 'swing', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-034-spinner',
    category: 'Playground',
    filename: '034-spinner.svg',
    displayName: 'spinner',
    aliases: ['034-spinner', 'spinner', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-035-kite',
    category: 'Playground',
    filename: '035-kite.svg',
    displayName: 'kite',
    aliases: ['035-kite', 'kite', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-036-skate park',
    category: 'Playground',
    filename: '036-skate park.svg',
    displayName: 'skate park',
    aliases: ['036-skate park', 'skate park', 'skatepark', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-037-carousel',
    category: 'Playground',
    filename: '037-carousel.svg',
    displayName: 'carousel',
    aliases: [
      '037-carousel',
      'carousel',
      '游乐场',
      'playground',
      '汽车',
      '车',
      '轿车',
      'car',
      'vehicle',
      '小车',
      'auto'
    ],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-038-swing',
    category: 'Playground',
    filename: '038-swing.svg',
    displayName: 'swing',
    aliases: ['038-swing', 'swing', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-039-rocket',
    category: 'Playground',
    filename: '039-rocket.svg',
    displayName: 'rocket',
    aliases: ['039-rocket', 'rocket', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-040-kiddie ride',
    category: 'Playground',
    filename: '040-kiddie ride.svg',
    displayName: 'kiddie ride',
    aliases: ['040-kiddie ride', 'kiddie ride', 'kiddieride', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-041-tent',
    category: 'Playground',
    filename: '041-tent.svg',
    displayName: 'tent',
    aliases: ['041-tent', 'tent', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-042-bars',
    category: 'Playground',
    filename: '042-bars.svg',
    displayName: 'bars',
    aliases: ['042-bars', 'bars', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-043-castle',
    category: 'Playground',
    filename: '043-castle.svg',
    displayName: 'castle',
    aliases: ['043-castle', 'castle', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-044-tyre',
    category: 'Playground',
    filename: '044-tyre.svg',
    displayName: 'tyre',
    aliases: ['044-tyre', 'tyre', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-045-kiddie ride',
    category: 'Playground',
    filename: '045-kiddie ride.svg',
    displayName: 'kiddie ride',
    aliases: ['045-kiddie ride', 'kiddie ride', 'kiddieride', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-046-rings',
    category: 'Playground',
    filename: '046-rings.svg',
    displayName: 'rings',
    aliases: ['046-rings', 'rings', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-047-park',
    category: 'Playground',
    filename: '047-park.svg',
    displayName: 'park',
    aliases: ['047-park', 'park', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-048-park',
    category: 'Playground',
    filename: '048-park.svg',
    displayName: 'park',
    aliases: ['048-park', 'park', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-049-swing',
    category: 'Playground',
    filename: '049-swing.svg',
    displayName: 'swing',
    aliases: ['049-swing', 'swing', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'Playground-050-seesaw',
    category: 'Playground',
    filename: '050-seesaw.svg',
    displayName: 'seesaw',
    aliases: ['050-seesaw', 'seesaw', '游乐场', 'playground'],
    tags: ['游乐场', 'playground']
  },
  {
    id: 'SweetCandy-001-cake',
    category: 'SweetCandy',
    filename: '001-cake.svg',
    displayName: 'cake',
    aliases: [
      '001-cake',
      'cake',
      '糖果甜品',
      'sweet & candy',
      '蛋糕',
      '糕点',
      'dessert',
      '甜品',
      'sweet'
    ],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-002-ice cream',
    category: 'SweetCandy',
    filename: '002-ice cream.svg',
    displayName: 'ice cream',
    aliases: ['002-ice cream', 'ice cream', 'icecream', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-003-ice cream',
    category: 'SweetCandy',
    filename: '003-ice cream.svg',
    displayName: 'ice cream',
    aliases: ['003-ice cream', 'ice cream', 'icecream', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-004-lollipop',
    category: 'SweetCandy',
    filename: '004-lollipop.svg',
    displayName: 'lollipop',
    aliases: ['004-lollipop', 'lollipop', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-005-donut',
    category: 'SweetCandy',
    filename: '005-donut.svg',
    displayName: 'donut',
    aliases: ['005-donut', 'donut', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-006-churros',
    category: 'SweetCandy',
    filename: '006-churros.svg',
    displayName: 'churros',
    aliases: ['006-churros', 'churros', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-007-jelly',
    category: 'SweetCandy',
    filename: '007-jelly.svg',
    displayName: 'jelly',
    aliases: ['007-jelly', 'jelly', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-008-cotton candy',
    category: 'SweetCandy',
    filename: '008-cotton candy.svg',
    displayName: 'cotton candy',
    aliases: ['008-cotton candy', 'cotton candy', 'cottoncandy', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-009-tiramisu',
    category: 'SweetCandy',
    filename: '009-tiramisu.svg',
    displayName: 'tiramisu',
    aliases: ['009-tiramisu', 'tiramisu', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-010-jelly',
    category: 'SweetCandy',
    filename: '010-jelly.svg',
    displayName: 'jelly',
    aliases: ['010-jelly', 'jelly', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-011-fondue',
    category: 'SweetCandy',
    filename: '011-fondue.svg',
    displayName: 'fondue',
    aliases: ['011-fondue', 'fondue', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-012-gummy bear',
    category: 'SweetCandy',
    filename: '012-gummy bear.svg',
    displayName: 'gummy bear',
    aliases: ['012-gummy bear', 'gummy bear', 'gummybear', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-013-cakes',
    category: 'SweetCandy',
    filename: '013-cakes.svg',
    displayName: 'cakes',
    aliases: [
      '013-cakes',
      'cakes',
      '糖果甜品',
      'sweet & candy',
      '蛋糕',
      '糕点',
      'cake',
      'dessert',
      '甜品',
      'sweet'
    ],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-014-cake',
    category: 'SweetCandy',
    filename: '014-cake.svg',
    displayName: 'cake',
    aliases: [
      '014-cake',
      'cake',
      '糖果甜品',
      'sweet & candy',
      '蛋糕',
      '糕点',
      'dessert',
      '甜品',
      'sweet'
    ],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-015-candy',
    category: 'SweetCandy',
    filename: '015-candy.svg',
    displayName: 'candy',
    aliases: ['015-candy', 'candy', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-016-cake',
    category: 'SweetCandy',
    filename: '016-cake.svg',
    displayName: 'cake',
    aliases: [
      '016-cake',
      'cake',
      '糖果甜品',
      'sweet & candy',
      '蛋糕',
      '糕点',
      'dessert',
      '甜品',
      'sweet'
    ],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-017-candies',
    category: 'SweetCandy',
    filename: '017-candies.svg',
    displayName: 'candies',
    aliases: ['017-candies', 'candies', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-019-chocolate',
    category: 'SweetCandy',
    filename: '019-chocolate.svg',
    displayName: 'chocolate',
    aliases: ['019-chocolate', 'chocolate', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-020-pancake',
    category: 'SweetCandy',
    filename: '020-pancake.svg',
    displayName: 'pancake',
    aliases: [
      '020-pancake',
      'pancake',
      '糖果甜品',
      'sweet & candy',
      '蛋糕',
      '糕点',
      'cake',
      'dessert',
      '甜品',
      'sweet'
    ],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-021-candy',
    category: 'SweetCandy',
    filename: '021-candy.svg',
    displayName: 'candy',
    aliases: ['021-candy', 'candy', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-022-wafer',
    category: 'SweetCandy',
    filename: '022-wafer.svg',
    displayName: 'wafer',
    aliases: ['022-wafer', 'wafer', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-023-hot chocolate',
    category: 'SweetCandy',
    filename: '023-hot chocolate.svg',
    displayName: 'hot chocolate',
    aliases: ['023-hot chocolate', 'hot chocolate', 'hotchocolate', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-024-chocolate egg',
    category: 'SweetCandy',
    filename: '024-chocolate egg.svg',
    displayName: 'chocolate egg',
    aliases: ['024-chocolate egg', 'chocolate egg', 'chocolateegg', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-025-cake',
    category: 'SweetCandy',
    filename: '025-cake.svg',
    displayName: 'cake',
    aliases: [
      '025-cake',
      'cake',
      '糖果甜品',
      'sweet & candy',
      '蛋糕',
      '糕点',
      'dessert',
      '甜品',
      'sweet'
    ],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-026-cupcake',
    category: 'SweetCandy',
    filename: '026-cupcake.svg',
    displayName: 'cupcake',
    aliases: [
      '026-cupcake',
      'cupcake',
      '糖果甜品',
      'sweet & candy',
      '蛋糕',
      '糕点',
      'cake',
      'dessert',
      '甜品',
      'sweet'
    ],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-027-muffin',
    category: 'SweetCandy',
    filename: '027-muffin.svg',
    displayName: 'muffin',
    aliases: ['027-muffin', 'muffin', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-028-gums',
    category: 'SweetCandy',
    filename: '028-gums.svg',
    displayName: 'gums',
    aliases: ['028-gums', 'gums', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-029-pudding',
    category: 'SweetCandy',
    filename: '029-pudding.svg',
    displayName: 'pudding',
    aliases: ['029-pudding', 'pudding', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-030-biscuit',
    category: 'SweetCandy',
    filename: '030-biscuit.svg',
    displayName: 'biscuit',
    aliases: ['030-biscuit', 'biscuit', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-031-cookie',
    category: 'SweetCandy',
    filename: '031-cookie.svg',
    displayName: 'cookie',
    aliases: ['031-cookie', 'cookie', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-032-lollipop',
    category: 'SweetCandy',
    filename: '032-lollipop.svg',
    displayName: 'lollipop',
    aliases: ['032-lollipop', 'lollipop', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-033-wafer',
    category: 'SweetCandy',
    filename: '033-wafer.svg',
    displayName: 'wafer',
    aliases: ['033-wafer', 'wafer', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-034-lollipop',
    category: 'SweetCandy',
    filename: '034-lollipop.svg',
    displayName: 'lollipop',
    aliases: ['034-lollipop', 'lollipop', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-035-donut',
    category: 'SweetCandy',
    filename: '035-donut.svg',
    displayName: 'donut',
    aliases: ['035-donut', 'donut', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-036-popsicle',
    category: 'SweetCandy',
    filename: '036-popsicle.svg',
    displayName: 'popsicle',
    aliases: ['036-popsicle', 'popsicle', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-037-honey',
    category: 'SweetCandy',
    filename: '037-honey.svg',
    displayName: 'honey',
    aliases: ['037-honey', 'honey', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-038-candy',
    category: 'SweetCandy',
    filename: '038-candy.svg',
    displayName: 'candy',
    aliases: ['038-candy', 'candy', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-039-cookie',
    category: 'SweetCandy',
    filename: '039-cookie.svg',
    displayName: 'cookie',
    aliases: ['039-cookie', 'cookie', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-040-ice cream',
    category: 'SweetCandy',
    filename: '040-ice cream.svg',
    displayName: 'ice cream',
    aliases: ['040-ice cream', 'ice cream', 'icecream', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-041-jam',
    category: 'SweetCandy',
    filename: '041-jam.svg',
    displayName: 'jam',
    aliases: ['041-jam', 'jam', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-042-cookie',
    category: 'SweetCandy',
    filename: '042-cookie.svg',
    displayName: 'cookie',
    aliases: ['042-cookie', 'cookie', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-043-pie',
    category: 'SweetCandy',
    filename: '043-pie.svg',
    displayName: 'pie',
    aliases: ['043-pie', 'pie', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-044-marshmallows',
    category: 'SweetCandy',
    filename: '044-marshmallows.svg',
    displayName: 'marshmallows',
    aliases: ['044-marshmallows', 'marshmallows', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-045-candy',
    category: 'SweetCandy',
    filename: '045-candy.svg',
    displayName: 'candy',
    aliases: ['045-candy', 'candy', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-046-choco balls',
    category: 'SweetCandy',
    filename: '046-choco balls.svg',
    displayName: 'choco balls',
    aliases: ['046-choco balls', 'choco balls', 'chocoballs', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-047-lollipop',
    category: 'SweetCandy',
    filename: '047-lollipop.svg',
    displayName: 'lollipop',
    aliases: ['047-lollipop', 'lollipop', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-048-macaron',
    category: 'SweetCandy',
    filename: '048-macaron.svg',
    displayName: 'macaron',
    aliases: [
      '048-macaron',
      'macaron',
      '糖果甜品',
      'sweet & candy',
      '汽车',
      '车',
      '轿车',
      'car',
      'vehicle',
      '小车',
      'auto'
    ],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-049-eclair',
    category: 'SweetCandy',
    filename: '049-eclair.svg',
    displayName: 'eclair',
    aliases: ['049-eclair', 'eclair', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'SweetCandy-050-croissant',
    category: 'SweetCandy',
    filename: '050-croissant.svg',
    displayName: 'croissant',
    aliases: ['050-croissant', 'croissant', '糖果甜品', 'sweet & candy'],
    tags: ['糖果甜品', 'sweet & candy']
  },
  {
    id: 'alphhabetAndNumbers-001-A',
    category: 'alphhabetAndNumbers',
    filename: '001-A.svg',
    displayName: 'A',
    aliases: ['001-A', 'A', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-002-b',
    category: 'alphhabetAndNumbers',
    filename: '002-b.svg',
    displayName: 'b',
    aliases: ['002-b', 'b', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-003-c',
    category: 'alphhabetAndNumbers',
    filename: '003-c.svg',
    displayName: 'c',
    aliases: ['003-c', 'c', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-004-c',
    category: 'alphhabetAndNumbers',
    filename: '004-c.svg',
    displayName: 'c',
    aliases: ['004-c', 'c', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-005-d',
    category: 'alphhabetAndNumbers',
    filename: '005-d.svg',
    displayName: 'd',
    aliases: ['005-d', 'd', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-006-e',
    category: 'alphhabetAndNumbers',
    filename: '006-e.svg',
    displayName: 'e',
    aliases: ['006-e', 'e', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-007-f',
    category: 'alphhabetAndNumbers',
    filename: '007-f.svg',
    displayName: 'f',
    aliases: ['007-f', 'f', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-008-g',
    category: 'alphhabetAndNumbers',
    filename: '008-g.svg',
    displayName: 'g',
    aliases: ['008-g', 'g', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-009-h',
    category: 'alphhabetAndNumbers',
    filename: '009-h.svg',
    displayName: 'h',
    aliases: ['009-h', 'h', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-010-I',
    category: 'alphhabetAndNumbers',
    filename: '010-I.svg',
    displayName: 'I',
    aliases: ['010-I', 'I', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-011-j',
    category: 'alphhabetAndNumbers',
    filename: '011-j.svg',
    displayName: 'j',
    aliases: ['011-j', 'j', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-012-k',
    category: 'alphhabetAndNumbers',
    filename: '012-k.svg',
    displayName: 'k',
    aliases: ['012-k', 'k', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-013-l',
    category: 'alphhabetAndNumbers',
    filename: '013-l.svg',
    displayName: 'l',
    aliases: ['013-l', 'l', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-014-m',
    category: 'alphhabetAndNumbers',
    filename: '014-m.svg',
    displayName: 'm',
    aliases: ['014-m', 'm', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-015-n',
    category: 'alphhabetAndNumbers',
    filename: '015-n.svg',
    displayName: 'n',
    aliases: ['015-n', 'n', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-016-n',
    category: 'alphhabetAndNumbers',
    filename: '016-n.svg',
    displayName: 'n',
    aliases: ['016-n', 'n', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-017-o',
    category: 'alphhabetAndNumbers',
    filename: '017-o.svg',
    displayName: 'o',
    aliases: ['017-o', 'o', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-018-p',
    category: 'alphhabetAndNumbers',
    filename: '018-p.svg',
    displayName: 'p',
    aliases: ['018-p', 'p', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-019-q',
    category: 'alphhabetAndNumbers',
    filename: '019-q.svg',
    displayName: 'q',
    aliases: ['019-q', 'q', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-020-r',
    category: 'alphhabetAndNumbers',
    filename: '020-r.svg',
    displayName: 'r',
    aliases: ['020-r', 'r', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-021-s',
    category: 'alphhabetAndNumbers',
    filename: '021-s.svg',
    displayName: 's',
    aliases: ['021-s', 's', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-022-t',
    category: 'alphhabetAndNumbers',
    filename: '022-t.svg',
    displayName: 't',
    aliases: ['022-t', 't', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-023-u',
    category: 'alphhabetAndNumbers',
    filename: '023-u.svg',
    displayName: 'u',
    aliases: ['023-u', 'u', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-024-v',
    category: 'alphhabetAndNumbers',
    filename: '024-v.svg',
    displayName: 'v',
    aliases: ['024-v', 'v', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-025-w',
    category: 'alphhabetAndNumbers',
    filename: '025-w.svg',
    displayName: 'w',
    aliases: ['025-w', 'w', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-026-x',
    category: 'alphhabetAndNumbers',
    filename: '026-x.svg',
    displayName: 'x',
    aliases: ['026-x', 'x', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-027-y',
    category: 'alphhabetAndNumbers',
    filename: '027-y.svg',
    displayName: 'y',
    aliases: ['027-y', 'y', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-028-z',
    category: 'alphhabetAndNumbers',
    filename: '028-z.svg',
    displayName: 'z',
    aliases: ['028-z', 'z', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-029-ch',
    category: 'alphhabetAndNumbers',
    filename: '029-ch.svg',
    displayName: 'ch',
    aliases: ['029-ch', 'ch', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-030-A',
    category: 'alphhabetAndNumbers',
    filename: '030-A.svg',
    displayName: 'A',
    aliases: ['030-A', 'A', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-031-e',
    category: 'alphhabetAndNumbers',
    filename: '031-e.svg',
    displayName: 'e',
    aliases: ['031-e', 'e', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-032-I',
    category: 'alphhabetAndNumbers',
    filename: '032-I.svg',
    displayName: 'I',
    aliases: ['032-I', 'I', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-033-o',
    category: 'alphhabetAndNumbers',
    filename: '033-o.svg',
    displayName: 'o',
    aliases: ['033-o', 'o', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-034-u',
    category: 'alphhabetAndNumbers',
    filename: '034-u.svg',
    displayName: 'u',
    aliases: ['034-u', 'u', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-035-1',
    category: 'alphhabetAndNumbers',
    filename: '035-1.svg',
    displayName: '1',
    aliases: ['035-1', '1', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-036-2',
    category: 'alphhabetAndNumbers',
    filename: '036-2.svg',
    displayName: '2',
    aliases: ['036-2', '2', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-037-3',
    category: 'alphhabetAndNumbers',
    filename: '037-3.svg',
    displayName: '3',
    aliases: ['037-3', '3', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-038-4',
    category: 'alphhabetAndNumbers',
    filename: '038-4.svg',
    displayName: '4',
    aliases: ['038-4', '4', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-039-5',
    category: 'alphhabetAndNumbers',
    filename: '039-5.svg',
    displayName: '5',
    aliases: ['039-5', '5', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-040-6',
    category: 'alphhabetAndNumbers',
    filename: '040-6.svg',
    displayName: '6',
    aliases: ['040-6', '6', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-041-7',
    category: 'alphhabetAndNumbers',
    filename: '041-7.svg',
    displayName: '7',
    aliases: ['041-7', '7', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-042-8',
    category: 'alphhabetAndNumbers',
    filename: '042-8.svg',
    displayName: '8',
    aliases: ['042-8', '8', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-043-9',
    category: 'alphhabetAndNumbers',
    filename: '043-9.svg',
    displayName: '9',
    aliases: ['043-9', '9', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-044-0',
    category: 'alphhabetAndNumbers',
    filename: '044-0.svg',
    displayName: '0',
    aliases: ['044-0', '0', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-045-10',
    category: 'alphhabetAndNumbers',
    filename: '045-10.svg',
    displayName: '10',
    aliases: ['045-10', '10', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-046-infinity',
    category: 'alphhabetAndNumbers',
    filename: '046-infinity.svg',
    displayName: 'infinity',
    aliases: ['046-infinity', 'infinity', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-047-hashtag',
    category: 'alphhabetAndNumbers',
    filename: '047-hashtag.svg',
    displayName: 'hashtag',
    aliases: ['047-hashtag', 'hashtag', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-048-asterisk',
    category: 'alphhabetAndNumbers',
    filename: '048-asterisk.svg',
    displayName: 'asterisk',
    aliases: ['048-asterisk', 'asterisk', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-049-exclamation mark',
    category: 'alphhabetAndNumbers',
    filename: '049-exclamation mark.svg',
    displayName: 'exclamation mark',
    aliases: [
      '049-exclamation mark',
      'exclamation mark',
      'exclamationmark',
      '字母数字',
      'alphabet & numbers'
    ],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'alphhabetAndNumbers-050-alphabet',
    category: 'alphhabetAndNumbers',
    filename: '050-alphabet.svg',
    displayName: 'alphabet',
    aliases: ['050-alphabet', 'alphabet', '字母数字', 'alphabet & numbers'],
    tags: ['字母数字', 'alphabet & numbers']
  },
  {
    id: 'bubbleTea-001-boba',
    category: 'bubbleTea',
    filename: '001-boba.svg',
    displayName: 'boba',
    aliases: ['001-boba', 'boba', '奶茶饮品', 'bubble tea'],
    tags: ['奶茶饮品', 'bubble tea']
  },
  {
    id: 'bubbleTea-002-bubble tea',
    category: 'bubbleTea',
    filename: '002-bubble tea.svg',
    displayName: 'bubble tea',
    aliases: ['002-bubble tea', 'bubble tea', 'bubbletea', '奶茶饮品'],
    tags: ['奶茶饮品', 'bubble tea']
  },
  {
    id: 'bubbleTea-003-tea',
    category: 'bubbleTea',
    filename: '003-tea.svg',
    displayName: 'tea',
    aliases: ['003-tea', 'tea', '奶茶饮品', 'bubble tea'],
    tags: ['奶茶饮品', 'bubble tea']
  },
  {
    id: 'bubbleTea-004-red tea',
    category: 'bubbleTea',
    filename: '004-red tea.svg',
    displayName: 'red tea',
    aliases: ['004-red tea', 'red tea', 'redtea', '奶茶饮品', 'bubble tea'],
    tags: ['奶茶饮品', 'bubble tea']
  },
  {
    id: 'bubbleTea-005-lemon tea',
    category: 'bubbleTea',
    filename: '005-lemon tea.svg',
    displayName: 'lemon tea',
    aliases: ['005-lemon tea', 'lemon tea', 'lemontea', '奶茶饮品', 'bubble tea'],
    tags: ['奶茶饮品', 'bubble tea']
  },
  {
    id: 'bubbleTea-006-cassava root',
    category: 'bubbleTea',
    filename: '006-cassava root.svg',
    displayName: 'cassava root',
    aliases: ['006-cassava root', 'cassava root', 'cassavaroot', '奶茶饮品', 'bubble tea'],
    tags: ['奶茶饮品', 'bubble tea']
  },
  {
    id: 'bubbleTea-007-bubble tea',
    category: 'bubbleTea',
    filename: '007-bubble tea.svg',
    displayName: 'bubble tea',
    aliases: ['007-bubble tea', 'bubble tea', 'bubbletea', '奶茶饮品'],
    tags: ['奶茶饮品', 'bubble tea']
  },
  {
    id: 'bubbleTea-008-mik tea',
    category: 'bubbleTea',
    filename: '008-mik tea.svg',
    displayName: 'mik tea',
    aliases: ['008-mik tea', 'mik tea', 'miktea', '奶茶饮品', 'bubble tea'],
    tags: ['奶茶饮品', 'bubble tea']
  },
  {
    id: 'bubbleTea-009-black tea',
    category: 'bubbleTea',
    filename: '009-black tea.svg',
    displayName: 'black tea',
    aliases: ['009-black tea', 'black tea', 'blacktea', '奶茶饮品', 'bubble tea'],
    tags: ['奶茶饮品', 'bubble tea']
  },
  {
    id: 'bubbleTea-010-shop',
    category: 'bubbleTea',
    filename: '010-shop.svg',
    displayName: 'shop',
    aliases: ['010-shop', 'shop', '奶茶饮品', 'bubble tea'],
    tags: ['奶茶饮品', 'bubble tea']
  },
  {
    id: 'bubbleTea-012-bubble tea',
    category: 'bubbleTea',
    filename: '012-bubble tea.svg',
    displayName: 'bubble tea',
    aliases: ['012-bubble tea', 'bubble tea', 'bubbletea', '奶茶饮品'],
    tags: ['奶茶饮品', 'bubble tea']
  },
  {
    id: 'bubbleTea-013-green tea',
    category: 'bubbleTea',
    filename: '013-green tea.svg',
    displayName: 'green tea',
    aliases: ['013-green tea', 'green tea', 'greentea', '奶茶饮品', 'bubble tea'],
    tags: ['奶茶饮品', 'bubble tea']
  },
  {
    id: 'bubbleTea-014-herbal tea',
    category: 'bubbleTea',
    filename: '014-herbal tea.svg',
    displayName: 'herbal tea',
    aliases: ['014-herbal tea', 'herbal tea', 'herbaltea', '奶茶饮品', 'bubble tea'],
    tags: ['奶茶饮品', 'bubble tea']
  },
  {
    id: 'bubbleTea-015-bubble tea',
    category: 'bubbleTea',
    filename: '015-bubble tea.svg',
    displayName: 'bubble tea',
    aliases: ['015-bubble tea', 'bubble tea', 'bubbletea', '奶茶饮品'],
    tags: ['奶茶饮品', 'bubble tea']
  },
  {
    id: 'bubbleTea-016-milk tea',
    category: 'bubbleTea',
    filename: '016-milk tea.svg',
    displayName: 'milk tea',
    aliases: ['016-milk tea', 'milk tea', 'milktea', '奶茶饮品', 'bubble tea'],
    tags: ['奶茶饮品', 'bubble tea']
  },
  {
    id: 'bubbleTea-017-bubble tea',
    category: 'bubbleTea',
    filename: '017-bubble tea.svg',
    displayName: 'bubble tea',
    aliases: ['017-bubble tea', 'bubble tea', 'bubbletea', '奶茶饮品'],
    tags: ['奶茶饮品', 'bubble tea']
  },
  {
    id: 'bubbleTea-018-bubble tea',
    category: 'bubbleTea',
    filename: '018-bubble tea.svg',
    displayName: 'bubble tea',
    aliases: ['018-bubble tea', 'bubble tea', 'bubbletea', '奶茶饮品'],
    tags: ['奶茶饮品', 'bubble tea']
  },
  {
    id: 'bubbleTea-019-bubble tea',
    category: 'bubbleTea',
    filename: '019-bubble tea.svg',
    displayName: 'bubble tea',
    aliases: ['019-bubble tea', 'bubble tea', 'bubbletea', '奶茶饮品'],
    tags: ['奶茶饮品', 'bubble tea']
  },
  {
    id: 'bubbleTea-020-bubble tea',
    category: 'bubbleTea',
    filename: '020-bubble tea.svg',
    displayName: 'bubble tea',
    aliases: ['020-bubble tea', 'bubble tea', 'bubbletea', '奶茶饮品'],
    tags: ['奶茶饮品', 'bubble tea']
  },
  {
    id: 'bubbleTea-021-bubble tea',
    category: 'bubbleTea',
    filename: '021-bubble tea.svg',
    displayName: 'bubble tea',
    aliases: ['021-bubble tea', 'bubble tea', 'bubbletea', '奶茶饮品'],
    tags: ['奶茶饮品', 'bubble tea']
  },
  {
    id: 'bubbleTea-022-bubble tea',
    category: 'bubbleTea',
    filename: '022-bubble tea.svg',
    displayName: 'bubble tea',
    aliases: ['022-bubble tea', 'bubble tea', 'bubbletea', '奶茶饮品'],
    tags: ['奶茶饮品', 'bubble tea']
  },
  {
    id: 'bubbleTea-023-bubble tea',
    category: 'bubbleTea',
    filename: '023-bubble tea.svg',
    displayName: 'bubble tea',
    aliases: ['023-bubble tea', 'bubble tea', 'bubbletea', '奶茶饮品'],
    tags: ['奶茶饮品', 'bubble tea']
  },
  {
    id: 'bubbleTea-024-bubble tea',
    category: 'bubbleTea',
    filename: '024-bubble tea.svg',
    displayName: 'bubble tea',
    aliases: ['024-bubble tea', 'bubble tea', 'bubbletea', '奶茶饮品'],
    tags: ['奶茶饮品', 'bubble tea']
  },
  {
    id: 'bubbleTea-025-bubble tea',
    category: 'bubbleTea',
    filename: '025-bubble tea.svg',
    displayName: 'bubble tea',
    aliases: ['025-bubble tea', 'bubble tea', 'bubbletea', '奶茶饮品'],
    tags: ['奶茶饮品', 'bubble tea']
  },
  {
    id: 'bubbleTea-026-bubble tea',
    category: 'bubbleTea',
    filename: '026-bubble tea.svg',
    displayName: 'bubble tea',
    aliases: ['026-bubble tea', 'bubble tea', 'bubbletea', '奶茶饮品'],
    tags: ['奶茶饮品', 'bubble tea']
  },
  {
    id: 'bubbleTea-027-bubble tea',
    category: 'bubbleTea',
    filename: '027-bubble tea.svg',
    displayName: 'bubble tea',
    aliases: ['027-bubble tea', 'bubble tea', 'bubbletea', '奶茶饮品'],
    tags: ['奶茶饮品', 'bubble tea']
  },
  {
    id: 'bubbleTea-028-bubble tea',
    category: 'bubbleTea',
    filename: '028-bubble tea.svg',
    displayName: 'bubble tea',
    aliases: ['028-bubble tea', 'bubble tea', 'bubbletea', '奶茶饮品'],
    tags: ['奶茶饮品', 'bubble tea']
  },
  {
    id: 'bubbleTea-029-bubble tea',
    category: 'bubbleTea',
    filename: '029-bubble tea.svg',
    displayName: 'bubble tea',
    aliases: ['029-bubble tea', 'bubble tea', 'bubbletea', '奶茶饮品'],
    tags: ['奶茶饮品', 'bubble tea']
  },
  {
    id: 'bubbleTea-030-bubble tea',
    category: 'bubbleTea',
    filename: '030-bubble tea.svg',
    displayName: 'bubble tea',
    aliases: ['030-bubble tea', 'bubble tea', 'bubbletea', '奶茶饮品'],
    tags: ['奶茶饮品', 'bubble tea']
  },
  {
    id: 'cinema-001-popcorn',
    category: 'cinema',
    filename: '001-popcorn.svg',
    displayName: 'popcorn',
    aliases: ['001-popcorn', 'popcorn', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-002-film clapperboard',
    category: 'cinema',
    filename: '002-film clapperboard.svg',
    displayName: 'film clapperboard',
    aliases: ['002-film clapperboard', 'film clapperboard', 'filmclapperboard', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-003-3d glasses',
    category: 'cinema',
    filename: '003-3d glasses.svg',
    displayName: '3d glasses',
    aliases: ['003-3d glasses', '3d glasses', '3dglasses', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-004-film reel',
    category: 'cinema',
    filename: '004-film reel.svg',
    displayName: 'film reel',
    aliases: ['004-film reel', 'film reel', 'filmreel', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-005-no camera',
    category: 'cinema',
    filename: '005-no camera.svg',
    displayName: 'no camera',
    aliases: ['005-no camera', 'no camera', 'nocamera', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-006-Premiere',
    category: 'cinema',
    filename: '006-Premiere.svg',
    displayName: 'Premiere',
    aliases: ['006-Premiere', 'Premiere', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-007-countdown',
    category: 'cinema',
    filename: '007-countdown.svg',
    displayName: 'countdown',
    aliases: ['007-countdown', 'countdown', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-008-exit door',
    category: 'cinema',
    filename: '008-exit door.svg',
    displayName: 'exit door',
    aliases: ['008-exit door', 'exit door', 'exitdoor', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-009-projector',
    category: 'cinema',
    filename: '009-projector.svg',
    displayName: 'projector',
    aliases: ['009-projector', 'projector', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-010-special effects',
    category: 'cinema',
    filename: '010-special effects.svg',
    displayName: 'special effects',
    aliases: ['010-special effects', 'special effects', 'specialeffects', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-011-director chair',
    category: 'cinema',
    filename: '011-director chair.svg',
    displayName: 'director chair',
    aliases: ['011-director chair', 'director chair', 'directorchair', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-012-megaphone',
    category: 'cinema',
    filename: '012-megaphone.svg',
    displayName: 'megaphone',
    aliases: ['012-megaphone', 'megaphone', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-013-tv',
    category: 'cinema',
    filename: '013-tv.svg',
    displayName: 'tv',
    aliases: ['013-tv', 'tv', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-014-theater masks',
    category: 'cinema',
    filename: '014-theater masks.svg',
    displayName: 'theater masks',
    aliases: ['014-theater masks', 'theater masks', 'theatermasks', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-015-movies',
    category: 'cinema',
    filename: '015-movies.svg',
    displayName: 'movies',
    aliases: ['015-movies', 'movies', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-016-sci fi',
    category: 'cinema',
    filename: '016-sci fi.svg',
    displayName: 'sci fi',
    aliases: ['016-sci fi', 'sci fi', 'scifi', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-017-vr',
    category: 'cinema',
    filename: '017-vr.svg',
    displayName: 'vr',
    aliases: ['017-vr', 'vr', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-018-detective',
    category: 'cinema',
    filename: '018-detective.svg',
    displayName: 'detective',
    aliases: ['018-detective', 'detective', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-019-witch hat',
    category: 'cinema',
    filename: '019-witch hat.svg',
    displayName: 'witch hat',
    aliases: ['019-witch hat', 'witch hat', 'witchhat', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-020-no smoking',
    category: 'cinema',
    filename: '020-no smoking.svg',
    displayName: 'no smoking',
    aliases: ['020-no smoking', 'no smoking', 'nosmoking', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-021-spotlight',
    category: 'cinema',
    filename: '021-spotlight.svg',
    displayName: 'spotlight',
    aliases: ['021-spotlight', 'spotlight', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-022-sword',
    category: 'cinema',
    filename: '022-sword.svg',
    displayName: 'sword',
    aliases: ['022-sword', 'sword', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-023-ID',
    category: 'cinema',
    filename: '023-ID.svg',
    displayName: 'ID',
    aliases: ['023-ID', 'ID', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-024-smoking pipe',
    category: 'cinema',
    filename: '024-smoking pipe.svg',
    displayName: 'smoking pipe',
    aliases: ['024-smoking pipe', 'smoking pipe', 'smokingpipe', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-025-cowboy hat',
    category: 'cinema',
    filename: '025-cowboy hat.svg',
    displayName: 'cowboy hat',
    aliases: ['025-cowboy hat', 'cowboy hat', 'cowboyhat', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-026-crime',
    category: 'cinema',
    filename: '026-crime.svg',
    displayName: 'crime',
    aliases: ['026-crime', 'crime', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-027-award',
    category: 'cinema',
    filename: '027-award.svg',
    displayName: 'award',
    aliases: ['027-award', 'award', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-028-film editing',
    category: 'cinema',
    filename: '028-film editing.svg',
    displayName: 'film editing',
    aliases: ['028-film editing', 'film editing', 'filmediting', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-029-cinema',
    category: 'cinema',
    filename: '029-cinema.svg',
    displayName: 'cinema',
    aliases: ['029-cinema', 'cinema', '电影院'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-030-Soft drink',
    category: 'cinema',
    filename: '030-Soft drink.svg',
    displayName: 'Soft drink',
    aliases: ['030-Soft drink', 'Soft drink', 'Softdrink', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-031-ticket',
    category: 'cinema',
    filename: '031-ticket.svg',
    displayName: 'ticket',
    aliases: ['031-ticket', 'ticket', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-032-aperture',
    category: 'cinema',
    filename: '032-aperture.svg',
    displayName: 'aperture',
    aliases: ['032-aperture', 'aperture', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-033-video camera',
    category: 'cinema',
    filename: '033-video camera.svg',
    displayName: 'video camera',
    aliases: ['033-video camera', 'video camera', 'videocamera', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-034-rec',
    category: 'cinema',
    filename: '034-rec.svg',
    displayName: 'rec',
    aliases: ['034-rec', 'rec', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-035-cinema',
    category: 'cinema',
    filename: '035-cinema.svg',
    displayName: 'cinema',
    aliases: ['035-cinema', 'cinema', '电影院'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-036-dracula',
    category: 'cinema',
    filename: '036-dracula.svg',
    displayName: 'dracula',
    aliases: ['036-dracula', 'dracula', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-037-actor',
    category: 'cinema',
    filename: '037-actor.svg',
    displayName: 'actor',
    aliases: ['037-actor', 'actor', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-038-soldier',
    category: 'cinema',
    filename: '038-soldier.svg',
    displayName: 'soldier',
    aliases: ['038-soldier', 'soldier', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-039-actress',
    category: 'cinema',
    filename: '039-actress.svg',
    displayName: 'actress',
    aliases: ['039-actress', 'actress', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-040-film director',
    category: 'cinema',
    filename: '040-film director.svg',
    displayName: 'film director',
    aliases: ['040-film director', 'film director', 'filmdirector', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-041-no phone',
    category: 'cinema',
    filename: '041-no phone.svg',
    displayName: 'no phone',
    aliases: ['041-no phone', 'no phone', 'nophone', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-042-gun',
    category: 'cinema',
    filename: '042-gun.svg',
    displayName: 'gun',
    aliases: ['042-gun', 'gun', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-043-ticket office',
    category: 'cinema',
    filename: '043-ticket office.svg',
    displayName: 'ticket office',
    aliases: ['043-ticket office', 'ticket office', 'ticketoffice', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-044-knight',
    category: 'cinema',
    filename: '044-knight.svg',
    displayName: 'knight',
    aliases: ['044-knight', 'knight', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-045-Booking',
    category: 'cinema',
    filename: '045-Booking.svg',
    displayName: 'Booking',
    aliases: ['045-Booking', 'Booking', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-046-script',
    category: 'cinema',
    filename: '046-script.svg',
    displayName: 'script',
    aliases: ['046-script', 'script', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-047-film',
    category: 'cinema',
    filename: '047-film.svg',
    displayName: 'film',
    aliases: ['047-film', 'film', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-048-storyboard',
    category: 'cinema',
    filename: '048-storyboard.svg',
    displayName: 'storyboard',
    aliases: ['048-storyboard', 'storyboard', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-049-film',
    category: 'cinema',
    filename: '049-film.svg',
    displayName: 'film',
    aliases: ['049-film', 'film', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'cinema-050-review',
    category: 'cinema',
    filename: '050-review.svg',
    displayName: 'review',
    aliases: ['050-review', 'review', '电影院', 'cinema'],
    tags: ['电影院', 'cinema']
  },
  {
    id: 'city-001-traffic lights',
    category: 'city',
    filename: '001-traffic lights.svg',
    displayName: 'traffic lights',
    aliases: ['001-traffic lights', 'traffic lights', 'trafficlights', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-002-fountain',
    category: 'city',
    filename: '002-fountain.svg',
    displayName: 'fountain',
    aliases: ['002-fountain', 'fountain', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-003-gas station',
    category: 'city',
    filename: '003-gas station.svg',
    displayName: 'gas station',
    aliases: ['003-gas station', 'gas station', 'gasstation', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-004-street light',
    category: 'city',
    filename: '004-street light.svg',
    displayName: 'street light',
    aliases: ['004-street light', 'street light', 'streetlight', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-005-vending machine',
    category: 'city',
    filename: '005-vending machine.svg',
    displayName: 'vending machine',
    aliases: ['005-vending machine', 'vending machine', 'vendingmachine', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-006-crane',
    category: 'city',
    filename: '006-crane.svg',
    displayName: 'crane',
    aliases: ['006-crane', 'crane', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-007-lighthouse',
    category: 'city',
    filename: '007-lighthouse.svg',
    displayName: 'lighthouse',
    aliases: ['007-lighthouse', 'lighthouse', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-008-ferris wheel',
    category: 'city',
    filename: '008-ferris wheel.svg',
    displayName: 'ferris wheel',
    aliases: ['008-ferris wheel', 'ferris wheel', 'ferriswheel', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-009-parking meter',
    category: 'city',
    filename: '009-parking meter.svg',
    displayName: 'parking meter',
    aliases: ['009-parking meter', 'parking meter', 'parkingmeter', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-010-parking barrier',
    category: 'city',
    filename: '010-parking barrier.svg',
    displayName: 'parking barrier',
    aliases: ['010-parking barrier', 'parking barrier', 'parkingbarrier', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-011-tram',
    category: 'city',
    filename: '011-tram.svg',
    displayName: 'tram',
    aliases: ['011-tram', 'tram', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-012-phone booth',
    category: 'city',
    filename: '012-phone booth.svg',
    displayName: 'phone booth',
    aliases: ['012-phone booth', 'phone booth', 'phonebooth', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-013-mailbox',
    category: 'city',
    filename: '013-mailbox.svg',
    displayName: 'mailbox',
    aliases: ['013-mailbox', 'mailbox', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-014-football field',
    category: 'city',
    filename: '014-football field.svg',
    displayName: 'football field',
    aliases: ['014-football field', 'football field', 'footballfield', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-015-trash can',
    category: 'city',
    filename: '015-trash can.svg',
    displayName: 'trash can',
    aliases: ['015-trash can', 'trash can', 'trashcan', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-016-taxi',
    category: 'city',
    filename: '016-taxi.svg',
    displayName: 'taxi',
    aliases: ['016-taxi', 'taxi', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-017-ambulance',
    category: 'city',
    filename: '017-ambulance.svg',
    displayName: 'ambulance',
    aliases: ['017-ambulance', 'ambulance', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-018-firetruck',
    category: 'city',
    filename: '018-firetruck.svg',
    displayName: 'firetruck',
    aliases: ['018-firetruck', 'firetruck', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-019-school bus',
    category: 'city',
    filename: '019-school bus.svg',
    displayName: 'school bus',
    aliases: ['019-school bus', 'school bus', 'schoolbus', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-020-police car',
    category: 'city',
    filename: '020-police car.svg',
    displayName: 'police car',
    aliases: [
      '020-police car',
      'police car',
      'policecar',
      '城市建筑',
      'city',
      '汽车',
      '车',
      '轿车',
      'car',
      'vehicle',
      '小车',
      'auto'
    ],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-021-atm',
    category: 'city',
    filename: '021-atm.svg',
    displayName: 'atm',
    aliases: ['021-atm', 'atm', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-022-electric tower',
    category: 'city',
    filename: '022-electric tower.svg',
    displayName: 'electric tower',
    aliases: ['022-electric tower', 'electric tower', 'electrictower', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-023-billboard',
    category: 'city',
    filename: '023-billboard.svg',
    displayName: 'billboard',
    aliases: ['023-billboard', 'billboard', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-024-electric pole',
    category: 'city',
    filename: '024-electric pole.svg',
    displayName: 'electric pole',
    aliases: ['024-electric pole', 'electric pole', 'electricpole', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-025-church',
    category: 'city',
    filename: '025-church.svg',
    displayName: 'church',
    aliases: ['025-church', 'church', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-026-skyscraper',
    category: 'city',
    filename: '026-skyscraper.svg',
    displayName: 'skyscraper',
    aliases: ['026-skyscraper', 'skyscraper', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-027-bus stop',
    category: 'city',
    filename: '027-bus stop.svg',
    displayName: 'bus stop',
    aliases: ['027-bus stop', 'bus stop', 'busstop', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-028-bridge',
    category: 'city',
    filename: '028-bridge.svg',
    displayName: 'bridge',
    aliases: ['028-bridge', 'bridge', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-030-mosque',
    category: 'city',
    filename: '030-mosque.svg',
    displayName: 'mosque',
    aliases: ['030-mosque', 'mosque', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-031-bank',
    category: 'city',
    filename: '031-bank.svg',
    displayName: 'bank',
    aliases: ['031-bank', 'bank', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-032-hospital',
    category: 'city',
    filename: '032-hospital.svg',
    displayName: 'hospital',
    aliases: ['032-hospital', 'hospital', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-033-school',
    category: 'city',
    filename: '033-school.svg',
    displayName: 'school',
    aliases: ['033-school', 'school', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-034-forest',
    category: 'city',
    filename: '034-forest.svg',
    displayName: 'forest',
    aliases: ['034-forest', 'forest', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-035-factory',
    category: 'city',
    filename: '035-factory.svg',
    displayName: 'factory',
    aliases: ['035-factory', 'factory', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-036-coffee shop',
    category: 'city',
    filename: '036-coffee shop.svg',
    displayName: 'coffee shop',
    aliases: [
      '036-coffee shop',
      'coffee shop',
      'coffeeshop',
      '城市建筑',
      'city',
      '咖啡',
      '拿铁',
      'coffee',
      'latte',
      '饮料',
      'drink'
    ],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-037-cargo ship',
    category: 'city',
    filename: '037-cargo ship.svg',
    displayName: 'cargo ship',
    aliases: [
      '037-cargo ship',
      'cargo ship',
      'cargoship',
      '城市建筑',
      'city',
      '汽车',
      '车',
      '轿车',
      'car',
      'vehicle',
      '小车',
      'auto'
    ],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-038-restroom',
    category: 'city',
    filename: '038-restroom.svg',
    displayName: 'restroom',
    aliases: ['038-restroom', 'restroom', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-039-cctv',
    category: 'city',
    filename: '039-cctv.svg',
    displayName: 'cctv',
    aliases: ['039-cctv', 'cctv', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-040-police station',
    category: 'city',
    filename: '040-police station.svg',
    displayName: 'police station',
    aliases: ['040-police station', 'police station', 'policestation', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-041-mall',
    category: 'city',
    filename: '041-mall.svg',
    displayName: 'mall',
    aliases: ['041-mall', 'mall', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-042-airport',
    category: 'city',
    filename: '042-airport.svg',
    displayName: 'airport',
    aliases: ['042-airport', 'airport', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-043-apartment',
    category: 'city',
    filename: '043-apartment.svg',
    displayName: 'apartment',
    aliases: ['043-apartment', 'apartment', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-044-government building',
    category: 'city',
    filename: '044-government building.svg',
    displayName: 'government building',
    aliases: [
      '044-government building',
      'government building',
      'governmentbuilding',
      '城市建筑',
      'city'
    ],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-045-park',
    category: 'city',
    filename: '045-park.svg',
    displayName: 'park',
    aliases: ['045-park', 'park', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-046-food cart',
    category: 'city',
    filename: '046-food cart.svg',
    displayName: 'food cart',
    aliases: [
      '046-food cart',
      'food cart',
      'foodcart',
      '城市建筑',
      'city',
      '汽车',
      '车',
      '轿车',
      'car',
      'vehicle',
      '小车',
      'auto'
    ],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-047-cemetery',
    category: 'city',
    filename: '047-cemetery.svg',
    displayName: 'cemetery',
    aliases: ['047-cemetery', 'cemetery', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-048-hydrant',
    category: 'city',
    filename: '048-hydrant.svg',
    displayName: 'hydrant',
    aliases: ['048-hydrant', 'hydrant', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-049-basketball',
    category: 'city',
    filename: '049-basketball.svg',
    displayName: 'basketball',
    aliases: ['049-basketball', 'basketball', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'city-050-street sign',
    category: 'city',
    filename: '050-street sign.svg',
    displayName: 'street sign',
    aliases: ['050-street sign', 'street sign', 'streetsign', '城市建筑', 'city'],
    tags: ['城市建筑', 'city']
  },
  {
    id: 'commercial-书籍资料',
    category: 'commercial',
    filename: '书籍资料.svg',
    displayName: '书籍资料',
    aliases: ['书籍资料', '商业金融', 'business'],
    tags: ['商业金融', 'business', '商业', '办公', 'office']
  },
  {
    id: 'commercial-公司',
    category: 'commercial',
    filename: '公司.svg',
    displayName: '公司',
    aliases: ['公司', '商业金融', 'business'],
    tags: ['商业金融', 'business', '商业', '办公', 'office']
  },
  {
    id: 'commercial-图表',
    category: 'commercial',
    filename: '图表.svg',
    displayName: '图表',
    aliases: ['图表', '商业金融', 'business'],
    tags: ['商业金融', 'business', '商业', '办公', 'office']
  },
  {
    id: 'commercial-天平',
    category: 'commercial',
    filename: '天平.svg',
    displayName: '天平',
    aliases: ['天平', '商业金融', 'business'],
    tags: ['商业金融', 'business', '商业', '办公', 'office']
  },
  {
    id: 'commercial-工牌',
    category: 'commercial',
    filename: '工牌.svg',
    displayName: '工牌',
    aliases: ['工牌', '商业金融', 'business'],
    tags: ['商业金融', 'business', '商业', '办公', 'office']
  },
  {
    id: 'commercial-成交',
    category: 'commercial',
    filename: '成交.svg',
    displayName: '成交',
    aliases: ['成交', '商业金融', 'business'],
    tags: ['商业金融', 'business', '商业', '办公', 'office']
  },
  {
    id: 'commercial-投资',
    category: 'commercial',
    filename: '投资.svg',
    displayName: '投资',
    aliases: ['投资', '商业金融', 'business'],
    tags: ['商业金融', 'business', '商业', '办公', 'office']
  },
  {
    id: 'commercial-收银机',
    category: 'commercial',
    filename: '收银机.svg',
    displayName: '收银机',
    aliases: ['收银机', '商业金融', 'business'],
    tags: ['商业金融', 'business', '商业', '办公', 'office']
  },
  {
    id: 'commercial-数据',
    category: 'commercial',
    filename: '数据.svg',
    displayName: '数据',
    aliases: ['数据', '商业金融', 'business'],
    tags: ['商业金融', 'business', '商业', '办公', 'office']
  },
  {
    id: 'commercial-数据趋势',
    category: 'commercial',
    filename: '数据趋势.svg',
    displayName: '数据趋势',
    aliases: ['数据趋势', '商业金融', 'business'],
    tags: ['商业金融', 'business', '商业', '办公', 'office']
  },
  {
    id: 'commercial-档案袋',
    category: 'commercial',
    filename: '档案袋.svg',
    displayName: '档案袋',
    aliases: ['档案袋', '商业金融', 'business'],
    tags: ['商业金融', 'business', '商业', '办公', 'office']
  },
  {
    id: 'commercial-现金',
    category: 'commercial',
    filename: '现金.svg',
    displayName: '现金',
    aliases: ['现金', '商业金融', 'business'],
    tags: ['商业金融', 'business', '商业', '办公', 'office']
  },
  {
    id: 'commercial-笔记记录',
    category: 'commercial',
    filename: '笔记记录.svg',
    displayName: '笔记记录',
    aliases: ['笔记记录', '商业金融', 'business'],
    tags: ['商业金融', 'business', '商业', '办公', 'office']
  },
  {
    id: 'commercial-箭靶',
    category: 'commercial',
    filename: '箭靶.svg',
    displayName: '箭靶',
    aliases: ['箭靶', '商业金融', 'business'],
    tags: ['商业金融', 'business', '商业', '办公', 'office']
  },
  {
    id: 'commercial-统计分析',
    category: 'commercial',
    filename: '统计分析.svg',
    displayName: '统计分析',
    aliases: ['统计分析', '商业金融', 'business'],
    tags: ['商业金融', 'business', '商业', '办公', 'office']
  },
  {
    id: 'commercial-统计占比',
    category: 'commercial',
    filename: '统计占比.svg',
    displayName: '统计占比',
    aliases: ['统计占比', '商业金融', 'business'],
    tags: ['商业金融', 'business', '商业', '办公', 'office']
  },
  {
    id: 'commercial-计算器',
    category: 'commercial',
    filename: '计算器.svg',
    displayName: '计算器',
    aliases: ['计算器', '商业金融', 'business'],
    tags: ['商业金融', 'business', '商业', '办公', 'office']
  },
  {
    id: 'commercial-资产',
    category: 'commercial',
    filename: '资产.svg',
    displayName: '资产',
    aliases: ['资产', '商业金融', 'business'],
    tags: ['商业金融', 'business', '商业', '办公', 'office']
  },
  {
    id: 'commercial-趋势分析',
    category: 'commercial',
    filename: '趋势分析.svg',
    displayName: '趋势分析',
    aliases: ['趋势分析', '商业金融', 'business'],
    tags: ['商业金融', 'business', '商业', '办公', 'office']
  },
  {
    id: 'commercial-钱包',
    category: 'commercial',
    filename: '钱包.svg',
    displayName: '钱包',
    aliases: ['钱包', '商业金融', 'business'],
    tags: ['商业金融', 'business', '商业', '办公', 'office']
  },
  {
    id: 'commercial-铃铛',
    category: 'commercial',
    filename: '铃铛.svg',
    displayName: '铃铛',
    aliases: ['铃铛', '商业金融', 'business'],
    tags: ['商业金融', 'business', '商业', '办公', 'office']
  },
  {
    id: 'commercial-黄金',
    category: 'commercial',
    filename: '黄金.svg',
    displayName: '黄金',
    aliases: ['黄金', '商业金融', 'business'],
    tags: ['商业金融', 'business', '商业', '办公', 'office']
  },
  {
    id: 'emojis-努力加油',
    category: 'emojis',
    filename: '努力加油.svg',
    displayName: '努力加油',
    aliases: ['努力加油', '表情符号', 'emojis'],
    tags: ['表情符号', 'emojis', '表情', '情感', 'emotion', 'feeling']
  },
  {
    id: 'emojis-喜欢',
    category: 'emojis',
    filename: '喜欢.svg',
    displayName: '喜欢',
    aliases: ['喜欢', '表情符号', 'emojis'],
    tags: ['表情符号', 'emojis', '表情', '情感', 'emotion', 'feeling']
  },
  {
    id: 'emojis-喜欢1',
    category: 'emojis',
    filename: '喜欢1.svg',
    displayName: '喜欢1',
    aliases: ['喜欢1', '表情符号', 'emojis'],
    tags: ['表情符号', 'emojis', '表情', '情感', 'emotion', 'feeling']
  },
  {
    id: 'emojis-大哭悲伤',
    category: 'emojis',
    filename: '大哭悲伤.svg',
    displayName: '大哭悲伤',
    aliases: ['大哭悲伤', '表情符号', 'emojis'],
    tags: ['表情符号', 'emojis', '表情', '情感', 'emotion', 'feeling']
  },
  {
    id: 'emojis-太棒了',
    category: 'emojis',
    filename: '太棒了.svg',
    displayName: '太棒了',
    aliases: ['太棒了', '表情符号', 'emojis'],
    tags: ['表情符号', 'emojis', '表情', '情感', 'emotion', 'feeling']
  },
  {
    id: 'emojis-尴尬流汗',
    category: 'emojis',
    filename: '尴尬流汗.svg',
    displayName: '尴尬流汗',
    aliases: ['尴尬流汗', '表情符号', 'emojis'],
    tags: ['表情符号', 'emojis', '表情', '情感', 'emotion', 'feeling']
  },
  {
    id: 'emojis-惊讶惊奇',
    category: 'emojis',
    filename: '惊讶惊奇.svg',
    displayName: '惊讶惊奇',
    aliases: ['惊讶惊奇', '表情符号', 'emojis'],
    tags: ['表情符号', 'emojis', '表情', '情感', 'emotion', 'feeling']
  },
  {
    id: 'emojis-生气愤怒',
    category: 'emojis',
    filename: '生气愤怒.svg',
    displayName: '生气愤怒',
    aliases: ['生气愤怒', '表情符号', 'emojis'],
    tags: ['表情符号', 'emojis', '表情', '情感', 'emotion', 'feeling']
  },
  {
    id: 'emojis-笑脸',
    category: 'emojis',
    filename: '笑脸.svg',
    displayName: '笑脸',
    aliases: ['笑脸', '表情符号', 'emojis'],
    tags: ['表情符号', 'emojis', '表情', '情感', 'emotion', 'feeling']
  },
  {
    id: 'emojis-装可爱',
    category: 'emojis',
    filename: '装可爱.svg',
    displayName: '装可爱',
    aliases: ['装可爱', '表情符号', 'emojis'],
    tags: ['表情符号', 'emojis', '表情', '情感', 'emotion', 'feeling']
  },
  {
    id: 'emojis-装酷耍帅',
    category: 'emojis',
    filename: '装酷耍帅.svg',
    displayName: '装酷耍帅',
    aliases: ['装酷耍帅', '表情符号', 'emojis'],
    tags: ['表情符号', 'emojis', '表情', '情感', 'emotion', 'feeling']
  },
  {
    id: 'emojis-闭嘴尴尬',
    category: 'emojis',
    filename: '闭嘴尴尬.svg',
    displayName: '闭嘴尴尬',
    aliases: ['闭嘴尴尬', '表情符号', 'emojis'],
    tags: ['表情符号', 'emojis', '表情', '情感', 'emotion', 'feeling']
  },
  {
    id: 'emojis-高兴',
    category: 'emojis',
    filename: '高兴.svg',
    displayName: '高兴',
    aliases: ['高兴', '表情符号', 'emojis'],
    tags: ['表情符号', 'emojis', '表情', '情感', 'emotion', 'feeling']
  },
  {
    id: 'food-bread',
    category: 'food',
    filename: 'bread.svg',
    displayName: 'bread',
    aliases: ['bread', '食物', 'food'],
    tags: ['食物', 'food', '餐饮', 'dining']
  },
  {
    id: 'food-bread1',
    category: 'food',
    filename: 'bread1.svg',
    displayName: 'bread1',
    aliases: ['bread1', '食物', 'food'],
    tags: ['食物', 'food', '餐饮', 'dining']
  },
  {
    id: 'food-cookie',
    category: 'food',
    filename: 'cookie.svg',
    displayName: 'cookie',
    aliases: ['cookie', '食物', 'food'],
    tags: ['食物', 'food', '餐饮', 'dining']
  },
  {
    id: 'food-fridefries',
    category: 'food',
    filename: 'fridefries.svg',
    displayName: 'fridefries',
    aliases: ['fridefries', '食物', 'food'],
    tags: ['食物', 'food', '餐饮', 'dining']
  },
  {
    id: 'food-hamburger',
    category: 'food',
    filename: 'hamburger.svg',
    displayName: 'hamburger',
    aliases: ['hamburger', '食物', 'food', '汉堡', '汉堡包', 'burger', '快餐'],
    tags: ['食物', 'food', '餐饮', 'dining']
  },
  {
    id: 'food-hamburger1',
    category: 'food',
    filename: 'hamburger1.svg',
    displayName: 'hamburger1',
    aliases: ['hamburger1', '食物', 'food', '汉堡', '汉堡包', 'hamburger', 'burger', '快餐'],
    tags: ['食物', 'food', '餐饮', 'dining']
  },
  {
    id: 'food-hotDog',
    category: 'food',
    filename: 'hotDog.svg',
    displayName: 'hotDog',
    aliases: ['hotDog', '食物', 'food'],
    tags: ['食物', 'food', '餐饮', 'dining']
  },
  {
    id: 'food-icecream',
    category: 'food',
    filename: 'icecream.svg',
    displayName: 'icecream',
    aliases: ['icecream', '食物', 'food'],
    tags: ['食物', 'food', '餐饮', 'dining']
  },
  {
    id: 'food-noodles',
    category: 'food',
    filename: 'noodles.svg',
    displayName: 'noodles',
    aliases: ['noodles', '食物', 'food'],
    tags: ['食物', 'food', '餐饮', 'dining']
  },
  {
    id: 'food-pizza',
    category: 'food',
    filename: 'pizza.svg',
    displayName: 'pizza',
    aliases: ['pizza', '食物', 'food', '披萨', '比萨', '意式'],
    tags: ['食物', 'food', '餐饮', 'dining']
  },
  {
    id: 'food-sandwhich',
    category: 'food',
    filename: 'sandwhich.svg',
    displayName: 'sandwhich',
    aliases: ['sandwhich', '食物', 'food'],
    tags: ['食物', 'food', '餐饮', 'dining']
  },
  {
    id: 'food-sandwhich1',
    category: 'food',
    filename: 'sandwhich1.svg',
    displayName: 'sandwhich1',
    aliases: ['sandwhich1', '食物', 'food'],
    tags: ['食物', 'food', '餐饮', 'dining']
  },
  {
    id: 'food-soda',
    category: 'food',
    filename: 'soda.svg',
    displayName: 'soda',
    aliases: ['soda', '食物', 'food'],
    tags: ['食物', 'food', '餐饮', 'dining']
  },
  {
    id: 'food-taco',
    category: 'food',
    filename: 'taco.svg',
    displayName: 'taco',
    aliases: ['taco', '食物', 'food'],
    tags: ['食物', 'food', '餐饮', 'dining']
  },
  {
    id: 'fruits-蓝莓',
    category: 'fruits',
    filename: '蓝莓.svg',
    displayName: '蓝莓',
    aliases: ['蓝莓', '水果', 'fruits'],
    tags: ['水果', 'fruits']
  },
  {
    id: 'furniture-001-sofa',
    category: 'furniture',
    filename: '001-sofa.svg',
    displayName: 'sofa',
    aliases: ['001-sofa', 'sofa', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-002-bed',
    category: 'furniture',
    filename: '002-bed.svg',
    displayName: 'bed',
    aliases: ['002-bed', 'bed', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-003-cabinet',
    category: 'furniture',
    filename: '003-cabinet.svg',
    displayName: 'cabinet',
    aliases: ['003-cabinet', 'cabinet', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-004-cabinet',
    category: 'furniture',
    filename: '004-cabinet.svg',
    displayName: 'cabinet',
    aliases: ['004-cabinet', 'cabinet', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-005-cabinet drawer',
    category: 'furniture',
    filename: '005-cabinet drawer.svg',
    displayName: 'cabinet drawer',
    aliases: ['005-cabinet drawer', 'cabinet drawer', 'cabinetdrawer', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-006-cabinet',
    category: 'furniture',
    filename: '006-cabinet.svg',
    displayName: 'cabinet',
    aliases: ['006-cabinet', 'cabinet', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-007-cabinet',
    category: 'furniture',
    filename: '007-cabinet.svg',
    displayName: 'cabinet',
    aliases: ['007-cabinet', 'cabinet', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-008-cabinet',
    category: 'furniture',
    filename: '008-cabinet.svg',
    displayName: 'cabinet',
    aliases: ['008-cabinet', 'cabinet', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-009-cabinet',
    category: 'furniture',
    filename: '009-cabinet.svg',
    displayName: 'cabinet',
    aliases: ['009-cabinet', 'cabinet', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-010-cabinet',
    category: 'furniture',
    filename: '010-cabinet.svg',
    displayName: 'cabinet',
    aliases: ['010-cabinet', 'cabinet', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-011-cabinet',
    category: 'furniture',
    filename: '011-cabinet.svg',
    displayName: 'cabinet',
    aliases: ['011-cabinet', 'cabinet', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-012-cabinet',
    category: 'furniture',
    filename: '012-cabinet.svg',
    displayName: 'cabinet',
    aliases: ['012-cabinet', 'cabinet', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-013-cabinet',
    category: 'furniture',
    filename: '013-cabinet.svg',
    displayName: 'cabinet',
    aliases: ['013-cabinet', 'cabinet', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-014-cabinet',
    category: 'furniture',
    filename: '014-cabinet.svg',
    displayName: 'cabinet',
    aliases: ['014-cabinet', 'cabinet', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-015-cabinet',
    category: 'furniture',
    filename: '015-cabinet.svg',
    displayName: 'cabinet',
    aliases: ['015-cabinet', 'cabinet', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-016-table',
    category: 'furniture',
    filename: '016-table.svg',
    displayName: 'table',
    aliases: ['016-table', 'table', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-017-cabinets',
    category: 'furniture',
    filename: '017-cabinets.svg',
    displayName: 'cabinets',
    aliases: ['017-cabinets', 'cabinets', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-018-cabinets',
    category: 'furniture',
    filename: '018-cabinets.svg',
    displayName: 'cabinets',
    aliases: ['018-cabinets', 'cabinets', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-019-cabinets',
    category: 'furniture',
    filename: '019-cabinets.svg',
    displayName: 'cabinets',
    aliases: ['019-cabinets', 'cabinets', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-020-table',
    category: 'furniture',
    filename: '020-table.svg',
    displayName: 'table',
    aliases: ['020-table', 'table', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-021-chair',
    category: 'furniture',
    filename: '021-chair.svg',
    displayName: 'chair',
    aliases: ['021-chair', 'chair', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-022-chair',
    category: 'furniture',
    filename: '022-chair.svg',
    displayName: 'chair',
    aliases: ['022-chair', 'chair', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-025-dressing table',
    category: 'furniture',
    filename: '025-dressing table.svg',
    displayName: 'dressing table',
    aliases: ['025-dressing table', 'dressing table', 'dressingtable', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-026-dressing table',
    category: 'furniture',
    filename: '026-dressing table.svg',
    displayName: 'dressing table',
    aliases: ['026-dressing table', 'dressing table', 'dressingtable', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-027-dressing table',
    category: 'furniture',
    filename: '027-dressing table.svg',
    displayName: 'dressing table',
    aliases: ['027-dressing table', 'dressing table', 'dressingtable', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-028-mirror',
    category: 'furniture',
    filename: '028-mirror.svg',
    displayName: 'mirror',
    aliases: ['028-mirror', 'mirror', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-029-shelf',
    category: 'furniture',
    filename: '029-shelf.svg',
    displayName: 'shelf',
    aliases: ['029-shelf', 'shelf', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-030-sofa',
    category: 'furniture',
    filename: '030-sofa.svg',
    displayName: 'sofa',
    aliases: ['030-sofa', 'sofa', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-031-armchair',
    category: 'furniture',
    filename: '031-armchair.svg',
    displayName: 'armchair',
    aliases: ['031-armchair', 'armchair', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-032-armchair',
    category: 'furniture',
    filename: '032-armchair.svg',
    displayName: 'armchair',
    aliases: ['032-armchair', 'armchair', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-033-sofa',
    category: 'furniture',
    filename: '033-sofa.svg',
    displayName: 'sofa',
    aliases: ['033-sofa', 'sofa', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-034-storage',
    category: 'furniture',
    filename: '034-storage.svg',
    displayName: 'storage',
    aliases: ['034-storage', 'storage', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-035-table',
    category: 'furniture',
    filename: '035-table.svg',
    displayName: 'table',
    aliases: ['035-table', 'table', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-036-table',
    category: 'furniture',
    filename: '036-table.svg',
    displayName: 'table',
    aliases: ['036-table', 'table', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-037-table',
    category: 'furniture',
    filename: '037-table.svg',
    displayName: 'table',
    aliases: ['037-table', 'table', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-038-wardrobe',
    category: 'furniture',
    filename: '038-wardrobe.svg',
    displayName: 'wardrobe',
    aliases: ['038-wardrobe', 'wardrobe', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-039-wardrobe',
    category: 'furniture',
    filename: '039-wardrobe.svg',
    displayName: 'wardrobe',
    aliases: ['039-wardrobe', 'wardrobe', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-040-wardrobe',
    category: 'furniture',
    filename: '040-wardrobe.svg',
    displayName: 'wardrobe',
    aliases: ['040-wardrobe', 'wardrobe', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'furniture-041-wardrobe',
    category: 'furniture',
    filename: '041-wardrobe.svg',
    displayName: 'wardrobe',
    aliases: ['041-wardrobe', 'wardrobe', '家具', 'furniture'],
    tags: ['家具', 'furniture']
  },
  {
    id: 'graduation-001-mortarboard',
    category: 'graduation',
    filename: '001-mortarboard.svg',
    displayName: 'mortarboard',
    aliases: ['001-mortarboard', 'mortarboard', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-002-graduate',
    category: 'graduation',
    filename: '002-graduate.svg',
    displayName: 'graduate',
    aliases: ['002-graduate', 'graduate', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-003-graduate',
    category: 'graduation',
    filename: '003-graduate.svg',
    displayName: 'graduate',
    aliases: ['003-graduate', 'graduate', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-004-principal',
    category: 'graduation',
    filename: '004-principal.svg',
    displayName: 'principal',
    aliases: ['004-principal', 'principal', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-005-teacher',
    category: 'graduation',
    filename: '005-teacher.svg',
    displayName: 'teacher',
    aliases: ['005-teacher', 'teacher', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-006-college',
    category: 'graduation',
    filename: '006-college.svg',
    displayName: 'college',
    aliases: ['006-college', 'college', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-007-diploma',
    category: 'graduation',
    filename: '007-diploma.svg',
    displayName: 'diploma',
    aliases: ['007-diploma', 'diploma', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-008-mortarboard',
    category: 'graduation',
    filename: '008-mortarboard.svg',
    displayName: 'mortarboard',
    aliases: ['008-mortarboard', 'mortarboard', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-009-medal',
    category: 'graduation',
    filename: '009-medal.svg',
    displayName: 'medal',
    aliases: ['009-medal', 'medal', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-010-shield',
    category: 'graduation',
    filename: '010-shield.svg',
    displayName: 'shield',
    aliases: ['010-shield', 'shield', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-011-trophy',
    category: 'graduation',
    filename: '011-trophy.svg',
    displayName: 'trophy',
    aliases: ['011-trophy', 'trophy', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-012-sealing wax',
    category: 'graduation',
    filename: '012-sealing wax.svg',
    displayName: 'sealing wax',
    aliases: ['012-sealing wax', 'sealing wax', 'sealingwax', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-013-boy',
    category: 'graduation',
    filename: '013-boy.svg',
    displayName: 'boy',
    aliases: ['013-boy', 'boy', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-014-girl',
    category: 'graduation',
    filename: '014-girl.svg',
    displayName: 'girl',
    aliases: ['014-girl', 'girl', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-015-graduation toga',
    category: 'graduation',
    filename: '015-graduation toga.svg',
    displayName: 'graduation toga',
    aliases: ['015-graduation toga', 'graduation toga', 'graduationtoga', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-016-champagne',
    category: 'graduation',
    filename: '016-champagne.svg',
    displayName: 'champagne',
    aliases: ['016-champagne', 'champagne', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-017-cake',
    category: 'graduation',
    filename: '017-cake.svg',
    displayName: 'cake',
    aliases: [
      '017-cake',
      'cake',
      '毕业典礼',
      'graduation',
      '蛋糕',
      '糕点',
      'dessert',
      '甜品',
      'sweet'
    ],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-018-speech',
    category: 'graduation',
    filename: '018-speech.svg',
    displayName: 'speech',
    aliases: ['018-speech', 'speech', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-019-speech',
    category: 'graduation',
    filename: '019-speech.svg',
    displayName: 'speech',
    aliases: ['019-speech', 'speech', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-020-disco ball',
    category: 'graduation',
    filename: '020-disco ball.svg',
    displayName: 'disco ball',
    aliases: ['020-disco ball', 'disco ball', 'discoball', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-021-flower bouquet',
    category: 'graduation',
    filename: '021-flower bouquet.svg',
    displayName: 'flower bouquet',
    aliases: ['021-flower bouquet', 'flower bouquet', 'flowerbouquet', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-022-bell',
    category: 'graduation',
    filename: '022-bell.svg',
    displayName: 'bell',
    aliases: ['022-bell', 'bell', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-023-wreath',
    category: 'graduation',
    filename: '023-wreath.svg',
    displayName: 'wreath',
    aliases: ['023-wreath', 'wreath', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-024-briefcase',
    category: 'graduation',
    filename: '024-briefcase.svg',
    displayName: 'briefcase',
    aliases: ['024-briefcase', 'briefcase', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-025-owl',
    category: 'graduation',
    filename: '025-owl.svg',
    displayName: 'owl',
    aliases: ['025-owl', 'owl', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-026-apple',
    category: 'graduation',
    filename: '026-apple.svg',
    displayName: 'apple',
    aliases: ['026-apple', 'apple', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-027-yearbook',
    category: 'graduation',
    filename: '027-yearbook.svg',
    displayName: 'yearbook',
    aliases: ['027-yearbook', 'yearbook', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-028-shouting',
    category: 'graduation',
    filename: '028-shouting.svg',
    displayName: 'shouting',
    aliases: ['028-shouting', 'shouting', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-029-limousine',
    category: 'graduation',
    filename: '029-limousine.svg',
    displayName: 'limousine',
    aliases: ['029-limousine', 'limousine', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-030-globe',
    category: 'graduation',
    filename: '030-globe.svg',
    displayName: 'globe',
    aliases: ['030-globe', 'globe', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-031-test',
    category: 'graduation',
    filename: '031-test.svg',
    displayName: 'test',
    aliases: ['031-test', 'test', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-032-stage',
    category: 'graduation',
    filename: '032-stage.svg',
    displayName: 'stage',
    aliases: ['032-stage', 'stage', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-033-ring',
    category: 'graduation',
    filename: '033-ring.svg',
    displayName: 'ring',
    aliases: ['033-ring', 'ring', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-034-travel',
    category: 'graduation',
    filename: '034-travel.svg',
    displayName: 'travel',
    aliases: ['034-travel', 'travel', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-035-cruise',
    category: 'graduation',
    filename: '035-cruise.svg',
    displayName: 'cruise',
    aliases: ['035-cruise', 'cruise', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-036-toast',
    category: 'graduation',
    filename: '036-toast.svg',
    displayName: 'toast',
    aliases: ['036-toast', 'toast', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-037-mortarboard',
    category: 'graduation',
    filename: '037-mortarboard.svg',
    displayName: 'mortarboard',
    aliases: ['037-mortarboard', 'mortarboard', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-038-graduate',
    category: 'graduation',
    filename: '038-graduate.svg',
    displayName: 'graduate',
    aliases: ['038-graduate', 'graduate', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-039-graduate',
    category: 'graduation',
    filename: '039-graduate.svg',
    displayName: 'graduate',
    aliases: ['039-graduate', 'graduate', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-040-invitation',
    category: 'graduation',
    filename: '040-invitation.svg',
    displayName: 'invitation',
    aliases: ['040-invitation', 'invitation', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-041-punch',
    category: 'graduation',
    filename: '041-punch.svg',
    displayName: 'punch',
    aliases: ['041-punch', 'punch', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-042-stole',
    category: 'graduation',
    filename: '042-stole.svg',
    displayName: 'stole',
    aliases: ['042-stole', 'stole', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-043-balloon',
    category: 'graduation',
    filename: '043-balloon.svg',
    displayName: 'balloon',
    aliases: ['043-balloon', 'balloon', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-044-graduation',
    category: 'graduation',
    filename: '044-graduation.svg',
    displayName: 'graduation',
    aliases: ['044-graduation', 'graduation', '毕业典礼'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-045-pennant',
    category: 'graduation',
    filename: '045-pennant.svg',
    displayName: 'pennant',
    aliases: ['045-pennant', 'pennant', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-046-online',
    category: 'graduation',
    filename: '046-online.svg',
    displayName: 'online',
    aliases: ['046-online', 'online', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-047-tie',
    category: 'graduation',
    filename: '047-tie.svg',
    displayName: 'tie',
    aliases: ['047-tie', 'tie', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-048-board',
    category: 'graduation',
    filename: '048-board.svg',
    displayName: 'board',
    aliases: ['048-board', 'board', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-049-photo',
    category: 'graduation',
    filename: '049-photo.svg',
    displayName: 'photo',
    aliases: ['049-photo', 'photo', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'graduation-050-diploma',
    category: 'graduation',
    filename: '050-diploma.svg',
    displayName: 'diploma',
    aliases: ['050-diploma', 'diploma', '毕业典礼', 'graduation'],
    tags: ['毕业典礼', 'graduation']
  },
  {
    id: 'halloween-001-pumpkin',
    category: 'halloween',
    filename: '001-pumpkin.svg',
    displayName: 'pumpkin',
    aliases: ['001-pumpkin', 'pumpkin', '万圣节', 'halloween'],
    tags: ['万圣节', 'halloween']
  },
  {
    id: 'halloween-002-pumpkin',
    category: 'halloween',
    filename: '002-pumpkin.svg',
    displayName: 'pumpkin',
    aliases: ['002-pumpkin', 'pumpkin', '万圣节', 'halloween'],
    tags: ['万圣节', 'halloween']
  },
  {
    id: 'halloween-003-pumpkin',
    category: 'halloween',
    filename: '003-pumpkin.svg',
    displayName: 'pumpkin',
    aliases: ['003-pumpkin', 'pumpkin', '万圣节', 'halloween'],
    tags: ['万圣节', 'halloween']
  },
  {
    id: 'halloween-004-pumpkin',
    category: 'halloween',
    filename: '004-pumpkin.svg',
    displayName: 'pumpkin',
    aliases: ['004-pumpkin', 'pumpkin', '万圣节', 'halloween'],
    tags: ['万圣节', 'halloween']
  },
  {
    id: 'halloween-005-pumpkin',
    category: 'halloween',
    filename: '005-pumpkin.svg',
    displayName: 'pumpkin',
    aliases: ['005-pumpkin', 'pumpkin', '万圣节', 'halloween'],
    tags: ['万圣节', 'halloween']
  },
  {
    id: 'halloween-006-ghost',
    category: 'halloween',
    filename: '006-ghost.svg',
    displayName: 'ghost',
    aliases: ['006-ghost', 'ghost', '万圣节', 'halloween'],
    tags: ['万圣节', 'halloween']
  },
  {
    id: 'halloween-007-ghost',
    category: 'halloween',
    filename: '007-ghost.svg',
    displayName: 'ghost',
    aliases: ['007-ghost', 'ghost', '万圣节', 'halloween'],
    tags: ['万圣节', 'halloween']
  },
  {
    id: 'halloween-008-ghost',
    category: 'halloween',
    filename: '008-ghost.svg',
    displayName: 'ghost',
    aliases: ['008-ghost', 'ghost', '万圣节', 'halloween'],
    tags: ['万圣节', 'halloween']
  },
  {
    id: 'halloween-009-ghost',
    category: 'halloween',
    filename: '009-ghost.svg',
    displayName: 'ghost',
    aliases: ['009-ghost', 'ghost', '万圣节', 'halloween'],
    tags: ['万圣节', 'halloween']
  },
  {
    id: 'halloween-010-ghost',
    category: 'halloween',
    filename: '010-ghost.svg',
    displayName: 'ghost',
    aliases: ['010-ghost', 'ghost', '万圣节', 'halloween'],
    tags: ['万圣节', 'halloween']
  },
  {
    id: 'halloween-011-poison',
    category: 'halloween',
    filename: '011-poison.svg',
    displayName: 'poison',
    aliases: ['011-poison', 'poison', '万圣节', 'halloween'],
    tags: ['万圣节', 'halloween']
  },
  {
    id: 'halloween-012-bone',
    category: 'halloween',
    filename: '012-bone.svg',
    displayName: 'bone',
    aliases: ['012-bone', 'bone', '万圣节', 'halloween'],
    tags: ['万圣节', 'halloween']
  },
  {
    id: 'halloween-013-candy',
    category: 'halloween',
    filename: '013-candy.svg',
    displayName: 'candy',
    aliases: ['013-candy', 'candy', '万圣节', 'halloween'],
    tags: ['万圣节', 'halloween']
  },
  {
    id: 'halloween-014-candy',
    category: 'halloween',
    filename: '014-candy.svg',
    displayName: 'candy',
    aliases: ['014-candy', 'candy', '万圣节', 'halloween'],
    tags: ['万圣节', 'halloween']
  },
  {
    id: 'halloween-015-candy',
    category: 'halloween',
    filename: '015-candy.svg',
    displayName: 'candy',
    aliases: ['015-candy', 'candy', '万圣节', 'halloween'],
    tags: ['万圣节', 'halloween']
  },
  {
    id: 'halloween-016-cyclops',
    category: 'halloween',
    filename: '016-cyclops.svg',
    displayName: 'cyclops',
    aliases: ['016-cyclops', 'cyclops', '万圣节', 'halloween'],
    tags: ['万圣节', 'halloween']
  },
  {
    id: 'halloween-017-ghost custom',
    category: 'halloween',
    filename: '017-ghost custom.svg',
    displayName: 'ghost custom',
    aliases: ['017-ghost custom', 'ghost custom', 'ghostcustom', '万圣节', 'halloween'],
    tags: ['万圣节', 'halloween']
  },
  {
    id: 'halloween-018-skeleton',
    category: 'halloween',
    filename: '018-skeleton.svg',
    displayName: 'skeleton',
    aliases: ['018-skeleton', 'skeleton', '万圣节', 'halloween'],
    tags: ['万圣节', 'halloween']
  },
  {
    id: 'halloween-019-mummy',
    category: 'halloween',
    filename: '019-mummy.svg',
    displayName: 'mummy',
    aliases: ['019-mummy', 'mummy', '万圣节', 'halloween'],
    tags: ['万圣节', 'halloween']
  },
  {
    id: 'halloween-020-vampire',
    category: 'halloween',
    filename: '020-vampire.svg',
    displayName: 'vampire',
    aliases: ['020-vampire', 'vampire', '万圣节', 'halloween'],
    tags: ['万圣节', 'halloween']
  },
  {
    id: 'halloween-021-bat',
    category: 'halloween',
    filename: '021-bat.svg',
    displayName: 'bat',
    aliases: ['021-bat', 'bat', '万圣节', 'halloween'],
    tags: ['万圣节', 'halloween']
  },
  {
    id: 'halloween-022-grave',
    category: 'halloween',
    filename: '022-grave.svg',
    displayName: 'grave',
    aliases: ['022-grave', 'grave', '万圣节', 'halloween'],
    tags: ['万圣节', 'halloween']
  },
  {
    id: 'halloween-023-zombie hand',
    category: 'halloween',
    filename: '023-zombie hand.svg',
    displayName: 'zombie hand',
    aliases: ['023-zombie hand', 'zombie hand', 'zombiehand', '万圣节', 'halloween'],
    tags: ['万圣节', 'halloween']
  },
  {
    id: 'halloween-024-pumpkin bag',
    category: 'halloween',
    filename: '024-pumpkin bag.svg',
    displayName: 'pumpkin bag',
    aliases: ['024-pumpkin bag', 'pumpkin bag', 'pumpkinbag', '万圣节', 'halloween'],
    tags: ['万圣节', 'halloween']
  },
  {
    id: 'halloween-025-halloween day',
    category: 'halloween',
    filename: '025-halloween day.svg',
    displayName: 'halloween day',
    aliases: ['025-halloween day', 'halloween day', 'halloweenday', '万圣节', 'halloween'],
    tags: ['万圣节', 'halloween']
  },
  {
    id: 'halloween-026-frankenstein',
    category: 'halloween',
    filename: '026-frankenstein.svg',
    displayName: 'frankenstein',
    aliases: ['026-frankenstein', 'frankenstein', '万圣节', 'halloween'],
    tags: ['万圣节', 'halloween']
  },
  {
    id: 'halloween-027-monster',
    category: 'halloween',
    filename: '027-monster.svg',
    displayName: 'monster',
    aliases: ['027-monster', 'monster', '万圣节', 'halloween'],
    tags: ['万圣节', 'halloween']
  },
  {
    id: 'halloween-028-candle',
    category: 'halloween',
    filename: '028-candle.svg',
    displayName: 'candle',
    aliases: ['028-candle', 'candle', '万圣节', 'halloween'],
    tags: ['万圣节', 'halloween']
  },
  {
    id: 'halloween-029-witch hat',
    category: 'halloween',
    filename: '029-witch hat.svg',
    displayName: 'witch hat',
    aliases: ['029-witch hat', 'witch hat', 'witchhat', '万圣节', 'halloween'],
    tags: ['万圣节', 'halloween']
  },
  {
    id: 'halloween-030-witch broom',
    category: 'halloween',
    filename: '030-witch broom.svg',
    displayName: 'witch broom',
    aliases: ['030-witch broom', 'witch broom', 'witchbroom', '万圣节', 'halloween'],
    tags: ['万圣节', 'halloween']
  },
  {
    id: 'herbs-002-arugula',
    category: 'herbs',
    filename: '002-arugula.svg',
    displayName: 'arugula',
    aliases: ['002-arugula', 'arugula', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-003-barberry',
    category: 'herbs',
    filename: '003-barberry.svg',
    displayName: 'barberry',
    aliases: ['003-barberry', 'barberry', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-004-basil',
    category: 'herbs',
    filename: '004-basil.svg',
    displayName: 'basil',
    aliases: ['004-basil', 'basil', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-005-bay',
    category: 'herbs',
    filename: '005-bay.svg',
    displayName: 'bay',
    aliases: ['005-bay', 'bay', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-006-cardamom',
    category: 'herbs',
    filename: '006-cardamom.svg',
    displayName: 'cardamom',
    aliases: [
      '006-cardamom',
      'cardamom',
      '香草香料',
      'herbs',
      '汽车',
      '车',
      '轿车',
      'car',
      'vehicle',
      '小车',
      'auto'
    ],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-007-chilli',
    category: 'herbs',
    filename: '007-chilli.svg',
    displayName: 'chilli',
    aliases: ['007-chilli', 'chilli', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-008-chives',
    category: 'herbs',
    filename: '008-chives.svg',
    displayName: 'chives',
    aliases: ['008-chives', 'chives', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-009-cinnamon',
    category: 'herbs',
    filename: '009-cinnamon.svg',
    displayName: 'cinnamon',
    aliases: ['009-cinnamon', 'cinnamon', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-010-clove',
    category: 'herbs',
    filename: '010-clove.svg',
    displayName: 'clove',
    aliases: ['010-clove', 'clove', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-011-coriander',
    category: 'herbs',
    filename: '011-coriander.svg',
    displayName: 'coriander',
    aliases: ['011-coriander', 'coriander', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-012-cumin',
    category: 'herbs',
    filename: '012-cumin.svg',
    displayName: 'cumin',
    aliases: ['012-cumin', 'cumin', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-013-dill',
    category: 'herbs',
    filename: '013-dill.svg',
    displayName: 'dill',
    aliases: ['013-dill', 'dill', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-014-fennel',
    category: 'herbs',
    filename: '014-fennel.svg',
    displayName: 'fennel',
    aliases: ['014-fennel', 'fennel', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-015-garlic',
    category: 'herbs',
    filename: '015-garlic.svg',
    displayName: 'garlic',
    aliases: ['015-garlic', 'garlic', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-016-ginger',
    category: 'herbs',
    filename: '016-ginger.svg',
    displayName: 'ginger',
    aliases: ['016-ginger', 'ginger', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-017-green onion',
    category: 'herbs',
    filename: '017-green onion.svg',
    displayName: 'green onion',
    aliases: ['017-green onion', 'green onion', 'greenonion', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-018-juniper',
    category: 'herbs',
    filename: '018-juniper.svg',
    displayName: 'juniper',
    aliases: ['018-juniper', 'juniper', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-019-lemon',
    category: 'herbs',
    filename: '019-lemon.svg',
    displayName: 'lemon',
    aliases: ['019-lemon', 'lemon', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-020-mint',
    category: 'herbs',
    filename: '020-mint.svg',
    displayName: 'mint',
    aliases: ['020-mint', 'mint', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-021-mustard',
    category: 'herbs',
    filename: '021-mustard.svg',
    displayName: 'mustard',
    aliases: ['021-mustard', 'mustard', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-022-nutmeg',
    category: 'herbs',
    filename: '022-nutmeg.svg',
    displayName: 'nutmeg',
    aliases: ['022-nutmeg', 'nutmeg', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-023-olives',
    category: 'herbs',
    filename: '023-olives.svg',
    displayName: 'olives',
    aliases: ['023-olives', 'olives', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-024-onion',
    category: 'herbs',
    filename: '024-onion.svg',
    displayName: 'onion',
    aliases: ['024-onion', 'onion', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-025-oregano',
    category: 'herbs',
    filename: '025-oregano.svg',
    displayName: 'oregano',
    aliases: ['025-oregano', 'oregano', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-026-paprika',
    category: 'herbs',
    filename: '026-paprika.svg',
    displayName: 'paprika',
    aliases: ['026-paprika', 'paprika', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-027-parsley',
    category: 'herbs',
    filename: '027-parsley.svg',
    displayName: 'parsley',
    aliases: ['027-parsley', 'parsley', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-028-pepper',
    category: 'herbs',
    filename: '028-pepper.svg',
    displayName: 'pepper',
    aliases: ['028-pepper', 'pepper', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-029-poppy',
    category: 'herbs',
    filename: '029-poppy.svg',
    displayName: 'poppy',
    aliases: ['029-poppy', 'poppy', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-030-rosehip',
    category: 'herbs',
    filename: '030-rosehip.svg',
    displayName: 'rosehip',
    aliases: ['030-rosehip', 'rosehip', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-031-rosemary',
    category: 'herbs',
    filename: '031-rosemary.svg',
    displayName: 'rosemary',
    aliases: ['031-rosemary', 'rosemary', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-032-saffron',
    category: 'herbs',
    filename: '032-saffron.svg',
    displayName: 'saffron',
    aliases: ['032-saffron', 'saffron', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-033-savory',
    category: 'herbs',
    filename: '033-savory.svg',
    displayName: 'savory',
    aliases: ['033-savory', 'savory', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-034-sesame',
    category: 'herbs',
    filename: '034-sesame.svg',
    displayName: 'sesame',
    aliases: ['034-sesame', 'sesame', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-035-anise',
    category: 'herbs',
    filename: '035-anise.svg',
    displayName: 'anise',
    aliases: ['035-anise', 'anise', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-036-tamarind',
    category: 'herbs',
    filename: '036-tamarind.svg',
    displayName: 'tamarind',
    aliases: ['036-tamarind', 'tamarind', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-037-tarragon',
    category: 'herbs',
    filename: '037-tarragon.svg',
    displayName: 'tarragon',
    aliases: ['037-tarragon', 'tarragon', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-038-thyme',
    category: 'herbs',
    filename: '038-thyme.svg',
    displayName: 'thyme',
    aliases: ['038-thyme', 'thyme', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-039-turmeric',
    category: 'herbs',
    filename: '039-turmeric.svg',
    displayName: 'turmeric',
    aliases: ['039-turmeric', 'turmeric', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'herbs-040-vanilla',
    category: 'herbs',
    filename: '040-vanilla.svg',
    displayName: 'vanilla',
    aliases: ['040-vanilla', 'vanilla', '香草香料', 'herbs'],
    tags: ['香草香料', 'herbs']
  },
  {
    id: 'history-001-book',
    category: 'history',
    filename: '001-book.svg',
    displayName: 'book',
    aliases: ['001-book', 'book', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-002-tomb',
    category: 'history',
    filename: '002-tomb.svg',
    displayName: 'tomb',
    aliases: ['002-tomb', 'tomb', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-003-vase',
    category: 'history',
    filename: '003-vase.svg',
    displayName: 'vase',
    aliases: ['003-vase', 'vase', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-004-archery',
    category: 'history',
    filename: '004-archery.svg',
    displayName: 'archery',
    aliases: ['004-archery', 'archery', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-005-anvil',
    category: 'history',
    filename: '005-anvil.svg',
    displayName: 'anvil',
    aliases: ['005-anvil', 'anvil', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-006-pillar',
    category: 'history',
    filename: '006-pillar.svg',
    displayName: 'pillar',
    aliases: ['006-pillar', 'pillar', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-007-treasure chest',
    category: 'history',
    filename: '007-treasure chest.svg',
    displayName: 'treasure chest',
    aliases: ['007-treasure chest', 'treasure chest', 'treasurechest', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-008-dagger',
    category: 'history',
    filename: '008-dagger.svg',
    displayName: 'dagger',
    aliases: ['008-dagger', 'dagger', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-009-crown',
    category: 'history',
    filename: '009-crown.svg',
    displayName: 'crown',
    aliases: ['009-crown', 'crown', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-010-prehistoric man',
    category: 'history',
    filename: '010-prehistoric man.svg',
    displayName: 'prehistoric man',
    aliases: ['010-prehistoric man', 'prehistoric man', 'prehistoricman', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-011-compass',
    category: 'history',
    filename: '011-compass.svg',
    displayName: 'compass',
    aliases: ['011-compass', 'compass', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-012-armor',
    category: 'history',
    filename: '012-armor.svg',
    displayName: 'armor',
    aliases: ['012-armor', 'armor', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-013-bone',
    category: 'history',
    filename: '013-bone.svg',
    displayName: 'bone',
    aliases: ['013-bone', 'bone', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-014-boomerang',
    category: 'history',
    filename: '014-boomerang.svg',
    displayName: 'boomerang',
    aliases: ['014-boomerang', 'boomerang', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-015-fortress',
    category: 'history',
    filename: '015-fortress.svg',
    displayName: 'fortress',
    aliases: ['015-fortress', 'fortress', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-016-maya',
    category: 'history',
    filename: '016-maya.svg',
    displayName: 'maya',
    aliases: ['016-maya', 'maya', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-017-greek temple',
    category: 'history',
    filename: '017-greek temple.svg',
    displayName: 'greek temple',
    aliases: ['017-greek temple', 'greek temple', 'greektemple', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-018-lyre',
    category: 'history',
    filename: '018-lyre.svg',
    displayName: 'lyre',
    aliases: ['018-lyre', 'lyre', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-019-native indian',
    category: 'history',
    filename: '019-native indian.svg',
    displayName: 'native indian',
    aliases: ['019-native indian', 'native indian', 'nativeindian', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-020-treasure map',
    category: 'history',
    filename: '020-treasure map.svg',
    displayName: 'treasure map',
    aliases: ['020-treasure map', 'treasure map', 'treasuremap', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-021-Hieroglyph',
    category: 'history',
    filename: '021-Hieroglyph.svg',
    displayName: 'Hieroglyph',
    aliases: ['021-Hieroglyph', 'Hieroglyph', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-022-catapult',
    category: 'history',
    filename: '022-catapult.svg',
    displayName: 'catapult',
    aliases: ['022-catapult', 'catapult', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-023-moai',
    category: 'history',
    filename: '023-moai.svg',
    displayName: 'moai',
    aliases: ['023-moai', 'moai', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-024-great sphinx of giza',
    category: 'history',
    filename: '024-great sphinx of giza.svg',
    displayName: 'great sphinx of giza',
    aliases: [
      '024-great sphinx of giza',
      'great sphinx of giza',
      'greatsphinxofgiza',
      '历史文物',
      'history'
    ],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-025-skull',
    category: 'history',
    filename: '025-skull.svg',
    displayName: 'skull',
    aliases: ['025-skull', 'skull', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-026-roman helmet',
    category: 'history',
    filename: '026-roman helmet.svg',
    displayName: 'roman helmet',
    aliases: ['026-roman helmet', 'roman helmet', 'romanhelmet', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-027-papyrus',
    category: 'history',
    filename: '027-papyrus.svg',
    displayName: 'papyrus',
    aliases: ['027-papyrus', 'papyrus', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-028-shield',
    category: 'history',
    filename: '028-shield.svg',
    displayName: 'shield',
    aliases: ['028-shield', 'shield', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-029-Pharaoh',
    category: 'history',
    filename: '029-Pharaoh.svg',
    displayName: 'Pharaoh',
    aliases: ['029-Pharaoh', 'Pharaoh', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-030-spear',
    category: 'history',
    filename: '030-spear.svg',
    displayName: 'spear',
    aliases: ['030-spear', 'spear', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-031-stone axe',
    category: 'history',
    filename: '031-stone axe.svg',
    displayName: 'stone axe',
    aliases: ['031-stone axe', 'stone axe', 'stoneaxe', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-032-totem',
    category: 'history',
    filename: '032-totem.svg',
    displayName: 'totem',
    aliases: ['032-totem', 'totem', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-033-torah',
    category: 'history',
    filename: '033-torah.svg',
    displayName: 'torah',
    aliases: ['033-torah', 'torah', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-034-viking',
    category: 'history',
    filename: '034-viking.svg',
    displayName: 'viking',
    aliases: ['034-viking', 'viking', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-035-torii gate',
    category: 'history',
    filename: '035-torii gate.svg',
    displayName: 'torii gate',
    aliases: ['035-torii gate', 'torii gate', 'toriigate', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-036-torch',
    category: 'history',
    filename: '036-torch.svg',
    displayName: 'torch',
    aliases: ['036-torch', 'torch', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-037-harp',
    category: 'history',
    filename: '037-harp.svg',
    displayName: 'harp',
    aliases: ['037-harp', 'harp', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-038-fossil',
    category: 'history',
    filename: '038-fossil.svg',
    displayName: 'fossil',
    aliases: ['038-fossil', 'fossil', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-039-painting',
    category: 'history',
    filename: '039-painting.svg',
    displayName: 'painting',
    aliases: ['039-painting', 'painting', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-040-stonehenge',
    category: 'history',
    filename: '040-stonehenge.svg',
    displayName: 'stonehenge',
    aliases: ['040-stonehenge', 'stonehenge', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-041-dinosaur',
    category: 'history',
    filename: '041-dinosaur.svg',
    displayName: 'dinosaur',
    aliases: ['041-dinosaur', 'dinosaur', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-042-treasure',
    category: 'history',
    filename: '042-treasure.svg',
    displayName: 'treasure',
    aliases: ['042-treasure', 'treasure', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-043-statue',
    category: 'history',
    filename: '043-statue.svg',
    displayName: 'statue',
    aliases: ['043-statue', 'statue', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-044-gemstone',
    category: 'history',
    filename: '044-gemstone.svg',
    displayName: 'gemstone',
    aliases: ['044-gemstone', 'gemstone', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-045-obelisk',
    category: 'history',
    filename: '045-obelisk.svg',
    displayName: 'obelisk',
    aliases: ['045-obelisk', 'obelisk', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-046-pyramid',
    category: 'history',
    filename: '046-pyramid.svg',
    displayName: 'pyramid',
    aliases: ['046-pyramid', 'pyramid', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-047-pagoda',
    category: 'history',
    filename: '047-pagoda.svg',
    displayName: 'pagoda',
    aliases: ['047-pagoda', 'pagoda', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-048-quill',
    category: 'history',
    filename: '048-quill.svg',
    displayName: 'quill',
    aliases: ['048-quill', 'quill', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-049-pickaxe',
    category: 'history',
    filename: '049-pickaxe.svg',
    displayName: 'pickaxe',
    aliases: ['049-pickaxe', 'pickaxe', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'history-050-sword',
    category: 'history',
    filename: '050-sword.svg',
    displayName: 'sword',
    aliases: ['050-sword', 'sword', '历史文物', 'history'],
    tags: ['历史文物', 'history']
  },
  {
    id: 'information-3D',
    category: 'information',
    filename: '3D.svg',
    displayName: '3D',
    aliases: ['3D', '信息图表', 'information'],
    tags: ['信息图表', 'information']
  },
  {
    id: 'information-图表',
    category: 'information',
    filename: '图表.svg',
    displayName: '图表',
    aliases: ['图表', '信息图表', 'information'],
    tags: ['信息图表', 'information']
  },
  {
    id: 'information-数据库',
    category: 'information',
    filename: '数据库.svg',
    displayName: '数据库',
    aliases: ['数据库', '信息图表', 'information'],
    tags: ['信息图表', 'information']
  },
  {
    id: 'information-检查列表',
    category: 'information',
    filename: '检查列表.svg',
    displayName: '检查列表',
    aliases: ['检查列表', '信息图表', 'information'],
    tags: ['信息图表', 'information']
  },
  {
    id: 'information-线条图',
    category: 'information',
    filename: '线条图.svg',
    displayName: '线条图',
    aliases: ['线条图', '信息图表', 'information'],
    tags: ['信息图表', 'information']
  },
  {
    id: 'insect-001-flying',
    category: 'insect',
    filename: '001-flying.svg',
    displayName: 'flying',
    aliases: ['001-flying', 'flying', '昆虫', 'insects'],
    tags: ['昆虫', 'insects']
  },
  {
    id: 'insect-005-bedbug',
    category: 'insect',
    filename: '005-bedbug.svg',
    displayName: 'bedbug',
    aliases: ['005-bedbug', 'bedbug', '昆虫', 'insects'],
    tags: ['昆虫', 'insects']
  },
  {
    id: 'insect-006-bug',
    category: 'insect',
    filename: '006-bug.svg',
    displayName: 'bug',
    aliases: ['006-bug', 'bug', '昆虫', 'insects'],
    tags: ['昆虫', 'insects']
  },
  {
    id: 'insect-007-cicada',
    category: 'insect',
    filename: '007-cicada.svg',
    displayName: 'cicada',
    aliases: ['007-cicada', 'cicada', '昆虫', 'insects'],
    tags: ['昆虫', 'insects']
  },
  {
    id: 'insect-008-fly',
    category: 'insect',
    filename: '008-fly.svg',
    displayName: 'fly',
    aliases: ['008-fly', 'fly', '昆虫', 'insects'],
    tags: ['昆虫', 'insects']
  },
  {
    id: 'insect-009-fly',
    category: 'insect',
    filename: '009-fly.svg',
    displayName: 'fly',
    aliases: ['009-fly', 'fly', '昆虫', 'insects'],
    tags: ['昆虫', 'insects']
  },
  {
    id: 'insect-010-spider',
    category: 'insect',
    filename: '010-spider.svg',
    displayName: 'spider',
    aliases: ['010-spider', 'spider', '昆虫', 'insects'],
    tags: ['昆虫', 'insects']
  },
  {
    id: 'insect-011-caterpillar',
    category: 'insect',
    filename: '011-caterpillar.svg',
    displayName: 'caterpillar',
    aliases: ['011-caterpillar', 'caterpillar', '昆虫', 'insects'],
    tags: ['昆虫', 'insects']
  },
  {
    id: 'insect-012-firefly',
    category: 'insect',
    filename: '012-firefly.svg',
    displayName: 'firefly',
    aliases: ['012-firefly', 'firefly', '昆虫', 'insects'],
    tags: ['昆虫', 'insects']
  },
  {
    id: 'insect-013-fruit fly',
    category: 'insect',
    filename: '013-fruit fly.svg',
    displayName: 'fruit fly',
    aliases: ['013-fruit fly', 'fruit fly', 'fruitfly', '昆虫', 'insects'],
    tags: ['昆虫', 'insects']
  },
  {
    id: 'insect-014-grasshopper',
    category: 'insect',
    filename: '014-grasshopper.svg',
    displayName: 'grasshopper',
    aliases: ['014-grasshopper', 'grasshopper', '昆虫', 'insects'],
    tags: ['昆虫', 'insects']
  },
  {
    id: 'insect-015-dragonfly',
    category: 'insect',
    filename: '015-dragonfly.svg',
    displayName: 'dragonfly',
    aliases: ['015-dragonfly', 'dragonfly', '昆虫', 'insects'],
    tags: ['昆虫', 'insects']
  },
  {
    id: 'insect-016-leaf insect',
    category: 'insect',
    filename: '016-leaf insect.svg',
    displayName: 'leaf insect',
    aliases: ['016-leaf insect', 'leaf insect', 'leafinsect', '昆虫', 'insects'],
    tags: ['昆虫', 'insects']
  },
  {
    id: 'insect-017-mantis',
    category: 'insect',
    filename: '017-mantis.svg',
    displayName: 'mantis',
    aliases: ['017-mantis', 'mantis', '昆虫', 'insects'],
    tags: ['昆虫', 'insects']
  },
  {
    id: 'insect-018-fly',
    category: 'insect',
    filename: '018-fly.svg',
    displayName: 'fly',
    aliases: ['018-fly', 'fly', '昆虫', 'insects'],
    tags: ['昆虫', 'insects']
  },
  {
    id: 'insect-019-leaf insect',
    category: 'insect',
    filename: '019-leaf insect.svg',
    displayName: 'leaf insect',
    aliases: ['019-leaf insect', 'leaf insect', 'leafinsect', '昆虫', 'insects'],
    tags: ['昆虫', 'insects']
  },
  {
    id: 'insect-020-zoology',
    category: 'insect',
    filename: '020-zoology.svg',
    displayName: 'zoology',
    aliases: ['020-zoology', 'zoology', '昆虫', 'insects'],
    tags: ['昆虫', 'insects']
  },
  {
    id: 'insect-021-moth',
    category: 'insect',
    filename: '021-moth.svg',
    displayName: 'moth',
    aliases: ['021-moth', 'moth', '昆虫', 'insects'],
    tags: ['昆虫', 'insects']
  },
  {
    id: 'insect-022-butterfly',
    category: 'insect',
    filename: '022-butterfly.svg',
    displayName: 'butterfly',
    aliases: ['022-butterfly', 'butterfly', '昆虫', 'insects'],
    tags: ['昆虫', 'insects']
  },
  {
    id: 'insect-023-leaf insect',
    category: 'insect',
    filename: '023-leaf insect.svg',
    displayName: 'leaf insect',
    aliases: ['023-leaf insect', 'leaf insect', 'leafinsect', '昆虫', 'insects'],
    tags: ['昆虫', 'insects']
  },
  {
    id: 'insect-024-bug',
    category: 'insect',
    filename: '024-bug.svg',
    displayName: 'bug',
    aliases: ['024-bug', 'bug', '昆虫', 'insects'],
    tags: ['昆虫', 'insects']
  },
  {
    id: 'insect-025-bug',
    category: 'insect',
    filename: '025-bug.svg',
    displayName: 'bug',
    aliases: ['025-bug', 'bug', '昆虫', 'insects'],
    tags: ['昆虫', 'insects']
  },
  {
    id: 'insect-026-bug',
    category: 'insect',
    filename: '026-bug.svg',
    displayName: 'bug',
    aliases: ['026-bug', 'bug', '昆虫', 'insects'],
    tags: ['昆虫', 'insects']
  },
  {
    id: 'insect-027-silverfish',
    category: 'insect',
    filename: '027-silverfish.svg',
    displayName: 'silverfish',
    aliases: ['027-silverfish', 'silverfish', '昆虫', 'insects'],
    tags: ['昆虫', 'insects']
  },
  {
    id: 'insect-028-fly',
    category: 'insect',
    filename: '028-fly.svg',
    displayName: 'fly',
    aliases: ['028-fly', 'fly', '昆虫', 'insects'],
    tags: ['昆虫', 'insects']
  },
  {
    id: 'insect-029-stag beetle',
    category: 'insect',
    filename: '029-stag beetle.svg',
    displayName: 'stag beetle',
    aliases: ['029-stag beetle', 'stag beetle', 'stagbeetle', '昆虫', 'insects'],
    tags: ['昆虫', 'insects']
  },
  {
    id: 'insect-030-bug',
    category: 'insect',
    filename: '030-bug.svg',
    displayName: 'bug',
    aliases: ['030-bug', 'bug', '昆虫', 'insects'],
    tags: ['昆虫', 'insects']
  },
  {
    id: 'languageLearning-001-flashcard',
    category: 'languageLearning',
    filename: '001-flashcard.svg',
    displayName: 'flashcard',
    aliases: [
      '001-flashcard',
      'flashcard',
      '语言学习',
      'language learning',
      '汽车',
      '车',
      '轿车',
      'car',
      'vehicle',
      '小车',
      'auto'
    ],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-002-conversation',
    category: 'languageLearning',
    filename: '002-conversation.svg',
    displayName: 'conversation',
    aliases: ['002-conversation', 'conversation', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-003-conversation',
    category: 'languageLearning',
    filename: '003-conversation.svg',
    displayName: 'conversation',
    aliases: ['003-conversation', 'conversation', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-004-languages',
    category: 'languageLearning',
    filename: '004-languages.svg',
    displayName: 'languages',
    aliases: ['004-languages', 'languages', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-005-dictionary',
    category: 'languageLearning',
    filename: '005-dictionary.svg',
    displayName: 'dictionary',
    aliases: ['005-dictionary', 'dictionary', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-006-blackboard',
    category: 'languageLearning',
    filename: '006-blackboard.svg',
    displayName: 'blackboard',
    aliases: ['006-blackboard', 'blackboard', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-007-test',
    category: 'languageLearning',
    filename: '007-test.svg',
    displayName: 'test',
    aliases: ['007-test', 'test', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-008-headphone',
    category: 'languageLearning',
    filename: '008-headphone.svg',
    displayName: 'headphone',
    aliases: ['008-headphone', 'headphone', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-009-translation',
    category: 'languageLearning',
    filename: '009-translation.svg',
    displayName: 'translation',
    aliases: ['009-translation', 'translation', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-010-online lesson',
    category: 'languageLearning',
    filename: '010-online lesson.svg',
    displayName: 'online lesson',
    aliases: [
      '010-online lesson',
      'online lesson',
      'onlinelesson',
      '语言学习',
      'language learning'
    ],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-011-writing',
    category: 'languageLearning',
    filename: '011-writing.svg',
    displayName: 'writing',
    aliases: ['011-writing', 'writing', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-012-study',
    category: 'languageLearning',
    filename: '012-study.svg',
    displayName: 'study',
    aliases: ['012-study', 'study', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-013-translate',
    category: 'languageLearning',
    filename: '013-translate.svg',
    displayName: 'translate',
    aliases: ['013-translate', 'translate', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-014-language',
    category: 'languageLearning',
    filename: '014-language.svg',
    displayName: 'language',
    aliases: ['014-language', 'language', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-015-pronunciation',
    category: 'languageLearning',
    filename: '015-pronunciation.svg',
    displayName: 'pronunciation',
    aliases: ['015-pronunciation', 'pronunciation', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-016-online',
    category: 'languageLearning',
    filename: '016-online.svg',
    displayName: 'online',
    aliases: ['016-online', 'online', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-017-listening',
    category: 'languageLearning',
    filename: '017-listening.svg',
    displayName: 'listening',
    aliases: ['017-listening', 'listening', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-018-desk',
    category: 'languageLearning',
    filename: '018-desk.svg',
    displayName: 'desk',
    aliases: ['018-desk', 'desk', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-019-online translator',
    category: 'languageLearning',
    filename: '019-online translator.svg',
    displayName: 'online translator',
    aliases: [
      '019-online translator',
      'online translator',
      'onlinetranslator',
      '语言学习',
      'language learning'
    ],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-020-travel',
    category: 'languageLearning',
    filename: '020-travel.svg',
    displayName: 'travel',
    aliases: ['020-travel', 'travel', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-021-school',
    category: 'languageLearning',
    filename: '021-school.svg',
    displayName: 'school',
    aliases: ['021-school', 'school', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-022-vocabulary',
    category: 'languageLearning',
    filename: '022-vocabulary.svg',
    displayName: 'vocabulary',
    aliases: ['022-vocabulary', 'vocabulary', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-023-countries',
    category: 'languageLearning',
    filename: '023-countries.svg',
    displayName: 'countries',
    aliases: ['023-countries', 'countries', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-024-certificate',
    category: 'languageLearning',
    filename: '024-certificate.svg',
    displayName: 'certificate',
    aliases: ['024-certificate', 'certificate', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-025-head',
    category: 'languageLearning',
    filename: '025-head.svg',
    displayName: 'head',
    aliases: ['025-head', 'head', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-026-books',
    category: 'languageLearning',
    filename: '026-books.svg',
    displayName: 'books',
    aliases: [
      '026-books',
      'books',
      '语言学习',
      'language learning',
      '书籍',
      '资料',
      '文档',
      'documents',
      'book',
      '图书'
    ],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-027-online',
    category: 'languageLearning',
    filename: '027-online.svg',
    displayName: 'online',
    aliases: ['027-online', 'online', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-028-game',
    category: 'languageLearning',
    filename: '028-game.svg',
    displayName: 'game',
    aliases: ['028-game', 'game', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-029-listening',
    category: 'languageLearning',
    filename: '029-listening.svg',
    displayName: 'listening',
    aliases: ['029-listening', 'listening', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-030-book',
    category: 'languageLearning',
    filename: '030-book.svg',
    displayName: 'book',
    aliases: ['030-book', 'book', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-031-conversation',
    category: 'languageLearning',
    filename: '031-conversation.svg',
    displayName: 'conversation',
    aliases: ['031-conversation', 'conversation', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-032-video lesson',
    category: 'languageLearning',
    filename: '032-video lesson.svg',
    displayName: 'video lesson',
    aliases: ['032-video lesson', 'video lesson', 'videolesson', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-033-notebook',
    category: 'languageLearning',
    filename: '033-notebook.svg',
    displayName: 'notebook',
    aliases: ['033-notebook', 'notebook', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-034-online learning',
    category: 'languageLearning',
    filename: '034-online learning.svg',
    displayName: 'online learning',
    aliases: [
      '034-online learning',
      'online learning',
      'onlinelearning',
      '语言学习',
      'language learning'
    ],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-035-braille',
    category: 'languageLearning',
    filename: '035-braille.svg',
    displayName: 'braille',
    aliases: ['035-braille', 'braille', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-036-sign language',
    category: 'languageLearning',
    filename: '036-sign language.svg',
    displayName: 'sign language',
    aliases: [
      '036-sign language',
      'sign language',
      'signlanguage',
      '语言学习',
      'language learning'
    ],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-037-notes',
    category: 'languageLearning',
    filename: '037-notes.svg',
    displayName: 'notes',
    aliases: ['037-notes', 'notes', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-038-slides',
    category: 'languageLearning',
    filename: '038-slides.svg',
    displayName: 'slides',
    aliases: ['038-slides', 'slides', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-039-typewriter',
    category: 'languageLearning',
    filename: '039-typewriter.svg',
    displayName: 'typewriter',
    aliases: ['039-typewriter', 'typewriter', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-040-vocabulary',
    category: 'languageLearning',
    filename: '040-vocabulary.svg',
    displayName: 'vocabulary',
    aliases: ['040-vocabulary', 'vocabulary', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-041-online',
    category: 'languageLearning',
    filename: '041-online.svg',
    displayName: 'online',
    aliases: ['041-online', 'online', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-042-translator',
    category: 'languageLearning',
    filename: '042-translator.svg',
    displayName: 'translator',
    aliases: ['042-translator', 'translator', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-043-business',
    category: 'languageLearning',
    filename: '043-business.svg',
    displayName: 'business',
    aliases: ['043-business', 'business', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-044-certificate',
    category: 'languageLearning',
    filename: '044-certificate.svg',
    displayName: 'certificate',
    aliases: ['044-certificate', 'certificate', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-045-certificate',
    category: 'languageLearning',
    filename: '045-certificate.svg',
    displayName: 'certificate',
    aliases: ['045-certificate', 'certificate', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-046-certificate',
    category: 'languageLearning',
    filename: '046-certificate.svg',
    displayName: 'certificate',
    aliases: ['046-certificate', 'certificate', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-047-materials',
    category: 'languageLearning',
    filename: '047-materials.svg',
    displayName: 'materials',
    aliases: ['047-materials', 'materials', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-048-speaking',
    category: 'languageLearning',
    filename: '048-speaking.svg',
    displayName: 'speaking',
    aliases: ['048-speaking', 'speaking', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-049-classroom',
    category: 'languageLearning',
    filename: '049-classroom.svg',
    displayName: 'classroom',
    aliases: ['049-classroom', 'classroom', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'languageLearning-050-reading',
    category: 'languageLearning',
    filename: '050-reading.svg',
    displayName: 'reading',
    aliases: ['050-reading', 'reading', '语言学习', 'language learning'],
    tags: ['语言学习', 'language learning']
  },
  {
    id: 'magic-001-wizard',
    category: 'magic',
    filename: '001-wizard.svg',
    displayName: 'wizard',
    aliases: ['001-wizard', 'wizard', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-002-magic wand',
    category: 'magic',
    filename: '002-magic wand.svg',
    displayName: 'magic wand',
    aliases: ['002-magic wand', 'magic wand', 'magicwand', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-003-magic book',
    category: 'magic',
    filename: '003-magic book.svg',
    displayName: 'magic book',
    aliases: ['003-magic book', 'magic book', 'magicbook', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-004-magic ball',
    category: 'magic',
    filename: '004-magic ball.svg',
    displayName: 'magic ball',
    aliases: ['004-magic ball', 'magic ball', 'magicball', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-005-potion',
    category: 'magic',
    filename: '005-potion.svg',
    displayName: 'potion',
    aliases: ['005-potion', 'potion', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-006-magic hat',
    category: 'magic',
    filename: '006-magic hat.svg',
    displayName: 'magic hat',
    aliases: ['006-magic hat', 'magic hat', 'magichat', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-007-broom',
    category: 'magic',
    filename: '007-broom.svg',
    displayName: 'broom',
    aliases: ['007-broom', 'broom', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-008-eye',
    category: 'magic',
    filename: '008-eye.svg',
    displayName: 'eye',
    aliases: ['008-eye', 'eye', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-009-magician',
    category: 'magic',
    filename: '009-magician.svg',
    displayName: 'magician',
    aliases: ['009-magician', 'magician', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-010-trick',
    category: 'magic',
    filename: '010-trick.svg',
    displayName: 'trick',
    aliases: ['010-trick', 'trick', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-011-magic mirror',
    category: 'magic',
    filename: '011-magic mirror.svg',
    displayName: 'magic mirror',
    aliases: ['011-magic mirror', 'magic mirror', 'magicmirror', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-012-card',
    category: 'magic',
    filename: '012-card.svg',
    displayName: 'card',
    aliases: [
      '012-card',
      'card',
      '魔法奇幻',
      'magic',
      '汽车',
      '车',
      '轿车',
      'car',
      'vehicle',
      '小车',
      'auto'
    ],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-013-voodoo',
    category: 'magic',
    filename: '013-voodoo.svg',
    displayName: 'voodoo',
    aliases: ['013-voodoo', 'voodoo', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-014-witch hat',
    category: 'magic',
    filename: '014-witch hat.svg',
    displayName: 'witch hat',
    aliases: ['014-witch hat', 'witch hat', 'witchhat', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-016-magic',
    category: 'magic',
    filename: '016-magic.svg',
    displayName: 'magic',
    aliases: ['016-magic', 'magic', '魔法奇幻'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-018-cauldron',
    category: 'magic',
    filename: '018-cauldron.svg',
    displayName: 'cauldron',
    aliases: ['018-cauldron', 'cauldron', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-019-pentagram',
    category: 'magic',
    filename: '019-pentagram.svg',
    displayName: 'pentagram',
    aliases: ['019-pentagram', 'pentagram', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-020-stage',
    category: 'magic',
    filename: '020-stage.svg',
    displayName: 'stage',
    aliases: ['020-stage', 'stage', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-021-moon',
    category: 'magic',
    filename: '021-moon.svg',
    displayName: 'moon',
    aliases: ['021-moon', 'moon', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-022-tarot card',
    category: 'magic',
    filename: '022-tarot card.svg',
    displayName: 'tarot card',
    aliases: [
      '022-tarot card',
      'tarot card',
      'tarotcard',
      '魔法奇幻',
      'magic',
      '汽车',
      '车',
      '轿车',
      'car',
      'vehicle',
      '小车',
      'auto'
    ],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-023-trick',
    category: 'magic',
    filename: '023-trick.svg',
    displayName: 'trick',
    aliases: ['023-trick', 'trick', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-024-spell',
    category: 'magic',
    filename: '024-spell.svg',
    displayName: 'spell',
    aliases: ['024-spell', 'spell', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-025-mask',
    category: 'magic',
    filename: '025-mask.svg',
    displayName: 'mask',
    aliases: ['025-mask', 'mask', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-026-mystery',
    category: 'magic',
    filename: '026-mystery.svg',
    displayName: 'mystery',
    aliases: ['026-mystery', 'mystery', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-027-magic lamp',
    category: 'magic',
    filename: '027-magic lamp.svg',
    displayName: 'magic lamp',
    aliases: ['027-magic lamp', 'magic lamp', 'magiclamp', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-028-magic trick',
    category: 'magic',
    filename: '028-magic trick.svg',
    displayName: 'magic trick',
    aliases: ['028-magic trick', 'magic trick', 'magictrick', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-029-magic trick',
    category: 'magic',
    filename: '029-magic trick.svg',
    displayName: 'magic trick',
    aliases: ['029-magic trick', 'magic trick', 'magictrick', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-030-magic trick',
    category: 'magic',
    filename: '030-magic trick.svg',
    displayName: 'magic trick',
    aliases: ['030-magic trick', 'magic trick', 'magictrick', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-031-magic',
    category: 'magic',
    filename: '031-magic.svg',
    displayName: 'magic',
    aliases: ['031-magic', 'magic', '魔法奇幻'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-032-hypnotize',
    category: 'magic',
    filename: '032-hypnotize.svg',
    displayName: 'hypnotize',
    aliases: ['032-hypnotize', 'hypnotize', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-033-magical',
    category: 'magic',
    filename: '033-magical.svg',
    displayName: 'magical',
    aliases: ['033-magical', 'magical', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-034-recipe',
    category: 'magic',
    filename: '034-recipe.svg',
    displayName: 'recipe',
    aliases: ['034-recipe', 'recipe', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-035-magic box',
    category: 'magic',
    filename: '035-magic box.svg',
    displayName: 'magic box',
    aliases: ['035-magic box', 'magic box', 'magicbox', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-036-ring',
    category: 'magic',
    filename: '036-ring.svg',
    displayName: 'ring',
    aliases: ['036-ring', 'ring', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-037-skull',
    category: 'magic',
    filename: '037-skull.svg',
    displayName: 'skull',
    aliases: ['037-skull', 'skull', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-038-ghost',
    category: 'magic',
    filename: '038-ghost.svg',
    displayName: 'ghost',
    aliases: ['038-ghost', 'ghost', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-039-necklace',
    category: 'magic',
    filename: '039-necklace.svg',
    displayName: 'necklace',
    aliases: ['039-necklace', 'necklace', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-040-scroll',
    category: 'magic',
    filename: '040-scroll.svg',
    displayName: 'scroll',
    aliases: ['040-scroll', 'scroll', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-041-celtic',
    category: 'magic',
    filename: '041-celtic.svg',
    displayName: 'celtic',
    aliases: ['041-celtic', 'celtic', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-042-scepter',
    category: 'magic',
    filename: '042-scepter.svg',
    displayName: 'scepter',
    aliases: ['042-scepter', 'scepter', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-043-owl',
    category: 'magic',
    filename: '043-owl.svg',
    displayName: 'owl',
    aliases: ['043-owl', 'owl', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-044-ticket',
    category: 'magic',
    filename: '044-ticket.svg',
    displayName: 'ticket',
    aliases: ['044-ticket', 'ticket', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-045-magic dust',
    category: 'magic',
    filename: '045-magic dust.svg',
    displayName: 'magic dust',
    aliases: ['045-magic dust', 'magic dust', 'magicdust', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-046-chest',
    category: 'magic',
    filename: '046-chest.svg',
    displayName: 'chest',
    aliases: ['046-chest', 'chest', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-047-gem',
    category: 'magic',
    filename: '047-gem.svg',
    displayName: 'gem',
    aliases: ['047-gem', 'gem', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-048-rune',
    category: 'magic',
    filename: '048-rune.svg',
    displayName: 'rune',
    aliases: ['048-rune', 'rune', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-049-magic trick',
    category: 'magic',
    filename: '049-magic trick.svg',
    displayName: 'magic trick',
    aliases: ['049-magic trick', 'magic trick', 'magictrick', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'magic-050-magic trick',
    category: 'magic',
    filename: '050-magic trick.svg',
    displayName: 'magic trick',
    aliases: ['050-magic trick', 'magic trick', 'magictrick', '魔法奇幻', 'magic'],
    tags: ['魔法奇幻', 'magic']
  },
  {
    id: 'music-乐谱',
    category: 'music',
    filename: '乐谱.svg',
    displayName: '乐谱',
    aliases: ['乐谱', '音乐', 'music'],
    tags: ['音乐', 'music', '娱乐', 'entertainment']
  },
  {
    id: 'music-乐谱1',
    category: 'music',
    filename: '乐谱1.svg',
    displayName: '乐谱1',
    aliases: ['乐谱1', '音乐', 'music'],
    tags: ['音乐', 'music', '娱乐', 'entertainment']
  },
  {
    id: 'music-停止',
    category: 'music',
    filename: '停止.svg',
    displayName: '停止',
    aliases: ['停止', '音乐', 'music'],
    tags: ['音乐', 'music', '娱乐', 'entertainment']
  },
  {
    id: 'music-吉他',
    category: 'music',
    filename: '吉他.svg',
    displayName: '吉他',
    aliases: ['吉他', '音乐', 'music'],
    tags: ['音乐', 'music', '娱乐', 'entertainment']
  },
  {
    id: 'music-喇叭',
    category: 'music',
    filename: '喇叭.svg',
    displayName: '喇叭',
    aliases: ['喇叭', '音乐', 'music'],
    tags: ['音乐', 'music', '娱乐', 'entertainment']
  },
  {
    id: 'music-播放',
    category: 'music',
    filename: '播放.svg',
    displayName: '播放',
    aliases: ['播放', '音乐', 'music'],
    tags: ['音乐', 'music', '娱乐', 'entertainment']
  },
  {
    id: 'music-播放1',
    category: 'music',
    filename: '播放1.svg',
    displayName: '播放1',
    aliases: ['播放1', '音乐', 'music'],
    tags: ['音乐', 'music', '娱乐', 'entertainment']
  },
  {
    id: 'music-收音机',
    category: 'music',
    filename: '收音机.svg',
    displayName: '收音机',
    aliases: ['收音机', '音乐', 'music'],
    tags: ['音乐', 'music', '娱乐', 'entertainment']
  },
  {
    id: 'music-耳机',
    category: 'music',
    filename: '耳机.svg',
    displayName: '耳机',
    aliases: ['耳机', '音乐', 'music'],
    tags: ['音乐', 'music', '娱乐', 'entertainment']
  },
  {
    id: 'music-节奏',
    category: 'music',
    filename: '节奏.svg',
    displayName: '节奏',
    aliases: ['节奏', '音乐', 'music'],
    tags: ['音乐', 'music', '娱乐', 'entertainment']
  },
  {
    id: 'music-萨克斯',
    category: 'music',
    filename: '萨克斯.svg',
    displayName: '萨克斯',
    aliases: ['萨克斯', '音乐', 'music'],
    tags: ['音乐', 'music', '娱乐', 'entertainment']
  },
  {
    id: 'music-音乐',
    category: 'music',
    filename: '音乐.svg',
    displayName: '音乐',
    aliases: ['音乐', 'music'],
    tags: ['音乐', 'music', '娱乐', 'entertainment']
  },
  {
    id: 'music-音乐1',
    category: 'music',
    filename: '音乐1.svg',
    displayName: '音乐1',
    aliases: ['音乐1', '音乐', 'music'],
    tags: ['音乐', 'music', '娱乐', 'entertainment']
  },
  {
    id: 'music-音乐2',
    category: 'music',
    filename: '音乐2.svg',
    displayName: '音乐2',
    aliases: ['音乐2', '音乐', 'music'],
    tags: ['音乐', 'music', '娱乐', 'entertainment']
  },
  {
    id: 'music-音乐列表',
    category: 'music',
    filename: '音乐列表.svg',
    displayName: '音乐列表',
    aliases: ['音乐列表', '音乐', 'music'],
    tags: ['音乐', 'music', '娱乐', 'entertainment']
  },
  {
    id: 'music-麦克风',
    category: 'music',
    filename: '麦克风.svg',
    displayName: '麦克风',
    aliases: ['麦克风', '音乐', 'music'],
    tags: ['音乐', 'music', '娱乐', 'entertainment']
  },
  {
    id: 'music-鼓',
    category: 'music',
    filename: '鼓.svg',
    displayName: '鼓',
    aliases: ['鼓', '音乐', 'music'],
    tags: ['音乐', 'music', '娱乐', 'entertainment']
  },
  {
    id: 'nature-001-acorn',
    category: 'nature',
    filename: '001-acorn.svg',
    displayName: 'acorn',
    aliases: ['001-acorn', 'acorn', '自然', 'nature'],
    tags: ['自然', 'nature']
  },
  {
    id: 'nature-002-moon',
    category: 'nature',
    filename: '002-moon.svg',
    displayName: 'moon',
    aliases: ['002-moon', 'moon', '自然', 'nature'],
    tags: ['自然', 'nature']
  },
  {
    id: 'nature-003-wood',
    category: 'nature',
    filename: '003-wood.svg',
    displayName: 'wood',
    aliases: ['003-wood', 'wood', '自然', 'nature'],
    tags: ['自然', 'nature']
  },
  {
    id: 'nature-004-bee',
    category: 'nature',
    filename: '004-bee.svg',
    displayName: 'bee',
    aliases: ['004-bee', 'bee', '自然', 'nature'],
    tags: ['自然', 'nature']
  },
  {
    id: 'nature-005-tree',
    category: 'nature',
    filename: '005-tree.svg',
    displayName: 'tree',
    aliases: ['005-tree', 'tree', '自然', 'nature'],
    tags: ['自然', 'nature']
  },
  {
    id: 'nature-006-mushroom',
    category: 'nature',
    filename: '006-mushroom.svg',
    displayName: 'mushroom',
    aliases: ['006-mushroom', 'mushroom', '自然', 'nature'],
    tags: ['自然', 'nature']
  },
  {
    id: 'nature-007-world',
    category: 'nature',
    filename: '007-world.svg',
    displayName: 'world',
    aliases: ['007-world', 'world', '自然', 'nature'],
    tags: ['自然', 'nature']
  },
  {
    id: 'nature-008-leaf',
    category: 'nature',
    filename: '008-leaf.svg',
    displayName: 'leaf',
    aliases: ['008-leaf', 'leaf', '自然', 'nature'],
    tags: ['自然', 'nature']
  },
  {
    id: 'nature-009-leaf',
    category: 'nature',
    filename: '009-leaf.svg',
    displayName: 'leaf',
    aliases: ['009-leaf', 'leaf', '自然', 'nature'],
    tags: ['自然', 'nature']
  },
  {
    id: 'nature-010-tulip',
    category: 'nature',
    filename: '010-tulip.svg',
    displayName: 'tulip',
    aliases: ['010-tulip', 'tulip', '自然', 'nature'],
    tags: ['自然', 'nature']
  },
  {
    id: 'nature-011-sunflower',
    category: 'nature',
    filename: '011-sunflower.svg',
    displayName: 'sunflower',
    aliases: [
      '011-sunflower',
      'sunflower',
      '自然',
      'nature',
      '太阳',
      '晴天',
      'sun',
      'sunny',
      '阳光',
      'sunshine'
    ],
    tags: ['自然', 'nature']
  },
  {
    id: 'nature-012-plant',
    category: 'nature',
    filename: '012-plant.svg',
    displayName: 'plant',
    aliases: ['012-plant', 'plant', '自然', 'nature'],
    tags: ['自然', 'nature']
  },
  {
    id: 'nature-013-fields',
    category: 'nature',
    filename: '013-fields.svg',
    displayName: 'fields',
    aliases: ['013-fields', 'fields', '自然', 'nature'],
    tags: ['自然', 'nature']
  },
  {
    id: 'nature-014-cactus',
    category: 'nature',
    filename: '014-cactus.svg',
    displayName: 'cactus',
    aliases: ['014-cactus', 'cactus', '自然', 'nature'],
    tags: ['自然', 'nature']
  },
  {
    id: 'nature-015-cloud',
    category: 'nature',
    filename: '015-cloud.svg',
    displayName: 'cloud',
    aliases: ['015-cloud', 'cloud', '自然', 'nature', '云朵', '云', 'cloudy', '阴天', 'overcast'],
    tags: ['自然', 'nature']
  },
  {
    id: 'nature-016-desert',
    category: 'nature',
    filename: '016-desert.svg',
    displayName: 'desert',
    aliases: ['016-desert', 'desert', '自然', 'nature'],
    tags: ['自然', 'nature']
  },
  {
    id: 'nature-017-sun',
    category: 'nature',
    filename: '017-sun.svg',
    displayName: 'sun',
    aliases: ['017-sun', 'sun', '自然', 'nature', '太阳', '晴天', 'sunny', '阳光', 'sunshine'],
    tags: ['自然', 'nature']
  },
  {
    id: 'nature-018-forest',
    category: 'nature',
    filename: '018-forest.svg',
    displayName: 'forest',
    aliases: ['018-forest', 'forest', '自然', 'nature'],
    tags: ['自然', 'nature']
  },
  {
    id: 'nature-019-ladybug',
    category: 'nature',
    filename: '019-ladybug.svg',
    displayName: 'ladybug',
    aliases: ['019-ladybug', 'ladybug', '自然', 'nature'],
    tags: ['自然', 'nature']
  },
  {
    id: 'nature-020-rainbow',
    category: 'nature',
    filename: '020-rainbow.svg',
    displayName: 'rainbow',
    aliases: [
      '020-rainbow',
      'rainbow',
      '自然',
      'nature',
      '下雨',
      '雨天',
      'rain',
      'rainy',
      '降雨',
      'wet'
    ],
    tags: ['自然', 'nature']
  },
  {
    id: 'nature-021-water',
    category: 'nature',
    filename: '021-water.svg',
    displayName: 'water',
    aliases: ['021-water', 'water', '自然', 'nature'],
    tags: ['自然', 'nature']
  },
  {
    id: 'nature-022-earth',
    category: 'nature',
    filename: '022-earth.svg',
    displayName: 'earth',
    aliases: ['022-earth', 'earth', '自然', 'nature'],
    tags: ['自然', 'nature']
  },
  {
    id: 'nature-023-snow',
    category: 'nature',
    filename: '023-snow.svg',
    displayName: 'snow',
    aliases: ['023-snow', 'snow', '自然', 'nature', '下雪', '雪花', 'snowy', '雪', 'snowflake'],
    tags: ['自然', 'nature']
  },
  {
    id: 'nature-024-mountain',
    category: 'nature',
    filename: '024-mountain.svg',
    displayName: 'mountain',
    aliases: ['024-mountain', 'mountain', '自然', 'nature'],
    tags: ['自然', 'nature']
  },
  {
    id: 'nature-025-grass',
    category: 'nature',
    filename: '025-grass.svg',
    displayName: 'grass',
    aliases: ['025-grass', 'grass', '自然', 'nature'],
    tags: ['自然', 'nature']
  },
  {
    id: 'nature-026-rain',
    category: 'nature',
    filename: '026-rain.svg',
    displayName: 'rain',
    aliases: ['026-rain', 'rain', '自然', 'nature', '下雨', '雨天', 'rainy', '降雨', 'wet'],
    tags: ['自然', 'nature']
  },
  {
    id: 'nature-027-fish',
    category: 'nature',
    filename: '027-fish.svg',
    displayName: 'fish',
    aliases: ['027-fish', 'fish', '自然', 'nature'],
    tags: ['自然', 'nature']
  },
  {
    id: 'nature-028-tree',
    category: 'nature',
    filename: '028-tree.svg',
    displayName: 'tree',
    aliases: ['028-tree', 'tree', '自然', 'nature'],
    tags: ['自然', 'nature']
  },
  {
    id: 'nature-029-volcano',
    category: 'nature',
    filename: '029-volcano.svg',
    displayName: 'volcano',
    aliases: ['029-volcano', 'volcano', '自然', 'nature'],
    tags: ['自然', 'nature']
  },
  {
    id: 'nature-030-beach',
    category: 'nature',
    filename: '030-beach.svg',
    displayName: 'beach',
    aliases: ['030-beach', 'beach', '自然', 'nature'],
    tags: ['自然', 'nature']
  },
  {
    id: 'ocean-001-fish',
    category: 'ocean',
    filename: '001-fish.svg',
    displayName: 'fish',
    aliases: ['001-fish', 'fish', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-002-clam',
    category: 'ocean',
    filename: '002-clam.svg',
    displayName: 'clam',
    aliases: ['002-clam', 'clam', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-003-seaweed',
    category: 'ocean',
    filename: '003-seaweed.svg',
    displayName: 'seaweed',
    aliases: ['003-seaweed', 'seaweed', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-004-butterflyfish',
    category: 'ocean',
    filename: '004-butterflyfish.svg',
    displayName: 'butterflyfish',
    aliases: ['004-butterflyfish', 'butterflyfish', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-005-anchor',
    category: 'ocean',
    filename: '005-anchor.svg',
    displayName: 'anchor',
    aliases: ['005-anchor', 'anchor', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-006-clown loach',
    category: 'ocean',
    filename: '006-clown loach.svg',
    displayName: 'clown loach',
    aliases: ['006-clown loach', 'clown loach', 'clownloach', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-007-butterflyfish',
    category: 'ocean',
    filename: '007-butterflyfish.svg',
    displayName: 'butterflyfish',
    aliases: ['007-butterflyfish', 'butterflyfish', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-008-lifebuoy',
    category: 'ocean',
    filename: '008-lifebuoy.svg',
    displayName: 'lifebuoy',
    aliases: ['008-lifebuoy', 'lifebuoy', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-009-dolphin',
    category: 'ocean',
    filename: '009-dolphin.svg',
    displayName: 'dolphin',
    aliases: ['009-dolphin', 'dolphin', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-010-blue tang fish',
    category: 'ocean',
    filename: '010-blue tang fish.svg',
    displayName: 'blue tang fish',
    aliases: ['010-blue tang fish', 'blue tang fish', 'bluetangfish', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-011-banggai cardinalfish',
    category: 'ocean',
    filename: '011-banggai cardinalfish.svg',
    displayName: 'banggai cardinalfish',
    aliases: [
      '011-banggai cardinalfish',
      'banggai cardinalfish',
      'banggaicardinalfish',
      '海洋',
      'ocean',
      '汽车',
      '车',
      '轿车',
      'car',
      'vehicle',
      '小车',
      'auto'
    ],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-013-rockfish',
    category: 'ocean',
    filename: '013-rockfish.svg',
    displayName: 'rockfish',
    aliases: ['013-rockfish', 'rockfish', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-015-jewfish',
    category: 'ocean',
    filename: '015-jewfish.svg',
    displayName: 'jewfish',
    aliases: ['015-jewfish', 'jewfish', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-016-seahorse',
    category: 'ocean',
    filename: '016-seahorse.svg',
    displayName: 'seahorse',
    aliases: ['016-seahorse', 'seahorse', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-017-fish',
    category: 'ocean',
    filename: '017-fish.svg',
    displayName: 'fish',
    aliases: ['017-fish', 'fish', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-018-angelfish',
    category: 'ocean',
    filename: '018-angelfish.svg',
    displayName: 'angelfish',
    aliases: ['018-angelfish', 'angelfish', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-019-ship wheel',
    category: 'ocean',
    filename: '019-ship wheel.svg',
    displayName: 'ship wheel',
    aliases: ['019-ship wheel', 'ship wheel', 'shipwheel', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-020-shell',
    category: 'ocean',
    filename: '020-shell.svg',
    displayName: 'shell',
    aliases: ['020-shell', 'shell', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-021-coral',
    category: 'ocean',
    filename: '021-coral.svg',
    displayName: 'coral',
    aliases: ['021-coral', 'coral', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-022-angelfish',
    category: 'ocean',
    filename: '022-angelfish.svg',
    displayName: 'angelfish',
    aliases: ['022-angelfish', 'angelfish', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-023-starfish',
    category: 'ocean',
    filename: '023-starfish.svg',
    displayName: 'starfish',
    aliases: ['023-starfish', 'starfish', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-024-catfish',
    category: 'ocean',
    filename: '024-catfish.svg',
    displayName: 'catfish',
    aliases: ['024-catfish', 'catfish', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-025-anglerfish',
    category: 'ocean',
    filename: '025-anglerfish.svg',
    displayName: 'anglerfish',
    aliases: ['025-anglerfish', 'anglerfish', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-026-fish',
    category: 'ocean',
    filename: '026-fish.svg',
    displayName: 'fish',
    aliases: ['026-fish', 'fish', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-027-moorish idol',
    category: 'ocean',
    filename: '027-moorish idol.svg',
    displayName: 'moorish idol',
    aliases: ['027-moorish idol', 'moorish idol', 'moorishidol', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-028-anchor',
    category: 'ocean',
    filename: '028-anchor.svg',
    displayName: 'anchor',
    aliases: ['028-anchor', 'anchor', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-029-puffer fish',
    category: 'ocean',
    filename: '029-puffer fish.svg',
    displayName: 'puffer fish',
    aliases: ['029-puffer fish', 'puffer fish', 'pufferfish', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-030-coral',
    category: 'ocean',
    filename: '030-coral.svg',
    displayName: 'coral',
    aliases: ['030-coral', 'coral', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-031-shark',
    category: 'ocean',
    filename: '031-shark.svg',
    displayName: 'shark',
    aliases: ['031-shark', 'shark', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-032-clownfish',
    category: 'ocean',
    filename: '032-clownfish.svg',
    displayName: 'clownfish',
    aliases: ['032-clownfish', 'clownfish', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-033-flying fish',
    category: 'ocean',
    filename: '033-flying fish.svg',
    displayName: 'flying fish',
    aliases: ['033-flying fish', 'flying fish', 'flyingfish', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-034-shell',
    category: 'ocean',
    filename: '034-shell.svg',
    displayName: 'shell',
    aliases: ['034-shell', 'shell', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-035-orca',
    category: 'ocean',
    filename: '035-orca.svg',
    displayName: 'orca',
    aliases: ['035-orca', 'orca', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-036-fish',
    category: 'ocean',
    filename: '036-fish.svg',
    displayName: 'fish',
    aliases: ['036-fish', 'fish', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-037-fish',
    category: 'ocean',
    filename: '037-fish.svg',
    displayName: 'fish',
    aliases: ['037-fish', 'fish', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-038-compass',
    category: 'ocean',
    filename: '038-compass.svg',
    displayName: 'compass',
    aliases: ['038-compass', 'compass', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-039-fish',
    category: 'ocean',
    filename: '039-fish.svg',
    displayName: 'fish',
    aliases: ['039-fish', 'fish', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-040-life jacket',
    category: 'ocean',
    filename: '040-life jacket.svg',
    displayName: 'life jacket',
    aliases: ['040-life jacket', 'life jacket', 'lifejacket', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-041-manta ray',
    category: 'ocean',
    filename: '041-manta ray.svg',
    displayName: 'manta ray',
    aliases: ['041-manta ray', 'manta ray', 'mantaray', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-042-sunset',
    category: 'ocean',
    filename: '042-sunset.svg',
    displayName: 'sunset',
    aliases: [
      '042-sunset',
      'sunset',
      '海洋',
      'ocean',
      '太阳',
      '晴天',
      'sun',
      'sunny',
      '阳光',
      'sunshine'
    ],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-043-shark',
    category: 'ocean',
    filename: '043-shark.svg',
    displayName: 'shark',
    aliases: ['043-shark', 'shark', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-044-Shrimp',
    category: 'ocean',
    filename: '044-Shrimp.svg',
    displayName: 'Shrimp',
    aliases: ['044-Shrimp', 'Shrimp', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-045-neon tetra',
    category: 'ocean',
    filename: '045-neon tetra.svg',
    displayName: 'neon tetra',
    aliases: ['045-neon tetra', 'neon tetra', 'neontetra', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-046-guppy',
    category: 'ocean',
    filename: '046-guppy.svg',
    displayName: 'guppy',
    aliases: ['046-guppy', 'guppy', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-047-squid',
    category: 'ocean',
    filename: '047-squid.svg',
    displayName: 'squid',
    aliases: ['047-squid', 'squid', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-048-conch shell',
    category: 'ocean',
    filename: '048-conch shell.svg',
    displayName: 'conch shell',
    aliases: ['048-conch shell', 'conch shell', 'conchshell', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-049-diving',
    category: 'ocean',
    filename: '049-diving.svg',
    displayName: 'diving',
    aliases: ['049-diving', 'diving', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'ocean-050-porthole',
    category: 'ocean',
    filename: '050-porthole.svg',
    displayName: 'porthole',
    aliases: ['050-porthole', 'porthole', '海洋', 'ocean'],
    tags: ['海洋', 'ocean']
  },
  {
    id: 'plants-仙人掌',
    category: 'plants',
    filename: '仙人掌.svg',
    displayName: '仙人掌',
    aliases: ['仙人掌', '植物', 'plants'],
    tags: ['植物', 'plants']
  },
  {
    id: 'plants-仙人球',
    category: 'plants',
    filename: '仙人球.svg',
    displayName: '仙人球',
    aliases: ['仙人球', '植物', 'plants'],
    tags: ['植物', 'plants']
  },
  {
    id: 'plants-大树',
    category: 'plants',
    filename: '大树.svg',
    displayName: '大树',
    aliases: ['大树', '植物', 'plants'],
    tags: ['植物', 'plants']
  },
  {
    id: 'plants-幸运草',
    category: 'plants',
    filename: '幸运草.svg',
    displayName: '幸运草',
    aliases: ['幸运草', '植物', 'plants'],
    tags: ['植物', 'plants']
  },
  {
    id: 'plants-松果',
    category: 'plants',
    filename: '松果.svg',
    displayName: '松果',
    aliases: ['松果', '植物', 'plants'],
    tags: ['植物', 'plants']
  },
  {
    id: 'plants-果树',
    category: 'plants',
    filename: '果树.svg',
    displayName: '果树',
    aliases: ['果树', '植物', 'plants'],
    tags: ['植物', 'plants']
  },
  {
    id: 'plants-柿子',
    category: 'plants',
    filename: '柿子.svg',
    displayName: '柿子',
    aliases: ['柿子', '植物', 'plants'],
    tags: ['植物', 'plants']
  },
  {
    id: 'plants-树木',
    category: 'plants',
    filename: '树木.svg',
    displayName: '树木',
    aliases: ['树木', '植物', 'plants'],
    tags: ['植物', 'plants']
  },
  {
    id: 'plants-椰子树',
    category: 'plants',
    filename: '椰子树.svg',
    displayName: '椰子树',
    aliases: ['椰子树', '植物', 'plants'],
    tags: ['植物', 'plants']
  },
  {
    id: 'plants-樱桃水果',
    category: 'plants',
    filename: '樱桃水果.svg',
    displayName: '樱桃水果',
    aliases: ['樱桃水果', '植物', 'plants'],
    tags: ['植物', 'plants']
  },
  {
    id: 'plants-芦荟',
    category: 'plants',
    filename: '芦荟.svg',
    displayName: '芦荟',
    aliases: ['芦荟', '植物', 'plants'],
    tags: ['植物', 'plants']
  },
  {
    id: 'plants-花朵',
    category: 'plants',
    filename: '花朵.svg',
    displayName: '花朵',
    aliases: ['花朵', '植物', 'plants'],
    tags: ['植物', 'plants']
  },
  {
    id: 'plants-花朵1',
    category: 'plants',
    filename: '花朵1.svg',
    displayName: '花朵1',
    aliases: ['花朵1', '植物', 'plants'],
    tags: ['植物', 'plants']
  },
  {
    id: 'plants-花朵盆栽',
    category: 'plants',
    filename: '花朵盆栽.svg',
    displayName: '花朵盆栽',
    aliases: ['花朵盆栽', '植物', 'plants'],
    tags: ['植物', 'plants']
  },
  {
    id: 'plants-草丛',
    category: 'plants',
    filename: '草丛.svg',
    displayName: '草丛',
    aliases: ['草丛', '植物', 'plants'],
    tags: ['植物', 'plants']
  },
  {
    id: 'plants-蘑菇',
    category: 'plants',
    filename: '蘑菇.svg',
    displayName: '蘑菇',
    aliases: ['蘑菇', '植物', 'plants'],
    tags: ['植物', 'plants']
  },
  {
    id: 'plants-蘑菇1',
    category: 'plants',
    filename: '蘑菇1.svg',
    displayName: '蘑菇1',
    aliases: ['蘑菇1', '植物', 'plants'],
    tags: ['植物', 'plants']
  },
  {
    id: 'plants-银杏树',
    category: 'plants',
    filename: '银杏树.svg',
    displayName: '银杏树',
    aliases: ['银杏树', '植物', 'plants'],
    tags: ['植物', 'plants']
  },
  {
    id: 'study-astronomy',
    category: 'study',
    filename: 'astronomy.svg',
    displayName: 'astronomy',
    aliases: ['astronomy', '学习', 'study'],
    tags: ['学习', 'study', '教育', 'education']
  },
  {
    id: 'study-books',
    category: 'study',
    filename: 'books.svg',
    displayName: 'books',
    aliases: ['books', '学习', 'study', '书籍', '资料', '文档', 'documents', 'book', '图书'],
    tags: ['学习', 'study', '教育', 'education']
  },
  {
    id: 'study-calculator',
    category: 'study',
    filename: 'calculator.svg',
    displayName: 'calculator',
    aliases: ['calculator', '学习', 'study'],
    tags: ['学习', 'study', '教育', 'education']
  },
  {
    id: 'study-expirement',
    category: 'study',
    filename: 'expirement.svg',
    displayName: 'expirement',
    aliases: ['expirement', '学习', 'study'],
    tags: ['学习', 'study', '教育', 'education']
  },
  {
    id: 'study-flask',
    category: 'study',
    filename: 'flask.svg',
    displayName: 'flask',
    aliases: ['flask', '学习', 'study'],
    tags: ['学习', 'study', '教育', 'education']
  },
  {
    id: 'study-idea',
    category: 'study',
    filename: 'idea.svg',
    displayName: 'idea',
    aliases: ['idea', '学习', 'study'],
    tags: ['学习', 'study', '教育', 'education']
  },
  {
    id: 'study-incubator',
    category: 'study',
    filename: 'incubator.svg',
    displayName: 'incubator',
    aliases: ['incubator', '学习', 'study'],
    tags: ['学习', 'study', '教育', 'education']
  },
  {
    id: 'study-maths',
    category: 'study',
    filename: 'maths.svg',
    displayName: 'maths',
    aliases: ['maths', '学习', 'study'],
    tags: ['学习', 'study', '教育', 'education']
  },
  {
    id: 'study-medal',
    category: 'study',
    filename: 'medal.svg',
    displayName: 'medal',
    aliases: ['medal', '学习', 'study'],
    tags: ['学习', 'study', '教育', 'education']
  },
  {
    id: 'study-microscope',
    category: 'study',
    filename: 'microscope.svg',
    displayName: 'microscope',
    aliases: ['microscope', '学习', 'study'],
    tags: ['学习', 'study', '教育', 'education']
  },
  {
    id: 'study-paintPalette',
    category: 'study',
    filename: 'paintPalette.svg',
    displayName: 'paintPalette',
    aliases: ['paintPalette', '学习', 'study'],
    tags: ['学习', 'study', '教育', 'education']
  },
  {
    id: 'study-pnecil',
    category: 'study',
    filename: 'pnecil.svg',
    displayName: 'pnecil',
    aliases: ['pnecil', '学习', 'study'],
    tags: ['学习', 'study', '教育', 'education']
  },
  {
    id: 'study-report',
    category: 'study',
    filename: 'report.svg',
    displayName: 'report',
    aliases: ['report', '学习', 'study'],
    tags: ['学习', 'study', '教育', 'education']
  },
  {
    id: 'study-testCubes',
    category: 'study',
    filename: 'testCubes.svg',
    displayName: 'testCubes',
    aliases: ['testCubes', '学习', 'study'],
    tags: ['学习', 'study', '教育', 'education']
  },
  {
    id: 'transportation-公交车卡车',
    category: 'transportation',
    filename: '公交车卡车.svg',
    displayName: '公交车卡车',
    aliases: ['公交车卡车', '交通工具', 'transportation'],
    tags: ['交通工具', 'transportation', '交通', '出行', 'transport', 'vehicle']
  },
  {
    id: 'transportation-出租车',
    category: 'transportation',
    filename: '出租车.svg',
    displayName: '出租车',
    aliases: ['出租车', '交通工具', 'transportation'],
    tags: ['交通工具', 'transportation', '交通', '出行', 'transport', 'vehicle']
  },
  {
    id: 'transportation-出租车1',
    category: 'transportation',
    filename: '出租车1.svg',
    displayName: '出租车1',
    aliases: ['出租车1', '交通工具', 'transportation'],
    tags: ['交通工具', 'transportation', '交通', '出行', 'transport', 'vehicle']
  },
  {
    id: 'transportation-加油站',
    category: 'transportation',
    filename: '加油站.svg',
    displayName: '加油站',
    aliases: ['加油站', '交通工具', 'transportation'],
    tags: ['交通工具', 'transportation', '交通', '出行', 'transport', 'vehicle']
  },
  {
    id: 'transportation-发动机',
    category: 'transportation',
    filename: '发动机.svg',
    displayName: '发动机',
    aliases: ['发动机', '交通工具', 'transportation'],
    tags: ['交通工具', 'transportation', '交通', '出行', 'transport', 'vehicle']
  },
  {
    id: 'transportation-外卖',
    category: 'transportation',
    filename: '外卖.svg',
    displayName: '外卖',
    aliases: ['外卖', '交通工具', 'transportation'],
    tags: ['交通工具', 'transportation', '交通', '出行', 'transport', 'vehicle']
  },
  {
    id: 'transportation-大巴车',
    category: 'transportation',
    filename: '大巴车.svg',
    displayName: '大巴车',
    aliases: ['大巴车', '交通工具', 'transportation'],
    tags: ['交通工具', 'transportation', '交通', '出行', 'transport', 'vehicle']
  },
  {
    id: 'transportation-婴儿车',
    category: 'transportation',
    filename: '婴儿车.svg',
    displayName: '婴儿车',
    aliases: ['婴儿车', '交通工具', 'transportation'],
    tags: ['交通工具', 'transportation', '交通', '出行', 'transport', 'vehicle']
  },
  {
    id: 'transportation-小汽车',
    category: 'transportation',
    filename: '小汽车.svg',
    displayName: '小汽车',
    aliases: ['小汽车', '交通工具', 'transportation'],
    tags: ['交通工具', 'transportation', '交通', '出行', 'transport', 'vehicle']
  },
  {
    id: 'transportation-小轿车',
    category: 'transportation',
    filename: '小轿车.svg',
    displayName: '小轿车',
    aliases: ['小轿车', '交通工具', 'transportation'],
    tags: ['交通工具', 'transportation', '交通', '出行', 'transport', 'vehicle']
  },
  {
    id: 'transportation-巴士',
    category: 'transportation',
    filename: '巴士.svg',
    displayName: '巴士',
    aliases: ['巴士', '交通工具', 'transportation'],
    tags: ['交通工具', 'transportation', '交通', '出行', 'transport', 'vehicle']
  },
  {
    id: 'transportation-引擎发动机',
    category: 'transportation',
    filename: '引擎发动机.svg',
    displayName: '引擎发动机',
    aliases: ['引擎发动机', '交通工具', 'transportation'],
    tags: ['交通工具', 'transportation', '交通', '出行', 'transport', 'vehicle']
  },
  {
    id: 'transportation-快递',
    category: 'transportation',
    filename: '快递.svg',
    displayName: '快递',
    aliases: ['快递', '交通工具', 'transportation'],
    tags: ['交通工具', 'transportation', '交通', '出行', 'transport', 'vehicle']
  },
  {
    id: 'transportation-指南针',
    category: 'transportation',
    filename: '指南针.svg',
    displayName: '指南针',
    aliases: ['指南针', '交通工具', 'transportation'],
    tags: ['交通工具', 'transportation', '交通', '出行', 'transport', 'vehicle']
  },
  {
    id: 'transportation-挖掘机',
    category: 'transportation',
    filename: '挖掘机.svg',
    displayName: '挖掘机',
    aliases: ['挖掘机', '交通工具', 'transportation'],
    tags: ['交通工具', 'transportation', '交通', '出行', 'transport', 'vehicle']
  },
  {
    id: 'transportation-摩托车',
    category: 'transportation',
    filename: '摩托车.svg',
    displayName: '摩托车',
    aliases: ['摩托车', '交通工具', 'transportation'],
    tags: ['交通工具', 'transportation', '交通', '出行', 'transport', 'vehicle']
  },
  {
    id: 'transportation-方向盘',
    category: 'transportation',
    filename: '方向盘.svg',
    displayName: '方向盘',
    aliases: ['方向盘', '交通工具', 'transportation'],
    tags: ['交通工具', 'transportation', '交通', '出行', 'transport', 'vehicle']
  },
  {
    id: 'transportation-热气球',
    category: 'transportation',
    filename: '热气球.svg',
    displayName: '热气球',
    aliases: ['热气球', '交通工具', 'transportation'],
    tags: ['交通工具', 'transportation', '交通', '出行', 'transport', 'vehicle']
  },
  {
    id: 'transportation-电瓶',
    category: 'transportation',
    filename: '电瓶.svg',
    displayName: '电瓶',
    aliases: ['电瓶', '交通工具', 'transportation'],
    tags: ['交通工具', 'transportation', '交通', '出行', 'transport', 'vehicle']
  },
  {
    id: 'transportation-直升飞机',
    category: 'transportation',
    filename: '直升飞机.svg',
    displayName: '直升飞机',
    aliases: ['直升飞机', '交通工具', 'transportation'],
    tags: ['交通工具', 'transportation', '交通', '出行', 'transport', 'vehicle']
  },
  {
    id: 'transportation-红绿灯',
    category: 'transportation',
    filename: '红绿灯.svg',
    displayName: '红绿灯',
    aliases: ['红绿灯', '交通工具', 'transportation'],
    tags: ['交通工具', 'transportation', '交通', '出行', 'transport', 'vehicle']
  },
  {
    id: 'transportation-自行车单车',
    category: 'transportation',
    filename: '自行车单车.svg',
    displayName: '自行车单车',
    aliases: ['自行车单车', '交通工具', 'transportation'],
    tags: ['交通工具', 'transportation', '交通', '出行', 'transport', 'vehicle']
  },
  {
    id: 'transportation-警告',
    category: 'transportation',
    filename: '警告.svg',
    displayName: '警告',
    aliases: ['警告', '交通工具', 'transportation'],
    tags: ['交通工具', 'transportation', '交通', '出行', 'transport', 'vehicle']
  },
  {
    id: 'transportation-货车',
    category: 'transportation',
    filename: '货车.svg',
    displayName: '货车',
    aliases: ['货车', '交通工具', 'transportation'],
    tags: ['交通工具', 'transportation', '交通', '出行', 'transport', 'vehicle']
  },
  {
    id: 'transportation-路障',
    category: 'transportation',
    filename: '路障.svg',
    displayName: '路障',
    aliases: ['路障', '交通工具', 'transportation'],
    tags: ['交通工具', 'transportation', '交通', '出行', 'transport', 'vehicle']
  },
  {
    id: 'transportation-轮船',
    category: 'transportation',
    filename: '轮船.svg',
    displayName: '轮船',
    aliases: ['轮船', '交通工具', 'transportation'],
    tags: ['交通工具', 'transportation', '交通', '出行', 'transport', 'vehicle']
  },
  {
    id: 'transportation-飞机',
    category: 'transportation',
    filename: '飞机.svg',
    displayName: '飞机',
    aliases: ['飞机', '交通工具', 'transportation'],
    tags: ['交通工具', 'transportation', '交通', '出行', 'transport', 'vehicle']
  },
  {
    id: 'transportation-飞机1',
    category: 'transportation',
    filename: '飞机1.svg',
    displayName: '飞机1',
    aliases: ['飞机1', '交通工具', 'transportation'],
    tags: ['交通工具', 'transportation', '交通', '出行', 'transport', 'vehicle']
  },
  {
    id: 'transportation-驻车档',
    category: 'transportation',
    filename: '驻车档.svg',
    displayName: '驻车档',
    aliases: ['驻车档', '交通工具', 'transportation'],
    tags: ['交通工具', 'transportation', '交通', '出行', 'transport', 'vehicle']
  },
  {
    id: 'transportation-高铁火车动车',
    category: 'transportation',
    filename: '高铁火车动车.svg',
    displayName: '高铁火车动车',
    aliases: ['高铁火车动车', '交通工具', 'transportation'],
    tags: ['交通工具', 'transportation', '交通', '出行', 'transport', 'vehicle']
  },
  {
    id: 'weather-下雨天',
    category: 'weather',
    filename: '下雨天.svg',
    displayName: '下雨天',
    aliases: ['下雨天', '天气', 'weather'],
    tags: ['天气', 'weather', '气象', 'climate']
  },
  {
    id: 'weather-下雪天',
    category: 'weather',
    filename: '下雪天.svg',
    displayName: '下雪天',
    aliases: ['下雪天', '天气', 'weather'],
    tags: ['天气', 'weather', '气象', 'climate']
  },
  {
    id: 'weather-云',
    category: 'weather',
    filename: '云.svg',
    displayName: '云',
    aliases: ['云', '天气', 'weather'],
    tags: ['天气', 'weather', '气象', 'climate']
  },
  {
    id: 'weather-多云',
    category: 'weather',
    filename: '多云.svg',
    displayName: '多云',
    aliases: ['多云', '天气', 'weather'],
    tags: ['天气', 'weather', '气象', 'climate']
  },
  {
    id: 'weather-多云转晴',
    category: 'weather',
    filename: '多云转晴.svg',
    displayName: '多云转晴',
    aliases: ['多云转晴', '天气', 'weather'],
    tags: ['天气', 'weather', '气象', 'climate']
  },
  {
    id: 'weather-太阳',
    category: 'weather',
    filename: '太阳.svg',
    displayName: '太阳',
    aliases: ['太阳', '天气', 'weather'],
    tags: ['天气', 'weather', '气象', 'climate']
  },
  {
    id: 'weather-好天气',
    category: 'weather',
    filename: '好天气.svg',
    displayName: '好天气',
    aliases: ['好天气', '天气', 'weather'],
    tags: ['天气', 'weather', '气象', 'climate']
  },
  {
    id: 'weather-彩虹',
    category: 'weather',
    filename: '彩虹.svg',
    displayName: '彩虹',
    aliases: ['彩虹', '天气', 'weather'],
    tags: ['天气', 'weather', '气象', 'climate']
  },
  {
    id: 'weather-彩虹天',
    category: 'weather',
    filename: '彩虹天.svg',
    displayName: '彩虹天',
    aliases: ['彩虹天', '天气', 'weather'],
    tags: ['天气', 'weather', '气象', 'climate']
  },
  {
    id: 'weather-早上早晨',
    category: 'weather',
    filename: '早上早晨.svg',
    displayName: '早上早晨',
    aliases: ['早上早晨', '天气', 'weather'],
    tags: ['天气', 'weather', '气象', 'climate']
  },
  {
    id: 'weather-月亮晚上',
    category: 'weather',
    filename: '月亮晚上.svg',
    displayName: '月亮晚上',
    aliases: ['月亮晚上', '天气', 'weather'],
    tags: ['天气', 'weather', '气象', 'climate']
  },
  {
    id: 'weather-清新空气',
    category: 'weather',
    filename: '清新空气.svg',
    displayName: '清新空气',
    aliases: ['清新空气', '天气', 'weather'],
    tags: ['天气', 'weather', '气象', 'climate']
  },
  {
    id: 'weather-温度计',
    category: 'weather',
    filename: '温度计.svg',
    displayName: '温度计',
    aliases: ['温度计', '天气', 'weather'],
    tags: ['天气', 'weather', '气象', 'climate']
  },
  {
    id: 'weather-湿润天气',
    category: 'weather',
    filename: '湿润天气.svg',
    displayName: '湿润天气',
    aliases: ['湿润天气', '天气', 'weather'],
    tags: ['天气', 'weather', '气象', 'climate']
  },
  {
    id: 'weather-白云',
    category: 'weather',
    filename: '白云.svg',
    displayName: '白云',
    aliases: ['白云', '天气', 'weather'],
    tags: ['天气', 'weather', '气象', 'climate']
  },
  {
    id: 'weather-空气质量',
    category: 'weather',
    filename: '空气质量.svg',
    displayName: '空气质量',
    aliases: ['空气质量', '天气', 'weather'],
    tags: ['天气', 'weather', '气象', 'climate']
  },
  {
    id: 'weather-紫外线太阳伞',
    category: 'weather',
    filename: '紫外线太阳伞.svg',
    displayName: '紫外线太阳伞',
    aliases: ['紫外线太阳伞', '天气', 'weather'],
    tags: ['天气', 'weather', '气象', 'climate']
  },
  {
    id: 'weather-雨伞雨天下雨',
    category: 'weather',
    filename: '雨伞雨天下雨.svg',
    displayName: '雨伞雨天下雨',
    aliases: ['雨伞雨天下雨', '天气', 'weather'],
    tags: ['天气', 'weather', '气象', 'climate']
  },
  {
    id: 'weather-雪花',
    category: 'weather',
    filename: '雪花.svg',
    displayName: '雪花',
    aliases: ['雪花', '天气', 'weather'],
    tags: ['天气', 'weather', '气象', 'climate']
  },
  {
    id: 'weather-雷电暴雨',
    category: 'weather',
    filename: '雷电暴雨.svg',
    displayName: '雷电暴雨',
    aliases: ['雷电暴雨', '天气', 'weather'],
    tags: ['天气', 'weather', '气象', 'climate']
  },
  {
    id: 'weather-雾霾',
    category: 'weather',
    filename: '雾霾.svg',
    displayName: '雾霾',
    aliases: ['雾霾', '天气', 'weather'],
    tags: ['天气', 'weather', '气象', 'climate']
  }
]

// 配置统计
export const configStats = {
  totalCategories: 26,
  totalIcons: 858,
  generatedAt: '2025-07-13T07:52:44.744Z'
}

// 便捷查找函数 - 仅保留基础查找，复杂搜索使用 iconService
export function findIconsByCategory(categoryId: string): IconEnhancement[] {
  return iconEnhancements.filter((icon) => icon.category === categoryId)
}

export function findIconById(iconId: string): IconEnhancement | undefined {
  return iconEnhancements.find((icon) => icon.id === iconId)
}

// 注意：复杂搜索功能已移至 iconService.searchIcons()，请使用服务层的搜索功能
