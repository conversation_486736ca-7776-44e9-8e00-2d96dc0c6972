/**
 * 分屏系统专用二叉树
 * 基于改良版二叉树实现，专门用于管理分屏布局
 */

/**
 * 分屏容器数据接口
 * 只包含容器 ID，保持数据结构简洁
 */
export interface SplitScreenData {
    /**
     * 容器唯一标识符
     */
    containerId: string
}

/**
 * 分屏方向枚举
 */
export enum SplitDirection {
    /** 水平分割（左右分屏） */
    HORIZONTAL = 'horizontal',
    /** 垂直分割（上下分屏） */
    VERTICAL = 'vertical'
}

/**
 * 分屏节点类
 * 专门用于分屏系统的二叉树节点
 */
export class SplitScreenNode {
    /**
     * 节点唯一标识符（容器 ID）
     */
    public readonly id: string

    /**
     * 容器 ID
     */
    public containerId: string

    /**
     * 分割方向，只有非叶子节点才有意义
     */
    public splitDirection: SplitDirection | null

    /**
     * 分割比例，范围 0-1，表示左侧/上侧占比
     */
    public splitRatio: number

    /**
     * 子节点数组，最多包含 2 个元素
     */
    public children: SplitScreenNode[]

    /**
     * 父节点引用
     */
    public parent: SplitScreenNode | null

    /**
     * 创建分屏节点
     * @param containerId 容器 ID
     * @param parent 父节点
     * @param splitDirection 分割方向
     * @param splitRatio 分割比例，默认 0.5
     */
    constructor(
        containerId: string,
        parent: SplitScreenNode | null = null,
        splitDirection: SplitDirection | null = null,
        splitRatio: number = 0.5
    ) {
        this.id = containerId
        this.containerId = containerId
        this.splitDirection = splitDirection
        this.splitRatio = Math.max(0, Math.min(1, splitRatio)) // 确保比例在 0-1 之间
        this.children = []
        this.parent = parent
    }

    /**
     * 获取左子节点
     */
    get leftChild(): SplitScreenNode | undefined {
        return this.children[0]
    }

    /**
     * 获取右子节点
     */
    get rightChild(): SplitScreenNode | undefined {
        return this.children[1]
    }

    /**
     * 检查是否为叶子节点
     */
    isLeaf(): boolean {
        return this.children.length === 0
    }

    /**
     * 检查是否有左子节点
     */
    hasLeftChild(): boolean {
        return this.children.length > 0 && this.children[0] !== undefined
    }

    /**
     * 检查是否有右子节点
     */
    hasRightChild(): boolean {
        return this.children.length > 1 && this.children[1] !== undefined
    }

    /**
     * 添加子节点
     * @param node 要添加的子节点
     * @throws Error 如果子节点数量已达到最大值 2
     */
    addChild(node: SplitScreenNode): void {
        if (this.children.length >= 2) {
            throw new Error('Split screen node cannot have more than 2 children')
        }

        this.children.push(node)
        node.parent = this
    }

    /**
     * 移除子节点
     * @param node 要移除的子节点
     * @returns 是否成功移除
     */
    removeChild(node: SplitScreenNode): boolean {
        const index = this.children.indexOf(node)
        if (index !== -1) {
            this.children.splice(index, 1)
            node.parent = null
            return true
        }
        return false
    }

    /**
     * 水平分割当前节点
     * @param leftContainerId 左侧容器 ID
     * @param rightContainerId 右侧容器 ID
     * @param ratio 分割比例，默认 0.5
     * @returns 返回左右子节点
     */
    splitHorizontal(
        leftContainerId: string,
        rightContainerId: string,
        ratio: number = 0.5
    ): [SplitScreenNode, SplitScreenNode] {
        if (!this.isLeaf()) {
            throw new Error('Cannot split a node that already has children')
        }

        this.splitDirection = SplitDirection.HORIZONTAL
        this.splitRatio = Math.max(0, Math.min(1, ratio))

        const leftNode = new SplitScreenNode(leftContainerId, this)
        const rightNode = new SplitScreenNode(rightContainerId, this)

        this.children = [leftNode, rightNode]

        return [leftNode, rightNode]
    }

    /**
     * 垂直分割当前节点
     * @param topContainerId 上方容器 ID
     * @param bottomContainerId 下方容器 ID
     * @param ratio 分割比例，默认 0.5
     * @returns 返回上下子节点
     */
    splitVertical(
        topContainerId: string,
        bottomContainerId: string,
        ratio: number = 0.5
    ): [SplitScreenNode, SplitScreenNode] {
        if (!this.isLeaf()) {
            throw new Error('Cannot split a node that already has children')
        }

        this.splitDirection = SplitDirection.VERTICAL
        this.splitRatio = Math.max(0, Math.min(1, ratio))

        const topNode = new SplitScreenNode(topContainerId, this)
        const bottomNode = new SplitScreenNode(bottomContainerId, this)

        this.children = [topNode, bottomNode]

        return [topNode, bottomNode]
    }

    /**
     * 更新分割比例
     * @param ratio 新的分割比例
     */
    updateSplitRatio(ratio: number): void {
        this.splitRatio = Math.max(0, Math.min(1, ratio))
    }

    /**
     * 检查是否可以分割
     * @returns 如果是叶子节点返回 true
     */
    canSplit(): boolean {
        return this.isLeaf()
    }

    /**
     * 移除分割，将当前节点变为叶子节点
     * @param newContainerId 新的容器 ID，如果不提供则保持原有 ID
     */
    removeSplit(newContainerId?: string): void {
        this.children = []
        this.splitDirection = null
        this.splitRatio = 0.5

        if (newContainerId) {
            this.containerId = newContainerId
        }
    }

    /**
     * 获取节点深度（从根节点算起）
     */
    getDepth(): number {
        let depth = 0
        let current: SplitScreenNode | null = this.parent
        while (current !== null) {
            depth++
            current = current.parent
        }
        return depth
    }

    /**
     * 获取子树高度
     */
    getHeight(): number {
        if (this.isLeaf()) {
            return 0
        }

        const leftHeight = this.leftChild ? this.leftChild.getHeight() : -1
        const rightHeight = this.rightChild ? this.rightChild.getHeight() : -1

        return Math.max(leftHeight, rightHeight) + 1
    }

    /**
     * 查找包含指定容器 ID 的节点
     * @param containerId 要查找的容器 ID
     * @returns 找到的节点或 undefined
     */
    findByContainerId(containerId: string): SplitScreenNode | undefined {
        if (this.containerId === containerId) {
            return this
        }

        for (const child of this.children) {
            if (child) {
                const found = child.findByContainerId(containerId)
                if (found) {
                    return found
                }
            }
        }

        return undefined
    }

    /**
     * 查找满足条件的节点
     * @param predicate 判断条件函数
     * @returns 找到的节点或 undefined
     */
    find(predicate: (node: SplitScreenNode) => boolean): SplitScreenNode | undefined {
        if (predicate(this)) {
            return this
        }

        for (const child of this.children) {
            if (child) {
                const found = child.find(predicate)
                if (found) {
                    return found
                }
            }
        }

        return undefined
    }

    /**
     * 获取所有节点（深度优先）
     */
    getAllNodes(): SplitScreenNode[] {
        const nodes: SplitScreenNode[] = [this]

        for (const child of this.children) {
            if (child) {
                nodes.push(...child.getAllNodes())
            }
        }

        return nodes
    }

    /**
     * 获取所有叶子节点
     */
    getLeafNodes(): SplitScreenNode[] {
        if (this.isLeaf()) {
            return [this]
        }

        const leafNodes: SplitScreenNode[] = []
        for (const child of this.children) {
            if (child) {
                leafNodes.push(...child.getLeafNodes())
            }
        }

        return leafNodes
    }

    /**
     * 获取所有叶子节点的容器 ID
     */
    getLeafContainerIds(): string[] {
        const leafNodes = this.getLeafNodes()
        return leafNodes.map(node => node.containerId)
    }

    /**
     * 先序遍历
     * @param callback 访问节点时调用的回调函数
     */
    preOrderTraversal(callback: (node: SplitScreenNode) => void): void {
        callback(this)

        if (this.leftChild) {
            this.leftChild.preOrderTraversal(callback)
        }

        if (this.rightChild) {
            this.rightChild.preOrderTraversal(callback)
        }
    }

    /**
     * 中序遍历
     * @param callback 访问节点时调用的回调函数
     */
    inOrderTraversal(callback: (node: SplitScreenNode) => void): void {
        if (this.leftChild) {
            this.leftChild.inOrderTraversal(callback)
        }

        callback(this)

        if (this.rightChild) {
            this.rightChild.inOrderTraversal(callback)
        }
    }

    /**
     * 后序遍历
     * @param callback 访问节点时调用的回调函数
     */
    postOrderTraversal(callback: (node: SplitScreenNode) => void): void {
        if (this.leftChild) {
            this.leftChild.postOrderTraversal(callback)
        }

        if (this.rightChild) {
            this.rightChild.postOrderTraversal(callback)
        }

        callback(this)
    }

    /**
     * 层序遍历（广度优先）
     * @param callback 访问节点时调用的回调函数
     */
    levelOrderTraversal(callback: (node: SplitScreenNode) => void): void {
        const queue: SplitScreenNode[] = [this]

        while (queue.length > 0) {
            const current = queue.shift()!
            callback(current)

            current.children.forEach(child => {
                if (child) {
                    queue.push(child)
                }
            })
        }
    }

    /**
     * 克隆分屏节点
     */
    clone(): SplitScreenNode {
        const cloned = new SplitScreenNode(
            this.containerId,
            null,
            this.splitDirection,
            this.splitRatio
        )

        for (const child of this.children) {
            if (child) {
                const clonedChild = child.clone()
                clonedChild.parent = cloned
                cloned.children.push(clonedChild)
            }
        }

        return cloned
    }

    /**
     * 转换为分屏专用的 JSON 格式
     */
    toSplitScreenJSON(): any {
        return {
            containerId: this.containerId,
            splitDirection: this.splitDirection,
            splitRatio: this.splitRatio,
            children: this.children.map(child =>
                child ? child.toSplitScreenJSON() : null
            )
        }
    }

    /**
     * 从分屏 JSON 格式创建节点
     */
    static fromSplitScreenJSON(json: any, parent: SplitScreenNode | null = null): SplitScreenNode {
        const node = new SplitScreenNode(
            json.containerId,
            parent,
            json.splitDirection || null,
            json.splitRatio || 0.5
        )

        if (json.children && Array.isArray(json.children)) {
            for (const childJson of json.children) {
                if (childJson) {
                    const child = SplitScreenNode.fromSplitScreenJSON(childJson, node)
                    node.children.push(child)
                }
            }
        }

        return node
    }
}

/**
 * 分屏二叉树类
 * 专门用于管理分屏布局的树形结构
 */
export class SplitScreenTree {
    /**
     * 根节点
     */
    public root: SplitScreenNode | null

    /**
     * 创建分屏二叉树
     * @param rootContainerId 根容器 ID，可选
     */
    constructor(rootContainerId?: string) {
        this.root = rootContainerId ? new SplitScreenNode(rootContainerId) : null
    }

    /**
     * 设置根节点
     * @param containerId 根容器 ID
     * @returns 根节点
     */
    setRootContainer(containerId: string): SplitScreenNode {
        this.root = new SplitScreenNode(containerId)
        return this.root
    }

    /**
     * 检查树是否为空
     */
    isEmpty(): boolean {
        return this.root === null
    }

    /**
     * 获取树的高度
     */
    getHeight(): number {
        return this.root ? this.root.getHeight() : -1
    }

    /**
     * 获取树中节点的总数
     */
    getSize(): number {
        return this.root ? this.root.getAllNodes().length : 0
    }

    /**
     * 查找包含指定容器 ID 的节点
     * @param containerId 容器 ID
     * @returns 找到的节点或 undefined
     */
    findByContainerId(containerId: string): SplitScreenNode | undefined {
        return this.root ? this.root.findByContainerId(containerId) : undefined
    }

    /**
     * 查找满足条件的节点
     * @param predicate 判断条件函数
     * @returns 找到的节点或 undefined
     */
    find(predicate: (node: SplitScreenNode) => boolean): SplitScreenNode | undefined {
        return this.root ? this.root.find(predicate) : undefined
    }

    /**
     * 水平分割指定容器
     * @param containerId 要分割的容器 ID
     * @param leftContainerId 左侧新容器 ID
     * @param rightContainerId 右侧新容器 ID
     * @param ratio 分割比例，默认 0.5
     * @returns 成功返回新创建的子节点，失败返回 null
     */
    splitContainerHorizontal(
        containerId: string,
        leftContainerId: string,
        rightContainerId: string,
        ratio: number = 0.5
    ): [SplitScreenNode, SplitScreenNode] | null {
        const node = this.findByContainerId(containerId)
        if (!node || !node.canSplit()) {
            return null
        }

        return node.splitHorizontal(leftContainerId, rightContainerId, ratio)
    }

    /**
     * 垂直分割指定容器
     * @param containerId 要分割的容器 ID
     * @param topContainerId 上方新容器 ID
     * @param bottomContainerId 下方新容器 ID
     * @param ratio 分割比例，默认 0.5
     * @returns 成功返回新创建的子节点，失败返回 null
     */
    splitContainerVertical(
        containerId: string,
        topContainerId: string,
        bottomContainerId: string,
        ratio: number = 0.5
    ): [SplitScreenNode, SplitScreenNode] | null {
        const node = this.findByContainerId(containerId)
        if (!node || !node.canSplit()) {
            return null
        }

        return node.splitVertical(topContainerId, bottomContainerId, ratio)
    }

    /**
     * 移除指定容器的分割
     * @param containerId 容器 ID
     * @param newContainerId 新的容器 ID
     * @returns 是否成功移除
     */
    removeContainerSplit(containerId: string, newContainerId?: string): boolean {
        const node = this.findByContainerId(containerId)
        if (!node) {
            return false
        }

        node.removeSplit(newContainerId)
        return true
    }

    /**
     * 更新容器的分割比例
     * @param containerId 容器 ID
     * @param ratio 新的比例
     * @returns 是否成功更新
     */
    updateContainerSplitRatio(containerId: string, ratio: number): boolean {
        const node = this.findByContainerId(containerId)
        if (!node) {
            return false
        }

        node.updateSplitRatio(ratio)
        return true
    }

    /**
     * 获取所有叶子容器的 ID
     */
    getAllLeafContainerIds(): string[] {
        return this.root ? this.root.getLeafContainerIds() : []
    }

    /**
     * 获取所有容器 ID（包括非叶子节点）
     */
    getAllContainerIds(): string[] {
        if (!this.root) return []

        const containerIds: string[] = []
        this.root.preOrderTraversal((node) => {
            containerIds.push(node.containerId)
        })

        return containerIds
    }

    /**
     * 获取所有节点
     */
    getAllNodes(): SplitScreenNode[] {
        return this.root ? this.root.getAllNodes() : []
    }

    /**
     * 获取所有叶子节点
     */
    getLeafNodes(): SplitScreenNode[] {
        return this.root ? this.root.getLeafNodes() : []
    }

    /**
     * 检查容器是否可以分割
     * @param containerId 容器 ID
     * @returns 是否可以分割
     */
    canSplitContainer(containerId: string): boolean {
        const node = this.findByContainerId(containerId)
        return node ? node.canSplit() : false
    }

    /**
     * 移除指定容器节点
     * @param containerId 要移除的容器 ID
     * @returns 是否成功移除
     */
    removeContainer(containerId: string): boolean {
        const node = this.findByContainerId(containerId)
        if (!node || !node.parent) {
            // 如果是根节点，直接清空树
            if (node === this.root) {
                this.clear()
                return true
            }
            return false
        }

        const parent = node.parent
        const sibling = parent.leftChild === node ? parent.rightChild : parent.leftChild

        if (sibling && parent.parent) {
            // 将兄弟节点替换父节点
            const grandParent = parent.parent
            const parentIndex = grandParent.children.indexOf(parent)

            if (parentIndex !== -1) {
                grandParent.children[parentIndex] = sibling
                sibling.parent = grandParent
            }
        } else if (sibling && !parent.parent) {
            // 父节点是根节点，将兄弟节点设为新根节点
            this.root = sibling
            sibling.parent = null
        } else {
            // 没有兄弟节点，移除父节点
            if (parent.parent) {
                parent.parent.removeChild(parent)
            } else {
                this.clear()
            }
        }

        return true
    }

    /**
     * 克隆整个分屏树
     */
    clone(): SplitScreenTree {
        const clonedTree = new SplitScreenTree()
        if (this.root) {
            clonedTree.root = this.root.clone()
        }
        return clonedTree
    }

    /**
     * 转换为分屏专用的 JSON 格式
     */
    toSplitScreenJSON(): any {
        return this.root ? this.root.toSplitScreenJSON() : null
    }

    /**
     * 从分屏 JSON 格式创建树
     */
    static fromSplitScreenJSON(json: any): SplitScreenTree {
        const tree = new SplitScreenTree()
        if (json) {
            tree.root = SplitScreenNode.fromSplitScreenJSON(json)
        }
        return tree
    }

    /**
     * 清空树
     */
    clear(): void {
        this.root = null
    }

    /**
     * 遍历所有分屏节点
     * @param callback 访问节点时调用的回调函数
     */
    traverseSplitScreenNodes(callback: (node: SplitScreenNode) => void): void {
        if (this.root) {
            this.root.preOrderTraversal(callback)
        }
    }

    /**
     * 先序遍历整个树
     * @param callback 访问节点时调用的回调函数
     */
    preOrderTraversal(callback: (node: SplitScreenNode) => void): void {
        if (this.root) {
            this.root.preOrderTraversal(callback)
        }
    }

    /**
     * 中序遍历整个树
     * @param callback 访问节点时调用的回调函数
     */
    inOrderTraversal(callback: (node: SplitScreenNode) => void): void {
        if (this.root) {
            this.root.inOrderTraversal(callback)
        }
    }

    /**
     * 后序遍历整个树
     * @param callback 访问节点时调用的回调函数
     */
    postOrderTraversal(callback: (node: SplitScreenNode) => void): void {
        if (this.root) {
            this.root.postOrderTraversal(callback)
        }
    }

    /**
     * 层序遍历整个树
     * @param callback 访问节点时调用的回调函数
     */
    levelOrderTraversal(callback: (node: SplitScreenNode) => void): void {
        if (this.root) {
            this.root.levelOrderTraversal(callback)
        }
    }
} 