/**
 * SplitScreenCore 模块索引文件
 * 导出所有核心组件和类型定义
 */

// 导出二叉树相关的类和类型
export { BinaryTreeNode, BinaryTree, Tree } from './BinaryTree'
export type { TreeNode } from './BinaryTree'

// 导出分屏专用的类和类型
export { SplitScreenNode, SplitScreenTree, SplitDirection } from './SplitScreenTree'
export type { SplitScreenData } from './SplitScreenTree'

// 导出示例函数（可选，用于测试和演示）
export * from './SplitScreenExample'

// 可以在这里继续导出其他分屏相关的核心组件
// export { ... } from './OtherComponents' 