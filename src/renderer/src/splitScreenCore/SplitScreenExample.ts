/**
 * 分屏二叉树使用示例
 */

import { SplitScreenTree, SplitScreenNode, SplitDirection } from './SplitScreenTree'

/**
 * 基本使用示例
 */
export function basicSplitScreenExample() {
    console.log('=== 基本分屏示例 ===')

    // 创建分屏树，初始只有一个容器
    const tree = new SplitScreenTree('main-container')

    console.log('初始状态:')
    console.log('树大小:', tree.getSize())
    console.log('树高度:', tree.getHeight())
    console.log('叶子容器:', tree.getAllLeafContainerIds())

    return tree
}

/**
 * 水平分屏示例
 */
export function horizontalSplitExample() {
    console.log('\n=== 水平分屏示例 ===')

    const tree = basicSplitScreenExample()

    // 水平分割主容器
    const result = tree.splitContainerHorizontal(
        'main-container',
        'left-panel',
        'right-panel',
        0.6 // 左侧占 60%
    )

    if (result) {
        const [leftNode, rightNode] = result
        console.log('分割成功:')
        console.log('左侧容器:', leftNode.containerId)
        console.log('右侧容器:', rightNode.containerId)
        console.log('分割比例:', leftNode.parent?.splitRatio)
        console.log('分割方向:', leftNode.parent?.splitDirection)
    }

    console.log('分割后状态:')
    console.log('树大小:', tree.getSize())
    console.log('树高度:', tree.getHeight())
    console.log('叶子容器:', tree.getAllLeafContainerIds())

    return tree
}

/**
 * 垂直分屏示例
 */
export function verticalSplitExample() {
    console.log('\n=== 垂直分屏示例 ===')

    const tree = horizontalSplitExample()

    // 对右侧面板进行垂直分割
    const result = tree.splitContainerVertical(
        'right-panel',
        'right-top',
        'right-bottom',
        0.7 // 上方占 70%
    )

    if (result) {
        const [topNode, bottomNode] = result
        console.log('垂直分割成功:')
        console.log('上方容器:', topNode.containerId)
        console.log('下方容器:', bottomNode.containerId)
        console.log('分割比例:', topNode.parent?.splitRatio)
        console.log('分割方向:', topNode.parent?.splitDirection)
    }

    console.log('垂直分割后状态:')
    console.log('树大小:', tree.getSize())
    console.log('树高度:', tree.getHeight())
    console.log('叶子容器:', tree.getAllLeafContainerIds())

    return tree
}

/**
 * 复杂分屏布局示例
 */
export function complexLayoutExample() {
    console.log('\n=== 复杂分屏布局示例 ===')

    const tree = verticalSplitExample()

    // 对左侧面板进行垂直分割
    tree.splitContainerVertical('left-panel', 'left-top', 'left-bottom', 0.3)

    // 对左下角进行水平分割
    tree.splitContainerHorizontal('left-bottom', 'left-bottom-left', 'left-bottom-right', 0.5)

    console.log('复杂布局创建完成:')
    console.log('树大小:', tree.getSize())
    console.log('树高度:', tree.getHeight())
    console.log('叶子容器:', tree.getAllLeafContainerIds())

    // 打印树结构
    console.log('\n树结构:')
    tree.levelOrderTraversal((node) => {
        const indent = '  '.repeat(node.getDepth())
        const info = node.isLeaf()
            ? '(叶子)'
            : `(${node.splitDirection}, 比例: ${node.splitRatio})`
        console.log(`${indent}${node.containerId} ${info}`)
    })

    return tree
}

/**
 * 容器移除示例
 */
export function containerRemovalExample() {
    console.log('\n=== 容器移除示例 ===')

    const tree = complexLayoutExample()

    console.log('移除前的叶子容器:', tree.getAllLeafContainerIds())

    // 移除右下角容器
    const removed = tree.removeContainer('right-bottom')
    console.log('移除 right-bottom:', removed)
    console.log('移除后的叶子容器:', tree.getAllLeafContainerIds())

    // 移除左下角的一个子容器
    tree.removeContainer('left-bottom-left')
    console.log('移除 left-bottom-left 后:', tree.getAllLeafContainerIds())

    return tree
}

/**
 * 比例调整示例
 */
export function ratioAdjustmentExample() {
    console.log('\n=== 比例调整示例 ===')

    const tree = new SplitScreenTree('root')
    tree.splitContainerHorizontal('root', 'left', 'right', 0.5)

    console.log('初始比例:', tree.findByContainerId('root')?.splitRatio)

    // 调整分割比例
    tree.updateContainerSplitRatio('root', 0.3)
    console.log('调整后比例:', tree.findByContainerId('root')?.splitRatio)

    // 验证比例范围限制
    tree.updateContainerSplitRatio('root', 1.5) // 应该被限制为 1.0
    console.log('超出范围调整后比例:', tree.findByContainerId('root')?.splitRatio)

    tree.updateContainerSplitRatio('root', -0.5) // 应该被限制为 0.0
    console.log('负数调整后比例:', tree.findByContainerId('root')?.splitRatio)

    return tree
}

/**
 * JSON 序列化示例
 */
export function serializationExample() {
    console.log('\n=== JSON 序列化示例 ===')

    const tree = complexLayoutExample()

    // 序列化
    const json = tree.toSplitScreenJSON()
    console.log('序列化的 JSON:')
    console.log(JSON.stringify(json, null, 2))

    // 反序列化
    const restoredTree = SplitScreenTree.fromSplitScreenJSON(json)
    console.log('\n反序列化后的叶子容器:', restoredTree.getAllLeafContainerIds())

    // 验证结构相同
    const originalLeafIds = tree.getAllLeafContainerIds().sort()
    const restoredLeafIds = restoredTree.getAllLeafContainerIds().sort()
    const isEqual = JSON.stringify(originalLeafIds) === JSON.stringify(restoredLeafIds)
    console.log('结构是否相同:', isEqual)

    return restoredTree
}

/**
 * 克隆示例
 */
export function cloneExample() {
    console.log('\n=== 克隆示例 ===')

    const originalTree = basicSplitScreenExample()
    originalTree.splitContainerHorizontal('main-container', 'left', 'right')

    // 克隆树
    const clonedTree = originalTree.clone()

    console.log('原始树叶子容器:', originalTree.getAllLeafContainerIds())
    console.log('克隆树叶子容器:', clonedTree.getAllLeafContainerIds())

    // 修改克隆树，不应影响原树
    clonedTree.splitContainerVertical('left', 'left-top', 'left-bottom')

    console.log('修改克隆树后:')
    console.log('原始树叶子容器:', originalTree.getAllLeafContainerIds())
    console.log('克隆树叶子容器:', clonedTree.getAllLeafContainerIds())

    return { originalTree, clonedTree }
}

/**
 * 查找和遍历示例
 */
export function searchAndTraversalExample() {
    console.log('\n=== 查找和遍历示例 ===')

    const tree = complexLayoutExample()

    // 查找容器
    const node = tree.findByContainerId('right-top')
    console.log('找到节点:', node?.containerId)
    console.log('节点深度:', node?.getDepth())
    console.log('是否为叶子:', node?.isLeaf())

    // 按条件查找
    const nonLeafNode = tree.find(node => !node.isLeaf() && node.splitDirection === SplitDirection.HORIZONTAL)
    console.log('找到水平分割的非叶子节点:', nonLeafNode?.containerId)

    // 不同遍历方式
    console.log('\n先序遍历:')
    tree.preOrderTraversal(node => {
        console.log(`  ${node.containerId}`)
    })

    console.log('\n中序遍历:')
    tree.inOrderTraversal(node => {
        console.log(`  ${node.containerId}`)
    })

    console.log('\n后序遍历:')
    tree.postOrderTraversal(node => {
        console.log(`  ${node.containerId}`)
    })

    return tree
}

/**
 * 错误处理示例
 */
export function errorHandlingExample() {
    console.log('\n=== 错误处理示例 ===')

    const tree = new SplitScreenTree('root')

    // 尝试分割不存在的容器
    const result1 = tree.splitContainerHorizontal('non-existent', 'left', 'right')
    console.log('分割不存在的容器:', result1) // 应该返回 null

    // 尝试分割已有子节点的容器
    tree.splitContainerHorizontal('root', 'left', 'right')
    const result2 = tree.splitContainerHorizontal('root', 'new-left', 'new-right')
    console.log('分割已有子节点的容器:', result2) // 应该返回 null

    // 尝试给节点添加超过 2 个子节点
    try {
        const node = new SplitScreenNode('test')
        node.addChild(new SplitScreenNode('child1'))
        node.addChild(new SplitScreenNode('child2'))
        node.addChild(new SplitScreenNode('child3')) // 应该抛出错误
    } catch (error) {
        console.log('添加第三个子节点时的错误:', (error as Error).message)
    }

    // 尝试分割已有子节点的节点
    try {
        const parentNode = new SplitScreenNode('parent')
        parentNode.addChild(new SplitScreenNode('existing-child'))
        parentNode.splitHorizontal('new-left', 'new-right') // 应该抛出错误
    } catch (error) {
        console.log('分割已有子节点的节点时的错误:', (error as Error).message)
    }

    return tree
}

/**
 * 运行所有示例
 */
export function runAllExamples() {
    console.log('🌳 分屏二叉树示例演示 🌳\n')

    basicSplitScreenExample()
    horizontalSplitExample()
    verticalSplitExample()
    complexLayoutExample()
    containerRemovalExample()
    ratioAdjustmentExample()
    serializationExample()
    cloneExample()
    searchAndTraversalExample()
    errorHandlingExample()

    console.log('\n✅ 所有示例运行完成!')
}

// 如果作为脚本运行，执行所有示例
if (typeof require !== 'undefined' && require.main === module) {
    runAllExamples()
} 