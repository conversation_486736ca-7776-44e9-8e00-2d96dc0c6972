/**
 * 改良版二叉树节点类
 * 使用 children 数组替代传统的 left/right 指针
 * children 数组长度限制为最多 2 个元素
 */
export class BinaryTreeNode<T = any> {
    /**
     * 节点唯一标识符
     */
    public readonly id: string

    /**
     * 节点存储的数据
     */
    public data: T

    /**
     * 子节点数组，最多包含 2 个元素
     * children[0] 相当于传统二叉树的左子节点
     * children[1] 相当于传统二叉树的右子节点
     */
    public children: BinaryTreeNode<T>[]

    /**
     * 父节点引用
     */
    public parent: BinaryTreeNode<T> | null

    /**
     * 创建一个新的二叉树节点
     * @param id 节点唯一标识符
     * @param data 节点数据
     * @param parent 父节点，可选
     */
    constructor(id: string, data: T, parent: BinaryTreeNode<T> | null = null) {
        this.id = id
        this.data = data
        this.children = []
        this.parent = parent
    }

    /**
     * 获取左子节点
     * @returns 左子节点或 undefined
     */
    get leftChild(): BinaryTreeNode<T> | undefined {
        return this.children[0]
    }

    /**
     * 获取右子节点
     * @returns 右子节点或 undefined
     */
    get rightChild(): BinaryTreeNode<T> | undefined {
        return this.children[1]
    }

    /**
     * 设置左子节点
     * @param node 要设置的左子节点
     */
    setLeftChild(node: BinaryTreeNode<T>): void {
        if (this.children.length === 0) {
            this.children.push(node)
        } else if (this.children.length === 1) {
            this.children.unshift(node)
        } else {
            this.children[0] = node
        }
        node.parent = this
    }

    /**
     * 设置右子节点
     * @param node 要设置的右子节点
     * @throws Error 如果超过最大子节点数量
     */
    setRightChild(node: BinaryTreeNode<T>): void {
        if (this.children.length === 0) {
            this.children.push(undefined as any, node)
            this.children = this.children.filter(child => child !== undefined)
        } else if (this.children.length === 1) {
            this.children.push(node)
        } else if (this.children.length === 2) {
            this.children[1] = node
        } else {
            throw new Error('Cannot add more than 2 children to a binary tree node')
        }
        node.parent = this
    }

    /**
     * 添加子节点
     * @param node 要添加的子节点
     * @throws Error 如果子节点数量已达到最大值 2
     */
    addChild(node: BinaryTreeNode<T>): void {
        if (this.children.length >= 2) {
            throw new Error('Binary tree node cannot have more than 2 children')
        }

        this.children.push(node)
        node.parent = this
    }

    /**
     * 移除子节点
     * @param node 要移除的子节点
     * @returns 是否成功移除
     */
    removeChild(node: BinaryTreeNode<T>): boolean {
        const index = this.children.indexOf(node)
        if (index !== -1) {
            this.children.splice(index, 1)
            node.parent = null
            return true
        }
        return false
    }

    /**
     * 检查是否为叶子节点
     * @returns 如果是叶子节点返回 true
     */
    isLeaf(): boolean {
        return this.children.length === 0
    }

    /**
     * 检查是否有左子节点
     * @returns 如果有左子节点返回 true
     */
    hasLeftChild(): boolean {
        return this.children.length > 0 && this.children[0] !== undefined
    }

    /**
     * 检查是否有右子节点
     * @returns 如果有右子节点返回 true
     */
    hasRightChild(): boolean {
        return this.children.length > 1 && this.children[1] !== undefined
    }

    /**
     * 获取节点深度（从根节点算起）
     * @returns 节点深度
     */
    getDepth(): number {
        let depth = 0
        let current: BinaryTreeNode<T> | null = this.parent
        while (current !== null) {
            depth++
            current = current.parent
        }
        return depth
    }

    /**
     * 获取子树高度
     * @returns 子树高度
     */
    getHeight(): number {
        if (this.isLeaf()) {
            return 0
        }

        const leftHeight = this.leftChild ? this.leftChild.getHeight() : -1
        const rightHeight = this.rightChild ? this.rightChild.getHeight() : -1

        return Math.max(leftHeight, rightHeight) + 1
    }

    /**
     * 先序遍历（根-左-右）
     * @param callback 访问节点时调用的回调函数
     */
    preOrderTraversal(callback: (node: BinaryTreeNode<T>) => void): void {
        callback(this)

        if (this.leftChild) {
            this.leftChild.preOrderTraversal(callback)
        }

        if (this.rightChild) {
            this.rightChild.preOrderTraversal(callback)
        }
    }

    /**
     * 中序遍历（左-根-右）
     * @param callback 访问节点时调用的回调函数
     */
    inOrderTraversal(callback: (node: BinaryTreeNode<T>) => void): void {
        if (this.leftChild) {
            this.leftChild.inOrderTraversal(callback)
        }

        callback(this)

        if (this.rightChild) {
            this.rightChild.inOrderTraversal(callback)
        }
    }

    /**
     * 后序遍历（左-右-根）
     * @param callback 访问节点时调用的回调函数
     */
    postOrderTraversal(callback: (node: BinaryTreeNode<T>) => void): void {
        if (this.leftChild) {
            this.leftChild.postOrderTraversal(callback)
        }

        if (this.rightChild) {
            this.rightChild.postOrderTraversal(callback)
        }

        callback(this)
    }

    /**
     * 层序遍历（广度优先）
     * @param callback 访问节点时调用的回调函数
     */
    levelOrderTraversal(callback: (node: BinaryTreeNode<T>) => void): void {
        const queue: BinaryTreeNode<T>[] = [this]

        while (queue.length > 0) {
            const current = queue.shift()!
            callback(current)

            current.children.forEach(child => {
                if (child) {
                    queue.push(child)
                }
            })
        }
    }

    /**
     * 查找具有指定 ID 的节点
     * @param id 要查找的节点 ID
     * @returns 找到的节点或 undefined
     */
    findById(id: string): BinaryTreeNode<T> | undefined {
        if (this.id === id) {
            return this
        }

        for (const child of this.children) {
            if (child) {
                const found = child.findById(id)
                if (found) {
                    return found
                }
            }
        }

        return undefined
    }

    /**
     * 查找满足条件的节点
     * @param predicate 判断条件函数
     * @returns 找到的节点或 undefined
     */
    find(predicate: (node: BinaryTreeNode<T>) => boolean): BinaryTreeNode<T> | undefined {
        if (predicate(this)) {
            return this
        }

        for (const child of this.children) {
            if (child) {
                const found = child.find(predicate)
                if (found) {
                    return found
                }
            }
        }

        return undefined
    }

    /**
     * 获取所有节点（深度优先）
     * @returns 包含所有节点的数组
     */
    getAllNodes(): BinaryTreeNode<T>[] {
        const nodes: BinaryTreeNode<T>[] = [this]

        for (const child of this.children) {
            if (child) {
                nodes.push(...child.getAllNodes())
            }
        }

        return nodes
    }

    /**
     * 获取所有叶子节点
     * @returns 包含所有叶子节点的数组
     */
    getLeafNodes(): BinaryTreeNode<T>[] {
        if (this.isLeaf()) {
            return [this]
        }

        const leafNodes: BinaryTreeNode<T>[] = []
        for (const child of this.children) {
            if (child) {
                leafNodes.push(...child.getLeafNodes())
            }
        }

        return leafNodes
    }

    /**
     * 克隆节点（深拷贝）
     * @param cloneData 克隆数据的函数，默认直接复制
     * @returns 克隆的节点
     */
    clone(cloneData?: (data: T) => T): BinaryTreeNode<T> {
        const clonedData = cloneData ? cloneData(this.data) : this.data
        const clonedNode = new BinaryTreeNode(this.id, clonedData)

        for (const child of this.children) {
            if (child) {
                const clonedChild = child.clone(cloneData)
                clonedNode.addChild(clonedChild)
            }
        }

        return clonedNode
    }

    /**
     * 将树结构转换为 JSON 对象
     * @returns JSON 表示的树结构
     */
    toJSON(): any {
        return {
            id: this.id,
            data: this.data,
            children: this.children.map(child => child ? child.toJSON() : null)
        }
    }

    /**
     * 从 JSON 对象创建树结构
     * @param json JSON 对象
     * @param parent 父节点
     * @returns 创建的节点
     */
    static fromJSON<T>(json: any, parent: BinaryTreeNode<T> | null = null): BinaryTreeNode<T> {
        const node = new BinaryTreeNode<T>(json.id, json.data, parent)

        if (json.children && Array.isArray(json.children)) {
            for (const childJson of json.children) {
                if (childJson) {
                    const child = BinaryTreeNode.fromJSON<T>(childJson, node)
                    node.addChild(child)
                }
            }
        }

        return node
    }
}

/**
 * 改良版二叉树类
 * 管理整个树结构，提供树级别的操作
 */
export class BinaryTree<T = any> {
    /**
     * 树的根节点
     */
    public root: BinaryTreeNode<T> | null

    /**
     * 创建一个新的二叉树
     * @param rootData 根节点数据，可选
     */
    constructor(rootData?: T) {
        this.root = rootData !== undefined ? new BinaryTreeNode('root', rootData) : null
    }

    /**
     * 设置根节点
     * @param data 根节点数据
     * @param id 根节点 ID，默认为 'root'
     */
    setRoot(data: T, id: string = 'root'): BinaryTreeNode<T> {
        this.root = new BinaryTreeNode(id, data)
        return this.root
    }

    /**
     * 检查树是否为空
     * @returns 如果树为空返回 true
     */
    isEmpty(): boolean {
        return this.root === null
    }

    /**
     * 获取树的高度
     * @returns 树的高度，空树返回 -1
     */
    getHeight(): number {
        return this.root ? this.root.getHeight() : -1
    }

    /**
     * 获取树中节点的总数
     * @returns 节点总数
     */
    getSize(): number {
        return this.root ? this.root.getAllNodes().length : 0
    }

    /**
     * 查找具有指定 ID 的节点
     * @param id 要查找的节点 ID
     * @returns 找到的节点或 undefined
     */
    findById(id: string): BinaryTreeNode<T> | undefined {
        return this.root ? this.root.findById(id) : undefined
    }

    /**
     * 查找满足条件的节点
     * @param predicate 判断条件函数
     * @returns 找到的节点或 undefined
     */
    find(predicate: (node: BinaryTreeNode<T>) => boolean): BinaryTreeNode<T> | undefined {
        return this.root ? this.root.find(predicate) : undefined
    }

    /**
     * 获取所有节点
     * @returns 包含所有节点的数组
     */
    getAllNodes(): BinaryTreeNode<T>[] {
        return this.root ? this.root.getAllNodes() : []
    }

    /**
     * 获取所有叶子节点
     * @returns 包含所有叶子节点的数组
     */
    getLeafNodes(): BinaryTreeNode<T>[] {
        return this.root ? this.root.getLeafNodes() : []
    }

    /**
     * 清空树
     */
    clear(): void {
        this.root = null
    }

    /**
     * 先序遍历整个树
     * @param callback 访问节点时调用的回调函数
     */
    preOrderTraversal(callback: (node: BinaryTreeNode<T>) => void): void {
        if (this.root) {
            this.root.preOrderTraversal(callback)
        }
    }

    /**
     * 中序遍历整个树
     * @param callback 访问节点时调用的回调函数
     */
    inOrderTraversal(callback: (node: BinaryTreeNode<T>) => void): void {
        if (this.root) {
            this.root.inOrderTraversal(callback)
        }
    }

    /**
     * 后序遍历整个树
     * @param callback 访问节点时调用的回调函数
     */
    postOrderTraversal(callback: (node: BinaryTreeNode<T>) => void): void {
        if (this.root) {
            this.root.postOrderTraversal(callback)
        }
    }

    /**
     * 层序遍历整个树
     * @param callback 访问节点时调用的回调函数
     */
    levelOrderTraversal(callback: (node: BinaryTreeNode<T>) => void): void {
        if (this.root) {
            this.root.levelOrderTraversal(callback)
        }
    }

    /**
     * 将树转换为 JSON 对象
     * @returns JSON 表示的树结构
     */
    toJSON(): any {
        return this.root ? this.root.toJSON() : null
    }

    /**
     * 从 JSON 对象创建树
     * @param json JSON 对象
     * @returns 创建的树
     */
    static fromJSON<T>(json: any): BinaryTree<T> {
        const tree = new BinaryTree<T>()
        if (json) {
            tree.root = BinaryTreeNode.fromJSON<T>(json)
        }
        return tree
    }

    /**
     * 克隆整个树（深拷贝）
     * @param cloneData 克隆数据的函数，默认直接复制
     * @returns 克隆的树
     */
    clone(cloneData?: (data: T) => T): BinaryTree<T> {
        const clonedTree = new BinaryTree<T>()
        if (this.root) {
            clonedTree.root = this.root.clone(cloneData)
        }
        return clonedTree
    }
}

// 导出类型定义
export type { BinaryTreeNode as TreeNode }
export { BinaryTree as Tree } 