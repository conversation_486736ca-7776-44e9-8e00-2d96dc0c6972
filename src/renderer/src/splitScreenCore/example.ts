/**
 * 改良版二叉树使用示例
 */

import { BinaryTree, BinaryTreeNode } from './BinaryTree'

// 示例数据接口
interface PanelData {
    title: string
    type: 'editor' | 'viewer' | 'browser'
    content?: string
}

/**
 * 基本使用示例
 */
export function basicUsageExample() {
    // 创建一个新的二叉树
    const tree = new BinaryTree<PanelData>({
        title: 'Root Panel',
        type: 'editor',
        content: 'This is the root panel'
    })

    console.log('Tree created:', tree.isEmpty()) // false
    console.log('Tree size:', tree.getSize()) // 1
    console.log('Tree height:', tree.getHeight()) // 0

    // 添加左子节点
    const leftPanel: PanelData = {
        title: 'Left Panel',
        type: 'viewer',
        content: 'Left panel content'
    }

    const leftNode = new BinaryTreeNode('left-panel', leftPanel)
    tree.root?.addChild(leftNode)

    // 添加右子节点
    const rightPanel: PanelData = {
        title: 'Right Panel',
        type: 'browser'
    }

    const rightNode = new BinaryTreeNode('right-panel', rightPanel)
    tree.root?.addChild(rightNode)

    console.log('After adding children:')
    console.log('Tree size:', tree.getSize()) // 3
    console.log('Tree height:', tree.getHeight()) // 1
    console.log('Root has left child:', tree.root?.hasLeftChild()) // true
    console.log('Root has right child:', tree.root?.hasRightChild()) // true

    return tree
}

/**
 * 遍历示例
 */
export function traversalExample() {
    const tree = basicUsageExample()

    console.log('\n=== 先序遍历 ===')
    tree.preOrderTraversal((node) => {
        console.log(`${node.id}: ${node.data.title}`)
    })

    console.log('\n=== 中序遍历 ===')
    tree.inOrderTraversal((node) => {
        console.log(`${node.id}: ${node.data.title}`)
    })

    console.log('\n=== 层序遍历 ===')
    tree.levelOrderTraversal((node) => {
        console.log(`${node.id}: ${node.data.title} (深度: ${node.getDepth()})`)
    })

    return tree
}

/**
 * 查找示例
 */
export function searchExample() {
    const tree = basicUsageExample()

    // 按 ID 查找
    const foundNode = tree.findById('left-panel')
    console.log('Found by ID:', foundNode?.data.title)

    // 按条件查找
    const viewerNode = tree.find((node) => node.data.type === 'viewer')
    console.log('Found viewer:', viewerNode?.data.title)

    // 获取所有叶子节点
    const leafNodes = tree.getLeafNodes()
    console.log('Leaf nodes:', leafNodes.map(node => node.data.title))

    return tree
}

/**
 * 分屏布局示例
 */
export function splitScreenExample() {
    // 创建一个模拟分屏布局的树结构
    const tree = new BinaryTree<PanelData>()

    // 根节点表示整个屏幕
    tree.setRoot({
        title: 'Main Screen',
        type: 'editor'
    }, 'main-screen')

    // 第一次分割：左右分屏
    const leftSection = new BinaryTreeNode<PanelData>('left-section', {
        title: 'Left Section',
        type: 'editor'
    })

    const rightSection = new BinaryTreeNode<PanelData>('right-section', {
        title: 'Right Section',
        type: 'viewer'
    })

    tree.root?.addChild(leftSection)
    tree.root?.addChild(rightSection)

    // 对左侧再次分割：上下分屏
    const leftTop = new BinaryTreeNode<PanelData>('left-top', {
        title: 'Left Top',
        type: 'editor',
        content: 'Code Editor'
    })

    const leftBottom = new BinaryTreeNode<PanelData>('left-bottom', {
        title: 'Left Bottom',
        type: 'browser',
        content: 'Terminal'
    })

    leftSection.addChild(leftTop)
    leftSection.addChild(leftBottom)

    console.log('\n=== 分屏布局结构 ===')
    tree.levelOrderTraversal((node) => {
        const indent = '  '.repeat(node.getDepth())
        const childrenInfo = node.isLeaf() ? '(叶子节点)' : `(${node.children.length} 个子节点)`
        console.log(`${indent}${node.data.title} ${childrenInfo}`)
    })

    return tree
}

/**
 * JSON 序列化/反序列化示例
 */
export function serializationExample() {
    const tree = splitScreenExample()

    // 序列化为 JSON
    const json = tree.toJSON()
    console.log('\n=== 序列化的 JSON ===')
    console.log(JSON.stringify(json, null, 2))

    // 从 JSON 反序列化
    const restoredTree = BinaryTree.fromJSON<PanelData>(json)
    console.log('\n=== 反序列化后的树 ===')
    console.log('Restored tree size:', restoredTree.getSize())

    restoredTree.preOrderTraversal((node) => {
        console.log(`${node.id}: ${node.data.title}`)
    })

    return restoredTree
}

/**
 * 克隆示例
 */
export function cloneExample() {
    const tree = basicUsageExample()

    // 克隆树
    const clonedTree = tree.clone((data) => ({
        ...data,
        title: `${data.title} (Clone)`
    }))

    console.log('\n=== 原始树 ===')
    tree.preOrderTraversal((node) => {
        console.log(`${node.id}: ${node.data.title}`)
    })

    console.log('\n=== 克隆的树 ===')
    clonedTree.preOrderTraversal((node) => {
        console.log(`${node.id}: ${node.data.title}`)
    })

    return clonedTree
}

/**
 * 动态操作示例
 */
export function dynamicOperationsExample() {
    const tree = new BinaryTree<PanelData>()
    tree.setRoot({
        title: 'Dynamic Root',
        type: 'editor'
    })

    console.log('\n=== 动态操作示例 ===')
    console.log('Initial tree size:', tree.getSize())

    // 动态添加节点
    const node1 = new BinaryTreeNode<PanelData>('node-1', { title: 'Node 1', type: 'viewer' })
    const node2 = new BinaryTreeNode<PanelData>('node-2', { title: 'Node 2', type: 'browser' })

    tree.root?.addChild(node1)
    tree.root?.addChild(node2)

    console.log('After adding 2 nodes:', tree.getSize())

    // 尝试添加第三个节点（应该失败）
    try {
        const node3 = new BinaryTreeNode<PanelData>('node-3', { title: 'Node 3', type: 'editor' })
        tree.root?.addChild(node3)
    } catch (error) {
        console.log('Expected error:', (error as Error).message)
    }

    // 移除一个节点
    const removed = tree.root?.removeChild(node1)
    console.log('Node removed:', removed)
    console.log('Tree size after removal:', tree.getSize())

    // 现在可以添加新节点了
    const node3 = new BinaryTreeNode<PanelData>('node-3', { title: 'Node 3', type: 'editor' })
    tree.root?.addChild(node3)
    console.log('Tree size after adding node 3:', tree.getSize())

    return tree
}

// 如果作为脚本运行，执行所有示例
if (typeof require !== 'undefined' && require.main === module) {
    console.log('=== 改良版二叉树示例 ===')

    basicUsageExample()
    traversalExample()
    searchExample()
    splitScreenExample()
    serializationExample()
    cloneExample()
    dynamicOperationsExample()
} 