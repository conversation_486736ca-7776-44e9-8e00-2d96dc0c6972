@import 'katex/dist/katex.min.css';
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em;
}
code.hljs {
  padding: 3px 5px;
}
.hljs {
  color: #383a42;
  background: #fafafa;
}
.hljs-comment,
.hljs-quote {
  color: #a0a1a7;
  font-style: italic;
}
.hljs-doctag,
.hljs-formula,
.hljs-keyword {
  color: #a626a4;
}
.hljs-deletion,
.hljs-name,
.hljs-section,
.hljs-selector-tag,
.hljs-subst {
  color: #e45649;
}
.hljs-literal {
  color: #0184bb;
}
.hljs-addition,
.hljs-attribute,
.hljs-meta .hljs-string,
.hljs-regexp,
.hljs-string {
  color: #50a14f;
}
.hljs-attr,
.hljs-number,
.hljs-selector-attr,
.hljs-selector-class,
.hljs-selector-pseudo,
.hljs-template-variable,
.hljs-type,
.hljs-variable {
  color: #986801;
}
.hljs-bullet,
.hljs-link,
.hljs-meta,
.hljs-selector-id,
.hljs-symbol,
.hljs-title {
  color: #4078f2;
}
.hljs-built_in,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #c18401;
}
.hljs-emphasis {
  font-style: italic;
}
.hljs-strong {
  font-weight: 700;
}
.hljs-link {
  text-decoration: underline;
}

/* =============================================================================
   全局滚动条优化
   ============================================================================= */

/* 支持 Firefox 的滚动条样式 */
* {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

/* 全局 Webkit 滚动条基础样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

/* =============================================================================
   CSS Variables - 统一的主题系统
   ============================================================================= */
:root {
  /* Tailwind 兼容的灰度色彩 */
  --gray-1: rgb(249, 250, 251);
  --gray-2: rgb(243, 244, 246);
  --gray-3: rgb(229, 231, 235);
  --gray-4: rgb(209, 213, 219);
  --black: #000000;
  --white: #ffffff;

  /* 基础色彩系统 (来自宋朝茶文化主题) */
  --base-background-color: #ffffff;
  --base-text-color: #444444;
  --base-text-secondary-color: #888888;
  --base-text-tertiary-color: #d9d9d9;
  --base-background-secondary-color: #f3f5f7;
  --base-background-tertiary-color: #e4e5e6;
  --base-stroke-color: #d9d9d9;
  --base-stroke-secondary-color: #d9d9d9;
  --base-accent-color: #dd4c4f;

  /* 文档样式变量 */
  --document-background-color: var(--base-background-color);
  --document-text-color: var(--base-text-color);
  --document-text-secondary-color: var(--base-text-secondary-color);
  --document-text-light-color: var(--base-text-secondary-color);
  --document-accent-color: var(--base-accent-color);
  --document-link-color: var(--base-accent-color);
  --document-list-marker-color: var(--base-accent-color);
  --document-marker-color: var(--base-text-tertiary-color);

  /* 字体系统 */
  --document-text-font:
    'BearSansUI', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --document-text-size: 16px;
  --document-line-height-multiplier: 1.6;
  --document-headers-font:
    'BearSansUIHeading', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --document-code-font: 'Fira Code', 'Consolas', 'SF Mono', Monaco, monospace;

  /* 代码相关 */
  --document-code-text-color: var(--base-text-color);
  --document-code-background-color: var(--base-background-secondary-color);
  --document-inline-padding: 0.25rem;
  --document-hairline-width: 1px;
}

/* =============================================================================
   基础样式重置和通用组件
   ============================================================================= */

/* Slash command highlight */
.slash-highlight {
  @apply bg-base-100;
  border-radius: 4px;
  padding: 0.25rem;
}

/* ProseMirror editor base */
.ProseMirror {
  outline: none;
  @apply text-base-content leading-relaxed;
}

/* =============================================================================
   主编辑器容器 (.chron) - 统一的 markdown 样式
   ============================================================================= */
.chron {
  outline: none;
  font-family: var(--document-text-font);

  @apply px-16;
  font-size: var(--document-text-size);
  line-height: var(--document-line-height-multiplier);
  color: var(--document-text-color);
}

.chron:focus {
  outline: none;
}

/* =============================================================================
   标题系统
   ============================================================================= */
.chron h1,
.chron h2,
.chron h3,
.chron h4,
.chron h5,
.chron h6 {
  font-family: var(--document-headers-font);
  @apply font-[600] mb-4 mt-6;
  letter-spacing: -0.02em;
  position: relative;
  line-height: 1.3;
}

/* 编辑器焦点指示器 */
.chron h1.has-focus::before,
.chron h2.has-focus::before,
.chron h3.has-focus::before,
.chron h4.has-focus::before,
.chron h5.has-focus::before,
.chron h6.has-focus::before {
  @apply text-neutral-500 duration-200 transition-all;
  font-size: 0.75rem;
  top: 0;
  left: -28px;
  position: absolute;
}

.chron h1 {
  @apply text-3xl leading-tight;
  font-size: 2rem;
}

.chron h1.chron-title {
  @apply text-3xl font-[500] leading-tight text-base-content;
}

.chron h1:not(.chron-title).has-focus::before {
  content: 'H1';
}

.chron h2 {
  @apply text-2xl leading-tight;
  font-size: 1.6rem;
}

.chron h2.has-focus::before {
  content: 'H2';
}

.chron h3 {
  @apply text-xl leading-snug;
  font-size: 1.27rem;
}

.chron h3.has-focus::before {
  content: 'H3';
  font-size: 0.6rem;
}

.chron h4 {
  @apply text-lg leading-snug;
}

.chron h4.has-focus::before {
  content: 'H4';
  font-size: 0.6rem;
}

.chron h5 {
  @apply text-base;
}

.chron h5.has-focus::before {
  content: 'H5';
  font-size: 0.6rem;
}

.chron h6 {
  @apply text-sm;
}

.chron h6.has-focus::before {
  content: 'H6';
  font-size: 0.6rem;
}

/* =============================================================================
   段落系统
   ============================================================================= */
.chron p {
  @apply mb-4;
  margin-top: 0;
  transition: all 0.2s ease;
  border-radius: 0.25rem;
  padding: 0.125rem 0.25rem;
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}

.chron p:not(:first-child) {
  margin-top: 1rem;
}

/* 段落选择状态样式 */
.chron .ProseMirror-selectednode {
  @apply bg-base-100;
}

/* 段落间距调整 */
.chron p + p {
  @apply mt-0;
}

/* 缩进样式 */
.chron p[data-indent] {
  transition: text-indent 0.2s ease;
}

.chron p[data-indent='1'] {
  text-indent: 1rem;
}
.chron p[data-indent='2'] {
  text-indent: 2rem;
}
.chron p[data-indent='3'] {
  text-indent: 3rem;
}
.chron p[data-indent='4'] {
  text-indent: 4rem;
}
.chron p[data-indent='5'] {
  text-indent: 5rem;
}
.chron p[data-indent='6'] {
  text-indent: 6rem;
}
.chron p[data-indent='7'] {
  text-indent: 7rem;
}

/* =============================================================================
   列表系统
   ============================================================================= */
.chron ul,
.chron ol {
  @apply my-4 ml-6 space-y-1;
  padding-left: 1.5rem;
}

.chron ul li,
.chron ol li {
  @apply leading-7 mb-1 text-base-content;
}

.chron ul li::marker,
.chron ol li::marker {
  color: var(--document-list-marker-color);
}

/* 嵌套段落 */
.chron ul li > p,
.chron ol li > p {
  display: block;
  margin: 0 !important;
  margin-bottom: 0.5rem;
}

/* 如果列表项只有一个段落，则不需要下边距 */
.chron ul li > p:only-child,
.chron ol li > p:only-child {
  margin-bottom: 0;
}

/* 列表项中的最后一个段落不需要下边距 */
.chron ul li > p:last-child,
.chron ol li > p:last-child {
  margin-bottom: 0;
}

/* 无序列表 */
.chron ul {
  list-style-type: disc;
}

/* 嵌套列表样式 */
.chron ul ul {
  list-style-type: circle;
  margin-top: 0.5rem;
}

.chron ul ul ul {
  list-style-type: square;
}

.chron ul ul ul ul {
  list-style-type: disc;
}

/* 任务列表特殊样式 */
.chron ul[data-type='taskList'] {
  @apply list-none p-0 m-0 space-y-2;
  padding-left: 0;
}

.chron ul[data-type='taskList'] li {
  @apply flex items-start gap-3 mb-2;
  list-style: none;
}

.chron ul[data-type='taskList'] li label {
  @apply flex items-center select-none cursor-pointer mt-[0.125rem];
}

.chron ul[data-type='taskList'] li input[type='checkbox'] {
  @apply m-0 w-4 h-4 cursor-pointer flex-shrink-0;
  margin-top: 0.375rem;
}

.chron ul[data-type='taskList'] li div {
  @apply flex-1 mb-0;
}

.chron ul[data-type='taskList'] li div p {
  @apply m-0 min-h-[24px] block w-full;
  display: block;
}

/* 已完成任务的删除线效果 */
.chron ul[data-type='taskList'] li[data-checked='true'] > div > p {
  text-decoration: line-through;
  color: var(--document-text-secondary-color);
}

/* 有序列表 */
.chron ol {
  list-style-type: decimal;
  counter-reset: list-counter;
}

.chron ol li {
  counter-increment: list-counter;
}

.chron ol li p {
  @apply mb-0;
}

.chron ol li::marker {
  @apply text-base-content/75;
  color: var(--document-list-marker-color);
}

/* 嵌套有序列表 */
.chron ol ol {
  list-style-type: lower-alpha;
  margin-top: 0.5rem;
}

.chron ol ol li::marker {
  @apply text-base-content/75;
}

.chron ol ol ol {
  list-style-type: lower-roman;
}

.chron ol ol ol li::marker {
  @apply text-base-content/70;
}

/* =============================================================================
   引用块
   ============================================================================= */
.chron blockquote {
  @apply border-l-4 border-accent bg-base-100 py-1 px-4 my-6 rounded-r-lg;
  font-style: italic;
  position: relative;
  margin-left: 0;
  border-left-color: var(--document-accent-color);
}

.chron blockquote p {
  @apply leading-7;
  margin: 0;
}

.chron blockquote p:not(:last-child) {
  margin-bottom: 0.75rem;
}

/* 嵌套引用 */
.chron blockquote blockquote {
  margin: 1rem 0;
  opacity: 0.8;
}

/* =============================================================================
   代码样式
   ============================================================================= */
.chron code {
  font-size: 0.875rem;
  font-variant-ligatures: common-ligatures;
  font-weight: 500;
  @apply bg-base-100! px-2 py-1 text-base-content rounded text-sm;
}

.chron pre {
  @apply overflow-hidden bg-base-100 border-accent    my-6 rounded-lg leading-6;
  padding: 1.5rem;
}

.chron pre code {
  color: inherit;
  padding: 0;
  background: none !important;
  @apply text-sm;
  font-family: var(--document-code-font) !important;
  display: block;
  font-weight: 400;
}

/* =============================================================================
   链接
   ============================================================================= */
.chron a {
  color: var(--document-link-color);
  @apply no-underline border-b border-current hover:opacity-70 cursor-pointer transition-all duration-200;
  text-decoration: none;
  border-bottom: 1px solid currentColor;
}

.chron a:hover {
  opacity: 0.7;
}

/* =============================================================================
   文本格式化
   ============================================================================= */
.chron strong {
  @apply font-bold;
  font-family: var(--document-headers-font);
}

.chron em {
  font-style: italic;
}

.chron u {
  text-decoration: underline;
  text-decoration-color: var(--document-accent-color);
}

.chron s,
.chron del {
  text-decoration: line-through;
}

/* =============================================================================
   高亮和标记
   ============================================================================= */
.chron mark {
  background-color: #fff3cd;
  color: #856404;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

.chron mark.red {
  background-color: #fed09f;
  color: #321a00;
}

.chron mark.blue {
  background-color: #c9e5ff;
  color: #001a32;
}

.chron mark.green {
  background-color: #cdf7bd;
  color: #102d05;
}

.chron mark.yellow {
  background-color: #fcf195;
  color: #312c01;
}

.chron mark.purple {
  background-color: #fedaff;
  color: #310032;
}

/* =============================================================================
   水平线
   ============================================================================= */
.chron hr {
  border: none;
  /* 确保可以接收鼠标事件 */
  cursor: pointer;
  position: relative;
  /* 平衡的上下间距，扩大可交互区域 */
  margin: 1.5rem 0;
  padding: 1rem 0;
  /* 设置最小高度确保有足够的可交互区域 */
  min-height: 2rem;
  /* 使用 flex 布局让线条居中 */
  display: flex;
  align-items: center;
}

/* 使用伪元素在中间绘制线条 */
.chron hr::before {
  content: '';
  width: 100%;
  height: var(--document-hairline-width);
  background-color: var(--base-stroke-color);
  display: block;
}

.chron hr.has-focus::before {
  height: 2px;
  background-color: var(--document-accent-color);
}

/* HR 悬停时保持透明，无视觉变化 */
.chron hr:hover {
  background-color: transparent;
}

/* =============================================================================
   图片
   ============================================================================= */
.chron img {
  max-width: 100%;
  height: auto;
  margin: 1rem 0;
  border-radius: 0.5rem;
}

/* =============================================================================
   特殊元素和交互
   ============================================================================= */

/* Callout 样式 */
.chron .callout {
  @apply border-l-4 bg-base-200 py-3 px-4 my-4 rounded-r-lg;
}

.chron .callout div {
  @apply mb-0 mt-2;
}

.chron .callout div p {
  @apply leading-6 m-0;
}

/* =============================================================================
   编辑器特定功能
   ============================================================================= */

/* 引擎模式下的特殊样式 */
.chron-engine {
  @apply px-2 rounded-sm flex items-center !text-base-content;
}

/* 拖拽指示线 */
.ProseMirror .drop-cursor {
  position: relative;
  pointer-events: none;
}

.ProseMirror .drop-cursor::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -1000px;
  right: -1000px;
  height: 4px;
  @apply bg-primary;
  z-index: 50;
  border-radius: 2px;
}

.ProseMirror .ProseMirror-dropcursor {
  position: relative;
  pointer-events: none;
}

.ProseMirror .ProseMirror-dropcursor::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -1000px;
  right: -1000px;
  height: 4px;
  @apply bg-primary;
  z-index: 50;
  border-radius: 2px;
}

/* 搜索结果高亮 */
.search-result {
  @apply bg-yellow-300/70! dark:bg-yellow-500/40! rounded-sm;
}

.search-result-current {
  @apply bg-yellow-400! dark:bg-yellow-600!;
}

/* 空状态占位符 */
.is-empty::before {
  @apply text-base-content/30 dark:text-base-content/60;
  content: attr(data-placeholder);
  float: left;
  pointer-events: none;
  height: 0;
}

/* 块锚点样式 */
[data-block-anchor] {
  position: relative;
  cursor: pointer;
  border-radius: 3px;
  padding: 0 2px;
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
  border-bottom: 1px solid rgba(59, 130, 246, 0.3);
  transition: all 0.2s ease;
  background-color: transparent !important;
}

[data-block-anchor]:hover {
  color: #2563eb;
  border-bottom-color: rgba(37, 99, 235, 0.6);
}

/* 数学编辑器 */
.Tiptap-mathematics-editor {
  @apply bg-base-300 px-2 rounded-sm py-1;
}

/* =============================================================================
   表格样式 - 直接在.chron容器内的表格 (enhancedTable)
   ============================================================================= */
.chron table {
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
  background: var(--base-background);
  margin: 1.5rem 0;
  border: 1px solid var(--base-200);
  table-layout: fixed;
}

.chron table th,
.chron table td {
  padding: 0.5rem 0.75rem;
  border-right: 1px solid var(--base-200);
  border-bottom: 1px solid var(--base-200);
  text-align: left;
  vertical-align: top;
  position: relative;
  min-width: 120px;
  color: var(--base-content);
  background: var(--base-background);
  box-sizing: border-box;
}

/* header 和 body cell 颜色保持一致 */
.chron table th {
  font-weight: 600;
  background: var(--base-background);
  color: var(--base-content);
}

.chron table td {
  background: var(--base-background);
}

/* 移除最后一列的右边框 */
.chron table th:last-child,
.chron table td:last-child {
  border-right: none;
}

/* 移除最后一行的底边框 */
.chron table tbody tr:last-child td {
  border-bottom: none;
}


/* 聚焦状态 */
.chron table td:focus {
  outline: none;
  background: var(--base-150);
  box-shadow: 0 0 0 2px var(--accent);
}

/* 单元格选择状态 - 使用背景色而不是遮罩层 */
.chron table .selectedCell {
  background: rgba(59, 130, 246, 0.1) !important; /* 蓝色半透明背景 */
  border: 2px solid rgba(59, 130, 246, 0.3) !important; /* 蓝色边框 */
}

/* 选中状态的悬浮效果 */
.chron table .selectedCell:hover {
  background: rgba(59, 130, 246, 0.15) !important;
}

/* 可选：如果需要遮罩层效果，使用更轻的透明度 */
.chron table .selectedCell.with-overlay::after {
  background: rgba(59, 130, 246, 0.05);
  content: '';
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  pointer-events: none;
  position: absolute;
  z-index: 0; /* 在内容之下 */
}

/* 列调整大小句柄 */
.chron table .column-resize-handle {
  background-color: var(--document-accent-color);
  bottom: -2px;
  pointer-events: auto;
  position: absolute;
  right: -2px;
  top: 0;
  width: 4px;
  cursor: pointer;
  opacity: 0.3;
  transition: opacity 0.2s ease;
}

.chron table .column-resize-handle:active {
  opacity: 1;
  cursor: col-resize;
  background-color: var(--document-accent-color);
}

/* ProseMirror 选择相关的样式 */
.chron table .ProseMirror-selectednode {
  outline: none;
}

.chron table td p {
  margin: 0 !important;
  min-height: 1.2rem;
  line-height: 1.3;
  margin-top: 0 !important;
}

.chron table td p,
.chron table th p {
  margin: 0 !important;
  padding: 0;
  line-height: 1.3;
  margin-top: 0 !important;
}

/* 表格包装器 - 简化的容器样式 */
.chron .tableWrapper {
  margin: 1.5rem 0;
  position: relative;
}

/* 当表格调整大小时的光标样式 */
.chron.resize-cursor {
  cursor: ew-resize;
  cursor: col-resize;
}

/* =============================================================================
   Enhanced Table 悬浮按钮样式
   ============================================================================= */

/* 悬浮控制容器 */
.chron .tableWrapper .hover-controls {
  position: absolute;
  pointer-events: auto;
  z-index: 1000;
  opacity: 0;
  transition: opacity 0.15s ease;
}

.chron .tableWrapper:hover .hover-controls {
  opacity: 1;
}

/* 添加列按钮 - 动态定位 */
.chron .tableWrapper .add-column-btn {
  /* 位置将由 JavaScript 动态设置 */
}

/* 添加行按钮 - 动态定位 */
.chron .tableWrapper .add-row-btn {
  /* 位置将由 JavaScript 动态设置 */
}

/* Plus 按钮样式 */
.chron .tableWrapper .hover-controls .plus-btn {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: normal;
  cursor: pointer;
  background: var(--base-background);
  border: 1px solid var(--base-border);
  color: var(--base-content-secondary);
  transition: all 0.15s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  user-select: none;
}

.chron .tableWrapper .hover-controls .plus-btn:hover {
  background: var(--base-100);
  color: var(--base-content);
  border-color: var(--accent);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: scale(1.1);
}

/* 表格信息标签 - 动态定位 */
.chron .tableWrapper .table-info {
  position: absolute;
  /* 位置将由 JavaScript 动态设置 */
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.15s ease;
  background: var(--base-100);
  color: var(--base-content-secondary);
  border: 1px solid var(--base-border);
}

.chron .tableWrapper:hover .table-info {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chron .tableWrapper .hover-controls .plus-btn {
    width: 20px;
    height: 20px;
    font-size: 11px;
  }
}

/* 确保按钮在不同设备上都有足够的点击区域 */
@media (pointer: coarse) {
  .chron .tableWrapper .hover-controls .plus-btn {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .chron .tableWrapper .hover-controls .plus-btn {
    border-width: 0.5px;
  }
}
