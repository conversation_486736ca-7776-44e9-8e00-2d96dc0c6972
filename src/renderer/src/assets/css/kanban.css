/* Kanban Column Styles */
.kanban-column {
  width: 100%; /* 自适应宽度 */
  min-width: 0; /* 允许收缩 */
  border-radius: 0.5rem;
  border: 1px solid var(--base-300);
  background-color: var(--actual-background);
}

.kanban-column-header {
  padding: 1rem;
  border-bottom: 1px solid var(--base-300);
  background-color: var(--actual-background);
}

.kanban-column-content {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  height: 100%;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
  min-height: 200px;
}

.kanban-column-content:empty::before {
  content: '拖放待办事项到这里';
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 8rem;
  border: 2px dashed var(--base-300);
  border-radius: 0.5rem;
  color: rgba(var(--base-content), 0.4);
}

/* Drag and drop styling */
.kanban-column-content[data-status]:hover {
  background-color: var(--base-50);
}

.kanban-column-content[data-status].drag-over {
  border-color: rgb(147 197 253); /* blue-300 */
  background-color: rgb(239 246 255); /* blue-50 equivalent */
}

/* 已逾期列不接受拖拽 */
.kanban-column-content[data-status='overdue']:hover {
  background-color: var(--base-100) !important;
}

.kanban-column-content[data-status='overdue'].drag-over {
  border-color: var(--base-300) !important;
  background-color: var(--base-100) !important;
}

/* Kanban Card Styles */
.kanban-card {
  position: relative;
  background-color: var(--actual-background);
  border-radius: 0.5rem;
  border: 1px solid var(--base-300);
  padding: 0.75rem;
  cursor: pointer;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  transition: all 0.2s;
}

.kanban-card:hover {
  border-color: var(--base-400);
  box-shadow:
    0 4px 6px -1px rgb(0 0 0 / 0.1),
    0 2px 4px -2px rgb(0 0 0 / 0.1);
  transform: translateY(-1px);
}

/* 拖拽状态下的光标样式 */
.kanban-card:active {
  cursor: grabbing;
}

.kanban-card[draggable='true']:hover {
  cursor: grab;
}

.kanban-card--selected {
  border-color: var(--base-400);
  box-shadow: 0 0 0 2px var(--base-400);
}

.kanban-card--completed {
  opacity: 0.75;
}

.kanban-card--disabled:hover {
  transform: none !important;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05) !important;
  /* border-color: var(--error) !important; - removed red border */
}

/* 确保逾期的待办事项不显示拖拽手柄，保持普通pointer光标 */
.kanban-card--overdue:hover,
.kanban-card--disabled:hover {
  cursor: pointer !important;
}

.kanban-card--overdue[draggable='true']:hover,
.kanban-card--disabled[draggable='true']:hover {
  cursor: pointer !important;
}

.kanban-card__priority {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
}

.kanban-card__content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.kanban-card__title {
  font-weight: 600;

  font-size: 0.875rem;
  line-height: 1.25rem;
  padding-right: 1.5rem; /* Make room for priority icon */
}

.kanban-card--completed .kanban-card__title {
  text-decoration: line-through;
}

.kanban-card__notes {
  font-size: 12px !important;
  line-height: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.kanban-card__due-date {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: rgba(var(--base-content), 0.6);
}

/* .kanban-card__due-date--overdue {
  color: var(--error);
} */

.kanban-card__due-date--today {
  color: var(--warning);
}

.kanban-card__due-date--tomorrow {
  color: var(--info);
}

.kanban-card__due-date--completed {
  color: rgba(var(--base-content), 0.5);
}

.kanban-card__tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.kanban-card__tag {
  display: inline-flex;
  align-items: center;
  padding: 0.125rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  background-color: var(--base-200);
  color: rgba(var(--base-content), 0.8);
}
