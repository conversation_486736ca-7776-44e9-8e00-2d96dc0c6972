/* 拖拽手柄相关样式 */

/* 调整窗口大小时的鼠标样式 */
body.cursor-ew-resize,
body.cursor-ns-resize,
body.cursor-nwse-resize,
body.cursor-nesw-resize {
  user-select: none;
}

.resize-in-progress * {
  cursor: inherit !important;
  user-select: none !important;
}

/* 全局拖拽手柄样式 */
.global-drag-handle {
  position: fixed;
  bottom: 1.2rem;
  right: 1.2rem;
  z-index: 50;
  cursor: pointer;
  width: 1.2rem;
  height: 1.6rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.3rem;
  border-radius: 0.25rem;
}

.global-drag-handle:hover {
  background-color: rgba(0, 0, 0, 0.1);
  opacity: 1;
}

.global-drag-handle.hide {
  opacity: 0;
  pointer-events: none;
}

/* 窗口调整边缘样式 */
.resize-edge {
  position: absolute;
  z-index: 100;
}

.resize-edge.right {
  top: 0;
  right: 0;
  width: 10px;
  height: 100%;
  cursor: ew-resize;
}

.resize-edge.left {
  top: 0;
  left: 0;
  width: 10px;
  height: 100%;
  cursor: ew-resize;
}

.resize-edge.top {
  top: 0;
  left: 0;
  width: 100%;
  height: 10px;
  cursor: ns-resize;
}

.resize-edge.bottom {
  bottom: 0;
  left: 0;
  width: 100%;
  height: 10px;
  cursor: ns-resize;
}

.resize-edge.corner {
  width: 20px;
  height: 20px;
}

.resize-edge.top-left {
  top: 0;
  left: 0;
  cursor: nwse-resize;
}

.resize-edge.top-right {
  top: 0;
  right: 0;
  cursor: nesw-resize;
}

.resize-edge.bottom-left {
  bottom: 0;
  left: 0;
  cursor: nesw-resize;
}

.resize-edge.bottom-right {
  bottom: 0;
  right: 0;
  cursor: nwse-resize;
}

/* 拖拽手柄菜单样式 */
.drag-handle-menu {
  background-color: var(--base-100);
  border: 1px solid rgba(var(--base-content), 0.1);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.drag-handle-menu-item {
}

.drag-handle-menu-item:hover,
.drag-handle-menu-item:focus {
  background-color: rgba(var(--base-content), 0.1);
}

.drag-handle-menu-item.danger {
  color: #dc2626; /* red-600 */
}

/* 响应式调整 */
@media (max-width: 768px) {
  .global-drag-handle {
    bottom: 1rem;
    right: 1rem;
    width: 1rem;
    height: 1.4rem;
    font-size: 1.1rem;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .global-drag-handle:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
}
