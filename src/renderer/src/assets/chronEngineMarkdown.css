@import 'tailwindcss';
@import 'katex/dist/katex.min.css';
@plugin 'tailwind-scrollbar' {
  nocompatible: true;
}

/* ChronEngine AI 对话 Markdown 样式 */
.markdown-content {
  /* 基础变量定义 - Bear风格 */
  --transform-line-height-factor: 1.17;
  --base-text-color: #444444;
  --base-text-secondary-color: #888888;
  --base-text-tertiary-color: #d9d9d9;
  --base-background-color: #ffffff;
  --base-background-secondary-color: #f3f5f7;
  --base-background-tertiary-color: #e4e5e6;
  --base-stroke-color: #d9d9d9;
  --base-stroke-secondary-color: #d9d9d9;
  --base-accent-color: #dd4c4f;

  --document-background-color: var(--base-background-color);
  --document-text-color: var(--base-text-color);
  --document-text-secondary-color: var(--base-text-secondary-color);
  --document-text-light-color: var(--base-text-secondary-color);
  --document-accent-color: var(--base-accent-color);
  --document-link-color: var(--base-accent-color);
  --document-list-marker-color: var(--base-accent-color);
  --document-marker-color: var(--base-text-tertiary-color);
  --document-text-font:
    'BearSansUI', 'AvenirNext-Regular', system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
  --document-text-size: 15px;
  --document-line-height-multiplier: calc(1.5 * var(--transform-line-height-factor));
  --document-headers-font:
    'BearSansUIHeading', 'AvenirNext-Medium', system-ui, -apple-system, BlinkMacSystemFont,
    sans-serif;
  --document-headers-line-height-multiplier: calc(1.3 * var(--transform-line-height-factor));
  --document-code-text-color: var(--base-text-color);
  --document-code-background-color: var(--base-background-secondary-color);
  --document-code-font: 'Menlo-Regular', monospace;
  --document-code-text-size-multiplier: 0.91em;
  --document-highlighter-background-color: #cdf7bd;
  --document-highlighter-text-color: #102d05;
  --document-separator-border-color: var(--base-stroke-secondary-color);
  --document-hairline-width: calc(var(--document-text-size) / 15);
  --document-inline-padding-top-bottom: 0.25em;
  --document-inline-padding-left-right: 0.25em;

  color: var(--document-text-color);
  font-family: var(--document-text-font);
  font-size: var(--document-text-size);
  line-height: var(--document-line-height-multiplier);
  width: 100%;
  text-rendering: optimizeLegibility;

  /* 段落样式 */
  p {
    margin: 0;
    padding-bottom: 0.5em;
    white-space: normal;
    word-break: normal;
    word-wrap: normal;
    display: block;
  }

  /* 标题样式 */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: var(--document-headers-font);
    line-height: var(--document-headers-line-height-multiplier);
    font-weight: 500;
    color: var(--document-text-color);
    margin: 0;
  }

  h1 {
    font-size: 2em;
    padding-block-start: 0.8em;
    padding-block-end: 0.33em;
  }

  h2 {
    font-size: 1.6em;
    padding-block-start: 0.66em;
    padding-block-end: 0.27em;
  }

  h3 {
    font-size: 1.27em;
    padding-block-start: 0.53em;
    padding-block-end: 0.27em;
  }

  h4,
  h5,
  h6 {
    padding-block-start: 0.4em;
    padding-block-end: 0.27em;
  }

  /* 列表样式 */
  ul,
  ol {
    padding-inline-start: 2.13em;
    margin-top: 0;
    margin-bottom: 0.5em;
  }

  li {
    color: var(--document-text-color);
  }

  ul {
    list-style-type: disc;
  }

  ul ul {
    list-style-type: circle;
  }

  ul ul ul {
    list-style-type: disc;
  }

  ol {
    list-style-type: decimal;
  }

  li::marker {
    color: var(--document-list-marker-color);
  }

  /* 代码样式 */
  code,
  pre {
    font-family: var(--document-code-font), monospace;
    font-size: var(--document-code-text-size-multiplier);
  }

  pre {
    border-radius: 0.25em;
    padding: 0.5em;
    font-family: 'Fira Code', monospace;
    margin: 0.5em 0;
    white-space: pre-wrap;
    overflow-x: auto;
    border: 1px solid var(--document-separator-border-color);
  }

  code {
    border-radius: 0.25em;
    padding: var(--document-inline-padding-top-bottom) var(--document-inline-padding-left-right);
    background-color: var(--document-code-background-color);
  }

  /* 引用样式 */
  blockquote {
    position: relative;
    padding-inline-start: 2.13em;
    margin: 0.5em 0;
  }

  blockquote::before {
    content: '';
    position: absolute;
    top: 0.2em;
    left: 1em;
    width: 0.13em;
    height: calc(100% - 0.4em);
    border: var(--document-hairline-width) solid var(--document-list-marker-color);
    border-radius: 0.33em;
    background-color: var(--document-list-marker-color);
  }

  /* 链接样式 */
  a {
    color: var(--document-link-color);
    text-decoration: none;
    cursor: pointer;
  }

  a:hover {
    text-decoration: underline;
  }

  /* 文本格式化 */
  strong,
  b {
    font-weight: 600;
  }

  em,
  i {
    font-style: italic;
  }

  s,
  strike {
    text-decoration: line-through;
  }

  u {
    text-decoration: underline;
    text-decoration-color: var(--document-accent-color);
  }

  /* 分隔线 */
  hr {
    border: none;
    border-top: var(--document-hairline-width) solid var(--document-separator-border-color);
    margin: 1em 0;
  }

  /* 表格样式 */
  table {
    border-collapse: separate;
    border-spacing: 0;
    border: var(--document-hairline-width) solid var(--document-separator-border-color);
    border-radius: 0.33em;
    margin: 0.5em 0;
    max-width: 100%;
  }

  tr {
    background-color: var(--document-background-color);
  }

  tr:nth-child(odd) {
    background-color: var(--document-background-secondary-color);
  }

  th,
  td {
    padding: 0.37em 0.75em;
    text-align: left;
    border-right: var(--document-hairline-width) solid var(--document-separator-border-color);
  }

  th {
    font-weight: 700;
  }

  td:last-of-type,
  th:last-of-type {
    border-right: none;
  }

  /* 图片样式 */
  img {
    max-width: 100%;
    height: auto;
    border-radius: 0.25em;
  }

  /* KaTeX 数学公式样式 */
  .katex {
    font-size: 1.1em;
    line-height: 1.2;
    white-space: normal;
    text-indent: 0;
  }

  .katex-display {
    margin: 1.2em 0;
    overflow-x: auto;
    overflow-y: hidden;
    padding: 0.5em 0;
    text-align: center;

    > .katex {
      display: flex;
      justify-content: center;
    }
  }

  /* 代码高亮样式 */
  pre code {
    background: transparent;
    padding: 0;

    .hljs {
      display: block;
      overflow-x: auto;
      padding: 1em;
      color: #2a2c2d;
      background: white;
    }
    .hljs {
      color: #2a2c2d;
      background: white;
    }
    .hljs-emphasis {
      font-style: italic;
    }
    .hljs-strong {
      font-weight: bold;
    }
    .hljs-link {
      text-decoration: underline;
    }
    .hljs-comment,
    .hljs-quote {
      color: #676b79;
      font-style: italic;
    }
    .hljs-params {
      color: #676b79;
    }
    .hljs-punctuation,
    .hljs-attr {
      color: #2a2c2d;
    }
    .hljs-selector-tag,
    .hljs-name,
    .hljs-meta,
    .hljs-operator,
    .hljs-char.escape_ {
      color: #c56200;
    }
    .hljs-keyword,
    .hljs-deletion {
      color: #d92792;
    }
    .hljs-regexp,
    .hljs-selector-pseudo,
    .hljs-selector-attr,
    .hljs-variable.language_ {
      color: #cc5e91;
    }
    .hljs-subst,
    .hljs-property,
    .hljs-code,
    .hljs-formula,
    .hljs-section,
    .hljs-title.function_ {
      color: #3787c7;
    }
    .hljs-string,
    .hljs-symbol,
    .hljs-bullet,
    .hljs-addition,
    .hljs-selector-class,
    .hljs-title.class_,
    .hljs-title.class_.inherited__,
    .hljs-meta .hljs-string {
      color: #0d7d6c;
    }
    .hljs-variable,
    .hljs-template-variable,
    .hljs-number,
    .hljs-literal,
    .hljs-type,
    .hljs-link,
    .hljs-built_in,
    .hljs-title,
    .hljs-selector-id,
    .hljs-tag,
    .hljs-doctag,
    .hljs-attribute,
    .hljs-template-tag,
    .hljs-meta .hljs-keyword {
      color: #7641bb;
    }
  }

  /* 代码块容器样式 */
  .code-block-container {
    position: relative;
    margin: 1rem 0;
    border-radius: 0.5rem;
    border: 1px solid #e5e7eb;
    overflow: hidden;
    background: var(--document-background-color);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;

    :hover {
      /* 移除上移动画，避免抖动 */
    }

    /* macOS风格的窗口控制按钮 */
    ::before {
      content: '';
      position: absolute;
      left: 1.5rem;
      top: 1.5rem;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: #ff5f57;
      box-shadow:
        20px 0 0 #ffbd2e,
        40px 0 0 #28ca42;
      z-index: 10;
    }

    .code-block-header {
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1.5rem 1.5rem 0 8rem;
      background: transparent;
      z-index: 10;

      .code-language {
        position: absolute;
        left: 50%;
        top: 1.5rem;
        transform: translateX(-50%);
        color: rgba(108, 117, 125, 0.8);
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.1em;
        font-family: var(--document-code-font), monospace;
        font-size: 0.7rem;
        margin: 0;
        opacity: 0;
        transition: opacity 0.2s ease;
      }

      .code-copy-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        padding: 0;
        background: rgba(255, 255, 255, 0.9);
        border: none;
        border-radius: 0.25rem;
        color: #6c757d;
        cursor: pointer;
        transition: all 0.2s ease;
        opacity: 0;
        backdrop-filter: blur(8px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        margin-left: auto;

        :hover {
          background: rgba(255, 255, 255, 1);
          color: #495057;
          transform: scale(1.05);
        }

        svg {
          width: 14px;
          height: 14px;
        }
      }
    }

    pre {
      margin: 0;
      border: none;
      border-radius: 0;
      background: transparent;
      padding: 3rem 1.5rem 1.5rem 1.5rem;

      code {
        border-radius: 0;
        background: transparent;
        padding: 0;
        font-size: 0.9rem;
        line-height: 1.6;
        font-family:
          'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', monospace;
      }
    }

    /* 悬停时显示复制按钮和语言标签 */
    :hover .code-copy-btn {
      opacity: 1;
    }

    :hover .code-language {
      opacity: 1;
    }
  }
}

/* 暗色模式支持 */
:root[data-theme='dark'] .markdown-content {
  --base-text-color: #e0e0e0;
  --base-text-secondary-color: #a0a0a0;
  --base-text-tertiary-color: #555555;
  --base-background-color: #1e1e1e;
  --base-background-secondary-color: #2a2a2a;
  --base-background-tertiary-color: #333333;
  --base-stroke-color: #444444;
  --base-stroke-secondary-color: #444444;
  --base-accent-color: #e86d6d;

  /* 暗色模式下的代码高亮 */
  pre code {
    .hljs {
      color: #e0e0e0;
      background: #1e1e1e;
    }
    .hljs {
      color: #e0e0e0;
      background: #1e1e1e;
    }
    .hljs-comment,
    .hljs-quote {
      color: #7c7c7c;
    }
    .hljs-params {
      color: #7c7c7c;
    }
    .hljs-punctuation,
    .hljs-attr {
      color: #e0e0e0;
    }
    .hljs-selector-tag,
    .hljs-name,
    .hljs-meta,
    .hljs-operator,
    .hljs-char.escape_ {
      color: #ff9500;
    }
    .hljs-keyword,
    .hljs-deletion {
      color: #ff6b9d;
    }
    .hljs-regexp,
    .hljs-selector-pseudo,
    .hljs-selector-attr,
    .hljs-variable.language_ {
      color: #ff8cc8;
    }
    .hljs-subst,
    .hljs-property,
    .hljs-code,
    .hljs-formula,
    .hljs-section,
    .hljs-title.function_ {
      color: #5ba7f7;
    }
    .hljs-string,
    .hljs-symbol,
    .hljs-bullet,
    .hljs-addition,
    .hljs-selector-class,
    .hljs-title.class_,
    .hljs-title.class_.inherited__,
    .hljs-meta .hljs-string {
      color: #5dd8aa;
    }
    .hljs-variable,
    .hljs-template-variable,
    .hljs-number,
    .hljs-literal,
    .hljs-type,
    .hljs-link,
    .hljs-built_in,
    .hljs-title,
    .hljs-selector-id,
    .hljs-tag,
    .hljs-doctag,
    .hljs-attribute,
    .hljs-template-tag,
    .hljs-meta .hljs-keyword {
      color: #b78cf2;
    }
  }

  /* 暗色模式下的代码块容器 */
  .code-block-container {
    background: #1e1e1e;
    border-color: #374151;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);

    :hover {
      /* 移除hover时的阴影变化 */
    }

    /* macOS圆点在暗色模式下保持相同颜色 */
    ::before {
      background: #ff5f57;
      box-shadow:
        20px 0 0 #ffbd2e,
        40px 0 0 #28ca42;
    }

    .code-block-header {
      .code-language {
        color: rgba(160, 160, 160, 0.8);
      }

      .code-copy-btn {
        background: rgba(45, 45, 45, 0.9);
        color: #a0a0a0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

        :hover {
          background: rgba(45, 45, 45, 1);
          color: #e0e0e0;
        }
      }
    }
  }
}

/* 消息容器特定样式 */
.user-message-container,
.ai-message-container {
  word-break: normal;
  white-space: normal;

  .markdown-content {
    font-size: 14px;

    > *:first-child {
      margin-top: 0;
      padding-top: 0;
    }

    > *:last-child {
      margin-bottom: 0;
      padding-bottom: 0;
    }

    p {
      display: inline;
    }

    pre {
      margin: 0.5em 0;
      font-size: 0.85em;
    }

    table {
      font-size: 0.9em;
      margin: 0.75em 0;
    }
  }
}

/* AI消息容器特定样式 */
.ai-message-container {
  overflow: hidden;
}
