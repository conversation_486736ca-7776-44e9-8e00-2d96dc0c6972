/**
 * ChronEngine组件相关的TypeScript类型定义
 */

import { JSONContent } from '@tiptap/core'

// AI消息角色类型
export type AIRole = 'user' | 'assistant'

// AI消息接口
export interface AIMessage {
  role: AIRole
  content: string
  formattedContent?: string
  isProcessingToolCall?: boolean
  toolCode?: string
  isThinking?: boolean
  thinkingContent?: string
  thinkingCompleted?: boolean
}

// AI模式类型
export type AIMode = 'Smart' | 'Simple'

// ChronEngine组件Props
export interface ChronEngineProps {
  node: any // Dockview节点类型
  panel_id: string
  hideInput?: boolean
  hideTabs?: boolean
  externalInput?: string
}

// ChronEngine组件Emits
export interface ChronEngineEmits {
  'close-pane': []
  'split-horizontal': []
  'split-vertical': []
  'send-message': [message: string]
}

// 图片文件类型
export interface ImageFile {
  type: 'image'
  transfer_method: 'remote_url'
  url: string
}

// 消息处理结果
export interface MessageProcessResult {
  displayContent: string
  aiPrompt: string
  files: ImageFile[]
}

// 菜单项接口 (从GlobalPaletteContextMenu导入的类型)
export interface MenuItem {
  id: string
  label: string
  icon: any // Lucide图标组件
  action: () => void
}

// AI聊天选项
export interface AIChatOptions {
  selectedMode: AIMode
  selectedDeepSearch: boolean
  enableThinking: boolean
}

// 滚动管理选项
export interface ScrollOptions {
  container: HTMLElement | null
  threshold?: number
  autoScroll?: boolean
}

// 图片上传映射
export interface ImageUploadMap {
  imageUrl: string
  cozeFileId: string
}

// Coze文件ID映射管理
export interface CozeFileManager {
  set: (imageUrl: string, cozeFileId: string) => void
  get: (imageUrl: string) => string | undefined
  clear: () => void
  getAllFileIds: () => string[]
}

// 工具调用状态
export interface ToolCallState {
  isProcessing: boolean
  toolCode?: string
  error?: string
}

// 思考状态
export interface ThinkingState {
  isThinking: boolean
  content: string
  completed: boolean
}

// AI处理状态
export interface AIProcessingState {
  isProcessing: boolean
  currentRequest: (() => void) | null
  conversationId: string
}

// 事件处理器类型
export interface EventHandlers {
  handleAddImage: (data: { panelId: string; imageUrl: string; cozeFileId?: string }) => void
  handleAddText: (data: { panelId: string; text: string }) => void
  handleCozeImageUploaded: (event: CustomEvent) => void
}

// 消息操作类型
export type MessageAction = 'copy' | 'delete' | 'save' | 'share'

// 消息操作处理器
export interface MessageActionHandlers {
  copyMessage: (index: number) => void
  copyAiMessage: (content: string) => void
  deleteAiMessage: (index: number) => void
  saveAiMessageToNote: (content: string) => Promise<void>
  shareAiMessage: (content: string) => void
}

// 编辑器操作
export interface EditorActions {
  addImageToEditor: (imageUrl: string, cozeFileId?: string) => void
  addTextToEditor: (text: string) => void
  clearEditor: () => void
  focusEditor: () => void
}

// 面板操作
export interface PanelActions {
  handleClosePane: () => void
  handleSplitHorizontal: () => void
  handleSplitVertical: () => void
}

// 键盘事件处理
export interface KeyboardEventHandlers {
  handleKeyDown: (e: KeyboardEvent) => void
}

// 外部消息发送
export interface ExternalMessageSender {
  sendExternalMessage: (message: string) => Promise<void>
}

// 组件暴露的方法
export interface ChronEngineExpose extends ExternalMessageSender {
  // 可以扩展其他需要暴露的方法
}

// 组件状态类型
export interface ChronEngineState {
  aiEditorContent: JSONContent
  aiPrompt: string
  aiConversation: AIMessage[]
  isAiProcessing: boolean
  currentConversationId: string
  selectedMode: AIMode
  selectedDeepSearch: boolean
  isUserScrolling: boolean
  lastScrollTop: number
  imageCozeFileIdMap: Map<string, string>
}

// 初始化选项
export interface ChronEngineInitOptions {
  autoFocus?: boolean
  enableImageUpload?: boolean
  enableToolCalling?: boolean
  enableThinking?: boolean
}