/**
 * AI API 相关类型定义
 */

// 文件上传响应
export interface UploadResponse {
  file_id: string
}

// 索引状态查询响应
export interface IndexStatusResponse {
  file_id: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
}

// AI聊天响应
export interface ChatResponse {
  content: string
  additional_kwargs: Record<string, any>
  response_metadata: {
    prompt_feedback: {
      block_reason: number
      safety_ratings: any[]
    }
    finish_reason: string
    model_name: string
    safety_ratings: any[]
  }
  type: 'ai'
  name: string | null
  id: string
  example: boolean
  tool_calls: any[]
  invalid_tool_calls: any[]
  usage_metadata: {
    input_tokens: number
    output_tokens: number
    total_tokens: number
    input_token_details: {
      cache_read: number
    }
  }
}

// AI聊天请求参数
export interface ChatRequest {
  user_prompt: string
  file_type?: string
  file_id?: string
  web_search?: boolean
}

// 服务器信息响应
export interface ServerInfoResponse {
  message: string
  version: string
}

// 健康检查响应
export interface HealthResponse {
  status: string
}

// 配置响应
export interface ConfigResponse {
  [key: string]: any
}

// 错误响应
export interface ErrorResponse {
  error: string
  type: string
} 