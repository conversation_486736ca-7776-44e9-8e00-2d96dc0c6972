import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'
import type { AddPanelOptions, DockviewApi, IDockviewPanel } from 'dockview-vue'

export const useDockviewStore = defineStore('dockview', () => {
  const api = ref<DockviewApi>()
  const panels = reactive<IDockviewPanel[]>([])
  const activePanelId = ref<string | null>(null)

  const setApi = (dockviewApi: any) => {
    api.value = dockviewApi
  }

  const getApi = () => {
    return api.value
  }

  const addPanel = <T extends object = Record<string, unknown>>(
    options: AddPanelOptions<T>
  ): IDockviewPanel | null => {
    if (!api.value) {
      console.warn('DockviewApi is not available')
      return null
    }
    
    // 为所有面板默认设置 renderer: 'always' 以防止 iframe 重新加载
    const optionsWithAlways = {
      ...options,
      renderer: options.renderer || 'always'
    }
    
    const panel = api.value.addPanel(optionsWithAlways)
    if (panel) {
      panels.push(panel as IDockviewPanel)
    }
    return panel || null
  }

  const removePanel = (id: string): boolean => {
    if (!api.value) {
      console.warn('DockviewApi is not available')
      return false
    }

    const panel = api.value.getPanel(id)
    if (panel) {
      panel.api.close()
      const index = panels.findIndex((p) => p.id === id)
      if (index >= 0) {
        panels.splice(index, 1)
        return true
      }
    }
    return false
  }

  const getPanel = (id: string): any => {
    return panels.find((p) => p.id === id)
  }

  const getAllPanels = (): any[] => {
    return [...panels]
  }

  const getPanelIds = (): string[] => {
    return panels.map((p) => p.id)
  }

  const replaceNode = (id: string, newParams: any): boolean => {
    if (!api.value) {
      console.warn('DockviewApi is not available')
      return false
    }

    const panel = api.value.getPanel(id)
    if (panel) {
      panel.api.updateParameters({
        ...panel.params,
        ...newParams
      })
      return true
    }
    return false
  }

  const setActivePanelId = (panelId: string | null) => {
    activePanelId.value = panelId
  }

  const getActivePanel = (): IDockviewPanel | null => {
    if (!api.value) return null
    return api.value.activePanel
  }

  const getActivePanelId = (): string | null => {
    if (!api.value) return null
    return api.value.activePanel?.id || null
  }

  const closeActivePanel = (): boolean => {
    const activePanel = getActivePanel()
    if (activePanel) {
      return removePanel(activePanel.id)
    }
    return false
  }

  return {
    api,
    panels,
    activePanelId,
    setApi,
    getApi,
    addPanel,
    removePanel,
    getPanel,
    getAllPanels,
    getPanelIds,
    replaceNode,
    setActivePanelId,
    getActivePanel,
    getActivePanelId,
    closeActivePanel
  }
})
