import { ref, readonly } from 'vue'
import { defineStore } from 'pinia'

/**
 * 功能介绍状态管理
 * 记录用户已经确认过的介绍弹窗，确保每个介绍只显示一次
 */
export const useIntroStore = defineStore(
  'intro',
  () => {
    // 已确认的介绍 ID 列表
    const acknowledgedIntros = ref<string[]>([])

    /**
     * 检查指定 ID 的介绍是否已经被确认过
     * @param id 介绍 ID
     * @returns 是否已确认
     */
    const isAcknowledged = (id: string): boolean => {
      return acknowledgedIntros.value.includes(id)
    }

    /**
     * 标记指定 ID 的介绍为已确认
     * @param id 介绍 ID
     */
    const acknowledge = (id: string): void => {
      if (!acknowledgedIntros.value.includes(id)) {
        acknowledgedIntros.value.push(id)
      }
    }

    /**
     * 重置指定 ID 的确认状态（用于开发调试）
     * @param id 介绍 ID，如果不提供则重置所有
     */
    const resetAcknowledgment = (id?: string): void => {
      if (id) {
        const index = acknowledgedIntros.value.indexOf(id)
        if (index > -1) {
          acknowledgedIntros.value.splice(index, 1)
        }
      } else {
        acknowledgedIntros.value = []
      }
    }

    /**
     * 获取所有已确认的介绍 ID 列表
     * @returns 已确认的 ID 数组
     */
    const getAcknowledgedList = (): readonly string[] => {
      return readonly(acknowledgedIntros.value)
    }

    return {
      acknowledgedIntros: readonly(acknowledgedIntros),
      isAcknowledged,
      acknowledge,
      resetAcknowledgment,
      getAcknowledgedList
    }
  },
  {
    persist: {
      key: 'feature-intro',
      storage: localStorage,
      paths: ['acknowledgedIntros']
    } as any
  }
)