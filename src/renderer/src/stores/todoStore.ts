import { defineStore } from 'pinia'
import { ref, computed, reactive } from 'vue'
import { useTodoService, type Todo } from '@renderer/services/TodoService'
import { showToast } from '@renderer/utils/toast'
import moment from 'moment'

/**
 * 扩展的响应式 Todo 接口，包含子待办事项
 */
export interface ExtendedTodo extends Omit<Todo, 'children'> {
  children: ExtendedTodo[]
}

/**
 * 筛选条件接口
 */
export interface TodoFilters {
  status: 'all' | 'active' | 'completed'
  priority: 'all' | 'high' | 'medium' | 'low'
  dueDate: 'all' | 'overdue' | 'today' | 'tomorrow' | 'this-week' | 'no-due-date'
}

/**
 * 显示选项接口
 */
export interface TodoDisplayOptions {
  sort: 'dueDate' | 'createdAt' | 'priority'
  showCompletedTodos: boolean
  showSubTodos: boolean
  showDueDates: boolean
  showPriority: boolean
  viewMode: 'list' | 'kanban'
}

export const useTodoStore = defineStore('todo', () => {
  const todoService = useTodoService()

  // 状态
  const todos = ref<ExtendedTodo[]>([])
  const isLoading = ref(true) // 初始设为true，防止空状态闪烁
  const hasInitialLoad = ref(false)
  const error = ref<string | null>(null)
  const currentListUuid = ref<string | null>(null)

  // 筛选和显示选项
  const filters = reactive<TodoFilters>({
    status: 'all',
    priority: 'all',
    dueDate: 'all'
  })

  const displayOptions = reactive<TodoDisplayOptions>({
    sort: 'priority',
    showCompletedTodos: true,
    showSubTodos: true,
    showDueDates: true,
    showPriority: true,
    viewMode: 'list'
  })

  // 工具函数
  const convertToExtendedTodo = (todo: Todo): ExtendedTodo => {
    return reactive({
      ...todo,
      children: []
    })
  }

  const applySortToArray = (todoArray: ExtendedTodo[]): void => {
    if (displayOptions.sort === 'dueDate') {
      todoArray.sort((a, b) => {
        if (!a.dueDate) return 1
        if (!b.dueDate) return -1
        return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime()
      })
    } else if (displayOptions.sort === 'createdAt') {
      todoArray.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    } else if (displayOptions.sort === 'priority') {
      todoArray.sort((a, b) => {
        // 1. 未完成的优先于已完成的
        if (a.isCompleted !== b.isCompleted) {
          return a.isCompleted - b.isCompleted
        }
        // 2. 按优先级降序排序 (高 -> 低)
        if (a.priority !== b.priority) {
          return (b.priority ?? 0) - (a.priority ?? 0)
        }
        // 3. 优先级相同，按创建时间升序排序 (旧 -> 新)，新建的在下面
        return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      })
    }
  }

  // 递归加载子待办事项
  const loadChildrenRecursively = async (extendedTodo: ExtendedTodo): Promise<void> => {
    try {
      const childTodos = await todoService.getChildTodos(extendedTodo.uuid)

      // 清空现有的子待办事项
      extendedTodo.children.splice(0)

      // 转换并添加子待办事项
      for (const childTodo of childTodos) {
        const extendedChild = convertToExtendedTodo(childTodo)
        extendedTodo.children.push(extendedChild)

        // 递归加载子待办事项的子待办事项
        await loadChildrenRecursively(extendedChild)
      }
    } catch (error) {
      console.error(`Failed to load children for todo ${extendedTodo.uuid}:`, error)
    }
  }

  // 检查单个待办事项是否匹配筛选条件
  const todoMatchesFilter = (todo: ExtendedTodo): boolean => {
    // Apply status filter
    if (filters.status === 'active' && todo.isCompleted !== 0) {
      return false
    }
    if (filters.status === 'completed' && todo.isCompleted !== 1) {
      return false
    }

    // Apply priority filter
    if (filters.priority !== 'all') {
      const priorityMap = { high: 2, medium: 1, low: 0 }
      const targetPriority = priorityMap[filters.priority as keyof typeof priorityMap]
      if (targetPriority !== undefined && todo.priority !== targetPriority) {
        return false
      }
    }

    // Apply due date filter
    if (filters.dueDate !== 'all') {
      const now = moment()
      const today = moment().startOf('day')
      const tomorrow = moment().add(1, 'day').startOf('day')
      const weekEnd = moment().endOf('week')

      if (!todo.dueDate && filters.dueDate === 'no-due-date') return true
      if (!todo.dueDate && filters.dueDate !== 'no-due-date') return false

      const dueDate = moment(todo.dueDate)

      switch (filters.dueDate) {
        case 'overdue':
          if (!dueDate.isBefore(now)) return false
          break
        case 'today':
          if (!dueDate.isSame(today, 'day')) return false
          break
        case 'tomorrow':
          if (!dueDate.isSame(tomorrow, 'day')) return false
          break
        case 'this-week':
          if (!dueDate.isBetween(today, weekEnd, 'day', '[]')) return false
          break
        case 'no-due-date':
          return false
        default:
          break
      }
    }

    return true
  }

  // 检查单个待办事项是否匹配显示条件
  const todoMatchesDisplay = (todo: ExtendedTodo): boolean => {
    // Apply showCompletedTodos option
    if (!displayOptions.showCompletedTodos && todo.isCompleted === 1) {
      return false
    }
    return true
  }

  // 递归筛选待办事项，包括子待办事项
  const filterTodosRecursively = (todoArray: ExtendedTodo[]): ExtendedTodo[] => {
    const result: ExtendedTodo[] = []

    for (const todo of todoArray) {
      // 递归筛选子待办事项
      const filteredChildren =
        todo.children && displayOptions.showSubTodos ? filterTodosRecursively(todo.children) : []

      // 检查当前待办事项是否匹配筛选条件和显示条件
      const todoMatches = todoMatchesFilter(todo) && todoMatchesDisplay(todo)

      // 如果当前待办事项匹配或者有子待办事项匹配，则包含此待办事项
      if (todoMatches || filteredChildren.length > 0) {
        const filteredTodo: ExtendedTodo = {
          ...todo,
          children: filteredChildren
        }
        result.push(filteredTodo)
      }
    }

    return result
  }

  // 计算属性
  const filteredTodos = computed(() => {
    // 检查是否有激活的筛选条件
    const hasActiveFilters =
      filters.status !== 'all' || filters.priority !== 'all' || filters.dueDate !== 'all'

    const hasActiveDisplayFilters =
      !displayOptions.showCompletedTodos || !displayOptions.showSubTodos

    // 如果有激活的筛选条件或显示条件，进行递归筛选
    if (hasActiveFilters || hasActiveDisplayFilters) {
      const result = filterTodosRecursively(todos.value)
      applySortToArray(result)
      return result
    }

    // 如果没有筛选条件，直接返回原始数据并应用排序
    const result = [...todos.value]
    applySortToArray(result)
    return result
  })

  // 检查待办事项是否已逾期
  const isTodoOverdue = (todo: ExtendedTodo): boolean => {
    if (!todo.dueDate || todo.isCompleted === 1) return false
    const now = moment()
    const dueDate = moment(todo.dueDate)
    return dueDate.isBefore(now)
  }

  // 递归收集待办事项的辅助函数
  const collectTodosRecursively = (
    todoArray: ExtendedTodo[],
    result: { overdue: ExtendedTodo[]; todo: ExtendedTodo[]; completed: ExtendedTodo[] }
  ): void => {
    for (const todo of todoArray) {
      if (todo.isCompleted === 1) {
        result.completed.push(todo)
      } else if (isTodoOverdue(todo)) {
        result.overdue.push(todo)
      } else {
        result.todo.push(todo)
      }

      // 递归处理子待办事项
      if (todo.children && todo.children.length > 0) {
        collectTodosRecursively(todo.children, result)
      }
    }
  }

  // 根据状态获取待办事项（用于看板视图和列表视图的分组）
  const todosByStatus = computed(() => {
    const result = {
      overdue: [] as ExtendedTodo[],
      todo: [] as ExtendedTodo[],
      completed: [] as ExtendedTodo[]
    }

    const processedTodos = filteredTodos.value
    collectTodosRecursively(processedTodos, result)

    return result
  })

  // Actions
  const loadTodoList = async (listUuid: string): Promise<void> => {
    // 如果是同一个列表且已经加载过，直接返回
    if (currentListUuid.value === listUuid && hasInitialLoad.value) {
      return
    }

    isLoading.value = true
    hasInitialLoad.value = false
    error.value = null
    currentListUuid.value = listUuid

    try {
      const todoList = await todoService.getTodoList(listUuid)
      if (todoList) {
        const rawTodos = await todoService.getTodosByList(listUuid)

        // 清空现有的待办事项
        todos.value = []

        // 转换为响应式待办事项并加载子待办事项
        for (const rawTodo of rawTodos) {
          const extendedTodo = convertToExtendedTodo(rawTodo)
          todos.value.push(extendedTodo)

          // 递归加载子待办事项
          await loadChildrenRecursively(extendedTodo)
        }

        // 应用排序
        applySortToArray(todos.value)
      }
    } catch (err) {
      console.error('Failed to load todo list:', err)
      error.value = '加载待办清单失败'
      showToast('错误', '加载待办清单失败', 3000)
    } finally {
      isLoading.value = false
      hasInitialLoad.value = true
    }
  }

  // 递归查找待办事项
  const findTodoRecursively = (todoArray: ExtendedTodo[], uuid: string): ExtendedTodo | null => {
    for (const todo of todoArray) {
      if (todo.uuid === uuid) {
        return todo
      }
      if (todo.children && todo.children.length > 0) {
        const found = findTodoRecursively(todo.children, uuid)
        if (found) return found
      }
    }
    return null
  }

  // 创建新的待办事项
  const createTodo = async (
    todoData: {
      title: string
      notes?: string
      priority?: number
      dueDate?: string
    },
    listUuid: string
  ): Promise<ExtendedTodo | null> => {
    try {
      // 准备待办事项数据
      const newTodoData = {
        title: todoData.title.trim(),
        notes: todoData.notes || '',
        isCompleted: 0,
        priority: todoData.priority || 0,
        dueDate: todoData.dueDate ? moment(todoData.dueDate).toISOString() : undefined
      }

      const newTodoItem = await todoService.createTodo(newTodoData, listUuid)

      // 转换为响应式待办事项并添加到列表
      const extendedTodo = convertToExtendedTodo(newTodoItem)
      todos.value.push(extendedTodo)

      // 应用排序
      applySortToArray(todos.value)

      showToast('成功', '待办事项创建成功', 2000)
      return extendedTodo
    } catch (error) {
      console.error('Failed to create new todo:', error)
      showToast('错误', '创建待办事项失败', 3000)
      return null
    }
  }

  // 添加子待办事项到响应式结构
  const addSubTodo = async (parentUuid: string, subTodo: Todo): Promise<void> => {
    try {
      // 在响应式结构中查找父待办事项
      const parentTodo = findTodoRecursively(todos.value, parentUuid)
      if (parentTodo) {
        // 转换为响应式待办事项并添加到父待办事项的子项中
        const extendedSubTodo = convertToExtendedTodo(subTodo)
        parentTodo.children.push(extendedSubTodo)

        // 对子待办事项进行排序
        applySortToArray(parentTodo.children)

        console.log(
          `Sub-todo added to reactive structure: ${subTodo.title} for parent: ${parentUuid}`
        )
      } else {
        console.error(`Parent todo not found in reactive structure: ${parentUuid}`)
      }
    } catch (error) {
      console.error('Error adding sub-todo to reactive structure:', error)
    }
  }

  // 递归查找并更新待办事项的辅助函数
  const findAndUpdateTodoRecursively = (
    todoArray: ExtendedTodo[],
    uuid: string,
    updatedTodo: Todo
  ): boolean => {
    for (let i = 0; i < todoArray.length; i++) {
      if (todoArray[i].uuid === uuid) {
        // 找到了，直接更新属性（保持响应式）
        Object.assign(todoArray[i], updatedTodo, {
          children: todoArray[i].children
        })
        return true
      }

      // 递归查找子待办事项
      if (todoArray[i].children && todoArray[i].children.length > 0) {
        if (findAndUpdateTodoRecursively(todoArray[i].children, uuid, updatedTodo)) {
          return true
        }
      }
    }
    return false
  }

  // 更新待办事项
  const updateTodo = async (uuid: string, updates: Partial<Todo>): Promise<boolean> => {
    try {
      // 确保优先级是数字类型
      if (updates.priority !== undefined) {
        updates.priority = Number(updates.priority)
      }

      const success = await todoService.updateTodo(uuid, updates)
      if (success) {
        // 获取更新后的待办事项
        const updatedTodo = await todoService.getTodoByUuid(uuid)
        if (!updatedTodo) {
          console.error('Failed to get updated todo:', uuid)
          return false
        }

        console.log('Got updated todo from database:', updatedTodo)

        // 递归查找并更新待办事项
        const found = findAndUpdateTodoRecursively(todos.value, uuid, updatedTodo)

        if (!found) {
          console.warn(`Todo ${uuid} not found in reactive structure`)
          return false
        }

        // 应用排序（如果优先级或其他排序字段发生变化）
        if (
          updates.priority !== undefined ||
          updates.dueDate !== undefined ||
          updates.createdAt !== undefined
        ) {
          applySortToArray(todos.value)
        }

        return true
      }
      return false
    } catch (error) {
      console.error('Failed to update todo:', error)
      showToast('错误', '更新待办事项失败', 3000)
      return false
    }
  }

  // 更新筛选条件
  const updateFilters = (newFilters: Partial<TodoFilters>): void => {
    Object.assign(filters, newFilters)
  }

  // 递归删除待办事项的辅助函数
  const deleteTodoRecursively = (todoArray: ExtendedTodo[], uuid: string): boolean => {
    for (let i = 0; i < todoArray.length; i++) {
      if (todoArray[i].uuid === uuid) {
        // 找到了，从数组中移除
        todoArray.splice(i, 1)
        return true
      }

      // 递归查找子待办事项
      if (todoArray[i].children && todoArray[i].children.length > 0) {
        if (deleteTodoRecursively(todoArray[i].children, uuid)) {
          return true
        }
      }
    }
    return false
  }

  // 删除待办事项
  const deleteTodo = async (uuid: string, listUuid?: string): Promise<boolean> => {
    try {
      // 使用 TodoService 删除待办事项（递归删除包括子待办事项）
      const success = await todoService.deleteTodoRecursively(uuid)

      if (success) {
        // 从待办清单中移除该待办事项（如果指定了listUuid）
        if (listUuid) {
          await todoService.removeTodoFromList(uuid, listUuid)
        }

        // 从响应式数据中移除待办事项
        const removed = deleteTodoRecursively(todos.value, uuid)

        if (removed) {
          showToast('成功', '待办事项已删除', 2000)
          return true
        } else {
          console.warn(`Todo ${uuid} not found in reactive structure`)
          // 即使在响应式结构中没找到，数据库删除成功也算成功
          showToast('成功', '待办事项已删除', 2000)
          return true
        }
      } else {
        showToast('错误', '删除待办事项失败', 3000)
        return false
      }
    } catch (error) {
      console.error('Failed to delete todo:', error)
      showToast('错误', '删除待办事项失败', 3000)
      return false
    }
  }

  // 强制重新加载待办清单
  const forceReloadTodoList = async (listUuid: string): Promise<void> => {
    // 重置状态以强制重新加载
    hasInitialLoad.value = false
    currentListUuid.value = null
    await loadTodoList(listUuid)
  }

  // 更新显示选项
  const updateDisplayOptions = (newOptions: Partial<TodoDisplayOptions>): void => {
    Object.assign(displayOptions, newOptions)

    // 如果排序条件发生变化，重新排序
    if (newOptions.sort) {
      applySortToArray(todos.value)
    }
  }

  // 获取空状态文本
  const getEmptyStateText = (): string => {
    if (filters.status === 'active') return '没有符合条件的未完成待办事项'
    if (filters.status === 'completed') return '没有符合条件的已完成待办事项'
    if (filters.priority !== 'all' || filters.dueDate !== 'all') {
      return '没有符合筛选条件的待办事项'
    }
    return '没有待办事项'
  }

  return {
    // State
    todos,
    isLoading,
    hasInitialLoad,
    error,
    currentListUuid,
    filters,
    displayOptions,

    // Getters
    filteredTodos,
    todosByStatus,

    // Actions
    loadTodoList,
    forceReloadTodoList,
    createTodo,
    addSubTodo,
    updateTodo,
    deleteTodo,
    findTodoRecursively,
    updateFilters,
    updateDisplayOptions,
    getEmptyStateText
  }
})
