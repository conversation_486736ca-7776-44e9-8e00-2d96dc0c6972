/**
 * CozeUtils - Coze API工具类
 * 提供对Coze API的访问方法
 */

import { CONFIG_KEYS, ConfigAccessor, DEFAULT_CONFIG_VALUES } from '@renderer/api/uniform/config';
import { useConfig } from '@renderer/composables/useConfig'

// 响应事件类型
export type CozeEventType =
  | 'start'
  | 'text'
  | 'error'
  | 'completed'
  | 'conversation_id';

// 事件回调函数类型
export type CozeEventCallback = (type: CozeEventType, data: any) => void;

// 聊天消息类型
export interface CozeMessage {
  role: 'user' | 'assistant';
  content: string;
  content_type?: 'text' | 'object_string';
  formattedContent?: string;
}

// 聊天选项
export interface ChatOptions {
  workflowId?: string;
  botId?: string;
  messages?: CozeMessage[];
  parameters?: Record<string, any>;
  conversationId?: string;
  apiToken?: string;
  // 钩子函数直接放在顶层
  onStart?: () => void;
  onText?: (content: string) => void;
  onError?: (error: string) => void;
  onCompleted?: () => void;
  onConversationId?: (id: string) => void;
}

// 文件上传响应
interface FileUploadResponse {
  code: number;
  data: {
    bytes: number;
    created_at: number;
    file_name: string;
    id: string;
  };
  msg: string;
}

// 多模态消息对象
interface ObjectStringItem {
  type: 'text' | 'file' | 'image' | 'audio';
  text?: string;
  file_id?: string;
  file_url?: string;
}

// 创建会话响应接口
export interface CreateConversationResponse {
  id: string;
  created_at: number;
  last_section_id: string;
  meta_data?: Record<string, string>;
}

// 消息接口
export interface EnterMessage {
  role: 'user' | 'assistant';
  content: string;
  content_type?: 'text' | 'object_string';
  type?: string;
  meta_data?: Record<string, string>;
}

// 创建会话选项
export interface CreateConversationOptions {
  botId?: string;
  metaData?: Record<string, string>;
  messages?: EnterMessage[];
  connectorId?: string;
  apiToken?: string;
}

/**
 * CozeUtils类 - 提供与Coze API交互的方法
 */
export class CozeUtils {
  private static readonly API_BASE_URL = 'https://api.coze.cn';

  /**
   * 获取API Token
   * @returns API Token
   */
  private static async getApiToken(): Promise<string> {
    return await ConfigAccessor.getString(CONFIG_KEYS.COZE_API_TOKEN, DEFAULT_CONFIG_VALUES.COZE_API_TOKEN)
  }

  /**
   * 上传文件到Coze
   * @param file 要上传的文件
   * @returns 上传的文件ID
   */
  static async uploadFile(file: File): Promise<string> {
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await fetch(`${this.API_BASE_URL}/v1/files/upload`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${await this.getApiToken()}`,
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const result: FileUploadResponse = await response.json();

      if (result.code === 0 && result.data.id) {
        return result.data.id;
      } else {
        throw new Error(result.msg || '文件上传失败');
      }
    } catch (error) {
      console.error('文件上传错误:', error);
      throw error;
    }
  }

  /**
   * 创建多模态消息内容
   * @param text 文本内容
   * @param fileIds 文件ID数组
   * @returns 格式化的多模态消息内容
   */
  static createMultiModalContent(text: string, fileIds: string[] = []): string {
    const objects: ObjectStringItem[] = [];

    // 添加文本内容
    if (text) {
      objects.push({
        type: 'text',
        text: text
      });
    }

    // 添加图片内容
    for (const fileId of fileIds) {
      objects.push({
        type: 'image',
        file_id: fileId
      });
    }

    return JSON.stringify(objects);
  }

  /**
   * 发送聊天消息到Coze API
   * @param prompt 用户输入文本
   * @param options 聊天选项，包含钩子函数
   * @returns 取消请求的函数
   */
  static async chat(
    prompt: string,
    options: ChatOptions = {}
  ): Promise<() => void> {
    // 转换为统一的事件回调风格
    const eventCallback: CozeEventCallback = (type, data) => {
      switch (type) {
        case 'start':
          options.onStart?.();
          break;
        case 'text':
          options.onText?.(data.content);
          break;
        case 'error':
          options.onError?.(data.error);
          break;
        case 'completed':
          options.onCompleted?.();
          break;
        case 'conversation_id':
          options.onConversationId?.(data.id);
          break;
      }
    };

    // 复用现有函数
    return this.sendChatMessage(prompt, eventCallback, options);
  }

  /**
   * 发送聊天消息到Coze API
   * @param prompt 用户输入文本
   * @param onEvent 事件回调函数
   * @param options 聊天选项
   * @returns 取消请求的函数
   */
  static async sendChatMessage(
    prompt: string,
    onEvent: CozeEventCallback,
    options: ChatOptions = {}
  ): Promise<() => void> {
    // 创建AbortController用于取消请求
    const controller = new AbortController();

    // 准备请求参数
    const {
      workflowId = import.meta.env.DEV ? '7508055730168709161' : '7505384456564670518',
      botId = '7505382249266233370',
      messages = [],
      parameters = {},
      conversationId = '',
      apiToken = await this.getApiToken()
    } = options;

    console.log('apiToken', apiToken)

    try {
      // 通知开始事件
      onEvent('start', { message: '开始处理请求' });

      // 发送请求
      const response = await fetch(`${this.API_BASE_URL}/v1/workflows/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiToken}`,
          'Accept': 'text/event-stream',
        },
        body: JSON.stringify({
          workflow_id: workflowId,
          bot_id: botId,
          additional_messages: [
            ...messages,
            {
              role: 'user',
              content: prompt,
              content_type: 'text'
            }
          ],
          parameters: {
            CONVERSATION_NAME: 'Chron Engine',
            ...parameters
          },
          conversation_id: conversationId
        }),
        signal: controller.signal
      });

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      // 获取响应的可读流
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('ReadableStream not supported');
      }

      // 处理流式响应
      this.processStream(reader, onEvent);
    } catch (error) {
      // 处理错误
      console.error('CozeUtils Error:', error);
      onEvent('error', { error: error instanceof Error ? error.message : '未知错误' });
    }

    // 返回取消函数
    return () => controller.abort();
  }

  /**
   * 处理流式响应
   * @param reader 响应流的读取器
   * @param onEvent 事件回调函数
   */
  private static async processStream(
    reader: ReadableStreamDefaultReader<Uint8Array>,
    onEvent: CozeEventCallback
  ): Promise<void> {
    const decoder = new TextDecoder();
    let buffer = '';
    let done = false;
    let skipNextData = false; // 标记是否需要跳过下一个data行

    try {
      while (true) {
        const { done: readerDone, value } = await reader.read();

        if (readerDone) {
          // 流结束
          break;
        }

        // 将二进制数据解码为字符串
        buffer += decoder.decode(value, { stream: true });

        // 按行分割数据并处理
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // 保留未完成的最后一行

        for (const line of lines) {
          console.log('line', line);

          // 检查是否是完成事件
          if (line === 'event: conversation.chat.completed') {
            onEvent('completed', { message: '对话完成' });
            done = true;
            break;
          }

          // 检查是否是消息完成事件
          if (line === 'event: conversation.message.completed') {
            skipNextData = true; // 标记需要跳过下一个data行
            continue;
          }

          // 处理数据行
          if (line.startsWith('data: ')) {
            // 如果需要跳过当前data行
            if (skipNextData) {
              skipNextData = false; // 重置标记
              continue; // 跳过此data行
            }

            const jsonData = line.slice(6); // 去掉 'data: ' 前缀
            try {
              // 解析事件数据
              const event = JSON.parse(jsonData);
              console.log('WorkflowEvent:', event);

              // 提取文本内容
              let content = this.extractContent(event);
              if (content.includes('generate_answer_finish')) {
                continue;
              }
              if (content) {
                onEvent('text', { content });
              }

              // 提取对话ID
              if (event.conversation_id) {
                onEvent('conversation_id', { id: event.conversation_id });
              }
            } catch (error) {
              console.error('JSON解析错误:', error, jsonData);
            }
          }
        }

        if (done) {
          break;
        }
      }
    } catch (error) {
      // 只处理非取消的错误
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error('Stream处理错误:', error);
        onEvent('error', { error: error.message });
      }
    } finally {
      // 确保释放读取器资源
      reader.releaseLock();
    }
  }

  /**
   * 从事件对象中提取内容
   * @param event 事件对象
   * @returns 提取的内容或空字符串
   */
  private static extractContent(event: any): string {
    // 如果整个事件就是一个字符串
    if (typeof event === 'string') {
      return event;
    }

    // 如果事件是一个对象
    if (typeof event === 'object') {
      // 检查各种可能的内容字段
      if (event.data) {
        if (typeof event.data === 'string') {
          return event.data;
        } else if (event.data?.content) {
          return event.data.content;
        }
      } else if (event.content) {
        return event.content;
      } else if (event.delta?.content) {
        return event.delta.content;
      } else if (event.choices && event.choices[0]?.delta?.content) {
        return event.choices[0].delta.content;
      }
    }

    // 没有找到内容
    return '';
  }

  /**
   * 创建新的会话
   * @param options 创建会话的选项
   * @returns 创建的会话信息
   */
  static async createConversation(
    options: CreateConversationOptions = {}
  ): Promise<CreateConversationResponse> {
    const {
      botId,
      metaData,
      messages = [],
      connectorId = '1024', // 默认使用API渠道
      apiToken = await this.getApiToken()
    } = options;

    try {
      const response = await fetch(`${this.API_BASE_URL}/v1/conversation/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiToken}`
        },
        body: JSON.stringify({
          bot_id: botId,
          meta_data: metaData,
          messages: messages,
          connector_id: connectorId
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const result = await response.json();

      if (result.code === 0 && result.data) {
        return result.data as CreateConversationResponse;
      } else {
        throw new Error(result.msg || '创建会话失败');
      }
    } catch (error) {
      console.error('创建会话错误:', error);
      throw error;
    }
  }
}
