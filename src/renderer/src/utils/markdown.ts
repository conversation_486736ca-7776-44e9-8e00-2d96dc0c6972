/**
 * Markdown 相关工具函数
 */

/**
 * 检测文本是否包含块级 markdown 语法
 * @param text 要检测的文本
 * @returns 如果包含块级 markdown 语法返回 true，否则返回 false
 */
export function isBlockLevelMarkdown(text: string): boolean {
  // 如果文本为空或只有空白字符，不是 markdown
  if (!text || !text.trim()) {
    return false
  }

  // 只检测块级 markdown 语法模式
  const blockMarkdownPatterns = [
    // 标题
    /^#{1,6}\s+/m,

    // 代码块（不包括行内代码）
    /```[\s\S]*?```/, // 代码块

    // 列表
    /^[\s]*[-*+]\s+/m,        // 无序列表
    /^[\s]*\d+\.\s+/m,        // 有序列表

    // 引用
    /^[\s]*>\s+/m,

    // 分割线
    /^[\s]*(-{3,}|\*{3,}|_{3,})[\s]*$/m,

    // 表格
    /\|.*\|/,

    // 块级数学公式（不包括行内公式）
    /\$\$[\s\S]*?\$\$/,       // 块级数学公式
    /\\\[[\s\S]*?\\\]/,       // LaTeX 块级公式

    // 任务列表
    /^[\s]*-\s+\[[ x]\]\s+/m,

    // 图片（作为块级元素处理）
    /!\[([^\]]*)\]\(([^)]+)\)/
  ]

  // 检查是否匹配任何块级 markdown 模式
  return blockMarkdownPatterns.some(pattern => pattern.test(text))
}
