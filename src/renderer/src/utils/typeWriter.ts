/**
 * TypeWriter - 打字机效果工具类
 * 提供流式文本显示的打字机动画效果
 */

export class TypeWriter {
  private queue: string[] = []
  private isTyping = false
  private frameId: number | null = null
  private lastCharTime = 0
  private readonly typingInterval: number
  private appendChar: (char: string) => void
  public onAllCharsTyped: (() => void) | null = null

  constructor(
    appendChar: (char: string) => void,
    typingInterval = 10, // ms
    onAllCharsTyped: (() => void) | null = null
  ) {
    this.appendChar = appendChar
    this.typingInterval = typingInterval
    this.onAllCharsTyped = onAllCharsTyped
  }

  /**
   * 将文本加入打字队列
   * @param text 要显示的文本
   */
  enqueue(text: string) {
    this.queue.push(...text.split(''))
    if (!this.isTyping) {
      this.type()
    }
  }

  /**
   * 开始打字动画
   */
  private type = () => {
    this.isTyping = true
    const now = performance.now()

    if (now - this.lastCharTime >= this.typingInterval) {
      if (this.queue.length > 0) {
        const char = this.queue.shift()!
        this.appendChar(char)
        this.lastCharTime = now
      }
    }

    if (this.queue.length > 0) {
      this.frameId = requestAnimationFrame(this.type)
    } else {
      this.isTyping = false
      if (this.frameId) {
        cancelAnimationFrame(this.frameId)
        this.frameId = null
      }
      if (this.onAllCharsTyped) {
        this.onAllCharsTyped()
      }
    }
  }

  /**
   * 停止打字动画
   */
  stop() {
    if (this.frameId) {
      cancelAnimationFrame(this.frameId)
      this.frameId = null
    }
    this.isTyping = false
  }

  /**
   * 清空队列并停止动画
   */
  clear() {
    this.stop()
    this.queue = []
  }

  /**
   * 检查是否正在工作（打字或有队列）
   */
  get isBusy(): boolean {
    return this.isTyping || this.queue.length > 0
  }

  /**
   * 获取队列中剩余字符数量
   */
  get queueLength(): number {
    return this.queue.length
  }

  /**
   * 检查是否正在打字
   */
  get isActive(): boolean {
    return this.isTyping
  }
}

/**
 * 创建TypeWriter实例的工厂函数
 * @param appendChar 字符添加回调函数
 * @param options 配置选项
 * @returns TypeWriter实例
 */
export function createTypeWriter(
  appendChar: (char: string) => void,
  options: {
    typingInterval?: number
    onAllCharsTyped?: () => void
  } = {}
): TypeWriter {
  return new TypeWriter(
    appendChar,
    options.typingInterval ?? 10,
    options.onAllCharsTyped ?? null
  )
}