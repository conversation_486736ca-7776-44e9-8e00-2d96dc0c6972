/**
 * 文件系统工具函数
 * 提供跨平台的文件系统操作辅助方法
 */

/**
 * 跨平台检查文件stat对象是否为目录
 * 
 * 在不同平台和环境下，fs.stat()返回的对象格式可能不同：
 * - Node.js: 返回带有isDirectory()方法的Stats对象
 * - Web/Browser: 可能返回带有isDirectory布尔属性的对象
 * - 某些平台: 只有mode字段，需要通过位运算判断
 * 
 * @param stats - fs.stat()返回的统计信息对象
 * @returns boolean - 是否为目录
 * 
 * @example
 * ```typescript
 * const fs = new FileSystemManager()
 * const stats = await fs.stat('/some/path')
 * if (isDirectory(stats)) {
 *   console.log('这是一个目录')
 * }
 * ```
 */
export function isDirectory(stats: any): boolean {
  // 方法1: 标准Node.js Stats对象，有isDirectory()方法
  if (typeof stats.isDirectory === 'function') {
    return stats.isDirectory()
  }
  
  // 方法2: 某些环境可能直接提供布尔属性
  if (typeof stats.isDirectory === 'boolean') {
    return stats.isDirectory
  }
  
  // 方法3: 通过mode字段的位运算判断
  // 在Unix-like系统中，文件类型信息存储在mode字段的高位
  // 0o170000 (S_IFMT) - 文件类型掩码
  // 0o040000 (S_IFDIR) - 目录类型标识
  if (typeof stats.mode === 'number') {
    return (stats.mode & 0o170000) === 0o040000
  }
  
  // 兜底：无法确定时返回false
  console.warn('无法确定文件类型，stats对象格式不支持:', stats)
  return false
}

/**
 * 跨平台检查文件stat对象是否为普通文件
 * 
 * @param stats - fs.stat()返回的统计信息对象
 * @returns boolean - 是否为普通文件
 */
export function isFile(stats: any): boolean {
  // 方法1: 标准Node.js Stats对象，有isFile()方法
  if (typeof stats.isFile === 'function') {
    return stats.isFile()
  }
  
  // 方法2: 某些环境可能直接提供布尔属性
  if (typeof stats.isFile === 'boolean') {
    return stats.isFile
  }
  
  // 方法3: 通过mode字段的位运算判断
  // 0o100000 (S_IFREG) - 普通文件类型标识
  if (typeof stats.mode === 'number') {
    return (stats.mode & 0o170000) === 0o100000
  }
  
  // 兜底：无法确定时返回false
  console.warn('无法确定文件类型，stats对象格式不支持:', stats)
  return false
}

/**
 * 获取文件类型的可读字符串描述
 * 
 * @param stats - fs.stat()返回的统计信息对象
 * @returns string - 文件类型描述
 */
export function getFileType(stats: any): string {
  if (isDirectory(stats)) return 'directory'
  if (isFile(stats)) return 'file'
  
  // 其他特殊文件类型
  if (typeof stats.mode === 'number') {
    const fileType = stats.mode & 0o170000
    switch (fileType) {
      case 0o020000: return 'character device'
      case 0o060000: return 'block device'
      case 0o010000: return 'fifo'
      case 0o120000: return 'symbolic link'
      case 0o140000: return 'socket'
      default: return 'unknown'
    }
  }
  
  return 'unknown'
}

/**
 * 文件模式位掩码常量
 */
export const FILE_MODE_MASKS = {
  /** 文件类型掩码 */
  S_IFMT: 0o170000,
  /** 目录 */
  S_IFDIR: 0o040000,
  /** 普通文件 */
  S_IFREG: 0o100000,
  /** 符号链接 */
  S_IFLNK: 0o120000,
  /** 字符设备 */
  S_IFCHR: 0o020000,
  /** 块设备 */
  S_IFBLK: 0o060000,
  /** FIFO (命名管道) */
  S_IFIFO: 0o010000,
  /** Socket */
  S_IFSOCK: 0o140000
} as const