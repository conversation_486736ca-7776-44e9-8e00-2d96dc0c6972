import { UpdateService } from '@renderer/editor/provider/UpdateService'
import { TiptapTransformer } from '@hocuspocus/transformer'
import * as Y from 'yjs'
import { JSONContent } from '@tiptap/vue-3'

/**
 * 性能统计接口
 */
export interface PerformanceStats {
    totalTime: number
    dbReadTime: number
    ydocRebuildTime: number
    jsonConvertTime?: number
    updatesCount: number
    totalDataSize: number
    averageUpdateSize: number
}

/**
 * 从数据库的二进制 updates 重建 Y.Doc (带性能测量)
 * @param uuid 文档的唯一标识符
 * @returns 包含重建的 Y.Doc 实例和性能统计的对象
 */
export async function createYDocFromDatabaseWithStats(uuid: string): Promise<{
    ydoc: Y.Doc
    stats: PerformanceStats
}> {
    const startTime = performance.now()

    // 1. 数据库读取阶段
    const dbReadStart = performance.now()
    const updateService = new UpdateService()
    const updates = await updateService.getUpdatesByUuid(uuid)
    const dbReadEnd = performance.now()
    const dbReadTime = dbReadEnd - dbReadStart

    // 计算数据统计
    const validUpdates = updates.filter(update => update.dataUpdate)
    const totalDataSize = validUpdates.reduce((sum, update) => sum + update.dataUpdate.length, 0)
    const averageUpdateSize = validUpdates.length > 0 ? totalDataSize / validUpdates.length : 0

    console.log(`📊 数据库读取完成: ${dbReadTime.toFixed(2)}ms`)
    console.log(`📝 找到 ${updates.length} 个更新记录 (${validUpdates.length} 个有效)`)
    console.log(`💾 总数据大小: ${(totalDataSize / 1024).toFixed(2)} KB`)
    console.log(`📏 平均更新大小: ${averageUpdateSize.toFixed(0)} bytes`)

    // 2. Y.Doc 重建阶段
    const ydocRebuildStart = performance.now()
    const ydoc = new Y.Doc()

    // 按时间顺序应用所有 updates
    validUpdates
        .sort((a, b) => (a.timestamp || 0) - (b.timestamp || 0))
        .forEach((update, index) => {
            const applyStart = performance.now()
            Y.applyUpdate(ydoc, update.dataUpdate)
            const applyTime = performance.now() - applyStart

            if (index < 5 || applyTime > 10) { // 记录前5个或耗时超过10ms的更新
                console.log(`🔄 应用更新 ${index + 1}: ${applyTime.toFixed(2)}ms (${update.dataUpdate.length} bytes)`)
            }
        })

    const ydocRebuildEnd = performance.now()
    const ydocRebuildTime = ydocRebuildEnd - ydocRebuildStart

    console.log(`🏗️ Y.Doc 重建完成: ${ydocRebuildTime.toFixed(2)}ms`)

    const totalTime = performance.now() - startTime

    const stats: PerformanceStats = {
        totalTime,
        dbReadTime,
        ydocRebuildTime,
        updatesCount: validUpdates.length,
        totalDataSize,
        averageUpdateSize
    }

    console.log(`⏱️ 总耗时: ${totalTime.toFixed(2)}ms`)

    return { ydoc, stats }
}

/**
 * 从数据库的二进制 updates 重建 Y.Doc
 * @param uuid 文档的唯一标识符
 * @returns 重建的 Y.Doc 实例
 */
export async function createYDocFromDatabase(uuid: string): Promise<Y.Doc> {
    const { ydoc } = await createYDocFromDatabaseWithStats(uuid)
    return ydoc
}

/**
 * 从数据库的二进制 updates 直接转换为 Tiptap JSON 格式 (带性能测量)
 * @param uuid 文档的唯一标识符
 * @returns 包含 JSON 内容和性能统计的对象
 */
export async function getJsonContentFromDatabaseWithStats(uuid: string): Promise<{
    content: JSONContent | null
    stats: PerformanceStats
}> {
    try {
        const { ydoc, stats } = await createYDocFromDatabaseWithStats(uuid)

        // 3. JSON 转换阶段
        const jsonConvertStart = performance.now()
        const content = TiptapTransformer.fromYdoc(ydoc)
        const jsonConvertEnd = performance.now()
        const jsonConvertTime = jsonConvertEnd - jsonConvertStart

        console.log(`🔄 JSON 转换完成: ${jsonConvertTime.toFixed(2)}ms`)

        const finalStats = {
            ...stats,
            jsonConvertTime,
            totalTime: stats.totalTime + jsonConvertTime
        }

        console.log(`🎯 完整流程总耗时: ${finalStats.totalTime.toFixed(2)}ms`)
        console.log(`📊 性能分解:`)
        console.log(`   - 数据库读取: ${finalStats.dbReadTime.toFixed(2)}ms (${((finalStats.dbReadTime / finalStats.totalTime) * 100).toFixed(1)}%)`)
        console.log(`   - Y.Doc重建: ${finalStats.ydocRebuildTime.toFixed(2)}ms (${((finalStats.ydocRebuildTime / finalStats.totalTime) * 100).toFixed(1)}%)`)
        console.log(`   - JSON转换: ${finalStats.jsonConvertTime.toFixed(2)}ms (${((finalStats.jsonConvertTime / finalStats.totalTime) * 100).toFixed(1)}%)`)

        return { content, stats: finalStats }
    } catch (error) {
        console.error('Error converting database updates to JSON:', error)
        return {
            content: null,
            stats: {
                totalTime: 0,
                dbReadTime: 0,
                ydocRebuildTime: 0,
                updatesCount: 0,
                totalDataSize: 0,
                averageUpdateSize: 0
            }
        }
    }
}

/**
 * 从数据库的二进制 updates 直接转换为 Tiptap JSON 格式
 * @param uuid 文档的唯一标识符
 * @returns Tiptap 的 JSON 内容
 */
export async function getJsonContentFromDatabase(uuid: string): Promise<JSONContent | null> {
    const { content } = await getJsonContentFromDatabaseWithStats(uuid)
    return content
}

/**
 * 从现有的二进制 updates 数组创建 Y.Doc (带性能测量)
 * @param updates 包含 dataUpdate 字段的更新记录数组
 * @returns 包含 Y.Doc 实例和性能统计的对象
 */
export function createYDocFromUpdatesWithStats(updates: any[]): {
    ydoc: Y.Doc
    stats: Omit<PerformanceStats, 'dbReadTime'>
} {
    const startTime = performance.now()

    const ydoc = new Y.Doc()

    // 按时间戳排序确保正确的应用顺序
    const sortedUpdates = updates
        .filter(update => update.dataUpdate)
        .sort((a, b) => (a.timestamp || 0) - (b.timestamp || 0))

    console.log(`🔄 开始应用 ${sortedUpdates.length} 个更新`)

    // 应用所有更新
    sortedUpdates.forEach((update, index) => {
        const applyStart = performance.now()
        Y.applyUpdate(ydoc, update.dataUpdate)
        const applyTime = performance.now() - applyStart

        if (index < 3 || applyTime > 5) { // 记录前3个或耗时超过5ms的更新
            console.log(`🔄 应用更新 ${index + 1}: ${applyTime.toFixed(2)}ms`)
        }
    })

    const totalTime = performance.now() - startTime
    const totalDataSize = sortedUpdates.reduce((sum, update) => sum + update.dataUpdate.length, 0)
    const averageUpdateSize = sortedUpdates.length > 0 ? totalDataSize / sortedUpdates.length : 0

    const stats = {
        totalTime,
        ydocRebuildTime: totalTime,
        updatesCount: sortedUpdates.length,
        totalDataSize,
        averageUpdateSize
    }

    console.log(`✅ Y.Doc 重建完成: ${totalTime.toFixed(2)}ms`)

    return { ydoc, stats }
}

/**
 * 从现有的二进制 updates 数组创建 Y.Doc
 * @param updates 包含 dataUpdate 字段的更新记录数组
 * @returns Y.Doc 实例
 */
export function createYDocFromUpdates(updates: any[]): Y.Doc {
    const { ydoc } = createYDocFromUpdatesWithStats(updates)
    return ydoc
}

/**
 * 将 Y.Doc 转换为可读的 JSON 格式用于调试
 * @param ydoc Y.Doc 实例
 * @returns 包含文档状态和内容的调试信息
 */
export function debugYDoc(ydoc: Y.Doc) {
    return {
        clientId: ydoc.clientID,
        json: TiptapTransformer.fromYdoc(ydoc),
        stateVector: Y.encodeStateVector(ydoc),
        update: Y.encodeStateAsUpdate(ydoc)
    }
}

/**
 * 快速性能测试函数
 * @param uuid 文档的唯一标识符
 * @param iterations 测试次数，默认为3次
 */
export async function benchmarkYDocRebuild(uuid: string, iterations: number = 3) {
    console.log(`🏁 开始性能测试 (${iterations} 次迭代)`)

    const results: PerformanceStats[] = []

    for (let i = 0; i < iterations; i++) {
        console.log(`\n🔄 第 ${i + 1} 次测试:`)
        const { stats } = await getJsonContentFromDatabaseWithStats(uuid)
        results.push(stats)
    }

    // 计算平均值
    const avgStats = {
        totalTime: results.reduce((sum, s) => sum + s.totalTime, 0) / results.length,
        dbReadTime: results.reduce((sum, s) => sum + s.dbReadTime, 0) / results.length,
        ydocRebuildTime: results.reduce((sum, s) => sum + s.ydocRebuildTime, 0) / results.length,
        jsonConvertTime: results.reduce((sum, s) => sum + (s.jsonConvertTime || 0), 0) / results.length,
        updatesCount: results[0].updatesCount,
        totalDataSize: results[0].totalDataSize,
        averageUpdateSize: results[0].averageUpdateSize
    }

    console.log(`\n📊 性能测试总结 (${iterations} 次平均):`)
    console.log(`⏱️ 总耗时: ${avgStats.totalTime.toFixed(2)}ms`)
    console.log(`💾 数据库读取: ${avgStats.dbReadTime.toFixed(2)}ms`)
    console.log(`🏗️ Y.Doc重建: ${avgStats.ydocRebuildTime.toFixed(2)}ms`)
    console.log(`🔄 JSON转换: ${avgStats.jsonConvertTime.toFixed(2)}ms`)
    console.log(`📝 更新数量: ${avgStats.updatesCount}`)
    console.log(`💾 数据大小: ${(avgStats.totalDataSize / 1024).toFixed(2)} KB`)

    return avgStats
} 