import { FileSystemManager } from '../api/uniform/fs'
import { useConfig } from './configHelper'
import { isDirectory } from './fsUtils'
import { ref } from 'vue'

export interface MigrationReport {
  startTime: Date
  endTime?: Date
  status: 'pending' | 'running' | 'completed' | 'failed'
  usersFound: string[]
  filesProcessed: {
    databases: string[]
    blocks: string[]
    imgFiles: string[]
    pdfFiles: string[]
  }
  storageUsage: {
    before: number
    after: number
    ratio: number
  }
  errors: string[]
  backupPath?: string
}

export interface MigrationProgress {
  stage: 'scanning' | 'backing-up' | 'migrating' | 'verifying' | 'cleaning'
  progress: number // 0-100
  currentTask: string
  totalSteps: number
  currentStep: number
}

/**
 * 存储架构迁移服务
 * 负责将旧的存储结构迁移到新的用户隔离结构
 */
export class MigrationService {
  private fs: FileSystemManager
  private config: any
  private report: MigrationReport
  private progressCallback?: (progress: MigrationProgress) => void

  constructor() {
    this.fs = new FileSystemManager()
    this.config = useConfig()
    this.report = {
      startTime: new Date(),
      status: 'pending',
      usersFound: [],
      filesProcessed: {
        databases: [],
        blocks: [],
        imgFiles: [],
        pdfFiles: []
      },
      storageUsage: {
        before: 0,
        after: 0,
        ratio: 0
      },
      errors: []
    }
  }

  /**
   * 设置进度回调函数
   */
  setProgressCallback(callback: (progress: MigrationProgress) => void): void {
    this.progressCallback = callback
  }

  /**
   * 报告进度
   */
  private reportProgress(
    stage: MigrationProgress['stage'],
    progress: number,
    currentTask: string,
    currentStep: number,
    totalSteps: number
  ): void {
    if (this.progressCallback) {
      this.progressCallback({
        stage,
        progress,
        currentTask,
        currentStep,
        totalSteps
      })
    }
  }

  /**
   * 扫描现有存储结构，识别用户和文件
   */
  async scanExistingStructure(): Promise<{ users: string[]; needsMigration: boolean }> {
    try {
      this.reportProgress('scanning', 10, '扫描现有文件结构...', 1, 5)

      // 注意：workspacePath 已经指向 example 目录，不需要再添加 /example
      const examplePath = this.config.workspacePath
      console.log('检查example目录:', examplePath)
      console.log('workspacePath值:', this.config.workspacePath)

      // 检查example目录是否存在
      try {
        await this.fs.stat(examplePath)
        console.log('example目录存在，继续扫描...')
      } catch (error) {
        console.log('example目录不存在，返回无需迁移')
        // example目录不存在，无需迁移
        return { users: [], needsMigration: false }
      }

      const files = await this.fs.readdir(examplePath)
      console.log('扫描到的文件列表:', files)

      this.reportProgress('scanning', 30, '识别用户数据库文件...', 2, 5)

      // 识别数据库文件和用户ID
      const dbFiles = files.filter((file) => file.startsWith('chron_') && file.endsWith('.db'))
      const userIds = dbFiles.map((file) => file.replace('chron_', '').replace('.db', ''))

      this.reportProgress('scanning', 50, '识别块数据文件...', 3, 5)

      // 验证对应的blocks文件存在
      const blockFiles = files.filter(
        (file) => file.startsWith('blocks_') && file.endsWith('.json')
      )
      const blockUserIds = blockFiles.map((file) =>
        file.replace('blocks_', '').replace('.json', '')
      )

      // 确保数据库文件和块文件一一对应
      const validUserIds = userIds.filter((userId) => blockUserIds.includes(userId))

      console.log('扫描结果详情:')
      console.log('- 发现数据库文件:', dbFiles)
      console.log('- 提取用户ID:', userIds)
      console.log('- 发现blocks文件:', blockFiles)
      console.log('- blocks用户ID:', blockUserIds)
      console.log('- 有效用户ID:', validUserIds)

      this.reportProgress('scanning', 70, '扫描共享资源文件...', 4, 5)

      // 扫描共享文件
      const imgPath = `${examplePath}/img`
      const pdfPath = `${examplePath}/pdf`

      let imgFiles: string[] = []
      let pdfFiles: string[] = []

      try {
        imgFiles = await this.fs.readdir(imgPath)
      } catch (error) {
        // img目录不存在
      }

      try {
        pdfFiles = await this.fs.readdir(pdfPath)
      } catch (error) {
        // pdf目录不存在
      }

      // 更新报告
      this.report.usersFound = validUserIds
      this.report.filesProcessed.databases = dbFiles.filter((file) =>
        validUserIds.some((id) => file === `chron_${id}.db`)
      )
      this.report.filesProcessed.blocks = blockFiles.filter((file) =>
        validUserIds.some((id) => file === `blocks_${id}.json`)
      )
      this.report.filesProcessed.imgFiles = imgFiles
      this.report.filesProcessed.pdfFiles = pdfFiles

      this.reportProgress('scanning', 100, '扫描完成', 5, 5)

      // 检查是否需要迁移：有老架构用户数据，且对应的新架构数据不完整
      console.log('开始检查迁移需求...')
      console.log('- 有效用户数量:', validUserIds.length)

      let allMigrated = false
      if (validUserIds.length > 0) {
        allMigrated = await this.checkAllUsersAlreadyMigrated(validUserIds)
        console.log('- 所有用户已迁移:', allMigrated)
      }

      const needsMigration = validUserIds.length > 0 && !allMigrated
      console.log('- 最终迁移判断:', needsMigration)

      return { users: validUserIds, needsMigration }
    } catch (error) {
      this.report.errors.push(`扫描阶段失败: ${error}`)
      throw error
    }
  }

  /**
   * 检查是否已经存在新的目录结构
   */
  private async checkIfNewStructureExists(): Promise<boolean> {
    try {
      const usersPath = `${this.config.workspacePath}/users`
      console.log('检查users目录:', usersPath)
      await this.fs.stat(usersPath)
      console.log('users目录存在')
      return true
    } catch (error) {
      console.log('users目录不存在')
      return false
    }
  }

  /**
   * 检查所有用户是否已经完成迁移
   * @param userIds 用户ID列表
   * @returns 是否所有用户都已完成迁移
   */
  private async checkAllUsersAlreadyMigrated(userIds: string[]): Promise<boolean> {
    try {
      for (const userId of userIds) {
        const userPath = `${this.config.workspacePath}/users/${userId}`
        const dbPath = `${userPath}/chron.db`
        const blocksPath = `${userPath}/blocks.json`

        console.log(`检查用户 ${userId} 的迁移状态:`)
        console.log('- 数据库路径:', dbPath)
        console.log('- blocks路径:', blocksPath)

        try {
          // 检查新架构的必需文件是否存在
          await this.fs.stat(dbPath)
          await this.fs.stat(blocksPath)
          console.log(`用户 ${userId} 已完成迁移`)
        } catch (error) {
          console.log(`用户 ${userId} 未完成迁移，需要执行迁移`)
          return false
        }
      }

      console.log('所有用户都已完成迁移')
      return true
    } catch (error) {
      console.error('检查迁移状态失败:', error)
      return false
    }
  }

  /**
   * 创建迁移前备份
   */
  async createPreMigrationBackup(): Promise<string> {
    try {
      this.reportProgress('backing-up', 10, '创建迁移前备份...', 1, 3)

      const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\..+/, '')
      // 备份路径应该在workspacePath的父目录中，避免复制目录到自己的子目录
      const parentPath = this.config.workspacePath.replace(/\/example$/, '')
      const backupPath = `${parentPath}/migration_backup_${timestamp}`
      console.log('备份将创建在:', backupPath)

      await this.fs.mkdir(backupPath)

      this.reportProgress('backing-up', 50, '复制现有数据...', 2, 3)

      // 备份整个example目录
      // 注意：workspacePath 已经指向 example 目录
      const examplePath = this.config.workspacePath
      console.log('备份源路径:', examplePath)
      console.log('备份目标路径:', `${backupPath}/example`)
      await this.fs.copy(examplePath, `${backupPath}/example`)

      // 计算备份前的存储使用
      this.report.storageUsage.before = await this.calculateDirectorySize(examplePath)
      this.report.backupPath = backupPath

      this.reportProgress('backing-up', 100, '备份完成', 3, 3)

      return backupPath
    } catch (error) {
      this.report.errors.push(`备份阶段失败: ${error}`)
      throw error
    }
  }

  /**
   * 执行保守的文件迁移策略
   */
  async performConservativeMigration(userIds: string[]): Promise<void> {
    try {
      this.reportProgress('migrating', 5, '创建用户目录结构...', 1, 10)

      // 注意：workspacePath 已经指向 example 目录
      const examplePath = this.config.workspacePath
      const usersPath = `${this.config.workspacePath}/users`

      // 创建users根目录
      await this.fs.mkdir(usersPath)

      const totalSteps = userIds.length * 5 + 2 // 每个用户5个步骤，加上清理步骤
      let currentStep = 1

      for (const userId of userIds) {
        const userPath = `${usersPath}/${userId}`

        this.reportProgress(
          'migrating',
          (currentStep / totalSteps) * 100,
          `为用户 ${userId.substring(0, 8)}... 创建目录`,
          currentStep,
          totalSteps
        )

        // 1. 创建用户目录结构
        await this.fs.mkdir(userPath)
        await this.fs.mkdir(`${userPath}/img`)
        await this.fs.mkdir(`${userPath}/pdf`)
        currentStep++

        this.reportProgress(
          'migrating',
          (currentStep / totalSteps) * 100,
          `迁移用户 ${userId.substring(0, 8)}... 的数据库文件`,
          currentStep,
          totalSteps
        )

        // 2. 移动数据库文件
        const oldDbPath = `${examplePath}/chron_${userId}.db`
        const newDbPath = `${userPath}/chron.db`

        try {
          await this.fs.copy(oldDbPath, newDbPath)
          await this.fs.remove(oldDbPath)
        } catch (error) {
          this.report.errors.push(`移动数据库文件失败 ${userId}: ${error}`)
        }
        currentStep++

        this.reportProgress(
          'migrating',
          (currentStep / totalSteps) * 100,
          `迁移用户 ${userId.substring(0, 8)}... 的块数据文件`,
          currentStep,
          totalSteps
        )

        // 3. 移动块数据文件
        const oldBlockPath = `${examplePath}/blocks_${userId}.json`
        const newBlockPath = `${userPath}/blocks.json`

        try {
          await this.fs.copy(oldBlockPath, newBlockPath)
          await this.fs.remove(oldBlockPath)
        } catch (error) {
          this.report.errors.push(`移动块数据文件失败 ${userId}: ${error}`)
        }
        currentStep++

        this.reportProgress(
          'migrating',
          (currentStep / totalSteps) * 100,
          `为用户 ${userId.substring(0, 8)}... 复制图片文件`,
          currentStep,
          totalSteps
        )

        // 4. 保守策略：复制所有img文件给每个用户
        const imgPath = `${examplePath}/img`
        try {
          await this.fs.copy(imgPath, `${userPath}/img`)
        } catch (error) {
          // img目录可能不存在，这是正常的
          console.log(`img目录不存在或复制失败: ${error}`)
        }
        currentStep++

        this.reportProgress(
          'migrating',
          (currentStep / totalSteps) * 100,
          `为用户 ${userId.substring(0, 8)}... 复制PDF文件`,
          currentStep,
          totalSteps
        )

        // 5. 保守策略：复制所有pdf文件给每个用户
        const pdfPath = `${examplePath}/pdf`
        try {
          await this.fs.copy(pdfPath, `${userPath}/pdf`)
        } catch (error) {
          // pdf目录可能不存在，这是正常的
          console.log(`pdf目录不存在或复制失败: ${error}`)
        }
        currentStep++
      }

      this.reportProgress('migrating', 95, '清理旧的共享目录...', currentStep, totalSteps)

      // 清理旧的共享目录（在确认迁移成功后）
      try {
        await this.fs.remove(`${examplePath}/img`)
        await this.fs.remove(`${examplePath}/pdf`)
        await this.fs.remove(`${examplePath}/note`)
        await this.fs.remove(`${examplePath}/chat`)
        await this.fs.remove(`${examplePath}/flow`)
      } catch (error) {
        // 某些目录可能不存在，这是正常的
        console.log(`清理旧目录时出现错误: ${error}`)
      }

      this.reportProgress('migrating', 100, '迁移完成', totalSteps, totalSteps)
    } catch (error) {
      this.report.errors.push(`迁移阶段失败: ${error}`)
      throw error
    }
  }

  /**
   * 验证迁移结果
   */
  async verifyMigration(userIds: string[]): Promise<boolean> {
    try {
      this.reportProgress('verifying', 10, '验证迁移完整性...', 1, 4)

      const usersPath = `${this.config.workspacePath}/users`

      for (let i = 0; i < userIds.length; i++) {
        const userId = userIds[i]
        const userPath = `${usersPath}/${userId}`

        this.reportProgress(
          'verifying',
          20 + (i / userIds.length) * 60,
          `验证用户 ${userId.substring(0, 8)}... 的数据`,
          2,
          4
        )

        // 验证用户目录存在
        try {
          const userDir = await this.fs.stat(userPath)

          if (!isDirectory(userDir)) {
            throw new Error(`用户目录不是一个目录: ${userPath}`)
          }
        } catch (error) {
          this.report.errors.push(`用户目录验证失败 ${userId}: ${error}`)
          return false
        }

        // 验证核心文件存在
        try {
          await this.fs.stat(`${userPath}/chron.db`)
          await this.fs.stat(`${userPath}/blocks.json`)
        } catch (error) {
          this.report.errors.push(`核心文件验证失败 ${userId}: ${error}`)
          return false
        }

        // 验证资源目录存在
        try {
          const imgDir = await this.fs.stat(`${userPath}/img`)
          const pdfDir = await this.fs.stat(`${userPath}/pdf`)

          if (!isDirectory(imgDir) || !isDirectory(pdfDir)) {
            throw new Error(`资源目录不是一个目录`)
          }
        } catch (error) {
          this.report.errors.push(`资源目录验证失败 ${userId}: ${error}`)
          return false
        }
      }

      this.reportProgress('verifying', 90, '计算存储使用情况...', 3, 4)

      // 计算迁移后的存储使用
      this.report.storageUsage.after = await this.calculateDirectorySize(usersPath)
      this.report.storageUsage.ratio =
        this.report.storageUsage.before > 0
          ? this.report.storageUsage.after / this.report.storageUsage.before
          : 0

      this.reportProgress('verifying', 100, '验证完成', 4, 4)

      return true
    } catch (error) {
      this.report.errors.push(`验证阶段失败: ${error}`)
      return false
    }
  }

  /**
   * 执行完整的迁移流程
   */
  async performFullMigration(): Promise<MigrationReport> {
    this.report.status = 'running'
    this.report.startTime = new Date()

    try {
      // 1. 扫描现有结构
      const scanResult = await this.scanExistingStructure()

      if (!scanResult.needsMigration) {
        this.report.status = 'completed'
        this.report.endTime = new Date()
        return this.report
      }

      if (scanResult.users.length === 0) {
        this.report.status = 'completed'
        this.report.endTime = new Date()
        return this.report
      }

      // 2. 创建备份
      await this.createPreMigrationBackup()

      // 3. 执行迁移
      await this.performConservativeMigration(scanResult.users)

      // 4. 验证迁移
      const verificationResult = await this.verifyMigration(scanResult.users)

      if (verificationResult) {
        this.report.status = 'completed'
      } else {
        this.report.status = 'failed'
      }
    } catch (error) {
      this.report.status = 'failed'
      this.report.errors.push(`迁移流程失败: ${error}`)
    }

    this.report.endTime = new Date()
    return this.report
  }

  /**
   * 回滚迁移（从备份恢复）
   */
  async rollbackMigration(backupPath: string): Promise<void> {
    try {
      this.reportProgress('cleaning', 10, '开始回滚迁移...', 1, 4)

      // 注意：workspacePath 已经指向 example 目录
      const examplePath = this.config.workspacePath
      const usersPath = `${this.config.workspacePath}/users`

      this.reportProgress('cleaning', 30, '删除新结构...', 2, 4)

      // 删除新的目录结构
      await this.fs.remove(usersPath)
      await this.fs.remove(examplePath)

      this.reportProgress('cleaning', 70, '恢复备份数据...', 3, 4)

      // 从备份恢复
      await this.fs.copy(`${backupPath}/example`, examplePath)

      this.reportProgress('cleaning', 100, '回滚完成', 4, 4)
    } catch (error) {
      throw new Error(`回滚失败: ${error}`)
    }
  }

  /**
   * 获取迁移报告
   */
  getReport(): MigrationReport {
    return this.report
  }

  /**
   * 计算目录大小
   */
  private async calculateDirectorySize(dirPath: string): Promise<number> {
    try {
      const files = await this.fs.readdir(dirPath)
      let totalSize = 0

      for (const file of files) {
        const filePath = `${dirPath}/${file}`
        const stats = await this.fs.stat(filePath)

        if (isDirectory(stats)) {
          totalSize += await this.calculateDirectorySize(filePath)
        } else {
          totalSize += stats.size || 0
        }
      }

      return totalSize
    } catch (error) {
      return 0
    }
  }
}

// 创建单例实例
const migrationServiceInstance = ref<MigrationService | null>(null)

/**
 * 获取迁移服务实例
 */
export function useMigrationService(): MigrationService {
  if (!migrationServiceInstance.value) {
    migrationServiceInstance.value = new MigrationService()
  }
  return migrationServiceInstance.value
}
