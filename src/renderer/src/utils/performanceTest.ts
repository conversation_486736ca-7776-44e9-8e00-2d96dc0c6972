import { benchmarkYDocRebuild, getJsonContentFromDatabaseWithStats, type PerformanceStats } from './ydocUtils'

/**
 * 在浏览器控制台中运行的性能测试函数
 * 使用方法：在控制台输入 window.testYDocPerformance('your-uuid-here')
 */
export async function testYDocPerformance(uuid: string): Promise<PerformanceStats | null> {
    console.clear()
    console.log('🎯 Y.Doc 重建性能测试开始')
    console.log('===================================')

    try {
        // 单次测试
        console.log('\n🔍 单次重建测试:')
        const { content, stats } = await getJsonContentFromDatabaseWithStats(uuid)

        if (content) {
            console.log('✅ 重建成功!')
            console.log(`📊 详细统计:`)
            console.table({
                '总耗时': `${stats.totalTime.toFixed(2)}ms`,
                '数据库读取': `${stats.dbReadTime.toFixed(2)}ms`,
                'Y.Doc重建': `${stats.ydocRebuildTime.toFixed(2)}ms`,
                'JSON转换': `${stats.jsonConvertTime?.toFixed(2)}ms`,
                '更新数量': stats.updatesCount,
                '数据大小': `${(stats.totalDataSize / 1024).toFixed(2)} KB`,
                '平均更新大小': `${stats.averageUpdateSize.toFixed(0)} bytes`
            })
        }

        // 多次基准测试
        console.log('\n🏁 多次基准测试:')
        const avgStats = await benchmarkYDocRebuild(uuid, 5)

        // 性能评估
        console.log('\n🎯 性能评估:')
        if (avgStats.totalTime < 50) {
            console.log('🟢 性能优秀 (<50ms)')
        } else if (avgStats.totalTime < 200) {
            console.log('🟡 性能良好 (50-200ms)')
        } else if (avgStats.totalTime < 500) {
            console.log('🟠 性能一般 (200-500ms)')
        } else {
            console.log('🔴 性能较差 (>500ms)')
        }

        // 建议
        console.log('\n💡 优化建议:')
        if (avgStats.updatesCount > 100) {
            console.log('- 考虑定期压缩历史记录 (当前更新数量较多)')
        }
        if (avgStats.dbReadTime / avgStats.totalTime > 0.3) {
            console.log('- 数据库读取占比较高，可能需要优化数据库查询')
        }
        if (avgStats.ydocRebuildTime / avgStats.totalTime > 0.5) {
            console.log('- Y.Doc重建占比较高，可能需要优化更新数据结构')
        }

        return avgStats

    } catch (error) {
        console.error('❌ 性能测试失败:', error)
        return null
    }
}

/**
 * 简化版性能测试
 */
export async function quickPerformanceTest(uuid: string) {
    const start = performance.now()
    const { content, stats } = await getJsonContentFromDatabaseWithStats(uuid)
    const end = performance.now()

    console.log(`⚡ 快速测试结果: ${(end - start).toFixed(2)}ms`)
    return { content, stats, totalTime: end - start }
}

// 将函数绑定到全局对象，方便在控制台调用
if (typeof window !== 'undefined') {
    ; (window as any).testYDocPerformance = testYDocPerformance
        ; (window as any).quickPerformanceTest = quickPerformanceTest

    console.log('🛠️ 性能测试函数已绑定到全局对象:')
    console.log('- window.testYDocPerformance(uuid) - 完整性能测试')
    console.log('- window.quickPerformanceTest(uuid) - 快速性能测试')
} 