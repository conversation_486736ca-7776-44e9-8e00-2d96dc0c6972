import { jwtDecode } from 'jwt-decode'
import { useUserStore } from '@renderer/stores/user'
import { refreshToken } from '@renderer/api/uniform/auth'

/**
 * JWT Token 载荷接口
 */
interface JWTPayload {
  email: string
  id: number
  iat: number // 签发时间 (issued at)
  exp: number // 过期时间 (expiration time)
}

/**
 * 刷新Token响应数据接口
 */
interface RefreshTokenData {
  token: string
  userId: number
  email: string
}

/**
 * 检查token是否需要刷新
 * @param token JWT token字符串
 * @returns 是否需要刷新
 */
export const shouldRefreshToken = (token: string): boolean => {
  try {
    if (!token) {
      console.log('Token为空，无需刷新')
      return false
    }

    const decoded = jwtDecode<JWTPayload>(token)
    const currentTime = Math.floor(Date.now() / 1000) // 当前时间戳（秒）
    const expirationTime = decoded.exp
    const timeUntilExpiration = expirationTime - currentTime

    // 转换为天数
    const daysUntilExpiration = timeUntilExpiration / (24 * 60 * 60)

    console.log(`Token剩余有效期: ${daysUntilExpiration.toFixed(2)} 天`)

    // 如果剩余有效期小于等于10天，则需要刷新
    return daysUntilExpiration <= 10
  } catch (error) {
    console.error('解析JWT token失败:', error)
    return false
  }
}

/**
 * 执行token刷新操作
 * @returns Promise<boolean> 刷新是否成功
 */
export const performTokenRefresh = async (): Promise<boolean> => {
  try {
    console.log('开始刷新Token...')

    const tokenData = await refreshToken() as RefreshTokenData | null

    if (tokenData?.token) {
      const userStore = useUserStore()
      const newToken = tokenData.token

      // 更新store中的token
      userStore.setToken(newToken)

      // 更新用户信息（如果有变化）
      if (tokenData.email && tokenData.userId) {
        userStore.setUserInfo({
          uuid: tokenData.userId.toString(),
          email: tokenData.email
        })
      }

      console.log('Token刷新成功')
      return true
    } else {
      console.error('Token刷新失败: 响应数据为空', tokenData)
      return false
    }
  } catch (error) {
    console.error('Token刷新失败:', error)
    return false
  }
}

/**
 * 检查并自动刷新token
 * 这是主要的入口函数，会检查当前token状态并在需要时执行刷新
 */
export const checkAndRefreshToken = async (): Promise<void> => {
  try {
    const userStore = useUserStore()
    const currentToken = userStore.token

    if (!currentToken) {
      console.log('用户未登录，跳过token检查')
      return
    }

    console.log('开始检查Token状态...')

    if (shouldRefreshToken(currentToken)) {
      console.log('Token需要刷新，开始执行刷新操作...')
      const refreshSuccess = await performTokenRefresh()

      if (!refreshSuccess) {
        console.error('Token刷新失败，可能需要重新登录')
        // 可以在这里添加重新登录的逻辑或者清除用户信息
        // userStore.clearUserInfo()
      }
    } else {
      console.log('Token状态正常，无需刷新')
    }
  } catch (error) {
    console.error('检查Token状态时发生错误:', error)
  }
}

/**
 * 初始化认证系统
 * 在应用启动时调用，执行token状态检查和刷新
 */
export const initAuth = async (): Promise<void> => {
  console.log('初始化认证系统...')
  await checkAndRefreshToken()
}

/**
 * 获取token的剩余有效期（天数）
 * @param token JWT token字符串
 * @returns 剩余天数，如果解析失败返回null
 */
export const getTokenRemainingDays = (token: string): number | null => {
  try {
    if (!token) return null

    const decoded = jwtDecode<JWTPayload>(token)
    const currentTime = Math.floor(Date.now() / 1000)
    const expirationTime = decoded.exp
    const timeUntilExpiration = expirationTime - currentTime

    return timeUntilExpiration / (24 * 60 * 60)
  } catch (error) {
    console.error('解析JWT token失败:', error)
    return null
  }
}
