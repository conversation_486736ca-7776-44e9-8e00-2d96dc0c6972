/**
 * 图标自动扫描和发现系统
 * 支持零配置添加新分类和图标，自动生成元数据
 */

import { getAllSvgIcons, type SvgIcon } from './svgIcons'
import { pinyin } from 'pinyin-pro'
import { iconCategories } from '../config/iconConfig'

// 图标元数据接口
export interface IconMetadata {
  id: string
  name: string
  displayName: string
  category: string
  categoryLabel: string
  filename: string
  content?: string
  type: 'local' | 'preset' | 'generated'

  // 搜索相关
  aliases: string[]
  tags: string[]
  pinyin?: string
  pinyinShort?: string

  // 自动生成标记
  autoGenerated: boolean
  lastUpdated: string
}

// 分类元数据接口
export interface CategoryMetadata {
  id: string
  label: string
  labelEn: string
  labelZh: string
  iconCount: number
  lastScanned: string
  autoGenerated: boolean
}

// 扫描结果接口
export interface ScanResult {
  categories: CategoryMetadata[]
  icons: IconMetadata[]
  totalCategories: number
  totalIcons: number
  newCategories: string[]
  newIcons: string[]
  updatedAt: string
}

/**
 * 图标扫描器类
 */
export class IconScanner {
  private static instance: IconScanner
  private scanCache: Map<string, ScanResult> = new Map()
  private lastScanTime: string = ''

  static getInstance(): IconScanner {
    if (!IconScanner.instance) {
      IconScanner.instance = new IconScanner()
    }
    return IconScanner.instance
  }

  /**
   * 执行完整的图标扫描
   */
  async scanAllIcons(forceRescan = false): Promise<ScanResult> {
    const cacheKey = 'full-scan'
    const now = new Date().toISOString()

    // 检查缓存
    if (!forceRescan && this.scanCache.has(cacheKey)) {
      const cached = this.scanCache.get(cacheKey)!
      if (this.isCacheValid(cached.updatedAt)) {
        return cached
      }
    }

    console.log('🔍 开始扫描图标文件系统...')

    try {
      // 获取所有SVG图标
      const svgIcons = getAllSvgIcons()

      // 按分类组织图标
      const categoriesMap = this.groupIconsByCategory(svgIcons)

      // 生成分类元数据
      const categories = this.generateCategoryMetadata(categoriesMap)

      // 生成图标元数据
      const icons = this.generateIconMetadata(svgIcons, categoriesMap)

      // 检测新增内容
      const { newCategories, newIcons } = this.detectChanges(categories, icons)

      const result: ScanResult = {
        categories,
        icons,
        totalCategories: categories.length,
        totalIcons: icons.length,
        newCategories,
        newIcons,
        updatedAt: now
      }

      // 更新缓存
      this.scanCache.set(cacheKey, result)
      this.lastScanTime = now

      console.log(`✅ 扫描完成: ${result.totalCategories}个分类, ${result.totalIcons}个图标`)

      return result
    } catch (error) {
      console.error('❌ 图标扫描失败:', error)
      throw new Error(`Icon scanning failed: ${error}`)
    }
  }

  /**
   * 按分类组织图标
   */
  private groupIconsByCategory(svgIcons: SvgIcon[]): Map<string, SvgIcon[]> {
    const categoriesMap = new Map<string, SvgIcon[]>()

    svgIcons.forEach((icon) => {
      // 从文件路径提取分类名
      const category = this.extractCategoryFromPath(icon.name)

      if (!categoriesMap.has(category)) {
        categoriesMap.set(category, [])
      }
      categoriesMap.get(category)!.push(icon)
    })

    return categoriesMap
  }

  /**
   * 从文件路径提取分类名
   */
  private extractCategoryFromPath(iconName: string): string {
    // 假设图标名格式为 "category/filename" 或 "category-filename"
    if (iconName.includes('/')) {
      return iconName.split('/')[0]
    }
    if (iconName.includes('-')) {
      return iconName.split('-')[0]
    }
    return 'uncategorized'
  }

  /**
   * 生成分类元数据
   */
  private generateCategoryMetadata(categoriesMap: Map<string, SvgIcon[]>): CategoryMetadata[] {
    const categories: CategoryMetadata[] = []

    categoriesMap.forEach((icons, categoryId) => {
      const category: CategoryMetadata = {
        id: categoryId,
        label: this.generateCategoryLabel(categoryId),
        labelEn: this.generateEnglishLabel(categoryId),
        labelZh: this.generateChineseLabel(categoryId),
        iconCount: icons.length,
        lastScanned: new Date().toISOString(),
        autoGenerated: true
      }

      categories.push(category)
    })

    return categories.sort((a, b) => a.label.localeCompare(b.label))
  }

  /**
   * 生成图标元数据
   */
  private generateIconMetadata(
    svgIcons: SvgIcon[],
    categoriesMap: Map<string, SvgIcon[]>
  ): IconMetadata[] {
    const icons: IconMetadata[] = []

    svgIcons.forEach((svgIcon) => {
      const category = this.extractCategoryFromPath(svgIcon.name)
      const cleanName = this.cleanIconName(svgIcon.name)

      const icon: IconMetadata = {
        id: `${category}-${cleanName}`,
        name: cleanName,
        displayName: this.generateDisplayName(cleanName),
        category,
        categoryLabel: this.generateCategoryLabel(category),
        filename: svgIcon.name,
        content: svgIcon.content,
        type: 'local',

        // 搜索相关 (基础自动生成)
        aliases: this.generateBasicAliases(cleanName, category),
        tags: this.generateBasicTags(cleanName, category),
        pinyin: this.generatePinyin(cleanName),
        pinyinShort: this.generatePinyinShort(cleanName),

        // 元数据
        autoGenerated: true,
        lastUpdated: new Date().toISOString()
      }

      icons.push(icon)
    })

    return icons
  }

  /**
   * 清理图标名称
   */
  private cleanIconName(filename: string): string {
    return filename
      .replace(/^.*\//, '') // 移除路径前缀
      .replace(/\.svg$/, '') // 移除.svg扩展名
      .replace(/^\d+-/, '') // 移除编号前缀如 "001-"
      .replace(/[_\s]+/g, '-') // 下划线和空格转连字符
      .toLowerCase()
      .trim()
  }

  /**
   * 生成显示名称
   */
  private generateDisplayName(cleanName: string): string {
    return cleanName
      .split('-')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  }

  /**
   * 生成分类标签
   */
  private generateCategoryLabel(categoryId: string): string {
    const categoryConfig = iconCategories.find((c) => c.id === categoryId)
    if (categoryConfig) {
      return categoryConfig.labelZh || categoryConfig.labelEn || this.autoGenerateLabel(categoryId)
    }
    return this.autoGenerateLabel(categoryId)
  }

  /**
   * 自动生成标签
   */
  private autoGenerateLabel(categoryId: string): string {
    // 简单的驼峰转换
    return categoryId
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, (str) => str.toUpperCase())
      .trim()
  }

  /**
   * 生成英文标签
   */
  private generateEnglishLabel(categoryId: string): string {
    const categoryConfig = iconCategories.find((c) => c.id === categoryId)
    if (categoryConfig) {
      return categoryConfig.labelEn || this.autoGenerateLabel(categoryId)
    }
    return this.autoGenerateLabel(categoryId)
  }

  /**
   * 生成中文标签
   */
  private generateChineseLabel(categoryId: string): string {
    const categoryConfig = iconCategories.find((c) => c.id === categoryId)
    if (categoryConfig) {
      return categoryConfig.labelZh || this.autoGenerateLabel(categoryId)
    }
    return this.autoGenerateLabel(categoryId)
  }

  /**
   * 生成基础别名
   */
  private generateBasicAliases(iconName: string, category: string): string[] {
    const aliases: string[] = []

    // 添加原始名称变体
    aliases.push(iconName)
    aliases.push(iconName.replace(/-/g, ' '))
    aliases.push(iconName.replace(/-/g, ''))

    // 添加分类名作为别名
    aliases.push(category)

    // 移除重复项
    return [...new Set(aliases)].filter(Boolean)
  }

  /**
   * 生成基础标签
   */
  private generateBasicTags(iconName: string, category: string): string[] {
    const tags: string[] = []

    // 添加分类标签
    tags.push(category)
    tags.push(this.generateCategoryLabel(category))

    // 根据图标名生成语义标签
    const semanticTags = this.generateSemanticTags(iconName)
    tags.push(...semanticTags)

    return [...new Set(tags)].filter(Boolean)
  }

  /**
   * 生成语义标签
   */
  private generateSemanticTags(iconName: string): string[] {
    const tags: string[] = []

    // 常见语义映射
    const semanticMap: Record<string, string[]> = {
      home: ['房屋', '家', 'house'],
      car: ['汽车', '交通', 'vehicle'],
      food: ['食物', '餐饮', 'dining'],
      book: ['书籍', '学习', 'education'],
      music: ['音乐', '娱乐', 'entertainment'],
      heart: ['爱心', '喜欢', 'love'],
      star: ['星星', '收藏', 'favorite']
    }

    // 检查是否包含语义关键词
    Object.keys(semanticMap).forEach((keyword) => {
      if (iconName.includes(keyword)) {
        tags.push(...semanticMap[keyword])
      }
    })

    return tags
  }

  /**
   * 生成拼音 (使用 pinyin-pro 库)
   */
  private generatePinyin(iconName: string): string {
    try {
      // 从图标名称中提取中文字符
      const chineseText = this.extractChineseText(iconName)
      if (!chineseText) {
        // 如果没有中文，返回原始名称的拼音化版本
        return iconName.toLowerCase().replace(/[^a-z0-9]/g, '')
      }

      // 生成全拼音
      const fullPinyin = pinyin(chineseText, {
        pattern: 'pinyin',
        toneType: 'none',
        type: 'string'
      })

      return fullPinyin.toLowerCase().replace(/\s+/g, '')
    } catch (error) {
      console.warn('拼音生成失败:', error)
      return iconName.toLowerCase().replace(/[^a-z0-9]/g, '')
    }
  }

  /**
   * 生成拼音简写 (使用 pinyin-pro 库)
   */
  private generatePinyinShort(iconName: string): string {
    try {
      // 从图标名称中提取中文字符
      const chineseText = this.extractChineseText(iconName)
      if (!chineseText) {
        // 如果没有中文，返回英文首字母
        return iconName
          .split(/[-_\s]+/)
          .map((word) => word.charAt(0))
          .join('')
          .toLowerCase()
      }

      // 生成拼音首字母
      const shortPinyin = pinyin(chineseText, {
        pattern: 'first',
        toneType: 'none',
        type: 'string'
      })

      return shortPinyin.toLowerCase().replace(/\s+/g, '')
    } catch (error) {
      console.warn('拼音简写生成失败:', error)
      return iconName
        .split(/[-_\s]+/)
        .map((word) => word.charAt(0))
        .join('')
        .toLowerCase()
    }
  }

  /**
   * 提取中文文本
   */
  private extractChineseText(text: string): string {
    const chineseRegex = /[\u4e00-\u9fa5]+/g
    const matches = text.match(chineseRegex)
    return matches ? matches.join('') : ''
  }

  /**
   * 检测变更
   */
  private detectChanges(
    categories: CategoryMetadata[],
    icons: IconMetadata[]
  ): { newCategories: string[]; newIcons: string[] } {
    // 这里应该与之前的扫描结果比较
    // 目前返回空数组，表示没有变更检测
    return {
      newCategories: [],
      newIcons: []
    }
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(cacheTime: string): boolean {
    const now = new Date().getTime()
    const cacheTimestamp = new Date(cacheTime).getTime()
    const maxAge = 5 * 60 * 1000 // 5分钟缓存

    return now - cacheTimestamp < maxAge
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.scanCache.clear()
    console.log('🗑️ 图标扫描缓存已清除')
  }

  /**
   * 获取扫描统计信息
   */
  getScanStats(): { lastScanTime: string; cacheSize: number } {
    return {
      lastScanTime: this.lastScanTime,
      cacheSize: this.scanCache.size
    }
  }
}

// 导出单例实例
export const iconScanner = IconScanner.getInstance()

// 快捷方法
export const scanIcons = (forceRescan = false) => iconScanner.scanAllIcons(forceRescan)
export const clearIconCache = () => iconScanner.clearCache()
export const getIconScanStats = () => iconScanner.getScanStats()
