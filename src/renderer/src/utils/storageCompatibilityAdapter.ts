import { FileSystemManager } from '../api/uniform/fs'
import { useConfig } from './configHelper'
import { ref } from 'vue'

export type StorageMode = 'legacy' | 'modern'

export interface UserMigrationInfo {
  userId: string
  version: string
  migrated: boolean
  rollbackAvailable: boolean
  backupPath?: string
  migrationDate?: Date
}

export interface MigrationStatus {
  users: UserMigrationInfo[]
}

// 兼容老格式的接口
export interface LegacyMigrationStatus {
  version: string
  migrated: boolean
  rollbackAvailable: boolean
  backupPath?: string
  migrationDate?: Date
}

/**
 * 存储兼容性适配器
 * 负责检测和适配新旧两种存储架构
 */
export class StorageCompatibilityAdapter {
  private fs: FileSystemManager
  private config: any
  private storageModeCache = new Map<string, StorageMode>()
  private migrationStatusCache: MigrationStatus | null = null
  private userMigrationCache = new Map<string, UserMigrationInfo>()

  constructor() {
    this.fs = new FileSystemManager()
    this.config = useConfig()
  }

  /**
   * 检测用户的存储模式
   * @param userId 用户UUID
   * @returns 存储模式：'modern' 或 'legacy'
   */
  async detectStorageMode(userId: string): Promise<StorageMode> {
    // 检查缓存
    if (this.storageModeCache.has(userId)) {
      const cachedMode = this.storageModeCache.get(userId)!
      console.log(`使用缓存的存储模式: ${cachedMode}`)
      return cachedMode
    }
    console.log(`为用户 ${userId} 开始检测存储模式...`)

    try {
      // 第一优先级：检查用户级迁移状态
      try {
        const userMigrationStatus = await this.getUserMigrationStatus(userId)
        if (userMigrationStatus && userMigrationStatus.migrated && userMigrationStatus.version === 'v2.0') {
          console.log(`用户 ${userId} 的迁移状态显示已完成迁移到v2.0，返回modern模式`)
          this.storageModeCache.set(userId, 'modern')
          return 'modern'
        }
      } catch (error) {
        console.log('读取用户迁移状态失败，继续文件检测')
      }

      // 第二优先级：检查modern架构完整性
      const modernDbPath = `${this.config.workspacePath}/users/${userId}/chron.db`
      const modernBlocksPath = `${this.config.workspacePath}/users/${userId}/blocks.json`
      console.log('检查modern架构完整性:', modernDbPath)

      try {
        const modernDbStat = await this.fs.stat(modernDbPath)
        const modernBlocksStat = await this.fs.stat(modernBlocksPath)
        
        // 检查数据库文件和blocks文件都存在且为文件
        const isDbFile = modernDbStat.isFile ? modernDbStat.isFile() : (!modernDbStat.isDirectory && modernDbStat.size !== undefined)
        const isBlocksFile = modernBlocksStat.isFile ? modernBlocksStat.isFile() : (!modernBlocksStat.isDirectory && modernBlocksStat.size !== undefined)
        
        if (isDbFile && isBlocksFile) {
          console.log(`用户 ${userId} 的modern架构完整，重建用户迁移状态并返回modern模式`)
          // 重建用户级迁移状态
          await this.saveUserMigrationStatus(userId, {
            userId,
            version: 'v2.0',
            migrated: true,
            rollbackAvailable: false
          })
          this.storageModeCache.set(userId, 'modern')
          return 'modern'
        }
      } catch (error) {
        console.log('modern架构文件检查失败，继续检查legacy')
      }

      // 第三优先级：检查legacy架构
      const legacyDbPath = `${this.config.workspacePath}/chron_${userId}.db`
      const legacyBlocksPath = `${this.config.workspacePath}/blocks_${userId}.json`
      console.log('检查legacy架构:', legacyDbPath)
      
      try {
        const legacyDbStat = await this.fs.stat(legacyDbPath)
        const legacyBlocksStat = await this.fs.stat(legacyBlocksPath)
        
        // 检查数据库文件和blocks文件都存在且为文件
        const isDbFile = legacyDbStat.isFile ? legacyDbStat.isFile() : (!legacyDbStat.isDirectory && legacyDbStat.size !== undefined)
        const isBlocksFile = legacyBlocksStat.isFile ? legacyBlocksStat.isFile() : (!legacyBlocksStat.isDirectory && legacyBlocksStat.size !== undefined)
        
        if (isDbFile && isBlocksFile) {
          console.log('legacy架构完整，返回legacy模式')
          this.storageModeCache.set(userId, 'legacy')
          return 'legacy'
        }
      } catch (error) {
        console.log('legacy架构文件检查失败:', error)
      }

      // 都不存在时，默认使用新架构
      console.log('无有效数据架构，默认返回modern模式')
      this.storageModeCache.set(userId, 'modern')
      return 'modern'
    } catch (error) {
      console.warn(`检测存储模式失败，用户 ${userId}: ${error}`)
      // 发生错误时默认使用新架构
      this.storageModeCache.set(userId, 'modern')
      return 'modern'
    }
  }

  /**
   * 获取自适应的数据库路径
   * @param userId 用户UUID
   * @returns 数据库路径（不含扩展名）
   */
  async getAdaptiveDbPath(userId: string): Promise<string> {
    const mode = await this.detectStorageMode(userId)

    if (mode === 'legacy') {
      return `${this.config.workspacePath}/chron_${userId}`
    } else {
      return `${this.config.workspacePath}/users/${userId}/chron`
    }
  }

  /**
   * 获取自适应的图片目录路径
   * @param userId 用户UUID
   * @returns 图片目录路径
   */
  async getAdaptiveImgPath(userId: string): Promise<string> {
    const mode = await this.detectStorageMode(userId)

    if (mode === 'legacy') {
      return `${this.config.workspacePath}/img`
    } else {
      return `${this.config.workspacePath}/users/${userId}/img`
    }
  }

  /**
   * 获取自适应的PDF目录路径
   * @param userId 用户UUID
   * @returns PDF目录路径
   */
  async getAdaptivePdfPath(userId: string): Promise<string> {
    const mode = await this.detectStorageMode(userId)

    if (mode === 'legacy') {
      return `${this.config.workspacePath}/pdf`
    } else {
      return `${this.config.workspacePath}/users/${userId}/pdf`
    }
  }

  /**
   * 获取自适应的工作区路径
   * @param userId 用户UUID
   * @returns 用户工作区路径
   */
  async getAdaptiveWorkspace(userId: string): Promise<string> {
    const mode = await this.detectStorageMode(userId)

    if (mode === 'legacy') {
      return `${this.config.workspacePath}`
    } else {
      return `${this.config.workspacePath}/users/${userId}`
    }
  }

  /**
   * 获取自适应的blocks文件路径
   * @param userId 用户UUID
   * @returns blocks.json文件路径
   */
  async getAdaptiveBlocksPath(userId: string): Promise<string> {
    const mode = await this.detectStorageMode(userId)

    if (mode === 'legacy') {
      return `${this.config.workspacePath}/blocks_${userId}.json`
    } else {
      return `${this.config.workspacePath}/users/${userId}/blocks.json`
    }
  }

  /**
   * 获取迁移状态
   * @returns 迁移状态信息
   */
  async getMigrationStatus(): Promise<MigrationStatus> {
    // 检查缓存
    if (this.migrationStatusCache) {
      return this.migrationStatusCache
    }

    try {
      const statusPath = `${this.config.workspacePath}/.migration_status.json`
      const statusContent = await this.fs.readFile(statusPath)
      const rawData = JSON.parse(statusContent)

      // 检查是否是新格式（包含users数组）
      if (rawData.users && Array.isArray(rawData.users)) {
        const status = rawData as MigrationStatus
        // 解析用户级日期
        status.users.forEach(user => {
          if (user.migrationDate && typeof user.migrationDate === 'string') {
            user.migrationDate = new Date(user.migrationDate)
          }
        })
        this.migrationStatusCache = status
        return status
      } else {
        // 老格式，需要升级
        const legacyStatus = rawData as LegacyMigrationStatus
        console.log('检测到老格式的迁移状态文件，需要升级')
        
        // 返回空的新格式，让具体的用户检查逻辑处理升级
        const newStatus: MigrationStatus = {
          users: []
        }
        this.migrationStatusCache = newStatus
        return newStatus
      }
    } catch (error) {
      // 文件不存在或解析失败，返回默认状态
      const defaultStatus: MigrationStatus = {
        users: []
      }
      this.migrationStatusCache = defaultStatus
      return defaultStatus
    }
  }

  /**
   * 获取特定用户的迁移状态
   * @param userId 用户UUID
   * @returns 用户迁移状态信息
   */
  async getUserMigrationStatus(userId: string): Promise<UserMigrationInfo | null> {
    // 检查缓存
    if (this.userMigrationCache.has(userId)) {
      return this.userMigrationCache.get(userId)!
    }

    try {
      const migrationStatus = await this.getMigrationStatus()
      const userStatus = migrationStatus.users.find(user => user.userId === userId)
      
      if (userStatus) {
        this.userMigrationCache.set(userId, userStatus)
        return userStatus
      }

      // 检查是否存在老格式数据需要升级
      const legacyStatus = await this.checkLegacyMigrationStatus(userId)
      if (legacyStatus) {
        console.log(`为用户 ${userId} 升级老格式迁移状态`)
        await this.saveUserMigrationStatus(userId, legacyStatus)
        return legacyStatus
      }

      return null
    } catch (error) {
      console.error('获取用户迁移状态失败:', error)
      return null
    }
  }

  /**
   * 检查老格式迁移状态并转换
   * @param userId 用户UUID
   * @returns 转换后的用户迁移状态
   */
  async checkLegacyMigrationStatus(userId: string): Promise<UserMigrationInfo | null> {
    try {
      const statusPath = `${this.config.workspacePath}/.migration_status.json`
      const statusContent = await this.fs.readFile(statusPath)
      const rawData = JSON.parse(statusContent)

      // 如果是老格式且已迁移，转换为新格式
      if (!rawData.users && rawData.migrated && rawData.version) {
        const legacyStatus = rawData as LegacyMigrationStatus
        const userStatus: UserMigrationInfo = {
          userId,
          version: legacyStatus.version,
          migrated: legacyStatus.migrated,
          rollbackAvailable: legacyStatus.rollbackAvailable,
          backupPath: legacyStatus.backupPath,
          migrationDate: legacyStatus.migrationDate ? new Date(legacyStatus.migrationDate) : undefined
        }
        return userStatus
      }

      return null
    } catch (error) {
      return null
    }
  }

  /**
   * 设置迁移状态
   * @param status 迁移状态信息
   */
  async setMigrationStatus(status: MigrationStatus): Promise<void> {
    try {
      const statusPath = `${this.config.workspacePath}/.migration_status.json`
      await this.fs.writeFile(statusPath, JSON.stringify(status, null, 2))
      this.migrationStatusCache = status
    } catch (error) {
      console.error('设置迁移状态失败:', error)
      throw error
    }
  }

  /**
   * 保存用户迁移状态
   * @param userId 用户UUID
   * @param userInfo 用户迁移信息
   */
  async saveUserMigrationStatus(userId: string, userInfo: UserMigrationInfo): Promise<void> {
    try {
      // 获取当前迁移状态
      const migrationStatus = await this.getMigrationStatus()
      
      // 查找现有用户索引
      const existingIndex = migrationStatus.users.findIndex(user => user.userId === userId)
      
      // 添加迁移时间戳
      const userInfoWithDate = {
        ...userInfo,
        migrationDate: userInfo.migrationDate || new Date()
      }
      
      if (existingIndex >= 0) {
        // 更新现有用户
        migrationStatus.users[existingIndex] = userInfoWithDate
      } else {
        // 添加新用户
        migrationStatus.users.push(userInfoWithDate)
      }
      
      // 保存更新后的状态
      await this.setMigrationStatus(migrationStatus)
      
      // 更新缓存
      this.userMigrationCache.set(userId, userInfoWithDate)
      
      console.log(`用户 ${userId} 的迁移状态已保存: version=${userInfo.version}, migrated=${userInfo.migrated}`)
    } catch (error) {
      console.error('保存用户迁移状态失败:', error)
      throw error
    }
  }

  /**
   * 清除缓存
   * 在迁移或回滚后调用，确保重新检测存储模式
   */
  clearCache(): void {
    this.storageModeCache.clear()
    this.migrationStatusCache = null
    this.userMigrationCache.clear()
    console.log('存储适配器缓存已清除')
  }

  /**
   * 清除特定用户的缓存
   * @param userId 用户UUID
   */
  clearUserCache(userId: string): void {
    this.storageModeCache.delete(userId)
    console.log(`用户 ${userId} 的存储模式缓存已清除`)
  }

  /**
   * 批量检测多个用户的存储模式
   * @param userIds 用户UUID数组
   * @returns 用户ID到存储模式的映射
   */
  async detectMultipleStorageModes(userIds: string[]): Promise<Map<string, StorageMode>> {
    const results = new Map<string, StorageMode>()

    // 并行检测，但限制并发数量
    const concurrency = Math.min(userIds.length, 5)
    const chunks: string[][] = []

    for (let i = 0; i < userIds.length; i += concurrency) {
      chunks.push(userIds.slice(i, i + concurrency))
    }

    for (const chunk of chunks) {
      const promises = chunk.map(async (userId) => {
        const mode = await this.detectStorageMode(userId)
        results.set(userId, mode)
      })

      await Promise.all(promises)
    }

    return results
  }

  /**
   * 检查是否存在混合存储模式
   * 某些用户使用新架构，某些用户使用老架构
   * @param userIds 用户UUID数组
   * @returns 是否存在混合模式
   */
  async hasMixedStorageModes(userIds: string[]): Promise<{
    hasMixed: boolean
    modernUsers: string[]
    legacyUsers: string[]
  }> {
    const modes = await this.detectMultipleStorageModes(userIds)

    const modernUsers: string[] = []
    const legacyUsers: string[] = []

    modes.forEach((mode, userId) => {
      if (mode === 'modern') {
        modernUsers.push(userId)
      } else {
        legacyUsers.push(userId)
      }
    })

    return {
      hasMixed: modernUsers.length > 0 && legacyUsers.length > 0,
      modernUsers,
      legacyUsers
    }
  }

  /**
   * 验证存储结构完整性
   * @param userId 用户UUID
   * @returns 验证结果
   */
  async validateStorageIntegrity(userId: string): Promise<{
    isValid: boolean
    missingFiles: string[]
    issues: string[]
  }> {
    const missingFiles: string[] = []
    const issues: string[] = []

    try {
      const mode = await this.detectStorageMode(userId)

      if (mode === 'modern') {
        // 验证新架构完整性
        const userPath = `${this.config.workspacePath}/users/${userId}`
        const dbPath = `${userPath}/chron.db`
        const blocksPath = `${userPath}/blocks.json`
        const imgPath = `${userPath}/img`
        const pdfPath = `${userPath}/pdf`

        const checks = [
          { path: userPath, name: '用户目录', isDirectory: true },
          { path: dbPath, name: '数据库文件', isDirectory: false },
          { path: blocksPath, name: 'blocks文件', isDirectory: false },
          { path: imgPath, name: '图片目录', isDirectory: true },
          { path: pdfPath, name: 'PDF目录', isDirectory: true }
        ]

        for (const check of checks) {
          try {
            const stat = await this.fs.stat(check.path)
            if (check.isDirectory && !stat.isDirectory()) {
              issues.push(`${check.name}应该是目录但是文件: ${check.path}`)
            } else if (!check.isDirectory && !stat.isFile()) {
              issues.push(`${check.name}应该是文件但是目录: ${check.path}`)
            }
          } catch (error) {
            missingFiles.push(check.name)
          }
        }
      } else {
        // 验证老架构完整性
        const examplePath = `${this.config.workspacePath}`
        const dbPath = `${examplePath}/chron_${userId}.db`
        const blocksPath = `${examplePath}/blocks_${userId}.json`
        const imgPath = `${examplePath}/img`
        const pdfPath = `${examplePath}/pdf`

        const checks = [
          { path: examplePath, name: 'example目录', isDirectory: true },
          { path: dbPath, name: '数据库文件', isDirectory: false },
          { path: blocksPath, name: 'blocks文件', isDirectory: false },
          { path: imgPath, name: '共享图片目录', isDirectory: true },
          { path: pdfPath, name: '共享PDF目录', isDirectory: true }
        ]

        for (const check of checks) {
          try {
            const stat = await this.fs.stat(check.path)
            if (check.isDirectory && !stat.isDirectory()) {
              issues.push(`${check.name}应该是目录但是文件: ${check.path}`)
            } else if (!check.isDirectory && !stat.isFile()) {
              issues.push(`${check.name}应该是文件但是目录: ${check.path}`)
            }
          } catch (error) {
            missingFiles.push(check.name)
          }
        }
      }

      return {
        isValid: missingFiles.length === 0 && issues.length === 0,
        missingFiles,
        issues
      }
    } catch (error) {
      return {
        isValid: false,
        missingFiles: [],
        issues: [`验证过程失败: ${error}`]
      }
    }
  }

  /**
   * 获取存储统计信息
   * @param userId 用户UUID
   * @returns 存储统计信息
   */
  async getStorageStats(userId: string): Promise<{
    mode: StorageMode
    totalSize: number
    dbSize: number
    imgSize: number
    pdfSize: number
    blocksSize: number
  }> {
    const mode = await this.detectStorageMode(userId)

    const getFileSize = async (filePath: string): Promise<number> => {
      try {
        const stat = await this.fs.stat(filePath)
        return stat.size || 0
      } catch (error) {
        return 0
      }
    }

    const getDirectorySize = async (dirPath: string): Promise<number> => {
      try {
        const files = await this.fs.readdir(dirPath)
        let totalSize = 0

        for (const file of files) {
          const filePath = `${dirPath}/${file}`
          const stat = await this.fs.stat(filePath)

          if (stat.isDirectory()) {
            totalSize += await getDirectorySize(filePath)
          } else {
            totalSize += stat.size || 0
          }
        }

        return totalSize
      } catch (error) {
        return 0
      }
    }

    if (mode === 'modern') {
      const userPath = `${this.config.workspacePath}/users/${userId}`
      const dbSize = await getFileSize(`${userPath}/chron.db`)
      const blocksSize = await getFileSize(`${userPath}/blocks.json`)
      const imgSize = await getDirectorySize(`${userPath}/img`)
      const pdfSize = await getDirectorySize(`${userPath}/pdf`)

      return {
        mode,
        totalSize: dbSize + blocksSize + imgSize + pdfSize,
        dbSize,
        imgSize,
        pdfSize,
        blocksSize
      }
    } else {
      const examplePath = `${this.config.workspacePath}`
      const dbSize = await getFileSize(`${examplePath}/chron_${userId}.db`)
      const blocksSize = await getFileSize(`${examplePath}/blocks_${userId}.json`)
      const imgSize = await getDirectorySize(`${examplePath}/img`)
      const pdfSize = await getDirectorySize(`${examplePath}/pdf`)

      return {
        mode,
        totalSize: dbSize + blocksSize + imgSize + pdfSize,
        dbSize,
        imgSize,
        pdfSize,
        blocksSize
      }
    }
  }
}

// 创建单例实例
const adapterInstance = ref<StorageCompatibilityAdapter | null>(null)

/**
 * 获取存储兼容性适配器实例
 */
export function useStorageCompatibilityAdapter(): StorageCompatibilityAdapter {
  if (!adapterInstance.value) {
    try {
      adapterInstance.value = new StorageCompatibilityAdapter()
    } catch (error) {
      console.warn('无法初始化存储兼容性适配器:', error)
      // 在测试环境中可能会失败，返回一个mock实例
      adapterInstance.value = new StorageCompatibilityAdapter()
    }
  }
  return adapterInstance.value
}
