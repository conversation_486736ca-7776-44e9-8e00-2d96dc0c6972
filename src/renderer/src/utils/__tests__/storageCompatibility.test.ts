import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { FileSystemManager } from '../../api/uniform/fs'
import { StorageCompatibilityAdapter } from '../storageCompatibilityAdapter'
import { useConfig } from '../configHelper'
import path from 'path'

describe('StorageCompatibilityAdapter', () => {
  let adapter: StorageCompatibilityAdapter
  let fs: FileSystemManager
  let testWorkspacePath: string
  let config: any

  beforeEach(() => {
    fs = new FileSystemManager()
    config = useConfig()
    testWorkspacePath = path.resolve(process.cwd(), 'test-workspace')
    
    // 创建测试实例
    adapter = new StorageCompatibilityAdapter()
    
    // 模拟配置
    config.workspacePath = testWorkspacePath
  })

  afterEach(async () => {
    // 清理测试环境
    try {
      await fs.remove(testWorkspacePath)
    } catch (error) {
      // 忽略清理错误
    }
  })

  describe('存储模式检测', () => {
    it('应该检测到新架构（modern）', async () => {
      const userId = '3492ed08-b2dc-45bd-abed-20090a1954e5'
      
      // 创建新架构结构
      const userPath = `${testWorkspacePath}/users/${userId}`
      await fs.mkdir(userPath)
      await fs.writeFile(`${userPath}/chron.db`, '')
      
      const mode = await adapter.detectStorageMode(userId)
      expect(mode).toBe('modern')
    })

    it('应该检测到老架构（legacy）', async () => {
      const userId = '3492ed08-b2dc-45bd-abed-20090a1954e5'
      
      // 创建老架构结构
      const examplePath = `${testWorkspacePath}/example`
      await fs.mkdir(examplePath)
      await fs.writeFile(`${examplePath}/chron_${userId}.db`, '')
      
      const mode = await adapter.detectStorageMode(userId)
      expect(mode).toBe('legacy')
    })

    it('应该默认返回新架构（modern）当两种都不存在时', async () => {
      const userId = 'non-existent-user'
      
      const mode = await adapter.detectStorageMode(userId)
      expect(mode).toBe('modern')
    })

    it('应该优先检测新架构（当两种都存在时）', async () => {
      const userId = '3492ed08-b2dc-45bd-abed-20090a1954e5'
      
      // 创建两种架构
      const examplePath = `${testWorkspacePath}/example`
      const userPath = `${testWorkspacePath}/users/${userId}`
      
      await fs.mkdir(examplePath)
      await fs.writeFile(`${examplePath}/chron_${userId}.db`, '')
      
      await fs.mkdir(userPath)
      await fs.writeFile(`${userPath}/chron.db`, '')
      
      const mode = await adapter.detectStorageMode(userId)
      expect(mode).toBe('modern')
    })
  })

  describe('路径适配', () => {
    it('应该返回新架构的数据库路径', async () => {
      const userId = '3492ed08-b2dc-45bd-abed-20090a1954e5'
      
      // 创建新架构
      const userPath = `${testWorkspacePath}/users/${userId}`
      await fs.mkdir(userPath)
      await fs.writeFile(`${userPath}/chron.db`, '')
      
      const dbPath = await adapter.getAdaptiveDbPath(userId)
      expect(dbPath).toBe(`${testWorkspacePath}/users/${userId}/chron`)
    })

    it('应该返回老架构的数据库路径', async () => {
      const userId = '3492ed08-b2dc-45bd-abed-20090a1954e5'
      
      // 创建老架构
      const examplePath = `${testWorkspacePath}/example`
      await fs.mkdir(examplePath)
      await fs.writeFile(`${examplePath}/chron_${userId}.db`, '')
      
      const dbPath = await adapter.getAdaptiveDbPath(userId)
      expect(dbPath).toBe(`${testWorkspacePath}/example/chron_${userId}`)
    })

    it('应该返回新架构的图片路径', async () => {
      const userId = '3492ed08-b2dc-45bd-abed-20090a1954e5'
      
      // 创建新架构
      const userPath = `${testWorkspacePath}/users/${userId}`
      await fs.mkdir(userPath)
      await fs.mkdir(`${userPath}/img`)
      await fs.writeFile(`${userPath}/chron.db`, '')
      
      const imgPath = await adapter.getAdaptiveImgPath(userId)
      expect(imgPath).toBe(`${testWorkspacePath}/users/${userId}/img`)
    })

    it('应该返回老架构的共享图片路径', async () => {
      const userId = '3492ed08-b2dc-45bd-abed-20090a1954e5'
      
      // 创建老架构
      const examplePath = `${testWorkspacePath}/example`
      await fs.mkdir(examplePath)
      await fs.mkdir(`${examplePath}/img`)
      await fs.writeFile(`${examplePath}/chron_${userId}.db`, '')
      
      const imgPath = await adapter.getAdaptiveImgPath(userId)
      expect(imgPath).toBe(`${testWorkspacePath}/example/img`)
    })

    it('应该返回新架构的PDF路径', async () => {
      const userId = '3492ed08-b2dc-45bd-abed-20090a1954e5'
      
      // 创建新架构
      const userPath = `${testWorkspacePath}/users/${userId}`
      await fs.mkdir(userPath)
      await fs.mkdir(`${userPath}/pdf`)
      await fs.writeFile(`${userPath}/chron.db`, '')
      
      const pdfPath = await adapter.getAdaptivePdfPath(userId)
      expect(pdfPath).toBe(`${testWorkspacePath}/users/${userId}/pdf`)
    })

    it('应该返回老架构的共享PDF路径', async () => {
      const userId = '3492ed08-b2dc-45bd-abed-20090a1954e5'
      
      // 创建老架构
      const examplePath = `${testWorkspacePath}/example`
      await fs.mkdir(examplePath)
      await fs.mkdir(`${examplePath}/pdf`)
      await fs.writeFile(`${examplePath}/chron_${userId}.db`, '')
      
      const pdfPath = await adapter.getAdaptivePdfPath(userId)
      expect(pdfPath).toBe(`${testWorkspacePath}/example/pdf`)
    })

    it('应该返回新架构的blocks文件路径', async () => {
      const userId = '3492ed08-b2dc-45bd-abed-20090a1954e5'
      
      // 创建新架构
      const userPath = `${testWorkspacePath}/users/${userId}`
      await fs.mkdir(userPath)
      await fs.writeFile(`${userPath}/chron.db`, '')
      
      const blocksPath = await adapter.getAdaptiveBlocksPath(userId)
      expect(blocksPath).toBe(`${testWorkspacePath}/users/${userId}/blocks.json`)
    })

    it('应该返回老架构的blocks文件路径', async () => {
      const userId = '3492ed08-b2dc-45bd-abed-20090a1954e5'
      
      // 创建老架构
      const examplePath = `${testWorkspacePath}/example`
      await fs.mkdir(examplePath)
      await fs.writeFile(`${examplePath}/chron_${userId}.db`, '')
      
      const blocksPath = await adapter.getAdaptiveBlocksPath(userId)
      expect(blocksPath).toBe(`${testWorkspacePath}/example/blocks_${userId}.json`)
    })
  })

  describe('迁移状态管理', () => {
    it('应该创建用户级迁移状态', async () => {
      const testUserId = 'test-user-123'
      await adapter.saveUserMigrationStatus(testUserId, {
        userId: testUserId,
        version: 'v2.0',
        migrated: true,
        rollbackAvailable: true,
        backupPath: '/some/backup/path'
      })

      const userStatus = await adapter.getUserMigrationStatus(testUserId)
      expect(userStatus?.userId).toBe(testUserId)
      expect(userStatus?.version).toBe('v2.0')
      expect(userStatus?.migrated).toBe(true)
      expect(userStatus?.rollbackAvailable).toBe(true)
    })

    it('应该返回空的状态当文件不存在时', async () => {
      const status = await adapter.getMigrationStatus()
      expect(status.users).toEqual([])
    })

    it('应该返回null当用户不存在时', async () => {
      const userStatus = await adapter.getUserMigrationStatus('non-existent-user')
      expect(userStatus).toBeNull()
    })
  })

  describe('真实backup数据测试', () => {
    it('应该能够识别backup中的老架构数据', async () => {
      // 复制backup数据到测试环境
      const backupPath = path.resolve(process.cwd(), 'backup/2025-07-05T18-37-50')
      const testExamplePath = `${testWorkspacePath}/example`
      
      try {
        await fs.copy(backupPath, testExamplePath)
        
        const userId = '3492ed08-b2dc-45bd-abed-20090a1954e5'
        const mode = await adapter.detectStorageMode(userId)
        expect(mode).toBe('legacy')
        
        const dbPath = await adapter.getAdaptiveDbPath(userId)
        expect(dbPath).toBe(`${testWorkspacePath}/example/chron_${userId}`)
        
        // 验证文件确实存在
        const dbExists = await fs.stat(`${testExamplePath}/chron_${userId}.db`)
        expect(dbExists.isFile()).toBe(true)
      } catch (error) {
        console.warn('无法复制backup数据，跳过真实数据测试:', error)
      }
    })
  })

  describe('性能测试', () => {
    it('存储模式检测应该在合理时间内完成', async () => {
      const userId = '3492ed08-b2dc-45bd-abed-20090a1954e5'
      
      const startTime = Date.now()
      await adapter.detectStorageMode(userId)
      const endTime = Date.now()
      
      expect(endTime - startTime).toBeLessThan(100) // 应该在100ms内完成
    })

    it('应该缓存存储模式检测结果', async () => {
      const userId = '3492ed08-b2dc-45bd-abed-20090a1954e5'
      
      // 第一次检测
      const startTime1 = Date.now()
      const mode1 = await adapter.detectStorageMode(userId)
      const endTime1 = Date.now()
      
      // 第二次检测（应该使用缓存）
      const startTime2 = Date.now()
      const mode2 = await adapter.detectStorageMode(userId)
      const endTime2 = Date.now()
      
      expect(mode1).toBe(mode2)
      expect(endTime2 - startTime2).toBeLessThan(endTime1 - startTime1)
    })
  })
})