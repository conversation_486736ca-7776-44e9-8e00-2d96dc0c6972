import { StorageLike } from '@vueuse/core'
import { useUserStore } from '../stores/user'

/**
 * 用户维度的LocalStorage实现
 * 实现了VueUse的StorageLike接口，为每个用户提供独立的存储空间
 */
export class UserBasedLocalStorage implements StorageLike {
  private static instance: UserBasedLocalStorage | null = null
  private userStore: ReturnType<typeof useUserStore>

  private constructor() {
    this.userStore = useUserStore()
  }

  /**
   * 获取单例实例
   */
  static getInstance(): UserBasedLocalStorage {
    if (!UserBasedLocalStorage.instance) {
      UserBasedLocalStorage.instance = new UserBasedLocalStorage()
    }
    return UserBasedLocalStorage.instance
  }

  /**
   * 获取当前用户的email
   */
  private getUserEmail(): string {
    return this.userStore.userInfo?.email || 'anonymous'
  }

  /**
   * 获取当前用户的存储前缀
   */
  private getUserPrefix(): string {
    const userEmail = this.getUserEmail()
    return `${userEmail}_`
  }

  /**
   * 生成带用户前缀的key
   */
  private getUserKey(key: string): string {
    const prefix = this.getUserPrefix()
    return `${prefix}${key}`
  }

  /**
   * 获取存储项
   */
  getItem(key: string): string | null {
    const userKey = this.getUserKey(key)
    return localStorage.getItem(userKey)
  }

  /**
   * 设置存储项
   */
  setItem(key: string, value: string): void {
    const userKey = this.getUserKey(key)
    localStorage.setItem(userKey, value)
  }

  /**
   * 移除存储项
   */
  removeItem(key: string): void {
    const userKey = this.getUserKey(key)
    localStorage.removeItem(userKey)
  }

  /**
   * 清空当前用户的所有存储项
   */
  clear(): void {
    const prefix = this.getUserPrefix()

    // 获取所有localStorage的key
    const keysToRemove: string[] = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(prefix)) {
        keysToRemove.push(key)
      }
    }

    // 移除所有匹配的key
    keysToRemove.forEach(key => localStorage.removeItem(key))
  }

  /**
   * 获取当前用户存储项的数量
   */
  get length(): number {
    const prefix = this.getUserPrefix()
    let count = 0

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(prefix)) {
        count++
      }
    }

    return count
  }

  /**
   * 获取当前用户的第n个key（不包含用户前缀）
   */
  key(index: number): string | null {
    const prefix = this.getUserPrefix()
    let count = 0

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(prefix)) {
        if (count === index) {
          // 返回去掉用户前缀的key
          return key.substring(prefix.length)
        }
        count++
      }
    }

    return null
  }

  /**
   * 获取当前用户的所有key（不包含用户前缀）
   */
  getAllKeys(): string[] {
    const prefix = this.getUserPrefix()
    const keys: string[] = []

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(prefix)) {
        // 去掉用户前缀
        keys.push(key.substring(prefix.length))
      }
    }

    return keys
  }

  /**
   * 切换用户时清理旧用户数据（可选功能）
   */
  static clearUserData(userEmail: string): void {
    const prefix = `${userEmail}_`
    const keysToRemove: string[] = []

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(prefix)) {
        keysToRemove.push(key)
      }
    }

    keysToRemove.forEach(key => localStorage.removeItem(key))
  }
}
