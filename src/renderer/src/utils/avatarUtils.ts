/**
 * 头像相关工具函数
 */

/**
 * 简单的字符串哈希函数
 * @param str 要哈希的字符串
 * @returns 哈希值
 */
function simpleHash(str: string): number {
  let hash = 0
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }
  return Math.abs(hash)
}

/**
 * 预定义的颜色数组（使用Tailwind CSS颜色）
 */
const AVATAR_COLORS = [
  'bg-red-500',
  'bg-orange-500',
  'bg-amber-500',
  'bg-yellow-500',
  'bg-lime-500',
  'bg-green-500',
  'bg-emerald-500',
  'bg-teal-500',
  'bg-cyan-500',
  'bg-sky-500',
  'bg-blue-500',
  'bg-indigo-500',
  'bg-violet-500',
  'bg-purple-500',
  'bg-fuchsia-500',
  'bg-pink-500',
  'bg-rose-500',
  'bg-slate-500',
  'bg-gray-500',
  'bg-zinc-500'
]

/**
 * 根据邮箱生成头像颜色
 * @param email 用户邮箱
 * @returns Tailwind CSS颜色类名
 */
export function generateAvatarColor(email: string): string {
  if (!email) return 'bg-gray-500'

  const hash = simpleHash(email.toLowerCase())
  const colorIndex = hash % AVATAR_COLORS.length
  return AVATAR_COLORS[colorIndex]
}

/**
 * 从邮箱提取前两个字符（大写）
 * @param email 用户邮箱
 * @returns 前两个字符（大写）
 */
export function getEmailInitial(email: string): string {
  if (!email) return 'US'

  // 提取邮箱用户名部分（@之前的部分）
  const username = email.split('@')[0]
  if (!username) return 'US'

  // 返回前两个字符的大写形式，如果只有一个字符则补充第二个字符
  if (username.length === 1) {
    return username.charAt(0).toUpperCase() + 'S'
  }

  return username.substring(0, 2).toUpperCase()
}

/**
 * 截断邮箱显示（超过8个字符时截断）
 * @param email 用户邮箱
 * @param maxLength 最大长度，默认8
 * @returns 截断后的邮箱
 */
export function truncateEmail(email: string, maxLength: number = 8): string {
  if (!email) return ''

  if (email.length <= maxLength) {
    return email
  }

  return email.substring(0, maxLength) + '...'
}
