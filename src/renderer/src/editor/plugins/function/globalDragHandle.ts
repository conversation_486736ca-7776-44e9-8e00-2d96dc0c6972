import { Extension } from '@tiptap/core';
import {
    Node<PERSON>election,
    Plugin,
    Plugin<PERSON>ey,
    TextSelection,
    Selection,
} from '@tiptap/pm/state';
import { Fragment, Slice, Node } from '@tiptap/pm/model';
import { EditorView } from '@tiptap/pm/view';

export interface GlobalDragHandleOptions {
    /**
     * The width of the drag handle
     */
    dragHandleWidth: number;

    /**
     * The treshold for scrolling
     */
    scrollTreshold: number;

    /*
     * The css selector to query for the drag handle. (eg: '.custom-handle').
     * If handle element is found, that element will be used as drag handle. If not, a default handle will be created
     */
    dragHandleSelector?: string;

    /**
     * Tags to be excluded for drag handle
     */
    excludedTags: string[];

    /**
     * Custom nodes to be included for drag handle
     */
    customNodes: string[];
}

// 在文件顶部添加类型声明
interface CustomDragHandle extends HTMLElement {
    getCurrentNodePos?: () => number | null;
    getCurrentNode?: () => any | null;
    getEditorView?: () => EditorView | null;
    deleteCurrentNode?: () => boolean;
}

// 简化的剪贴板序列化函数
function serializeForClipboard(view: EditorView, slice: Slice) {
    // @ts-ignore
    if (view && typeof view.serializeForClipboard === 'function') {
        // @ts-ignore
        return view.serializeForClipboard(slice);
    }

    // 简单的后备方案
    return {
        dom: document.createElement('div'),
        text: slice.content.textBetween(0, slice.content.size, '\n')
    };
}

function absoluteRect(node: Element) {
    const data = node.getBoundingClientRect();
    const modal = node.closest('[role="dialog"]');

    if (modal && window.getComputedStyle(modal).transform !== 'none') {
        const modalRect = modal.getBoundingClientRect();

        return {
            top: data.top - modalRect.top,
            left: data.left - modalRect.left,
            width: data.width,
            height: data.height,
        };
    }
    return {
        top: data.top,
        left: data.left,
        width: data.width,
        height: data.height,
    };
}

function nodeDOMAtCoords(
    coords: { x: number; y: number },
    options: GlobalDragHandleOptions,
) {
    const selectors = [
        'li',
        'p:not(:first-child)',
        'pre',
        'blockquote',
        'h1',
        'h2',
        'h3',
        'h4',
        'h5',
        'h6',
        'hr',
        ...options.customNodes.map((node) => `[data-type=${node}]`),
    ].join(', ');
    return document
        .elementsFromPoint(coords.x, coords.y)
        .find(
            (elem: Element) =>
                elem.parentElement?.matches?.('.ProseMirror') ||
                elem.matches(selectors),
        );
}

function nodePosAtDOM(
    node: Element,
    view: EditorView,
    options: GlobalDragHandleOptions,
) {
    const boundingRect = node.getBoundingClientRect();

    return view.posAtCoords({
        left: boundingRect.left + 50 + options.dragHandleWidth,
        top: boundingRect.top + 1,
    })?.inside;
}

function calcNodePos(pos: number, view: EditorView) {
    const $pos = view.state.doc.resolve(pos);

    // 检查深度，避免在根级别调用before()
    if ($pos.depth > 1) {
        const beforePos = $pos.before($pos.depth);
        // 验证before位置的有效性
        if (isValidPosition(beforePos, view.state.doc)) {
            return beforePos;
        }
    }

    return pos;
}

// 获取指定位置的块级节点
function getBlockNodeAtPos(pos: number, view: EditorView): any | null {
    try {
        const resolvedPos = view.state.doc.resolve(pos);

        // console.log('getBlockNodeAtPos 调试信息:', {
        //     pos,
        //     depth: resolvedPos.depth,
        //     nodeAfter: resolvedPos.nodeAfter?.type.name,
        //     nodeBefore: resolvedPos.nodeBefore?.type.name,
        //     parent: resolvedPos.parent.type.name
        // });

        // 优先检查 nodeAfter，这通常是我们想要的块级节点
        const nodeAfter = resolvedPos.nodeAfter;
        if (nodeAfter && nodeAfter.type.isBlock &&
            nodeAfter.type.name !== 'doc' &&
            nodeAfter.type.name !== 'page' &&
            nodeAfter.type.name !== 'contents') {
            // console.log('找到 nodeAfter 块级节点:', nodeAfter.type.name);
            return nodeAfter;
        }

        // 检查 nodeBefore，可能光标在节点末尾
        const nodeBefore = resolvedPos.nodeBefore;
        if (nodeBefore && nodeBefore.type.isBlock &&
            nodeBefore.type.name !== 'doc' &&
            nodeBefore.type.name !== 'page' &&
            nodeBefore.type.name !== 'contents') {
            // console.log('找到 nodeBefore 块级节点:', nodeBefore.type.name);
            return nodeBefore;
        }

        // 向上查找最近的块级节点，但跳过文档级别的节点
        for (let depth = resolvedPos.depth; depth > 0; depth--) {
            const nodeAtDepth = resolvedPos.node(depth);
            // console.log(`深度 ${depth} 的节点:`, {
            //     type: nodeAtDepth.type.name,
            //     isBlock: nodeAtDepth.type.isBlock,
            //     textContent: nodeAtDepth.textContent?.substring(0, 50)
            // });

            if (nodeAtDepth.type.isBlock &&
                nodeAtDepth.type.name !== 'doc' &&
                nodeAtDepth.type.name !== 'page' &&
                nodeAtDepth.type.name !== 'contents') {
                // console.log('找到深度块级节点:', nodeAtDepth.type.name);
                
                // 对于表格子节点，优先返回 table 节点
                if (nodeAtDepth.type.name === 'tableRow' || 
                    nodeAtDepth.type.name === 'tableCell' || 
                    nodeAtDepth.type.name === 'tableHeader') {
                    // 继续向上查找 table 节点
                    for (let tableDepth = depth - 1; tableDepth > 0; tableDepth--) {
                        const tableNode = resolvedPos.node(tableDepth);
                        if (tableNode.type.name === 'table') {
                            // console.log('找到 table 节点，优先返回');
                            return tableNode;
                        }
                    }
                }
                
                return nodeAtDepth;
            }
        }

        // 特殊处理：如果深度为 0，说明我们在 page 的直接子级
        if (resolvedPos.depth === 0) {
            // 再次检查 nodeAfter，即使它可能是我们之前跳过的
            if (nodeAfter && nodeAfter.type.isBlock) {
                // console.log('深度0时使用 nodeAfter:', nodeAfter.type.name);
                return nodeAfter;
            }
            // 检查 nodeBefore
            if (nodeBefore && nodeBefore.type.isBlock) {
                // console.log('深度0时使用 nodeBefore:', nodeBefore.type.name);
                return nodeBefore;
            }
        }

        console.warn('未找到合适的块级节点');
        return null;
    } catch (error) {
        console.warn('Failed to get block node at position:', pos, error);
        return null;
    }
}

// 验证位置是否有效
function isValidPosition(pos: number, doc: any): boolean {
    return pos >= 1 && pos <= doc.content.size;
}

// 安全创建NodeSelection
function safeCreateNodeSelection(doc: any, pos: number): NodeSelection | null {
    try {
        if (!isValidPosition(pos, doc)) {
            console.warn('Invalid position for NodeSelection:', pos);
            return null;
        }
        return NodeSelection.create(doc, pos);
    } catch (error) {
        console.warn('Failed to create NodeSelection:', error);
        return null;
    }
}

// 安全创建TextSelection
function safeCreateTextSelection(doc: any, from: number, to?: number): TextSelection | null {
    try {
        if (!isValidPosition(from, doc)) {
            console.warn('Invalid from position for TextSelection:', from);
            return null;
        }
        if (to !== undefined && !isValidPosition(to, doc)) {
            console.warn('Invalid to position for TextSelection:', to);
            return null;
        }
        return TextSelection.create(doc, from, to);
    } catch (error) {
        console.warn('Failed to create TextSelection:', error);
        return null;
    }
}

export function DragHandlePlugin(
    options: GlobalDragHandleOptions & { pluginKey: string },
) {
    let listType = '';
    let currentNodePos: number | null = null;
    let currentNode: any | null = null;
    let editorView: EditorView | null = null;
    let dragHandleElement: CustomDragHandle | null = null;

    function handleDragStart(event: DragEvent, view: EditorView) {
        view.focus();

        if (!event.dataTransfer) return;

        const node = nodeDOMAtCoords(
            {
                x: event.clientX + 50 + options.dragHandleWidth,
                y: event.clientY,
            },
            options,
        );

        if (!(node instanceof Element)) return;

        let draggedNodePos = nodePosAtDOM(node, view, options);

        // 修复：添加更严格的位置验证
        if (draggedNodePos == null || draggedNodePos < 1) {
            console.warn('Invalid dragged node position:', draggedNodePos);
            return;
        }

        // 确保位置不超过文档大小
        if (draggedNodePos > view.state.doc.content.size) {
            console.warn('Dragged node position exceeds document size:', draggedNodePos);
            return;
        }

        draggedNodePos = calcNodePos(draggedNodePos, view);

        // 再次验证计算后的位置
        if (!isValidPosition(draggedNodePos, view.state.doc)) {
            console.warn('Invalid calculated node position:', draggedNodePos);
            return;
        }

        const { from, to } = view.state.selection;
        const diff = from - to;

        const fromSelectionPos = calcNodePos(from, view);
        let differentNodeSelected = false;

        // 修复：添加位置解析的安全检查
        let nodePos: any;
        try {
            nodePos = view.state.doc.resolve(fromSelectionPos);
        } catch (error) {
            console.warn('Failed to resolve from selection position:', error);
            return;
        }

        // Check if nodePos points to the top level node
        if (nodePos.node().type.name === 'doc') {
            differentNodeSelected = true;
        } else {
            // 修复：检查节点深度，避免调用before()时出现无效位置
            if (nodePos.depth === 0) {
                // 如果在根级别，则认为是不同的节点被选中
                differentNodeSelected = true;
            } else {
                const beforePos = nodePos.before();

                // 验证before位置的有效性
                if (!isValidPosition(beforePos, view.state.doc)) {
                    console.warn('Invalid before position from nodePos:', beforePos);
                    differentNodeSelected = true;
                } else {
                    // 修复：安全创建NodeSelection
                    const nodeSelection = safeCreateNodeSelection(view.state.doc, beforePos);

                    if (!nodeSelection) {
                        console.warn('Failed to create node selection for comparison');
                        return;
                    }

                    // Check if the node where the drag event started is part of the current selection
                    differentNodeSelected = !(
                        draggedNodePos + 1 >= nodeSelection.$from.pos &&
                        draggedNodePos <= nodeSelection.$to.pos
                    );
                }
            }
        }

        let selection: Selection = view.state.selection;

        if (
            !differentNodeSelected &&
            diff !== 0 &&
            !(view.state.selection instanceof NodeSelection)
        ) {
            // 修复：验证to位置的有效性
            if (!isValidPosition(to - 1, view.state.doc)) {
                console.warn('Invalid to position for end selection:', to - 1);
                return;
            }

            const endSelection = safeCreateNodeSelection(view.state.doc, to - 1);
            if (!endSelection) {
                console.warn('Failed to create end selection');
                return;
            }

            const textSelection = safeCreateTextSelection(
                view.state.doc,
                draggedNodePos,
                endSelection.$to.pos,
            );

            if (!textSelection) {
                console.warn('Failed to create text selection');
                return;
            }

            selection = textSelection;
        } else {
            const nodeSelection = safeCreateNodeSelection(view.state.doc, draggedNodePos);

            if (!nodeSelection) {
                console.warn('Failed to create node selection');
                return;
            }

            selection = nodeSelection;

            // if inline node is selected, e.g mention -> go to the parent node to select the whole node
            // if table row is selected, go to the parent node to select the whole node
            if (
                (selection as NodeSelection).node.type.isInline ||
                (selection as NodeSelection).node.type.name === 'tableRow'
            ) {
                let $pos: any;
                try {
                    $pos = view.state.doc.resolve(selection.from);
                } catch (error) {
                    console.warn('Failed to resolve selection position:', error);
                    return;
                }

                // 检查节点深度，避免调用before()时出现无效位置
                if ($pos.depth === 0) {
                    console.warn('Cannot get parent of root node');
                    return;
                }

                const beforePos = $pos.before();
                if (!isValidPosition(beforePos, view.state.doc)) {
                    console.warn('Invalid before position:', beforePos);
                    return;
                }

                const newSelection = safeCreateNodeSelection(view.state.doc, beforePos);
                if (!newSelection) {
                    console.warn('Failed to create parent node selection');
                    return;
                }

                selection = newSelection;
            }
        }

        // 最后的安全检查：确保selection是有效的
        if (!selection) {
            console.warn('No valid selection created');
            return;
        }

        view.dispatch(view.state.tr.setSelection(selection));

        // If the selected node is a list item, we need to save the type of the wrapping list e.g. OL or UL
        if (
            view.state.selection instanceof NodeSelection &&
            view.state.selection.node.type.name === 'listItem'
        ) {
            listType = node.parentElement!.tagName;
        }

        const slice = view.state.selection.content();
        const { dom, text } = serializeForClipboard(view, slice);

        event.dataTransfer.clearData();
        event.dataTransfer.setData('text/html', dom.innerHTML);
        event.dataTransfer.setData('text/plain', text);
        event.dataTransfer.effectAllowed = 'copyMove';

        event.dataTransfer.setDragImage(node, 0, 0);

        view.dragging = { slice, move: event.ctrlKey };

        // 更新当前节点位置
        currentNodePos = draggedNodePos;
    }

    function hideDragHandle() {
        if (dragHandleElement) {
            dragHandleElement.classList.add('hide');
        }
    }

    function showDragHandle() {
        if (dragHandleElement) {
            dragHandleElement.classList.remove('hide');
        }
    }

    function hideHandleOnEditorOut(event: MouseEvent) {
        if (event.target instanceof Element) {
            // Check if the relatedTarget class is still inside the editor
            const relatedTarget = event.relatedTarget as HTMLElement;
            const isInsideEditor =
                relatedTarget?.classList.contains('tiptap') ||
                relatedTarget?.classList.contains('drag-handle');

            if (isInsideEditor) return;
        }
        hideDragHandle();
    }

    return new Plugin({
        key: new PluginKey(options.pluginKey),
        view: (view) => {
            // 保存 editor view 引用
            editorView = view;

            const handleBySelector = options.dragHandleSelector
                ? document.querySelector<CustomDragHandle>(options.dragHandleSelector)
                : null;
            dragHandleElement = handleBySelector ?? document.createElement('div') as CustomDragHandle;
            dragHandleElement.draggable = true;
            dragHandleElement.dataset.dragHandle = '';
            dragHandleElement.classList.add('drag-handle');

            // 添加方法来获取当前节点信息
            function getCurrentNodePos() {
                return currentNodePos;
            }

            function getCurrentNode() {
                return currentNode;
            }

            function getEditorView() {
                return editorView;
            }

            // 删除当前节点的方法
            function deleteCurrentNode(): boolean {
                if (!editorView || currentNodePos === null || !currentNode) {
                    console.warn('无法删除节点：缺少必要信息');
                    return false;
                }

                try {
                    console.log('删除节点:', {
                        type: currentNode.type.name,
                        content: currentNode.textContent,
                        position: currentNodePos
                    });

                    // 验证位置是否有效
                    if (currentNodePos < 0 || currentNodePos > editorView.state.doc.content.size) {
                        console.warn('无效的删除位置:', currentNodePos);
                        return false;
                    }

                    // 解析位置获取节点信息
                    const resolvedPos = editorView.state.doc.resolve(currentNodePos);

                    console.log('节点深度信息:', {
                        depth: resolvedPos.depth,
                        parentType: resolvedPos.parent.type.name,
                        nodeAfter: resolvedPos.nodeAfter?.type.name,
                        nodeBefore: resolvedPos.nodeBefore?.type.name
                    });

                    // 创建删除事务
                    const tr = editorView.state.tr;

                    // 根据节点类型和深度执行不同的删除策略
                    const nodeType = currentNode.type.name;

                    // 防止删除顶级文档节点，但允许删除 page 下的直接子节点
                    if (nodeType === 'doc') {
                        console.warn('不能删除文档节点');
                        return false;
                    }

                    // 特殊处理表格相关节点：确保删除整个 table
                    if (nodeType === 'tableRow' || nodeType === 'tableCell' || nodeType === 'tableHeader') {
                        console.log('检测到表格子节点，查找完整的 table 节点');
                        
                        // 向上查找完整的表格节点
                        let tableNode = null;
                        let tablePos = null;
                        
                        for (let depth = resolvedPos.depth; depth >= 0; depth--) {
                            const nodeAtDepth = resolvedPos.node(depth);
                            console.log(`深度 ${depth} 的节点类型:`, nodeAtDepth.type.name);
                            
                            if (nodeAtDepth.type.name === 'table') {
                                tableNode = nodeAtDepth;
                                tablePos = resolvedPos.before(depth);
                                console.log('找到 table 节点，位置:', tablePos);
                                break;
                            }
                        }
                        
                        if (tableNode && tablePos !== null && tablePos >= 0) {
                            const deleteStart = tablePos;
                            const deleteEnd = tablePos + tableNode.nodeSize;
                            
                            console.log('table 删除范围:', { deleteStart, deleteEnd, nodeSize: tableNode.nodeSize });
                            
                            if (deleteStart >= 0 && deleteEnd <= editorView.state.doc.content.size && deleteStart < deleteEnd) {
                                tr.delete(deleteStart, deleteEnd);
                                
                                if (tr.docChanged) {
                                    editorView.dispatch(tr);
                                    console.log('成功删除整个表格');
                                    
                                    currentNodePos = null;
                                    currentNode = null;
                                    hideDragHandle();
                                    
                                    return true;
                                } else {
                                    console.warn('表格删除事务未生效');
                                    return false;
                                }
                            } else {
                                console.warn('表格删除范围无效:', { deleteStart, deleteEnd, docSize: editorView.state.doc.content.size });
                                return false;
                            }
                        } else {
                            console.warn('未找到 table 节点或位置无效');
                            return false;
                        }
                    }

                    // 如果是 page 节点下的直接子节点（depth=0），需要特殊处理
                    if (resolvedPos.depth === 0 && resolvedPos.nodeAfter) {
                        console.log('处理 page 下的直接子节点');
                        // 对于 page 下的直接子节点，使用 nodeAfter 来确定删除范围
                        const targetNode = resolvedPos.nodeAfter;
                        if (targetNode && targetNode.type.isBlock && targetNode.type.name !== 'doc' && targetNode.type.name !== 'page') {
                            const deleteStart = currentNodePos;
                            const deleteEnd = currentNodePos + targetNode.nodeSize;

                            console.log('page 子节点删除范围:', { deleteStart, deleteEnd, nodeSize: targetNode.nodeSize });

                            // 验证删除范围
                            if (deleteStart >= 0 && deleteEnd <= editorView.state.doc.content.size && deleteStart < deleteEnd) {
                                tr.delete(deleteStart, deleteEnd);

                                // 应用删除事务
                                if (tr.docChanged) {
                                    editorView.dispatch(tr);
                                    console.log('成功删除 page 子节点:', targetNode.type.name);

                                    // 清除当前节点信息
                                    currentNodePos = null;
                                    currentNode = null;

                                    // 立即隐藏拖拽手柄
                                    hideDragHandle();

                                    return true;
                                }
                            } else {
                                console.warn('page 子节点删除范围无效:', { deleteStart, deleteEnd, docSize: editorView.state.doc.content.size });
                                return false;
                            }
                        }
                    }

                    // 查找要删除的节点范围
                    let deleteStart: number;
                    let deleteEnd: number;

                    // 尝试找到当前节点的确切范围
                    if (resolvedPos.nodeAfter && resolvedPos.nodeAfter === currentNode) {
                        // 如果当前位置的下一个节点就是我们要删除的节点
                        deleteStart = currentNodePos;
                        deleteEnd = currentNodePos + currentNode.nodeSize;
                    } else {
                        // 否则，尝试通过深度来计算范围
                        try {
                            if (resolvedPos.depth > 0) {
                                deleteStart = resolvedPos.before(resolvedPos.depth);
                                deleteEnd = resolvedPos.after(resolvedPos.depth);
                            } else {
                                console.warn('无法确定删除范围：深度为0');
                                return false;
                            }
                        } catch (depthError) {
                            console.warn('通过深度计算范围失败，尝试其他方法:', depthError);

                            // 备用方案：查找包含当前位置的块级节点
                            let foundStart = -1;
                            let foundEnd = -1;

                            editorView.state.doc.descendants((node, pos) => {
                                if (node === currentNode || (node.type === currentNode.type && node.textContent === currentNode.textContent)) {
                                    foundStart = pos;
                                    foundEnd = pos + node.nodeSize;
                                    return false; // 停止遍历
                                }
                                return true;
                            });

                            if (foundStart >= 0 && foundEnd > foundStart) {
                                deleteStart = foundStart;
                                deleteEnd = foundEnd;
                            } else {
                                console.warn('无法找到节点的确切位置');
                                return false;
                            }
                        }
                    }

                    // 验证删除范围
                    if (deleteStart < 0 || deleteEnd > editorView.state.doc.content.size || deleteStart >= deleteEnd) {
                        console.warn('无效的删除范围:', { deleteStart, deleteEnd, docSize: editorView.state.doc.content.size });
                        return false;
                    }

                    console.log('删除范围:', { deleteStart, deleteEnd });

                    // 执行删除
                    tr.delete(deleteStart, deleteEnd);

                    // 应用删除事务
                    if (tr.docChanged) {
                        editorView.dispatch(tr);
                        console.log('成功删除节点:', nodeType);

                        // 清除当前节点信息
                        currentNodePos = null;
                        currentNode = null;

                        // 立即隐藏拖拽手柄
                        hideDragHandle();

                        return true;
                    } else {
                        console.warn('没有执行删除操作');
                        return false;
                    }

                } catch (error) {
                    console.error('删除节点时发生错误:', error);
                    return false;
                }
            }

            // 将方法暴露到 dragHandleElement 上
            dragHandleElement.getCurrentNodePos = getCurrentNodePos;
            dragHandleElement.getCurrentNode = getCurrentNode;
            dragHandleElement.getEditorView = getEditorView;
            dragHandleElement.deleteCurrentNode = deleteCurrentNode;

            function onDragHandleDragStart(e: DragEvent) {
                try {
                    handleDragStart(e, view);
                } catch (error) {
                    console.error('Error in drag start:', error);
                }
            }

            dragHandleElement.addEventListener('dragstart', onDragHandleDragStart);

            function onDragHandleDrag(e: DragEvent) {
                hideDragHandle();
                let scrollY = window.scrollY;
                if (e.clientY < options.scrollTreshold) {
                    window.scrollTo({ top: scrollY - 30, behavior: 'smooth' });
                } else if (window.innerHeight - e.clientY < options.scrollTreshold) {
                    window.scrollTo({ top: scrollY + 30, behavior: 'smooth' });
                }
            }

            dragHandleElement.addEventListener('drag', onDragHandleDrag);

            hideDragHandle();

            if (!handleBySelector) {
                view?.dom?.parentElement?.appendChild(dragHandleElement);
            }
            view?.dom?.parentElement?.addEventListener(
                'mouseout',
                hideHandleOnEditorOut,
            );

            return {
                destroy: () => {
                    if (!handleBySelector) {
                        dragHandleElement?.remove?.();
                    }
                    dragHandleElement?.removeEventListener('drag', onDragHandleDrag);
                    dragHandleElement?.removeEventListener(
                        'dragstart',
                        onDragHandleDragStart,
                    );
                    dragHandleElement = null;
                    view?.dom?.parentElement?.removeEventListener(
                        'mouseout',
                        hideHandleOnEditorOut,
                    );
                },
            };
        },
        props: {
            handleDOMEvents: {
                mousemove: (view, event) => {
                    if (!view.editable) {
                        return;
                    }

                    const node = nodeDOMAtCoords(
                        {
                            x: event.clientX + 50 + options.dragHandleWidth,
                            y: event.clientY,
                        },
                        options,
                    );

                    // 更新当前节点位置和节点对象
                    if (node instanceof Element) {
                        const pos = nodePosAtDOM(node, view, options);
                        if (pos !== undefined && pos !== null) {
                            // 使用 calcNodePos 来获取正确的块级节点位置
                            const blockPos = calcNodePos(pos, view);
                            currentNodePos = blockPos;

                            // 获取当前块级节点对象
                            currentNode = getBlockNodeAtPos(blockPos, view);
                        } else {
                            currentNodePos = null;
                            currentNode = null;
                        }
                    } else {
                        currentNodePos = null;
                        currentNode = null;
                    }

                    const notDragging = node?.closest('.not-draggable');
                    const excludedTagList = options.excludedTags
                        .concat(['ol', 'ul'])
                        .join(', ');

                    if (
                        !(node instanceof Element) ||
                        node.matches(excludedTagList) ||
                        notDragging
                    ) {
                        hideDragHandle();
                        return;
                    }

                    const compStyle = window.getComputedStyle(node);
                    const parsedLineHeight = parseInt(compStyle.lineHeight, 10);
                    const lineHeight = isNaN(parsedLineHeight)
                        ? parseInt(compStyle.fontSize) * 1.2
                        : parsedLineHeight;
                    const paddingTop = parseInt(compStyle.paddingTop, 10);

                    const rect = absoluteRect(node);

                    // 特殊处理 HR 元素的定位
                    if (node.matches('hr')) {
                        // HR 元素通常很薄，将手柄定位在 HR 的垂直中心
                        rect.top += (rect.height - 24) / 2;
                    } else if (node.matches('pre')) {
                        // 代码块特殊处理：对齐到代码块的真正顶部
                        // 不添加任何偏移，让 drag handle 直接对齐到代码块边框的顶部
                    } else {
                        rect.top += (lineHeight - 24) / 2;
                        rect.top += paddingTop;
                    }

                    // Li markers
                    if (node.matches('ul:not([data-type=taskList]) li, ol li')) {
                        rect.left -= options.dragHandleWidth;
                    }
                    rect.width = options.dragHandleWidth;

                    if (!dragHandleElement) return;

                    dragHandleElement.style.left = `${rect.left - rect.width}px`;
                    dragHandleElement.style.top = `${rect.top}px`;
                    showDragHandle();
                },
                keydown: () => {
                    hideDragHandle();
                },
                mousewheel: () => {
                    hideDragHandle();
                },
                // dragging class is used for CSS
                dragstart: (view) => {
                    view.dom.classList.add('dragging');
                },
                drop: (view, event) => {
                    view.dom.classList.remove('dragging');
                    hideDragHandle();
                    let droppedNode: Node | null = null;
                    const dropPos = view.posAtCoords({
                        left: event.clientX,
                        top: event.clientY,
                    });

                    if (!dropPos) return;

                    if (view.state.selection instanceof NodeSelection) {
                        droppedNode = view.state.selection.node;
                    }
                    if (!droppedNode) return;

                    const resolvedPos = view.state.doc.resolve(dropPos.pos);

                    const isDroppedInsideList =
                        resolvedPos.parent.type.name === 'listItem';

                    // If the selected node is a list item and is not dropped inside a list, we need to wrap it inside <ol> tag otherwise ol list items will be transformed into ul list item when dropped
                    if (
                        view.state.selection instanceof NodeSelection &&
                        view.state.selection.node.type.name === 'listItem' &&
                        !isDroppedInsideList &&
                        listType == 'OL'
                    ) {
                        const newList = view.state.schema.nodes.orderedList?.createAndFill(
                            null,
                            droppedNode,
                        );
                        const slice = new Slice(Fragment.from(newList), 0, 0);
                        view.dragging = { slice, move: event.ctrlKey };
                    }
                },
                dragend: (view) => {
                    view.dom.classList.remove('dragging');
                },
            },
        },
    });
}

const GlobalDragHandle = Extension.create({
    name: 'globalDragHandle',

    addOptions() {
        return {
            dragHandleWidth: 20,
            scrollTreshold: 100,
            excludedTags: [],
            customNodes: [],
        };
    },

    addProseMirrorPlugins() {
        return [
            DragHandlePlugin({
                pluginKey: 'globalDragHandle',
                dragHandleWidth: this.options.dragHandleWidth,
                scrollTreshold: this.options.scrollTreshold,
                dragHandleSelector: this.options.dragHandleSelector,
                excludedTags: this.options.excludedTags,
                customNodes: this.options.customNodes,
            }),
        ];
    },
});

export default GlobalDragHandle;