import { Extension } from '@tiptap/core'
import { Plugin, Plugin<PERSON>ey } from 'prosemirror-state'

interface AutoScrollOptions {
    scrollZone: number
    scrollSpeed: number
    scrollContainer: string | HTMLElement
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        dragAutoScroll: {
            setAutoScrollContainer: (container: HTMLElement) => ReturnType
        }
    }
}

// 全局状态管理滚动
class AutoScrollManager {
    private scrollInterval: NodeJS.Timeout | null = null
    private currentDistance = 0
    private currentContainer: HTMLElement | null = null

    autoScroll(container: HTMLElement, distance: number) {
        // 如果容器或距离变化，需要重新设置
        if (this.currentContainer !== container || Math.abs(this.currentDistance - distance) > 2) {
            this.stopAutoScroll()
            this.currentContainer = container
            this.currentDistance = distance

            // 创建新的滚动定时器
            this.scrollInterval = setInterval(() => {
                if (this.currentContainer) {
                    this.currentContainer.scrollBy({
                        top: this.currentDistance,
                        behavior: 'auto'
                    })
                }
            }, 16) // 约60fps
        }
    }

    stopAutoScroll() {
        if (this.scrollInterval) {
            clearInterval(this.scrollInterval)
            this.scrollInterval = null
        }
        this.currentDistance = 0
        this.currentContainer = null
    }
}

/**
 * 拖拽自动滚动扩展
 * 当拖拽元素靠近容器边缘时自动滚动
 */
export const DragAutoScroll = Extension.create<AutoScrollOptions>({
    name: 'dragAutoScroll',

    addOptions() {
        return {
            scrollZone: 150, // 触发滚动的边缘区域大小（像素）
            scrollSpeed: 20, // 每次滚动的像素数
            scrollContainer: '.flex-1.relative.scrollbar-gutter', // 滚动容器选择器或元素
        }
    },

    addCommands() {
        return {
            setAutoScrollContainer:
                (container: HTMLElement) =>
                    ({ tr }) => {
                        tr.setMeta('autoScrollContainer', container)
                        return true
                    },
        }
    },

    addProseMirrorPlugins() {
        const { scrollZone, scrollSpeed, scrollContainer } = this.options
        const scrollManager = new AutoScrollManager()

        return [
            new Plugin({
                key: new PluginKey('dragAutoScroll'),

                props: {
                    handleDOMEvents: {
                        dragover: (view, event) => {
                            // 获取滚动容器
                            let container: HTMLElement | null = null

                            if (typeof scrollContainer === 'string') {
                                // 先尝试从编辑器DOM开始查找
                                container = view.dom.closest(scrollContainer) as HTMLElement

                                // 如果没找到，从document查找
                                if (!container) {
                                    container = document.querySelector(scrollContainer)
                                }
                            } else if (scrollContainer instanceof HTMLElement) {
                                container = scrollContainer
                            }

                            // 如果没有找到滚动容器，自动查找最近的可滚动父元素
                            if (!container) {
                                container = view.dom.parentElement
                                while (container && container !== document.body) {
                                    const styles = window.getComputedStyle(container)
                                    if (
                                        styles.overflowY === 'auto' ||
                                        styles.overflowY === 'scroll' ||
                                        container.classList.contains('overflow-y-auto') ||
                                        container.classList.contains('overflow-auto')
                                    ) {
                                        break
                                    }
                                    container = container.parentElement
                                }
                            }

                            if (!container) {
                                console.warn('DragAutoScroll: No scrollable container found')
                                return false
                            }

                            const containerRect = container.getBoundingClientRect()
                            const mouseY = event.clientY

                            // 检查是否在上方滚动区域
                            if (mouseY < containerRect.top + scrollZone && container.scrollTop > 0) {
                                const intensity = Math.max(0.3, 1 - (mouseY - containerRect.top) / scrollZone)
                                const scrollDistance = Math.max(3, scrollSpeed * intensity)
                                scrollManager.autoScroll(container, -scrollDistance)
                                return false
                            }

                            // 检查是否在下方滚动区域
                            if (mouseY > containerRect.bottom - scrollZone &&
                                container.scrollTop < container.scrollHeight - container.clientHeight) {
                                const intensity = Math.max(0.3, 1 - (containerRect.bottom - mouseY) / scrollZone)
                                const scrollDistance = Math.max(3, scrollSpeed * intensity)
                                scrollManager.autoScroll(container, scrollDistance)
                                return false
                            }

                            // 停止滚动
                            scrollManager.stopAutoScroll()
                            return false
                        },

                        dragstart: () => {
                            // 拖拽开始时初始化
                            return false
                        },

                        dragend: () => {
                            scrollManager.stopAutoScroll()
                            return false
                        },

                        dragleave: () => {
                            // 只有当真正离开编辑器区域时才停止滚动
                            return false
                        },

                        drop: () => {
                            scrollManager.stopAutoScroll()
                            return false
                        }
                    }
                },

                destroy() {
                    scrollManager.stopAutoScroll()
                }
            })
        ]
    },

    onDestroy() {
        // 清理资源在插件的 destroy 方法中处理
    }
}) 