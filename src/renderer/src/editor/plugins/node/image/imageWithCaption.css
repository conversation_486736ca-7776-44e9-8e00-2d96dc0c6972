/* Image with Caption Node Styles */
.image-with-caption {
  display: block;
  margin: 1rem 0;
  padding: 0;
  width: 100%;
}

.image-container {
  position: relative;
  display: block;
  max-width: 100%;
  margin: 0 auto; /* 居中对齐 */
  user-select: none; /* 防止选择 */
  -webkit-user-select: none;
}

.main-image {
  display: block;
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  user-select: none; /* 防止选择 */
  -webkit-user-select: none;
  pointer-events: none; /* 防止鼠标事件，避免光标闪烁 */
}

.caption-content {
  position: relative;
  margin-top: 0.5rem;
  padding: 0.5rem;
  text-align: center;
  font-size: 0.875rem;
  line-height: 1.4;
  color: #6b7280;
  border: 1px solid transparent;
  border-radius: 0.25rem;
  min-height: 1.5rem;
  transition: all 0.2s ease;
  outline: none;
}

.caption-content:hover {
  border-color: rgba(0, 0, 0, 0.1);
  background-color: rgba(0, 0, 0, 0.02);
}

.caption-content:focus {
  border-color: #3b82f6;
  background-color: white;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* 占位符样式 */
.caption-content.is-empty::before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
  pointer-events: none;
  text-align: center;
  display: block;
  width: 100%;
}

.caption-content.is-empty:focus::before {
  display: none;
}

/* 当用户聚焦时隐藏占位符 */
.caption-content:focus.is-empty::before {
  opacity: 0.5;
}

/* 确保ProseMirror的样式不会干扰 */
.caption-content .ProseMirror {
  outline: none;
  border: none;
  padding: 0;
  margin: 0;
}

/* 选中状态 */
.image-with-caption.ProseMirror-selectednode {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  border-radius: 0.5rem;
}

/* 只在选中状态下显示caption */
.image-with-caption.ProseMirror-selectednode .caption-content {
  display: block;
}

/* 当caption有内容时，即使未选中也显示 */
.image-with-caption .caption-content:not(.is-empty) {
  display: block;
}

/* 防止gapcursor出现在图片和caption之间 */
.image-with-caption .ProseMirror-gapcursor {
  display: none !important;
}

/* 防止在整个图片节点内部出现gapcursor */
.image-with-caption .ProseMirror-gapcursor-drop {
  display: none !important;
}

/* 防止图片区域的光标闪烁 */
.image-with-caption .image-container * {
  caret-color: transparent !important;
}

/* 确保只有caption区域可以获得光标 */
.image-with-caption .caption-content {
  caret-color: auto;
  pointer-events: auto;
  user-select: text;
  -webkit-user-select: text;
}