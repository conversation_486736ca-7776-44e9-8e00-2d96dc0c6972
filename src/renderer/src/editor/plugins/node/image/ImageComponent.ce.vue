<template>
  <div :class="align" class="vue-component" :data-id="nodeId">
    <div
      ref="resizableElement"
      :class="{ 'auto-height': !imageSrc || hasError }"
      class="resizable"
      :style="{ width: nodeWidth }"
    >
      <!-- 加载中状态 -->
      <div v-if="isLoading" class="loading-container">
        <div class="loading-content">
          <LoaderIcon class="loading-icon" />
          <span class="loading-text">加载图片中...</span>
        </div>
      </div>

      <!-- AI图片生成状态 -->
      <div v-else-if="!imageSrc && byAI" class="ai-container">
        <div class="ai-prompt-section">
          <div class="ai-prompt-label">提示词</div>
          <textarea
            v-model="aiPrompt"
            class="ai-textarea"
            placeholder="描述你想要生成的图片"
          ></textarea>
        </div>

        <div class="ai-button-container">
          <button
            class="ai-button"
            @click="generateAIImage"
            :disabled="!aiPrompt.trim() || aiGenerating"
          >
            <svg
              v-if="!aiGenerating"
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="button-icon"
            >
              <path
                d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"
              />
              <path d="M5 3v4" />
              <path d="M3 5h4" />
              <path d="M19 17v4" />
              <path d="M17 19h4" />
            </svg>
            <LoaderIcon v-if="aiGenerating" class="loading-icon-small" />
            <span>{{ aiGenerating ? '生成中...' : '生成图片' }}</span>
          </button>
        </div>
      </div>

      <!-- 初始状态：添加图片按钮 -->
      <div v-else-if="!imageSrc" class="empty-container" @click="addImage">
        <div class="empty-icon-wrapper">
          <ImagePlusIcon class="empty-icon" />
        </div>
        <div class="empty-content">
          <h3 class="empty-title">添加图片</h3>
          <p class="empty-subtitle">点击从设备上传</p>
        </div>
      </div>

      <!-- 加载完成状态 -->
      <div v-else class="image-wrapper">
        <img
          v-if="!hasError"
          class="main-image"
          :src="imageSrc"
          draggable="false"
          alt="图片"
          @error="onImageError"
          @load="onImageLoad"
          @click="openPreview"
        />
        <!-- 错误状态 -->
        <div v-else class="error-container">
          <TriangleAlertIcon class="error-icon" />
          <span class="error-title">图片加载失败</span>
          <div class="error-message">无法加载图片。请重试或选择其他图片。</div>
          <button class="error-button" @click="addImage">重试</button>
        </div>

        <!-- Hover Controls -->
        <div class="hover-controls">
          <div class="controls-wrapper">
            <button class="control-button" @click.stop="toggleCaption" title="切换图片说明">
              <CaptionsIcon class="control-icon" />
            </button>
            <button class="control-button" @click.stop="copyImage" title="复制图片">
              <CopyIcon class="control-icon" />
            </button>
            <button class="control-button" @click.stop="deleteImage" title="删除图片">
              <TrashIcon class="control-icon" />
            </button>
          </div>
        </div>

        <!-- Resize Bars -->
        <div
          class="resize-handle resize-handle-left"
          draggable="false"
          @dragstart.prevent
          @selectstart.prevent
        >
          <div class="resize-bar" draggable="false" @dragstart.prevent @selectstart.prevent></div>
        </div>
        <div
          class="resize-handle resize-handle-right"
          draggable="false"
          @dragstart.prevent
          @selectstart.prevent
        >
          <div class="resize-bar" draggable="false" @dragstart.prevent @selectstart.prevent></div>
        </div>
      </div>

      <!-- Caption -->
      <div v-if="imageSrc && !hasError && showCaption" class="caption-container">
        <div
          v-if="!isEditingCaption"
          class="caption-display"
          @dblclick.stop="startEditingCaption"
          @mousedown.stop
          :class="{ 'caption-placeholder': !captionText }"
        >
          {{ captionText || '双击添加图片说明...' }}
        </div>
        <input
          v-else
          ref="captionInput"
          v-model="captionText"
          class="caption-input"
          type="text"
          placeholder="输入图片说明..."
          @blur="finishEditingCaption"
          @keydown.enter.exact="finishEditingCaption"
          @keydown.esc="cancelEditingCaption"
          @compositionstart="isComposing = true"
          @compositionend="isComposing = false"
          @click.stop
          @mousedown.stop
          @keydown.stop
          @keypress.stop
          @keyup.stop
        />
      </div>
    </div>

    <!-- Full Screen Preview -->
    <ImagePreviewModal
      :is-open="isPreviewOpen"
      :image-src="imageSrc"
      alt="图片预览"
      @close="isPreviewOpen = false"
    />
  </div>
</template>

<style scoped>
/* 主容器 */
.vue-component {
  display: flex;
  transition: all 0.3s ease;
  padding: 1rem 0;
}

.vue-component.justify-center {
  justify-content: center;
}
.vue-component.justify-start {
  justify-content: flex-start;
}
.vue-component.justify-end {
  justify-content: flex-end;
}

/* 可调整大小的容器 */
.resizable {
  position: relative;
  height: 100%;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.resizable.resizing {
  transition: none !important;
}

.resizable.auto-height {
  height: auto;
}

/* 加载状态 */
.loading-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 2rem;
}

.loading-icon {
  width: 1.5rem;
  height: 1.5rem;
  color: #3b82f6;
  animation: spin 1s linear infinite;
}

.loading-icon-small {
  width: 1rem;
  height: 1rem;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: rgba(107, 114, 128, 0.7);
  font-size: 0.875rem;
}

/* AI 生成状态 */
.ai-container {
  display: flex;
  flex-direction: column;
  padding: 1.5rem;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  border: 2px dashed rgba(107, 114, 128, 0.3);
  transition: border-color 0.2s ease;
}

.ai-container:hover {
  border-color: rgba(59, 130, 246, 0.5);
}

.ai-prompt-section {
  width: 100%;
  margin-bottom: 1rem;
}

.ai-prompt-label {
  color: #1f2937;
  font-size: 1.125rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.ai-textarea {
  width: 100%;
  padding: 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid rgba(107, 114, 128, 0.2);
  font-size: 0.875rem;
  min-height: 100px;
  resize: vertical;
  font-family: inherit;
}

.ai-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.ai-button-container {
  width: 100%;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 0.5rem;
}

.ai-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: none;
  background: #3b82f6;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.ai-button:hover:not(:disabled) {
  background: #2563eb;
}

.ai-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 1.5rem;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: #ffffff;
  border-radius: 0.5rem;
  border: 2px dashed rgba(107, 114, 128, 0.3);
  transition: border-color 0.2s ease;
}

.empty-container:hover {
  border-color: rgba(59, 130, 246, 0.5);
}

.empty-icon-wrapper {
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  width: 1.75rem;
  height: 1.75rem;
  stroke: #3b82f6;
}

.empty-content {
  text-align: center;
}

.empty-title {
  font-weight: 500;
  color: #1f2937;
  margin: 0;
}

.empty-subtitle {
  color: rgba(107, 114, 128, 0.7);
  font-size: 0.875rem;
  margin: 0.25rem 0 0 0;
}

/* 图片容器 */
.image-wrapper {
  width: 100%;
  height: auto;
  border-radius: 0.5rem;
  overflow: visible;
  position: relative;
}

.main-image {
  width: 100%;
  height: auto;
  object-fit: contain;
  min-width: 200px;
  cursor: zoom-in;
  user-select: none;
  -webkit-user-drag: none;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1.5rem;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.error-icon {
  width: 1.5rem;
  height: 1.5rem;
  stroke: #ef4444;
}

.error-title {
  color: rgba(220, 38, 38, 0.9);
  font-weight: 500;
}

.error-message {
  color: rgba(107, 114, 128, 0.7);
  font-size: 0.875rem;
  text-align: center;
  max-width: 20rem;
}

.error-button {
  margin-top: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 0.875rem;
}

.error-button:hover {
  background: #f3f4f6;
}

/* 悬停控制 */
.hover-controls {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: fit-content;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.resizable:hover .hover-controls {
  opacity: 1;
}

.controls-wrapper {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  border-radius: 0.375rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.control-button {
  padding: 0.25rem;
  border-radius: 0.375rem;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.control-button:hover {
  background: rgba(255, 255, 255, 0.15);
}

.control-icon {
  width: 0.875rem;
  height: 0.875rem;
  color: rgba(255, 255, 255, 0.9);
}

/* 调整大小手柄 */
.resize-handle {
  position: absolute;
  width: 20px; /* 扩大手柄区域 */
  cursor: ew-resize;
  opacity: 0;
  transition: all 0.3s ease;
  top: 50%;
  transform: translateY(-50%);
  height: 20%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  user-select: none;
  -webkit-user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  pointer-events: auto;
}

.resize-handle-left {
  left: -10px; /* 调整位置 */
}

.resize-handle-right {
  right: -10px; /* 调整位置 */
}

.resizable:hover .resize-handle {
  opacity: 1;
}

.resize-handle:hover {
  opacity: 1 !important;
  transform: translateY(-50%) scale(1.1);
}

.resize-bar {
  position: absolute;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
  width: 8px;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 3px;
  transition: background-color 0.2s ease;
  user-select: none;
  -webkit-user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  pointer-events: none;
}

.resize-handle:hover .resize-bar {
  background: rgba(0, 0, 0, 0.6);
}

/* 图标通用样式 */
.button-icon,
svg {
  flex-shrink: 0;
}

/* Caption styles */
.caption-container {
  margin-top: 0.25rem;
  width: 100%;
  position: relative;
  z-index: 10;
}

.caption-display {
  padding: 0.25rem 0.5rem;
  color: #374151;
  font-size: 0.875rem;
  line-height: 1.4;
  text-align: center;
  cursor: pointer;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
  min-height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid transparent;
}

.caption-display:hover {
  background: rgba(0, 0, 0, 0.03);
  border-color: rgba(0, 0, 0, 0.1);
}

.caption-placeholder {
  color: #9ca3af;
  font-style: italic;
}

.caption-input {
  width: 100%;
  padding: 0.25rem 0.5rem;
  border: 1px solid #3b82f6;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  line-height: 1.4;
  text-align: center;
  background: white;
  resize: none;
  overflow: hidden;
  font-family: inherit;
  position: relative;
  z-index: 20;
  pointer-events: auto;
  user-select: text;
  -webkit-user-select: text;
}

.caption-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  z-index: 30;
}

/* 动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>

<script setup lang="ts">
import { FileSystemManager } from '@renderer/api/uniform/fs'
import { uploadImage } from '@renderer/api/uniform/note'
import { uploadToQiniu } from '@renderer/api/uniform/qiniuUpload'
import ImagePreviewModal from '@renderer/components/ui/ImagePreviewModal.vue'
import axiosInstance from '@renderer/utils/axios'
import { useConfig } from '@renderer/utils/configHelper'
import { useUserStore } from '@renderer/stores/user'
import { useStorageCompatibilityAdapter } from '@renderer/utils/storageCompatibilityAdapter'
import { showToast } from '@renderer/utils/toast'
import interact from 'interactjs'
import {
  CaptionsIcon,
  CopyIcon,
  ImagePlusIcon,
  LoaderIcon,
  TrashIcon,
  TriangleAlertIcon
} from 'lucide-vue-next'
import { computed, onMounted, ref, watch, watchEffect } from 'vue'

// Props for Web Component - 注意：Web Component的属性都是字符串
const props = defineProps<{
  nodeId?: string
  src?: string
  width?: string
  height?: string
  align?: 'left' | 'center' | 'right'
  useOnline?: string | boolean
  byAI?: string | boolean
  caption?: string
  showCaption?: string | boolean
}>()

// Emits for communication with TipTap
const emit = defineEmits<{
  'update-attributes': [attrs: Record<string, any>]
  'delete-node': []
  'open-preview': [src: string]
}>()

const userStore = useUserStore()

// Get user-specific img path - 使用ref和async函数，类似PDFViewer
const imgPath = ref('')

// 异步获取图片路径
const updateImgPath = async () => {
  if (!userStore.userInfo?.uuid) {
    console.error('用户未登录，无法获取图片路径')
    imgPath.value = ''
    return
  }

  const adapter = useStorageCompatibilityAdapter()
  try {
    imgPath.value = await adapter.getAdaptiveImgPath(userStore.userInfo.uuid)
    console.log(imgPath.value, 'IMGPATH')
  } catch (error) {
    console.error('获取图片路径失败:', error)
    imgPath.value = ''
  }
}

// State
const isLoading = ref(true)
const hasError = ref(false)
const storedImagePath = ref('')
const useLocalFallback = ref(false)
const resizableElement = ref<HTMLDivElement>()
const aiPrompt = ref('')
const aiGenerating = ref(false)
const isPreviewOpen = ref(false)
const isDragging = ref(false)
const rafId = ref<number | null>(null)
const cachedParentWidth = ref<number | null>(null)
const captionText = ref('')
const isEditingCaption = ref(false)
const showCaption = ref(true)
const isComposing = ref(false)

// Computed
const nodeId = computed(() => props.nodeId || '')
const nodeWidth = computed(() => props.width || '100%')

// 布尔值转换函数 - Web Component属性都是字符串
const parseBooleanProp = (
  value: string | boolean | undefined,
  defaultValue: boolean = false
): boolean => {
  if (typeof value === 'boolean') return value
  if (typeof value === 'string') return value === 'true'
  return defaultValue
}

const useOnline = computed(() => parseBooleanProp(props.useOnline, false))
const byAI = computed(() => parseBooleanProp(props.byAI, false))
const showCaptionComputed = computed(() => parseBooleanProp(props.showCaption, true))

const imageSrc = computed(() => {
  if (!storedImagePath.value) return ''

  // 如果是完整的URL（七牛云或其他在线URL），直接返回
  if (storedImagePath.value.startsWith('http://') || storedImagePath.value.startsWith('https://')) {
    return storedImagePath.value
  }

  // 如果imgPath还没有初始化，返回空字符串等待
  if (!imgPath.value) {
    return ''
  }

  // 如果需要使用本地回退或者不使用在线模式
  if ((useOnline.value && useLocalFallback.value) || !useOnline.value) {
    return `chron://${imgPath.value}/${storedImagePath.value}`
  }

  // 使用在线模式
  return `http://localhost:4000/example/img/${storedImagePath.value}`
})

const align = computed(() => {
  switch (props.align) {
    case 'center':
      return 'justify-center'
    case 'left':
      return 'justify-start'
    default:
      return 'justify-end'
  }
})

// Methods
const openPreview = () => {
  isPreviewOpen.value = true
  emit('open-preview', imageSrc.value)
}

const copyImage = async () => {
  try {
    const response = await fetch(imageSrc.value)
    const blob = await response.blob()
    const arrayBuffer = await blob.arrayBuffer()
    const electronAPI = window.electron as any
    if (electronAPI?.clipboard?.writeImage) {
      await electronAPI.clipboard.writeImage(new Uint8Array(arrayBuffer))
      showToast('复制成功', '图片已复制到剪贴板!', 2000)
    } else {
      showToast('复制失败', '剪贴板功能不可用', 2000)
    }
  } catch (error) {
    console.error('Failed to copy image:', error)
    showToast('复制失败', '请向开发者反馈', 2000)
  }
}

const deleteImage = () => {
  emit('delete-node')
}

const toggleCaption = () => {
  showCaption.value = !showCaption.value
  updateAttributes({ showCaption: showCaption.value })
}

const updateAttributes = (attrs: Record<string, any>) => {
  emit('update-attributes', attrs)
}

const captionInput = ref<HTMLInputElement>()
const originalCaptionText = ref('')

const startEditingCaption = () => {
  isEditingCaption.value = true
  originalCaptionText.value = captionText.value

  // 暂时禁用TipTap的焦点管理
  const editorElement = document.querySelector('.ProseMirror') as HTMLElement
  if (editorElement) {
    editorElement.contentEditable = 'false'
  }

  // 延迟聚焦，确保DOM更新完成且TipTap焦点管理稳定
  const attemptFocus = (attempt = 1) => {
    const input = captionInput.value
    if (input && attempt <= 5) {
      input.focus()
      input.select()

      console.log(`Focus attempt ${attempt}, success:`, document.activeElement === input)

      // 如果聚焦失败，继续尝试
      if (document.activeElement !== input) {
        setTimeout(() => attemptFocus(attempt + 1), 20 * attempt)
      } else {
        console.log('Caption input focus successful!')
      }
    }
  }

  // 延迟开始聚焦尝试
  setTimeout(() => {
    attemptFocus()
  }, 10)
}

const finishEditingCaption = () => {
  // 如果正在输入法输入中，不结束编辑
  if (isComposing.value) {
    return
  }

  // 恢复TipTap编辑器
  const editorElement = document.querySelector('.ProseMirror') as HTMLElement
  if (editorElement) {
    editorElement.contentEditable = 'true'
  }

  isEditingCaption.value = false
  updateAttributes({ caption: captionText.value })
}

const cancelEditingCaption = () => {
  // 恢复TipTap编辑器
  const editorElement = document.querySelector('.ProseMirror') as HTMLElement
  if (editorElement) {
    editorElement.contentEditable = 'true'
  }

  captionText.value = originalCaptionText.value
  isEditingCaption.value = false
}

const generateAIImage = async () => {
  if (!aiPrompt.value.trim()) return

  try {
    aiGenerating.value = true
    isLoading.value = true
    hasError.value = false

    const timestamp = Date.now().toString()
    const filename = `ai_${timestamp}.png`

    const response = await axiosInstance.post(
      '/ai/generate-image',
      { prompt: aiPrompt.value },
      { timeout: 15000 }
    )

    if (response.data.code !== 0) {
      throw new Error(`API returned error: ${response.data.message}`)
    }

    // Extract the /public path from the full URL
    const imageUrl = response.data.data[0].url
    const publicPath = imageUrl.split('localhost:4000')[1] // This will get everything after localhost:4000

    try {
      const imageResponse = await axiosInstance.get(publicPath, {
        responseType: 'arraybuffer',
        headers: { Accept: 'image/*, */*' }
      })

      const fs = new FileSystemManager()
      await fs.writeBinaryFile(`${imgPath.value}/${filename}`, imageResponse.data)
      storedImagePath.value = filename
      updateAttributes({ src: filename })
      showToast('成功', '图片生成成功！', 2000)
    } catch (error) {
      console.error('Image download error:', error)
      storedImagePath.value = publicPath // Store just the public path
      updateAttributes({ src: publicPath })
      showToast('提示', '图片已生成但使用临时URL', 3000)
    }
  } catch (error) {
    console.error('AI image generation failed:', error)
    showToast('错误', '生成图片失败。请重试。', 3000)
    hasError.value = true
  } finally {
    aiGenerating.value = false
    isLoading.value = false
  }
}

const addImage = async () => {
  try {
    const filePath = await window.electronFS.openDialog()
    if (!filePath) return

    isLoading.value = true
    hasError.value = false

    if (filePath.startsWith('http://') || filePath.startsWith('https://')) {
      storedImagePath.value = filePath
      updateAttributes({ src: filePath })
      return
    }

    const fs = new FileSystemManager()
    const fileBuffer = await fs.readFileSync(filePath)
    const timestamp = Date.now().toString()
    const filename = `${timestamp}.png`
    const file = new File([fileBuffer], filename, { type: 'image/png' })

    if (useOnline.value) {
      try {
        // 尝试使用七牛云上传
        const uploadResult = await uploadToQiniu(file, 'chronnote')
        storedImagePath.value = uploadResult.fileUrl
        updateAttributes({ src: uploadResult.fileUrl })
        showToast('成功', '图片已上传到云端', 2000)
        return
      } catch (qiniuError) {
        console.error('Failed to upload to Qiniu:', qiniuError)

        // 七牛云上传失败，尝试使用原有的上传方式
        try {
          const res = await uploadImage(file)
          storedImagePath.value = res.data.data.url
          updateAttributes({ src: res.data.data.url })
          return
        } catch (uploadError) {
          console.error('Failed to upload image:', uploadError)
          // 所有上传方式都失败，回退到本地存储
          await fs.copyFile(filePath, `${imgPath.value}/${filename}`)
          storedImagePath.value = filename
          showToast('提示', '云端上传失败，已保存到本地', 3000)
        }
      }
    } else {
      // 不使用在线模式，直接保存到本地
      await fs.copyFile(filePath, `${imgPath.value}/${filename}`)
      storedImagePath.value = filename
    }

    updateAttributes({ src: storedImagePath.value })
  } catch (error) {
    console.error('Failed to add image:', error)
    hasError.value = true
  } finally {
    isLoading.value = false
  }
}

const onImageError = () => {
  console.log('图片加载失败 - onImageError触发')
  console.log('当前状态:', {
    useOnline: useOnline.value,
    useLocalFallback: useLocalFallback.value,
    imgPath: imgPath.value,
    storedImagePath: storedImagePath.value,
    imageSrc: imageSrc.value
  })
  
  if (useOnline.value && !useLocalFallback.value && imgPath.value) {
    console.log('尝试本地回退...')
    useLocalFallback.value = true
    // 使用动态的imgPath，触发imageSrc重新计算
    // 这将自动触发loadImage()通过watch(imageSrc)
    return
  }

  console.log('图片加载彻底失败，显示错误状态')
  hasError.value = true
  isLoading.value = false
}

const onImageLoad = () => {
  hasError.value = false
  isLoading.value = false
}

// 创建一个响应式的图片加载函数
const loadImage = () => {
  const src = imageSrc.value
  if (!src) {
    isLoading.value = false
    return
  }
  
  console.log('开始加载图片:', src)
  isLoading.value = true
  hasError.value = false
  
  const img = new Image()
  img.src = src
  img.onload = onImageLoad
  img.onerror = onImageError
}

// Watch props changes
watch(
  () => props.src,
  (newSrc) => {
    if (newSrc) {
      storedImagePath.value = newSrc
      useLocalFallback.value = false
    } else {
      isLoading.value = false
    }
  },
  { immediate: true }
)

// Use watchEffect for reactive image loading - similar to PDFViewer pattern
watchEffect(() => {
  // 响应式地监听imageSrc的变化
  const src = imageSrc.value
  const hasStoredPath = Boolean(storedImagePath.value)
  
  console.log('watchEffect触发:', { src, hasStoredPath, imgPath: imgPath.value })
  
  // 只有当imageSrc有值且storedImagePath有值时才加载图片
  if (src && hasStoredPath) {
    console.log('条件满足，开始加载图片:', src)
    loadImage()
  }
})

watch(
  () => props.caption,
  (newCaption) => {
    captionText.value = newCaption || ''
  },
  { immediate: true }
)

watch(
  () => showCaptionComputed.value,
  (newShowCaption) => {
    showCaption.value = newShowCaption
  },
  { immediate: true }
)

// 监听编辑状态变化，备用聚焦机制
watch(
  () => isEditingCaption.value,
  (isEditing) => {
    if (isEditing) {
      // 备用聚焦 - 如果主要聚焦机制失败
      setTimeout(() => {
        const input = captionInput.value
        if (input && document.activeElement !== input) {
          console.log('Backup focus attempt')
          input.focus()
          input.select()
        }
      }, 100)
    }
  }
)

// resize结束时更新TipTap的函数
const updateTipTapOnResizeEnd = (width: string) => {
  updateAttributes({ width })
}

// 优化的DOM更新函数
const updateElementWidth = (target: HTMLElement, width: number) => {
  if (rafId.value) {
    cancelAnimationFrame(rafId.value)
  }

  rafId.value = requestAnimationFrame(() => {
    target.style.width = `${width}px`
    rafId.value = null
  })
}

// Lifecycle
onMounted(() => {
  // 组件挂载时获取图片路径
  updateImgPath()

  if (resizableElement.value) {
    interact(resizableElement.value).resizable({
      modifiers: [interact.modifiers.aspectRatio({ ratio: 'preserve' })],
      edges: { right: true, left: true },
      listeners: {
        start(event) {
          isDragging.value = true
          const target = event.target

          // 添加resizing类禁用transition
          target.classList.add('resizing')

          // 缓存父容器宽度避免重复计算
          cachedParentWidth.value = target.parentElement?.clientWidth || window.innerWidth
        },
        move(event) {
          const target = event.target as HTMLElement
          let width = event.rect.width

          // 限制最小宽度
          if (width < 200) width = 200

          // 限制最大宽度 - 使用缓存的值
          if (width > cachedParentWidth.value!) width = cachedParentWidth.value!

          // 只更新DOM样式，不触发TipTap更新
          updateElementWidth(target, width)
        },
        end(event) {
          isDragging.value = false
          const target = event.target as HTMLElement

          // 移除resizing类恢复transition
          target.classList.remove('resizing')

          // 清理RAF
          if (rafId.value) {
            cancelAnimationFrame(rafId.value)
            rafId.value = null
          }

          // 获取最终宽度并更新TipTap（仅在resize结束时更新一次）
          const finalWidth = target.style.width || nodeWidth.value
          updateTipTapOnResizeEnd(finalWidth)

          // 清理缓存
          cachedParentWidth.value = null
        }
      }
    })
  }
})
</script>
