# 图片节点 Vue Web Component 重构

## 概述

使用 Vue 的 `defineCustomElement` 将原有的 Vue 组件转换为 Web Component，既保留了 Vue 的开发体验，又获得了 Web Component 的性能优势。

## 文件结构

```
image/
├── README.md                    # 本文档
├── ImageWebComponent.ts         # Vue Web Component 注册
├── ImageComponent.vue           # 适配 Web Component 的 Vue 组件
├── index.ts                     # TipTap 节点配置
└── customImg.vue               # 原有 Vue 组件（可保留作参考）
```

## 主要改进

### 1. 最佳实践结合
- **Vue 开发体验**: 保留组合式 API、响应式系统和模板语法
- **Web Component 性能**: 获得样式隔离和更好的性能
- **无缝集成**: 直接在 TipTap 中使用，无需复杂包装

### 2. 样式隔离
- **Scoped CSS**: Vue 的样式隔离确保样式不冲突
- **Shadow DOM**: Web Component 的原生样式隔离
- **Tailwind 兼容**: 通过内嵌样式支持 Tailwind CSS
- **主题支持**: 通过 CSS 变量支持动态主题

### 3. 架构优势
- **简化通信**: 通过 CustomEvent 与 TipTap 通信
- **属性驱动**: 使用标准的 Web Component 属性传递数据
- **生命周期管理**: Vue 的生命周期与 Web Component 完美结合

## 核心特性

### 状态管理
- **加载状态**: 图片加载中的 spinner 动画
- **AI 生成**: 支持 AI 图片生成功能
- **错误处理**: 网络错误自动回退到本地
- **空状态**: 友好的添加图片界面

### 交互功能
- **拖拽调整**: 支持左右拖拽调整图片大小
- **悬停控制**: 复制、删除等操作按钮
- **图片预览**: 点击放大预览
- **键盘支持**: Mod+5 快捷键插入

### 样式系统
```css
/* 主题变量示例 */
:root {
  --primary-color: #3b82f6;
  --surface-color: #ffffff;
  --text-color: #1f2937;
  /* 等等... */
}
```

## 使用方法

### 1. 自动注册
Vue Web Component 会自动注册为 `chronnote-image`，TipTap 节点配置已更新。

### 2. 属性传递
```typescript
// Web Component 属性
<chronnote-image
  node-id="uuid"
  src="image.png" 
  width="400px"
  align="center"
  :use-online="true"
  :by-ai="false"
/>
```

### 3. 事件通信
```typescript
// 组件发出的事件
element.addEventListener('update-attributes', (e) => {
  console.log('属性更新:', e.detail)
})

element.addEventListener('delete-node', () => {
  console.log('删除节点')
})

element.addEventListener('open-preview', (e) => {
  console.log('打开预览:', e.detail)
})
```

## 浏览器兼容性

- **Chrome**: 54+
- **Firefox**: 63+
- **Safari**: 10.1+
- **Edge**: 79+

所有现代浏览器都支持 Web Components 和 Shadow DOM。

## 开发指南

### 自定义样式
通过 CSS Custom Properties 覆盖默认样式：

```css
chronnote-image {
  --chronnote-primary: #your-color;
  --chronnote-surface: #your-background;
}
```

### 扩展功能
可以通过修改 `CustomImageElement.ts` 添加新功能，或者创建新的 Web Component 继承现有实现。

### 调试
```javascript
// 获取 Web Component 实例
const imageElement = document.querySelector('chronnote-image');

// 手动触发更新
imageElement.updateFromNode();
```

## 迁移指南

从 Vue 组件迁移到 Web Component 是完全透明的：

1. **无需更改**: TipTap 节点接口保持不变
2. **自动切换**: 重新加载应用即可使用新组件
3. **功能保持**: 所有原有功能都已实现

## 技术优势

### 开发体验
- **保留 Vue 语法**: 继续使用熟悉的 Vue 模板和组合式 API
- **类型安全**: 完整的 TypeScript 支持
- **调试友好**: Vue DevTools 兼容

### 性能提升
- **按需加载**: Web Component 只在需要时创建
- **样式隔离**: 避免全局样式污染
- **更少依赖**: 减少 TipTap Vue 包装层的开销

## 故障排除

### 常见问题

1. **样式不生效**
   - 检查 CSS Custom Properties 是否正确设置
   - 确认 Shadow DOM 隔离没有影响外部样式

2. **交互失效**
   - 确认 TipTap 集成正确初始化
   - 检查 `setTipTapIntegration` 是否被调用

3. **图片不显示**
   - 检查图片路径和权限
   - 确认网络/本地回退机制正常

### 调试技巧
```javascript
// 启用详细日志
localStorage.setItem('debug-chronnote-image', 'true');

// 查看组件状态
console.log(imageElement.tiptap);
```

## 后续计划

- [ ] 添加更多图片格式支持
- [ ] 实现图片压缩功能  
- [ ] 支持批量操作
- [ ] 添加图片标注功能
- [ ] 性能监控和优化

---

*本重构遵循 Vue 官方 Web Components 最佳实践指南*