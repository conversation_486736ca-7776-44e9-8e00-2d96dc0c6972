import { Editor } from '@tiptap/core'
import { Paragraph } from '@tiptap/extension-paragraph'

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        bParagraph: {
            /**
             * Select current paragraph block
             */
            selectCurrentParagraph: () => ReturnType
        }
    }
}

export const bParagraph = Paragraph.extend({
    addCommands() {
        return {
            selectCurrentParagraph: () => ({ state, commands }) => {
                console.log('selectCurrentParagraph', state.selection.$anchor.pos)
                const { $from } = state.selection
                const { parentOffset } = $from
                return commands.selectParentNode()
            }
        }
    },

    addKeyboardShortcuts() {
        return {
            'Mod-a': () => this.editor.commands.selectCurrentParagraph()
        }
    }
})