import '../types.js'

import { mergeAttributes, Node } from '@tiptap/core'
import type { Node as ProseMirrorNode } from '@tiptap/pm/model'
import type { NodeView } from '@tiptap/pm/view'

export interface TableHeaderOptions {
  /**
   * The HTML attributes for a table header node.
   * @default {}
   * @example { class: 'foo' }
   */
  HTMLAttributes: Record<string, any>
}

/**
 * This extension allows you to create table headers.
 * @see https://www.tiptap.dev/api/nodes/table-header
 */
export const TableHeader = Node.create<TableHeaderOptions>({
  name: 'tableHeader',

  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },

  content: 'block+',

  addAttributes() {
    return {
      colspan: {
        default: 1,
      },
      rowspan: {
        default: 1,
      },
      colwidth: {
        default: null,
        parseHTML: element => {
          const colwidth = element.getAttribute('colwidth')
          const value = colwidth ? colwidth.split(',').map(width => parseInt(width, 10)) : null

          return value
        },
      },
    }
  },

  tableRole: 'header_cell',

  isolating: true,

  parseHTML() {
    return [{ tag: 'th' }]
  },

  renderHTML({ HTMLAttributes }) {
    return ['th', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]
  },

  addNodeView() {
    console.log('TableHeader addNodeView')
    return ({ node, HTMLAttributes, editor, getPos }) => {
      return new TableHeaderView(node, HTMLAttributes, editor, getPos)
    }
  },
})

/**
 * 自定义 TableHeader 视图，添加右键菜单功能
 */
class TableHeaderView implements NodeView {
  dom: HTMLTableCellElement
  contentDOM: HTMLElement
  node: ProseMirrorNode
  editor: any
  getPos: (() => number | undefined) | undefined

  constructor(node: ProseMirrorNode, HTMLAttributes: Record<string, any>, editor?: any, getPos?: () => number | undefined) {
    console.log('TableHeaderView constructor called!', node, HTMLAttributes)
    this.node = node
    this.editor = editor
    this.getPos = getPos

    // 创建 th 元素
    this.dom = document.createElement('th')
    this.dom.style.position = 'relative'

    // 应用属性
    Object.entries(HTMLAttributes).forEach(([key, value]) => {
      if (key !== 'style') {
        this.dom.setAttribute(key, value)
      }
    })

    // 设置内容容器
    this.contentDOM = this.dom

    // 添加右键菜单功能
    this.addContextMenu()
    console.log('TableHeaderView created with context menu!')
  }

  /**
   * 添加右键菜单功能
   */
  private addContextMenu(): void {
    this.dom.addEventListener('contextmenu', (e) => {
      e.preventDefault()
      e.stopPropagation()
      
      console.log('TableHeader right-clicked!', this.node)
      this.showContextMenu(e)
    })
  }

  /**
   * 显示右键菜单
   */
  private showContextMenu(event: MouseEvent): void {
    // 移除现有菜单
    this.removeExistingMenu()

    // 创建菜单容器
    const menu = document.createElement('div')
    menu.className = 'table-cell-context-menu'
    menu.style.cssText = `
      position: fixed;
      top: ${event.clientY}px;
      left: ${event.clientX}px;
      background: var(--base-background);
      border: 1px solid var(--base-border);
      border-radius: 4px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
      z-index: 10000;
      padding: 4px 0;
      min-width: 120px;
      font-size: 14px;
      color: var(--base-content);
    `

    // 菜单项
    const menuItems = [
      { label: '➕ 插入行', action: () => this.insertRow() },
      { label: '🗑️ 删除行', action: () => this.deleteRow() },
      { label: '➕ 插入列', action: () => this.insertColumn() },
      { label: '🗑️ 删除列', action: () => this.deleteColumn() }
    ]

    menuItems.forEach(item => {
      const menuItem = document.createElement('div')
      menuItem.textContent = item.label
      menuItem.style.cssText = `
        padding: 8px 12px;
        cursor: pointer;
        transition: background-color 0.2s;
        color: var(--base-content);
      `
      
      menuItem.addEventListener('mouseenter', () => {
        menuItem.style.backgroundColor = 'var(--base-100)'
      })
      
      menuItem.addEventListener('mouseleave', () => {
        menuItem.style.backgroundColor = 'transparent'
      })
      
      menuItem.addEventListener('click', () => {
        item.action()
        this.removeExistingMenu()
      })
      
      menu.appendChild(menuItem)
    })

    // 添加到页面
    document.body.appendChild(menu)

    // 点击其他地方关闭菜单
    const closeMenu = (e: Event) => {
      if (!menu.contains(e.target as Node)) {
        this.removeExistingMenu()
        document.removeEventListener('click', closeMenu)
        document.removeEventListener('contextmenu', closeMenu)
      }
    }
    
    setTimeout(() => {
      document.addEventListener('click', closeMenu)
      document.addEventListener('contextmenu', closeMenu)
    }, 0)
  }

  /**
   * 移除现有菜单
   */
  private removeExistingMenu(): void {
    const existingMenu = document.querySelector('.table-cell-context-menu')
    if (existingMenu) {
      existingMenu.remove()
    }
  }

  // 菜单操作方法
  private insertRow(): void {
    console.log('Header插入行功能')
    if (!this.editor) {
      console.warn('Editor not available for insertRow')
      return
    }

    try {
      console.log('Available commands:', Object.keys(this.editor.commands || {}))
      
      if (this.editor.commands && this.editor.commands.addRowAfter) {
        const result = this.editor.commands.addRowAfter()
        console.log('Row inserted successfully, result:', result)
      } else {
        console.warn('addRowAfter command not available')
      }
    } catch (error) {
      console.error('Failed to insert row:', error)
    }
  }

  private deleteRow(): void {
    console.log('Header删除行功能')
    if (!this.editor) {
      console.warn('Editor not available for deleteRow')
      return
    }

    try {
      if (this.editor.commands && this.editor.commands.deleteRow) {
        const result = this.editor.commands.deleteRow()
        console.log('Row deleted successfully, result:', result)
      } else {
        console.warn('deleteRow command not available')
      }
    } catch (error) {
      console.error('Failed to delete row:', error)
    }
  }

  private insertColumn(): void {
    console.log('Header插入列功能')
    if (!this.editor) {
      console.warn('Editor not available for insertColumn')
      return
    }

    try {
      if (this.editor.commands && this.editor.commands.addColumnAfter) {
        const result = this.editor.commands.addColumnAfter()
        console.log('Column inserted successfully, result:', result)
      } else {
        console.warn('addColumnAfter command not available')
      }
    } catch (error) {
      console.error('Failed to insert column:', error)
    }
  }

  private deleteColumn(): void {
    console.log('Header删除列功能')
    if (!this.editor) {
      console.warn('Editor not available for deleteColumn')
      return
    }

    try {
      if (this.editor.commands && this.editor.commands.deleteColumn) {
        const result = this.editor.commands.deleteColumn()
        console.log('Column deleted successfully, result:', result)
      } else {
        console.warn('deleteColumn command not available')
      }
    } catch (error) {
      console.error('Failed to delete column:', error)
    }
  }

  update(node: ProseMirrorNode): boolean {
    if (node.type !== this.node.type) {
      return false
    }
    this.node = node
    return true
  }

  destroy(): void {
    // 清理右键菜单
    this.removeExistingMenu()
  }

  stopEvent(event: Event): boolean {
    // 拦截右键菜单的事件，避免干扰编辑器
    return (event.target as Element)?.closest('.table-cell-context-menu') ? true : false
  }
}
