import type { Node as ProseMirrorNode } from '@tiptap/pm/model'
import type { NodeView, ViewMutationRecord, EditorView } from '@tiptap/pm/view'
import { TextSelection } from '@tiptap/pm/state'
import { addRowAfter, addColumnAfter } from '@tiptap/pm/tables'

import { getColStyleDeclaration } from './utilities/colStyle.js'

export function updateColumns(
  node: ProseMirrorNode,
  colgroup: HTMLTableColElement, // <colgroup> has the same prototype as <col>
  table: HTMLTableElement,
  cellMinWidth: number,
  overrideCol?: number,
  overrideValue?: number,
) {
  let totalWidth = 0
  let fixedWidth = true
  let nextDOM = colgroup.firstChild
  const row = node.firstChild

  if (row !== null) {
    for (let i = 0, col = 0; i < row.childCount; i += 1) {
      const { colspan, colwidth } = row.child(i).attrs

      for (let j = 0; j < colspan; j += 1, col += 1) {
        const hasWidth = overrideCol === col ? overrideValue : ((colwidth && colwidth[j]) as number | undefined)
        const cssWidth = hasWidth ? `${hasWidth}px` : ''

        totalWidth += hasWidth || cellMinWidth

        if (!hasWidth) {
          fixedWidth = false
        }

        if (!nextDOM) {
          const colElement = document.createElement('col')

          const [propertyKey, propertyValue] = getColStyleDeclaration(cellMinWidth, hasWidth)

          colElement.style.setProperty(propertyKey, propertyValue)

          colgroup.appendChild(colElement)
        } else {
          if ((nextDOM as HTMLTableColElement).style.width !== cssWidth) {
            const [propertyKey, propertyValue] = getColStyleDeclaration(cellMinWidth, hasWidth)

            ;(nextDOM as HTMLTableColElement).style.setProperty(propertyKey, propertyValue)
          }

          nextDOM = nextDOM.nextSibling
        }
      }
    }
  }

  while (nextDOM) {
    const after = nextDOM.nextSibling

    nextDOM.parentNode?.removeChild(nextDOM)
    nextDOM = after
  }

  if (fixedWidth) {
    table.style.width = `${totalWidth}px`
    table.style.minWidth = ''
  } else {
    table.style.width = ''
    table.style.minWidth = `${totalWidth}px`
  }
}

export class TableView implements NodeView {
  node: ProseMirrorNode

  cellMinWidth: number

  dom: HTMLDivElement

  table: HTMLTableElement

  colgroup: HTMLTableColElement

  contentDOM: HTMLTableSectionElement

  // 悬浮按钮相关
  private view?: EditorView
  private getPos?: () => number | undefined
  private editor?: any
  private addColumnBtn?: HTMLElement
  private addRowBtn?: HTMLElement
  private infoLabel?: HTMLElement
  private resizeObserver?: ResizeObserver

  constructor(node: ProseMirrorNode, cellMinWidth: number, view?: EditorView, getPos?: () => number | undefined, editor?: any) {
    this.node = node
    this.cellMinWidth = cellMinWidth
    this.view = view
    this.getPos = getPos
    this.editor = editor
    
    // 创建表格容器
    this.dom = document.createElement('div')
    this.dom.className = 'tableWrapper'
    
    // 直接在容器内创建表格
    this.table = this.dom.appendChild(document.createElement('table'))
    this.colgroup = this.table.appendChild(document.createElement('colgroup'))
    updateColumns(node, this.colgroup, this.table, cellMinWidth)
    this.contentDOM = this.table.appendChild(document.createElement('tbody'))

    // 如果有view和getPos，创建悬浮按钮
    if (this.view && this.getPos) {
      this.createHoverButtons()
      this.setupResizeObserver()
    }
  }

  /**
   * 创建悬浮按钮系统
   */
  private createHoverButtons(): void {
    // 右侧添加列按钮
    this.addColumnBtn = this.createPlusButton('add-column-btn', () => this.addColumn(), '添加列')

    // 底部添加行按钮
    this.addRowBtn = this.createPlusButton('add-row-btn', () => this.addRow(), '添加行')

    // 表格信息标签
    this.infoLabel = this.createInfoLabel()

    // 添加到主容器
    this.dom.appendChild(this.addColumnBtn)
    this.dom.appendChild(this.addRowBtn)
    this.dom.appendChild(this.infoLabel)
  }

  /**
   * 创建 Plus 按钮
   */
  private createPlusButton(className: string, onClick: () => void, tooltip: string): HTMLElement {
    const container = document.createElement('div')
    container.className = `hover-controls ${className}`
    container.contentEditable = 'false'

    const button = document.createElement('div')
    button.className = 'plus-btn'
    button.textContent = '+'
    button.title = tooltip
    button.contentEditable = 'false'

    // 添加多种事件监听器
    const handleClick = (e: Event) => {
      e.stopPropagation()
      e.preventDefault()
      console.log(`Button clicked: ${className}`)
      onClick()
    }

    button.addEventListener('click', handleClick)
    button.addEventListener('mousedown', (e) => {
      e.preventDefault()
      e.stopPropagation()
    })

    // 添加触摸事件支持
    button.addEventListener('touchstart', (e) => {
      e.preventDefault()
      e.stopPropagation()
    })

    button.addEventListener('touchend', (e) => {
      e.preventDefault()
      e.stopPropagation()
      handleClick(e)
    })

    // 添加键盘事件支持
    button.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault()
        e.stopPropagation()
        handleClick(e)
      }
    })

    // 让按钮可获得焦点
    button.setAttribute('tabindex', '0')
    button.setAttribute('role', 'button')
    button.setAttribute('aria-label', tooltip)

    container.appendChild(button)
    return container
  }

  /**
   * 创建信息标签
   */
  private createInfoLabel(): HTMLElement {
    const label = document.createElement('div')
    label.className = 'table-info'
    label.contentEditable = 'false'
    this.updateInfoLabelText(label)
    return label
  }

  /**
   * 更新信息标签文本
   */
  private updateInfoLabelText(label?: HTMLElement): void {
    const targetLabel = label || this.infoLabel
    if (!targetLabel) return

    // 从节点结构计算行列数
    const rows = this.node.childCount
    const cols = rows > 0 ? this.node.child(0).childCount : 0
    targetLabel.textContent = `${rows}×${cols}`
  }

  /**
   * 添加行功能
   */
  private addRow(): void {
    if (!this.view) {
      console.warn('No view available for addRow')
      return
    }

    try {
      console.log('Adding row to table')
      // 直接调用 ProseMirror 的 addRowAfter 命令
      const result = addRowAfter(this.view.state, this.view.dispatch)
      console.log('addRowAfter result:', result)
    } catch (error) {
      console.error('Error adding row:', error)
    }
  }

  /**
   * 添加列功能
   */
  private addColumn(): void {
    if (!this.view) {
      console.warn('No view available for addColumn')
      return
    }

    try {
      console.log('Adding column to table')
      // 直接调用 ProseMirror 的 addColumnAfter 命令
      const result = addColumnAfter(this.view.state, this.view.dispatch)
      console.log('addColumnAfter result:', result)
    } catch (error) {
      console.error('Error adding column:', error)
    }
  }

  /**
   * 设置ResizeObserver来监听表格大小变化
   */
  private setupResizeObserver(): void {
    if (!window.ResizeObserver) return

    this.resizeObserver = new ResizeObserver(() => {
      this.updateButtonPositions()
    })

    this.resizeObserver.observe(this.table)
  }

  /**
   * 更新按钮位置
   */
  private updateButtonPositions(): void {
    if (!this.addColumnBtn || !this.addRowBtn || !this.infoLabel) return

    // 获取表格的实际尺寸
    const tableRect = this.table.getBoundingClientRect()
    const wrapperRect = this.dom.getBoundingClientRect()

    // 计算相对于wrapper的位置
    const rightOffset = tableRect.right - wrapperRect.left
    const bottomOffset = tableRect.bottom - wrapperRect.top

    // 更新添加列按钮位置 - 增加偏移量确保不被遮挡
    this.addColumnBtn.style.left = `${rightOffset + 8}px`
    this.addColumnBtn.style.top = `${(tableRect.height / 2)}px`
    this.addColumnBtn.style.transform = 'translateY(-50%)'

    // 更新添加行按钮位置 - 增加偏移量确保不被遮挡
    this.addRowBtn.style.top = `${bottomOffset + 8}px`
    this.addRowBtn.style.left = `${(tableRect.width / 2)}px`
    this.addRowBtn.style.transform = 'translateX(-50%)'

    // 更新info标签位置
    this.infoLabel.style.top = '-12px'
    this.infoLabel.style.left = `${rightOffset - 40}px`
  }

  update(node: ProseMirrorNode) {
    if (node.type !== this.node.type) {
      return false
    }

    this.node = node
    updateColumns(node, this.colgroup, this.table, this.cellMinWidth)

    // 更新信息标签
    if (this.infoLabel) {
      this.updateInfoLabelText()
    }

    // 更新按钮位置
    setTimeout(() => {
      this.updateButtonPositions()
    }, 0)

    return true
  }

  /**
   * stopEvent 方法 - 控制事件传播
   */
  stopEvent(event: Event): boolean {
    // 只拦截悬浮按钮的事件
    if ((event.target as Element)?.closest('.hover-controls')) {
      return true
    }

    // 让 TipTap 处理表格内容的事件
    return false
  }

  /**
   * destroy 方法
   */
  destroy(): void {
    // 清理ResizeObserver
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
      this.resizeObserver = undefined
    }
  }

  ignoreMutation(mutation: ViewMutationRecord) {
    // 忽略悬浮按钮相关的变化
    if (mutation.target) {
      const target = mutation.target as Element
      if (target.closest?.('.hover-controls') || target.closest?.('.table-info')) {
        return true
      }
    }

    return mutation.type === 'attributes' && (mutation.target === this.table || this.colgroup.contains(mutation.target))
  }
}
