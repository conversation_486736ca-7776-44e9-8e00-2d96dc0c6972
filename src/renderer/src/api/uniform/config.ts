import axiosInstance from '@renderer/utils/axios/index'

// 配置项类型定义
export interface ConfigItem {
  key: string
  value: any
}

// 后端现在直接返回value，不再包装在response结构中
export type ConfigValue = any

export interface BatchSetConfigsRequest {
  configs: Record<string, any>
}

export interface SetConfigRequest {
  value: any
}

/**
 * 获取所有配置
 */
export const getAllConfigs = async (): Promise<Record<string, any>> => {
  try {
    const response = await axiosInstance.get('/api/config')
    return response.data // 后端直接返回配置对象
  } catch (error) {
    console.error('获取所有配置API错误:', error)
    throw error
  }
}

/**
 * 获取指定配置
 * @param key 配置键
 */
export const getConfig = async (key: string): Promise<ConfigValue> => {
  try {
    const response = await axiosInstance.get(`/api/config/${encodeURIComponent(key)}`)
    return response.data.data // 后端直接返回value值
  } catch (error) {
    console.error(`获取配置 ${key} API错误:`, error)
    throw error
  }
}

/**
 * 批量设置配置
 * @param configs 配置对象
 */
export const batchSetConfigs = async (configs: Record<string, any>): Promise<void> => {
  try {
    await axiosInstance.post('/api/config', { configs })
  } catch (error) {
    console.error('批量设置配置API错误:', error)
    throw error
  }
}

/**
 * 设置单个配置
 * @param key 配置键
 * @param value 配置值
 */
export const setConfig = async (key: string, value: any): Promise<void> => {
  try {
    await axiosInstance.put(`/api/config/${encodeURIComponent(key)}`, { value })
  } catch (error) {
    console.error(`设置配置 ${key} API错误:`, error)
    throw error
  }
}

/**
 * 删除配置
 * @param key 配置键
 */
export const deleteConfig = async (key: string): Promise<void> => {
  try {
    await axiosInstance.delete(`/api/config/${encodeURIComponent(key)}`)
  } catch (error) {
    console.error(`删除配置 ${key} API错误:`, error)
    throw error
  }
}

// 类型安全的配置访问器类
export class ConfigAccessor {
  /**
   * 获取配置值（通用泛型方法）
   * @param key 配置键
   * @param defaultValue 默认值
   * @returns 配置值或默认值
   */
  static async get<T = any>(key: string, defaultValue: T): Promise<T> {
    try {
      const value = await getConfig(key)
      return value !== undefined && value !== null ? value : defaultValue
    } catch (error) {
      console.warn(`获取配置 ${key} 失败，使用默认值:`, defaultValue)
      return defaultValue
    }
  }

  /**
   * 设置配置值（通用泛型方法）
   * @param key 配置键
   * @param value 配置值
   */
  static async set<T = any>(key: string, value: T): Promise<void> {
    return await setConfig(key, value)
  }

  // 为了向后兼容，保留一些常用的类型特定方法
  /**
   * 获取字符串类型配置
   * @param key 配置键
   * @param defaultValue 默认值
   */
  static async getString(key: string, defaultValue: string = ''): Promise<string> {
    return this.get(key, defaultValue)
  }

  /**
   * 获取数字类型配置
   * @param key 配置键
   * @param defaultValue 默认值
   */
  static async getNumber(key: string, defaultValue: number = 0): Promise<number> {
    const value = await this.get(key, defaultValue)
    return typeof value === 'number' ? value : (typeof value === 'string' && !isNaN(Number(value))) ? Number(value) : defaultValue
  }

  /**
   * 获取布尔类型配置
   * @param key 配置键
   * @param defaultValue 默认值
   */
  static async getBoolean(key: string, defaultValue: boolean = false): Promise<boolean> {
    return this.get(key, defaultValue)
  }
}

// 配置管理工具类
export class ConfigManager {
  /**
   * 检查配置是否存在
   * @param key 配置键
   */
  static async hasConfig(key: string): Promise<boolean> {
    try {
      await getConfig(key)
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * 获取配置，如果不存在则设置默认值
   * @param key 配置键
   * @param defaultValue 默认值
   */
  static async getOrSetDefault<T = any>(key: string, defaultValue: T): Promise<T> {
    try {
      const value = await getConfig(key)
      return value
    } catch (error) {
      // 配置不存在，设置默认值
      await setConfig(key, defaultValue)
      return defaultValue
    }
  }

  /**
   * 批量获取配置
   * @param keys 配置键数组
   */
  static async batchGetConfigs(keys: string[]): Promise<Record<string, any>> {
    const result: Record<string, any> = {}

    // 并行获取所有配置
    const promises = keys.map(async (key) => {
      try {
        const value = await getConfig(key)
        result[key] = value
      } catch (error) {
        console.warn(`获取配置 ${key} 失败:`, error)
        result[key] = null
      }
    })

    await Promise.all(promises)
    return result
  }

  /**
   * 批量删除配置
   * @param keys 配置键数组
   */
  static async batchDeleteConfigs(keys: string[]): Promise<void> {
    const promises = keys.map(key => deleteConfig(key))
    await Promise.all(promises)
  }

  /**
   * 重置配置到默认值
   * @param key 配置键
   * @param defaultValue 默认值
   */
  static async resetToDefault<T = any>(key: string, defaultValue: T): Promise<void> {
    return await setConfig(key, defaultValue)
  }

  /**
   * 获取配置的原始值（不进行类型转换）
   * @param key 配置键
   */
  static async getRawValue(key: string): Promise<any> {
    return await getConfig(key)
  }

  /**
   * 复制配置
   * @param sourceKey 源配置键
   * @param targetKey 目标配置键
   */
  static async copyConfig(sourceKey: string, targetKey: string): Promise<void> {
    const sourceValue = await this.getRawValue(sourceKey)
    return await setConfig(targetKey, sourceValue)
  }

  /**
   * 移动配置（重命名）
   * @param oldKey 旧配置键
   * @param newKey 新配置键
   */
  static async moveConfig(oldKey: string, newKey: string): Promise<void> {
    const value = await this.getRawValue(oldKey)
    await setConfig(newKey, value)
    await deleteConfig(oldKey)
  }
}

// 常用配置键常量
export const CONFIG_KEYS = {
  COZE_API_TOKEN: 'cz',
} as const

// 默认配置值
export const DEFAULT_CONFIG_VALUES = {
  COZE_API_TOKEN: '',
} as const
