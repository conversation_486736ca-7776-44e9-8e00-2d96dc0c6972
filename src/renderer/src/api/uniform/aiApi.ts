import axiosInstance from '@renderer/utils/axios'
import type {
  UploadResponse,
  IndexStatusResponse,
  ChatResponse,
  ChatRequest,
  ServerInfoResponse,
  HealthResponse,
  ConfigResponse,
  ErrorResponse
} from '@renderer/types/aiApi'

/**
 * AI API 服务类
 * 封装与ChronNote Server AI API的交互
 */
export class AIApiService {
  private static readonly AI_API_PREFIX = '/api/ai'

  /**
   * 上传文档进行索引
   * @param file 要上传的文件
   * @returns Promise<UploadResponse> 包含文件ID的响应
   */
  static async uploadDocument(file: File): Promise<UploadResponse> {
    try {
      const formData = new FormData()
      formData.append('file', file)

      const response = await axiosInstance.post(
        `${this.AI_API_PREFIX}/index/upload`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }
      )

      return response.data as UploadResponse
    } catch (error) {
      console.error('上传文档失败:', error)
      throw error
    }
  }

  /**
   * 查询文档索引状态
   * @param fileId 文件ID
   * @returns Promise<IndexStatusResponse> 包含索引状态的响应
   */
  static async queryIndexStatus(fileId: string): Promise<IndexStatusResponse> {
    try {
      const response = await axiosInstance.get(`${this.AI_API_PREFIX}/index/query/${fileId}`)

      return response.data as IndexStatusResponse
    } catch (error) {
      console.error('查询索引状态失败:', error)
      throw error
    }
  }

  /**
   * 轮询查询索引状态，直到完成或失败
   * @param fileId 文件ID
   * @param maxAttempts 最大尝试次数，默认30次
   * @param interval 轮询间隔（毫秒），默认2000ms
   * @returns Promise<IndexStatusResponse> 最终状态
   */
  static async waitForIndexCompletion(
    fileId: string,
    maxAttempts: number = 30,
    interval: number = 2000
  ): Promise<IndexStatusResponse> {
    let attempts = 0

    while (attempts < maxAttempts) {
      try {
        const status = await this.queryIndexStatus(fileId)

        if (status.status === 'completed' || status.status === 'failed') {
          return status
        }

        // 等待指定间隔
        await new Promise(resolve => setTimeout(resolve, interval))
        attempts++
      } catch (error) {
        console.error(`轮询索引状态失败 (尝试 ${attempts + 1}/${maxAttempts}):`, error)
        attempts++

        if (attempts >= maxAttempts) {
          throw error
        }

        // 如果出错，等待更长时间再重试
        await new Promise(resolve => setTimeout(resolve, interval * 2))
      }
    }

    throw new Error(`索引状态轮询超时: 超过 ${maxAttempts} 次尝试`)
  }

  /**
   * AI聊天接口
   * @param chatRequest 聊天请求参数
   * @returns Promise<ChatResponse> AI响应
   */
  static async chat(chatRequest: ChatRequest): Promise<ChatResponse> {
    try {
      const formData = new FormData()
      formData.append('user_prompt', chatRequest.user_prompt)

      if (chatRequest.file_type) {
        formData.append('file_type', chatRequest.file_type)
      }

      if (chatRequest.file_id) {
        formData.append('file_id', chatRequest.file_id)
      }

      if (chatRequest.web_search !== undefined) {
        formData.append('web_search', chatRequest.web_search.toString())
      }

      const response = await axiosInstance.post(
        `${this.AI_API_PREFIX}/chat`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }
      )

      return response.data as ChatResponse
    } catch (error) {
      console.error('AI聊天失败:', error)
      throw error
    }
  }

  /**
   * 简化的聊天接口 - 仅文本
   * @param prompt 用户提示
   * @param enableWebSearch 是否启用网络搜索，默认false
   * @returns Promise<string> AI响应内容
   */
  static async simpleChat(prompt: string, enableWebSearch: boolean = false): Promise<string> {
    try {
      const response = await this.chat({
        user_prompt: prompt,
        web_search: enableWebSearch
      })

      return response.content
    } catch (error) {
      console.error('简单聊天失败:', error)
      throw error
    }
  }

  /**
   * 基于文档的聊天
   * @param prompt 用户提示
   * @param fileId 文档文件ID
   * @param fileType 文档类型，默认'pdf'
   * @param enableWebSearch 是否启用网络搜索，默认false
   * @returns Promise<string> AI响应内容
   */
  static async chatWithDocument(
    prompt: string,
    fileId: string,
    fileType: string = 'pdf',
    enableWebSearch: boolean = false
  ): Promise<string> {
    try {
      const response = await this.chat({
        user_prompt: prompt,
        file_type: fileType,
        file_id: fileId,
        web_search: enableWebSearch
      })

      return response.content
    } catch (error) {
      console.error('基于文档的聊天失败:', error)
      throw error
    }
  }

  /**
   * 上传文档并等待索引完成，然后进行聊天
   * @param file 要上传的文件
   * @param prompt 用户提示
   * @param enableWebSearch 是否启用网络搜索，默认false
   * @returns Promise<string> AI响应内容
   */
  static async uploadAndChat(
    file: File,
    prompt: string,
    enableWebSearch: boolean = false
  ): Promise<string> {
    try {
      // 1. 上传文档
      const uploadResult = await this.uploadDocument(file)
      console.log('文档上传成功，文件ID:', uploadResult.file_id)

      // 2. 等待索引完成
      const indexStatus = await this.waitForIndexCompletion(uploadResult.file_id)
      
      if (indexStatus.status === 'failed') {
        throw new Error('文档索引失败')
      }

      console.log('文档索引完成')

      // 3. 基于文档进行聊天
      const fileType = file.type === 'application/pdf' ? 'pdf' : 'document'
      return await this.chatWithDocument(prompt, uploadResult.file_id, fileType, enableWebSearch)
    } catch (error) {
      console.error('上传并聊天失败:', error)
      throw error
    }
  }

  /**
   * 健康检查
   * @returns Promise<HealthResponse> 健康状态
   */
  static async health(): Promise<HealthResponse> {
    try {
      const response = await axiosInstance.get(`${this.AI_API_PREFIX}/health`)
      return response.data as HealthResponse
    } catch (error) {
      console.error('健康检查失败:', error)
      throw error
    }
  }

  /**
   * 获取服务器信息
   * @returns Promise<ServerInfoResponse> 服务器信息
   */
  static async getServerInfo(): Promise<ServerInfoResponse> {
    try {
      const response = await axiosInstance.get(`${this.AI_API_PREFIX}/`)
      return response.data as ServerInfoResponse
    } catch (error) {
      console.error('获取服务器信息失败:', error)
      throw error
    }
  }

  /**
   * 获取配置信息
   * @returns Promise<ConfigResponse> 配置信息
   */
  static async getConfig(): Promise<ConfigResponse> {
    try {
      const response = await axiosInstance.get(`${this.AI_API_PREFIX}/config`)
      return response.data as ConfigResponse
    } catch (error) {
      console.error('获取配置信息失败:', error)
      throw error
    }
  }
}

// 导出便捷的函数式接口
export const aiApi = {
  // 文档相关
  uploadDocument: AIApiService.uploadDocument.bind(AIApiService),
  queryIndexStatus: AIApiService.queryIndexStatus.bind(AIApiService),
  waitForIndexCompletion: AIApiService.waitForIndexCompletion.bind(AIApiService),

  // 聊天相关
  chat: AIApiService.chat.bind(AIApiService),
  simpleChat: AIApiService.simpleChat.bind(AIApiService),
  chatWithDocument: AIApiService.chatWithDocument.bind(AIApiService),
  uploadAndChat: AIApiService.uploadAndChat.bind(AIApiService),

  // 系统信息
  health: AIApiService.health.bind(AIApiService),
  getServerInfo: AIApiService.getServerInfo.bind(AIApiService),
  getConfig: AIApiService.getConfig.bind(AIApiService)
}

// 默认导出
export default AIApiService 