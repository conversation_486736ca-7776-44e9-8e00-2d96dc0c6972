/**
 * AI API 使用示例
 * 展示如何使用封装好的AI API接口
 */

import { AIApiService, aiApi } from './aiApi'
import { showToast } from '@renderer/utils/toast'
import { ref, readonly } from 'vue'

/**
 * 示例1: 简单文本聊天
 */
export const simpleTextChatExample = async () => {
  try {
    const prompt = "请解释一下什么是机器学习？"
    const response = await aiApi.simpleChat(prompt)
    
    console.log('AI回复:', response)
    showToast('成功', 'AI回复已生成')
    
    return response
  } catch (error) {
    console.error('简单聊天失败:', error)
    showToast('错误', '聊天请求失败')
    throw error
  }
}

/**
 * 示例2: 启用网络搜索的聊天
 */
export const webSearchChatExample = async () => {
  try {
    const prompt = "最新的人工智能发展趋势是什么？"
    const response = await aiApi.simpleChat(prompt, true) // 启用网络搜索
    
    console.log('AI回复（含网络搜索）:', response)
    showToast('成功', 'AI回复已生成（含最新信息）')
    
    return response
  } catch (error) {
    console.error('网络搜索聊天失败:', error)
    showToast('错误', '聊天请求失败')
    throw error
  }
}

/**
 * 示例3: 上传文档并进行索引
 */
export const uploadDocumentExample = async (file: File) => {
  try {
    showToast('提示', '开始上传文档...')
    
    // 上传文档
    const uploadResult = await aiApi.uploadDocument(file)
    console.log('文档上传成功，文件ID:', uploadResult.file_id)
    showToast('成功', `文档上传成功，文件ID: ${uploadResult.file_id}`)
    
    // 查询索引状态
    showToast('提示', '开始索引文档...')
    const finalStatus = await aiApi.waitForIndexCompletion(uploadResult.file_id)
    
    if (finalStatus.status === 'completed') {
      showToast('成功', '文档索引完成')
      console.log('文档索引完成')
    } else {
      showToast('错误', '文档索引失败')
      throw new Error(`文档索引失败: ${finalStatus.status}`)
    }
    
    return uploadResult.file_id
  } catch (error) {
    console.error('文档上传失败:', error)
    showToast('错误', '文档上传或索引失败')
    throw error
  }
}

/**
 * 示例4: 基于文档的聊天
 */
export const documentChatExample = async (fileId: string) => {
  try {
    const prompt = "请总结这个文档的主要内容"
    const response = await aiApi.chatWithDocument(prompt, fileId, 'pdf')
    
    console.log('基于文档的AI回复:', response)
    showToast('成功', '文档分析完成')
    
    return response
  } catch (error) {
    console.error('基于文档的聊天失败:', error)
    showToast('错误', '文档分析失败')
    throw error
  }
}

/**
 * 示例5: 完整的文档上传和聊天流程
 */
export const uploadAndChatExample = async (file: File) => {
  try {
    showToast('提示', '开始处理文档...')
    
    const prompt = "请分析这个文档的主要观点，并提供一个简要总结"
    const response = await aiApi.uploadAndChat(file, prompt, false)
    
    console.log('文档分析结果:', response)
    showToast('成功', '文档分析完成')
    
    return response
  } catch (error) {
    console.error('文档上传和聊天失败:', error)
    showToast('错误', '文档处理失败')
    throw error
  }
}

/**
 * 示例6: 检查API服务状态
 */
export const checkApiStatusExample = async () => {
  try {
    // 健康检查
    const health = await aiApi.health()
    console.log('API健康状态:', health)
    
    // 获取服务器信息
    const serverInfo = await aiApi.getServerInfo()
    console.log('服务器信息:', serverInfo)
    
    // 获取配置信息
    const config = await aiApi.getConfig()
    console.log('配置信息:', config)
    
    showToast('成功', 'API服务状态正常')
    
    return { health, serverInfo, config }
  } catch (error) {
    console.error('API状态检查失败:', error)
    showToast('错误', 'API服务不可用')
    throw error
  }
}

/**
 * 示例7: 手动控制索引状态检查
 */
export const manualIndexStatusExample = async (fileId: string) => {
  try {
    let attempts = 0
    const maxAttempts = 10
    
    while (attempts < maxAttempts) {
      const status = await aiApi.queryIndexStatus(fileId)
      console.log(`索引状态检查 (${attempts + 1}/${maxAttempts}):`, status)
      
      if (status.status === 'completed') {
        showToast('成功', '文档索引完成')
        return status
      } else if (status.status === 'failed') {
        showToast('错误', '文档索引失败')
        throw new Error('文档索引失败')
      }
      
      // 等待2秒后重试
      await new Promise(resolve => setTimeout(resolve, 2000))
      attempts++
    }
    
    throw new Error('索引状态检查超时')
  } catch (error) {
    console.error('索引状态检查失败:', error)
    showToast('错误', '索引状态检查失败')
    throw error
  }
}

/**
 * 示例8: 使用类式API（与函数式API等效）
 */
export const classApiExample = async () => {
  try {
    // 使用类的静态方法
    const prompt = "解释一下深度学习的基本概念"
    const response = await AIApiService.simpleChat(prompt, false)
    
    console.log('类式API调用结果:', response)
    showToast('成功', '类式API调用成功')
    
    return response
  } catch (error) {
    console.error('类式API调用失败:', error)
    showToast('错误', '类式API调用失败')
    throw error
  }
}

/**
 * 示例9: 错误处理最佳实践
 */
export const errorHandlingExample = async (file: File) => {
  try {
    // 先检查API状态
    await aiApi.health()
    
    // 然后处理文件
    const fileId = await aiApi.uploadDocument(file)
    
    // 等待索引完成，使用较短的超时时间
    const status = await aiApi.waitForIndexCompletion(fileId.file_id, 15, 3000)
    
    if (status.status === 'completed') {
      const response = await aiApi.chatWithDocument(
        "请简要概述文档内容",
        fileId.file_id,
        'pdf'
      )
      return response
    }
    
  } catch (error) {
    // 根据错误类型进行不同的处理
    const errorMessage = error instanceof Error ? error.message : String(error)
    
    if (errorMessage.includes('网络')) {
      showToast('错误', '网络连接问题，请检查网络设置')
    } else if (errorMessage.includes('索引')) {
      showToast('错误', '文档处理失败，请稍后重试')
    } else if (errorMessage.includes('超时')) {
      showToast('错误', '请求超时，请稍后重试')
    } else {
      showToast('错误', '操作失败，请稍后重试')
    }
    
    console.error('操作失败详情:', error)
    throw error
  }
}

/**
 * 示例10: Vue组合式函数中的使用
 */
export const useAIApiComposable = () => {
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const result = ref<string | null>(null)
  
  const performChat = async (prompt: string, enableWebSearch = false) => {
    isLoading.value = true
    error.value = null
    result.value = null
    
    try {
      const response = await aiApi.simpleChat(prompt, enableWebSearch)
      result.value = response
      return response
    } catch (err) {
      error.value = err instanceof Error ? err.message : '聊天失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }
  
  const performDocumentChat = async (file: File, prompt: string) => {
    isLoading.value = true
    error.value = null
    result.value = null
    
    try {
      const response = await aiApi.uploadAndChat(file, prompt)
      result.value = response
      return response
    } catch (err) {
      error.value = err instanceof Error ? err.message : '文档处理失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }
  
  return {
    isLoading: readonly(isLoading),
    error: readonly(error),
    result: readonly(result),
    performChat,
    performDocumentChat
  }
}

// 导出所有示例函数
export const aiApiExamples = {
  simpleTextChat: simpleTextChatExample,
  webSearchChat: webSearchChatExample,
  uploadDocument: uploadDocumentExample,
  documentChat: documentChatExample,
  uploadAndChat: uploadAndChatExample,
  checkApiStatus: checkApiStatusExample,
  manualIndexStatus: manualIndexStatusExample,
  classApi: classApiExample,
  errorHandling: errorHandlingExample,
  useComposable: useAIApiComposable
} 