# AI API 封装使用文档

本文档介绍如何使用封装好的 ChronNote Server AI API 接口。

## 📁 文件结构

```
src/renderer/src/
├── types/
│   └── aiApi.ts              # AI API 类型定义
└── api/uniform/
    ├── aiApi.ts              # AI API 封装
    ├── aiApi.example.ts      # 使用示例
    └── README.md             # 本文档
```

## 🚀 快速开始

### 1. 导入接口

```typescript
// 方式一：使用函数式接口（推荐）
import { aiApi } from '@renderer/api/uniform/aiApi'

// 方式二：使用类式接口
import { AIApiService } from '@renderer/api/uniform/aiApi'

// 导入类型定义
import type { ChatRequest, ChatResponse } from '@renderer/types/aiApi'
```

### 2. 基本使用

#### 简单文本聊天

```typescript
// 基础聊天
const response = await aiApi.simpleChat("什么是机器学习？")
console.log(response) // AI的回复文本

// 启用网络搜索的聊天
const response = await aiApi.simpleChat("最新的AI发展趋势", true)
```

#### 文档处理

```typescript
// 上传文档并等待索引完成
const fileId = await aiApi.uploadDocument(file)
const status = await aiApi.waitForIndexCompletion(fileId.file_id)

// 基于文档聊天
const response = await aiApi.chatWithDocument(
  "总结文档内容", 
  fileId.file_id, 
  'pdf'
)

// 一站式：上传 + 索引 + 聊天
const response = await aiApi.uploadAndChat(
  file, 
  "分析这个文档的主要观点"
)
```

## 📖 详细接口说明

### 文档相关接口

#### `uploadDocument(file: File)`
上传文档进行索引处理。

```typescript
const result = await aiApi.uploadDocument(file)
// 返回: { file_id: string }
```

#### `queryIndexStatus(fileId: string)`
查询文档索引状态。

```typescript
const status = await aiApi.queryIndexStatus(fileId)
// 返回: { file_id: string, status: 'pending' | 'processing' | 'completed' | 'failed' }
```

#### `waitForIndexCompletion(fileId, maxAttempts?, interval?)`
轮询等待索引完成。

```typescript
const status = await aiApi.waitForIndexCompletion(
  fileId,
  30,    // 最大尝试次数，默认30
  2000   // 轮询间隔(ms)，默认2000
)
```

### 聊天相关接口

#### `chat(chatRequest: ChatRequest)`
完整的AI聊天接口。

```typescript
const response = await aiApi.chat({
  user_prompt: "你好",
  file_type: "pdf",        // 可选
  file_id: "123",          // 可选
  web_search: true         // 可选
})
// 返回完整的 ChatResponse 对象
```

#### `simpleChat(prompt: string, enableWebSearch?: boolean)`
简化的聊天接口，直接返回文本。

```typescript
const text = await aiApi.simpleChat("解释深度学习", false)
// 直接返回 AI 回复的文本内容
```

#### `chatWithDocument(prompt, fileId, fileType?, enableWebSearch?)`
基于特定文档的聊天。

```typescript
const response = await aiApi.chatWithDocument(
  "分析文档",
  "file123",
  "pdf",
  false
)
```

#### `uploadAndChat(file, prompt, enableWebSearch?)`
一站式文档处理和聊天。

```typescript
const response = await aiApi.uploadAndChat(
  file,
  "总结这个文档",
  false
)
```

### 系统信息接口

```typescript
// 健康检查
const health = await aiApi.health()

// 服务器信息
const info = await aiApi.getServerInfo()

// 配置信息
const config = await aiApi.getConfig()
```

## 🎯 使用示例

### Vue 组件中使用

```vue
<template>
  <div>
    <input type="file" @change="handleFileUpload" />
    <button @click="performChat" :disabled="isLoading">
      {{ isLoading ? '处理中...' : '开始聊天' }}
    </button>
    <div v-if="result">{{ result }}</div>
    <div v-if="error" class="error">{{ error }}</div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { aiApi } from '@renderer/api/uniform/aiApi'
import { showToast } from '@renderer/utils/toast'

const isLoading = ref(false)
const result = ref('')
const error = ref('')
const selectedFile = ref<File | null>(null)

const handleFileUpload = (event: Event) => {
  const target = event.target as HTMLInputElement
  selectedFile.value = target.files?.[0] || null
}

const performChat = async () => {
  if (!selectedFile.value) return
  
  isLoading.value = true
  error.value = ''
  
  try {
    const response = await aiApi.uploadAndChat(
      selectedFile.value,
      "请分析这个文档的主要内容"
    )
    result.value = response
    showToast('成功', '文档分析完成')
  } catch (err) {
    error.value = err instanceof Error ? err.message : '处理失败'
    showToast('错误', '文档处理失败')
  } finally {
    isLoading.value = false
  }
}
</script>
```

### 组合式函数

```typescript
import { useAIApiComposable } from '@renderer/api/uniform/aiApi.example'

// 在组件中使用
const { isLoading, error, result, performChat } = useAIApiComposable()

// 执行聊天
await performChat("你好，AI！", true) // 启用网络搜索
```

## ⚙️ 配置说明

### 超时设置

API 使用项目的全局 axios 配置，超时时间为 `API_CONFIG.TIMEOUT`。

### 错误处理

所有接口都包含了完整的错误处理：

- 网络错误自动重试
- 业务错误显示具体信息
- 支持自定义错误处理逻辑

### 认证

API 自动使用当前用户的认证令牌（通过 axios 拦截器处理）。

## 🔧 高级用法

### 自定义错误处理

```typescript
try {
  const response = await aiApi.simpleChat("测试")
} catch (error) {
  if (error.message.includes('网络')) {
    // 处理网络错误
  } else if (error.message.includes('超时')) {
    // 处理超时错误
  }
  // 其他错误处理
}
```

### 批量文档处理

```typescript
const processMultipleFiles = async (files: File[]) => {
  const results = await Promise.allSettled(
    files.map(file => 
      aiApi.uploadAndChat(file, "总结文档内容")
    )
  )
  
  results.forEach((result, index) => {
    if (result.status === 'fulfilled') {
      console.log(`文件 ${index + 1} 处理成功:`, result.value)
    } else {
      console.error(`文件 ${index + 1} 处理失败:`, result.reason)
    }
  })
}
```

### 流式处理监控

```typescript
const processWithProgress = async (file: File) => {
  // 上传文档
  showToast('提示', '正在上传文档...')
  const uploadResult = await aiApi.uploadDocument(file)
  
  // 监控索引进度
  showToast('提示', '正在索引文档...')
  let attempts = 0
  const maxAttempts = 30
  
  while (attempts < maxAttempts) {
    const status = await aiApi.queryIndexStatus(uploadResult.file_id)
    
    if (status.status === 'completed') {
      showToast('成功', '文档索引完成')
      break
    } else if (status.status === 'failed') {
      throw new Error('文档索引失败')
    }
    
    // 显示进度
    const progress = Math.round((attempts + 1) / maxAttempts * 100)
    showToast('提示', `索引进度: ${progress}%`)
    
    await new Promise(resolve => setTimeout(resolve, 2000))
    attempts++
  }
  
  // 执行聊天
  showToast('提示', '正在分析文档...')
  const result = await aiApi.chatWithDocument(
    "分析文档内容",
    uploadResult.file_id
  )
  
  showToast('成功', '分析完成')
  return result
}
```

## 📝 注意事项

1. **文件大小限制**：请确保上传的文件大小在服务器允许范围内
2. **网络连接**：某些操作可能需要较长时间，请确保网络连接稳定
3. **错误重试**：对于网络错误，建议实现重试机制
4. **内存管理**：处理大文件时注意内存使用情况
5. **用户体验**：长时间操作建议显示进度提示

## 🔍 故障排除

### 常见问题

**Q: 文档上传失败**
A: 检查文件格式是否支持，文件大小是否超限

**Q: 索引一直处于 pending 状态**
A: 服务器可能繁忙，建议稍后重试或联系管理员

**Q: 聊天响应慢**
A: 可能是网络问题或服务器负载较高，可以启用网络搜索加速响应

**Q: API 调用失败**
A: 检查网络连接和用户认证状态

### 调试技巧

```typescript
// 启用详细日志
console.log('API 调用开始')
try {
  const result = await aiApi.simpleChat("测试")
  console.log('API 调用成功:', result)
} catch (error) {
  console.error('API 调用失败:', error)
}

// 检查 API 状态
const health = await aiApi.health()
console.log('API 健康状态:', health)
```

## 📚 更多资源

- [API 使用示例](./aiApi.example.ts)
- [类型定义](../types/aiApi.ts)
- [项目 axios 配置](../utils/axios/index.ts) 