<template>
  <div
    class="relative w-full flex flex-col items-center justify-center h-full text-center empty-state overflow-auto px-4 scrollbar-gutter scrollbar-thin scrollbar-track-transparent scrollbar-thumb-transparent hover:scrollbar-thumb-base-content/25"
  >
    <div class="z-10 w-full max-w-md flex flex-col items-center text-center">
      <!-- 图标 - 更现代简约的设计 -->
      <div class="mb-6 inline-flex items-center justify-center w-24 h-24">
        <img src="@renderer/assets/en1.svg" class="w-20 h-20 text-primary/90" alt="Chron Engine" />
      </div>

      <!-- 标题和描述 - 更精致的排版 -->
      <h2 class="text-2xl font-medium text-base-content mb-2">Chron Engine</h2>
      <p class="text-base-content/70 text-base mb-6 max-w-md mx-auto leading-relaxed">
        您可以向 Chron Engine AI 提问任何问题，或请求帮助完成各种任务。
      </p>

      <!-- 功能提示卡片 - 恢复三行布局 -->
      <div class="grid grid-cols-1 gap-2 w-full max-w-xs mx-auto mb-4">
        <div
          class="p-2 rounded-lg border border-base-300/20 bg-base-background hover:bg-base-100 transition-all duration-200 cursor-pointer flex items-center group"
        >
          <div
            class="w-8 h-8 rounded-md bg-primary/5 flex items-center justify-center mr-3 group-hover:bg-primary/10 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 text-primary/80"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.5"
                d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <div class="flex flex-col items-start text-left">
            <span class="text-sm font-medium text-base-content">提问任何问题</span>
            <span class="text-xs text-base-content/60">向AI提问，获取即时回答</span>
          </div>
        </div>

        <div
          class="p-2 rounded-lg border border-base-300/20 bg-base-background hover:bg-base-100 transition-all duration-200 cursor-pointer flex items-center group"
        >
          <div
            class="w-8 h-8 rounded-md bg-primary/5 flex items-center justify-center mr-3 group-hover:bg-primary/10 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 text-primary/80"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.5"
                d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"
              />
            </svg>
          </div>
          <div class="flex flex-col items-start text-left">
            <span class="text-sm font-medium text-base-content">使用@联动笔记</span>
            <span class="text-xs text-base-content/60">引用笔记内容进行对话</span>
          </div>
        </div>

        <div
          class="p-2 rounded-lg border border-base-300/20 bg-base-background hover:bg-base-100 transition-all duration-200 cursor-pointer flex items-center group"
        >
          <div
            class="w-8 h-8 rounded-md bg-primary/5 flex items-center justify-center mr-3 group-hover:bg-primary/10 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 text-primary/80"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.5"
                d="M13 10V3L4 14h7v7l9-11h-7z"
              />
            </svg>
          </div>
          <div class="flex flex-col items-start text-left">
            <span class="text-sm font-medium text-base-content">快速操作</span>
            <span class="text-xs text-base-content/60">保存回答，复制内容</span>
          </div>
        </div>
      </div>

      <!-- 版本提示 - 更紧凑的设计 -->
      <div
        class="inline-flex items-center gap-1.5 px-2 py-1.5 rounded-md bg-base-100/50 border border-base-300/20 text-[10px] text-base-content/60"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-3 w-3"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
        <span>Chron Engine</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  hideTabs?: boolean
}>()
</script>

<style scoped>
/* 背景网格图案 */
.bg-grid-pattern {
  background-size: 40px 40px;
  background-image:
    linear-gradient(to right, var(--base-content) 1px, transparent 1px),
    linear-gradient(to bottom, var(--base-content) 1px, transparent 1px);
}
</style>
