<template>
  <div
    class="flex-1 overflow-y-auto scrollbar-thin scrollbar-track-transparent scrollbar-thumb-transparent hover:scrollbar-thumb-base-content/25 scrollbar-thumb-rounded-full ai-conversation-container relative"
    ref="conversationContainer"
  >
    <div class="max-w-4xl mx-auto px-6 py-4" :class="{ 'max-w-full px-4': hideTabs }">
      <!-- 对话内容 -->
      <div class="space-y-8 py-2 w-full">
        <ChronEngineMessage
          v-for="(message, index) in conversation"
          :key="index"
          :message="message"
          :index="index"
          :is-last="index === conversation.length - 1"
          :is-processing="isProcessing"
          :menu-items="menuItems"
          @copy-message="$emit('copyMessage', index)"
          @copy-ai-message="$emit('copyAiMessage', message.content)"
          @delete-message="$emit('deleteMessage', index)"
          @save-to-note="$emit('saveToNote', message.content)"
          @share-message="$emit('shareMessage', message.content)"
        />

        <!-- 清除对话按钮 -->
        <div v-if="conversation.length > 0 && !isProcessing" class="flex justify-center mt-4">
          <Button
            @click="$emit('clearConversation')"
            variant="ghost"
            size="sm"
            class="gap-2 text-base-content/60 hover:text-base-content hover:bg-base-200/50"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M3 6h18"></path>
              <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
              <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
            </svg>
            <span class="text-xs">清除对话</span>
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'
import type { AIMessage, MenuItem } from '@renderer/types/chronEngine'
import { Button } from '@renderer/components/ui/button'
import ChronEngineMessage from './ChronEngineMessage.vue'

interface Props {
  conversation: AIMessage[]
  isProcessing: boolean
  hideTabs?: boolean
  menuItems: MenuItem[]
}

interface Emits {
  copyMessage: [index: number]
  copyAiMessage: [content: string]
  deleteMessage: [index: number]
  saveToNote: [content: string]
  shareMessage: [content: string]
  clearConversation: []
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const conversationContainer = ref<HTMLElement | null>(null)

// 滚动到底部
const scrollToBottom = async () => {
  await nextTick()
  if (conversationContainer.value) {
    conversationContainer.value.scrollTop = conversationContainer.value.scrollHeight
  }
}

// 监听对话变化自动滚动
watch(
  () => props.conversation.length,
  () => {
    scrollToBottom()
  }
)

// 监听最后一条消息内容变化
watch(
  () => {
    if (props.conversation.length > 0) {
      return props.conversation[props.conversation.length - 1].content
    }
    return ''
  },
  () => {
    scrollToBottom()
  }
)

// 暴露滚动方法给父组件
defineExpose({
  scrollToBottom,
  container: conversationContainer
})
</script>

<style scoped>
.ai-conversation-container {
  height: calc(100% - 120px);
  scroll-behavior: smooth;
}
</style>
