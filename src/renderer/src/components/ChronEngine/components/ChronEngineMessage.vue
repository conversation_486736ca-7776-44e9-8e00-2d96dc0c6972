<template>
  <div
    class="message-container w-full"
    :class="{ 'animate-fade-in': isLast }"
  >
    <!-- 用户消息 - 右侧显示 -->
    <template v-if="message.role === 'user'">
      <div class="flex flex-col items-end mb-4 w-full">
        <div class="text-sm text-primary font-medium user-message-container">
          <MarkdownRenderer :content="message.formattedContent || message.content" />
        </div>
      </div>
    </template>

    <!-- AI助手消息 - 左侧显示 -->
    <template v-else>
      <div class="flex flex-col items-start mb-4 w-full">
        <div class="flex items-start w-full">
          <GlobalPaletteContextMenu
            @copyMessage="$emit('copyAiMessage', message.content)"
            @deleteMessage="$emit('deleteMessage', index)"
            @saveToNote="$emit('saveToNote', message.content)"
            @shareMessage="$emit('shareMessage', message.content)"
            :items="menuItems"
            :includeDefaultItems="false"
            class="w-full"
          >
            <div class="text-sm ai-message-container w-full">
              <!-- 思考中状态 -->
              <div v-if="message.isThinking" class="w-full">
                <AIThinkingStepper :thinkingContent="message.thinkingContent" />
              </div>

              <!-- 思考完成状态 - 显示思考结果和内容 -->
              <div v-else-if="message.thinkingCompleted" class="w-full">
                <!-- 先显示思考结果 -->
                <AIThinkingStepper
                  :thinkingContent="message.thinkingContent"
                  :completed="true"
                />
                <!-- 内容为空时显示占位动画 -->
                <div
                  v-if="!message.content"
                  class="w-full mt-4 flex items-center justify-center"
                >
                  <div class="typing-indicator">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
                <!-- 否则显示回答内容 -->
                <div v-else class="w-full mt-4">
                  <MarkdownRenderer :content="message.content" />
                </div>
              </div>

              <!-- 工具调用加载器 -->
              <div v-else-if="message.isProcessingToolCall" class="w-full">
                <MarkdownRenderer :content="message.content" />
                <ToolCallingLoader :toolCode="message.toolCode" />
              </div>

              <!-- 正常内容显示 -->
              <div v-else class="w-full">
                <!-- 如果有思考内容但不在思考状态，先显示思考内容再显示正常内容 -->
                <div
                  v-if="message.thinkingContent && !message.isThinking"
                  class="w-full mb-4"
                >
                  <AIThinkingStepper
                    :thinkingContent="message.thinkingContent"
                    :completed="true"
                  />
                </div>
                <MarkdownRenderer :content="message.content" />
              </div>
            </div>
          </GlobalPaletteContextMenu>
        </div>
        
        <!-- AI标识和操作按钮 -->
        <div class="text-xs text-base-content/50 mt-1 mb-1 flex items-center gap-2">
          <span class="flex items-center gap-1">
            <svg
              class="w-3 h-3 text-primary"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M12 16V12"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M12 8H12.01"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            Chron Engine
          </span>
          
          <!-- 快捷操作按钮 -->
          <div
            class="chat-actions flex gap-2 items-center opacity-0 transition-opacity duration-200 hover:opacity-100"
          >
            <!-- 复制按钮 -->
            <Button
              variant="ghost"
              size="xs"
              title="复制消息"
              @click="$emit('copyMessage', index)"
              class="hover:bg-base-300/50 hover:text-primary p-1"
            >
              <Copy class="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import type { AIMessage, MenuItem } from '@renderer/types/chronEngine'
import { Button } from '@renderer/components/ui/button'
import { Copy } from 'lucide-vue-next'

// 导入子组件
import GlobalPaletteContextMenu from '@renderer/components/Commands/GlobalPaletteContextMenu.vue'
import AIThinkingStepper from '../AIThinkingStepper.vue'
import MarkdownRenderer from '../MarkdownRenderer.vue'
import ToolCallingLoader from '../ToolCallingLoader.vue'

interface Props {
  message: AIMessage
  index: number
  isLast: boolean
  isProcessing: boolean
  menuItems: MenuItem[]
}

interface Emits {
  copyMessage: [index: number]
  copyAiMessage: [content: string]
  deleteMessage: [index: number]
  saveToNote: [content: string]
  shareMessage: [content: string]
}

defineProps<Props>()
defineEmits<Emits>()
</script>

<style scoped>
/* 消息容器样式 */
.message-container {
  position: relative;
  transition: all 0.3s ease;
}

.message-container:hover .chat-actions {
  opacity: 1 !important;
}

/* 用户消息和AI消息共享样式 */
.user-message-container,
.ai-message-container {
  word-break: normal;
  white-space: normal;
  width: 100%;
}

/* 用户消息特定样式 */
.user-message-container {
  min-width: 40px;
  display: inline-block;
  max-width: 90%;
  text-align: right;
}

/* 打字指示器样式 */
.typing-indicator {
  display: flex;
  align-items: center;
  column-gap: 6px;
  padding: 4px 0;
}

.typing-indicator span {
  height: 8px;
  width: 8px;
  background-color: var(--p);
  opacity: 0.4;
  border-radius: 50%;
  display: inline-block;
  animation: typing 1.5s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: 0s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.3s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes typing {
  0% {
    transform: translateY(0px);
    opacity: 0.4;
  }
  50% {
    transform: translateY(-5px);
    opacity: 0.8;
  }
  100% {
    transform: translateY(0px);
    opacity: 0.4;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}
</style>