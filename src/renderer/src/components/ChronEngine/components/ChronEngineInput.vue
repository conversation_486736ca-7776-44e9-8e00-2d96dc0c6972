<template>
  <div
    v-show="!isProcessing && !hideInput"
    class="w-full max-w-4xl mx-auto px-6 pb-5 pt-3 mb-4 relative input-container"
    :class="{ 'max-w-full px-4': hideTabs }"
  >
    <div class="chat-input-wrapper flex flex-col rounded-md border border-base-500/50 focus-within:border-primary/50 transition-all duration-300 backdrop-blur-sm">
      <!-- 编辑器 -->
      <CommandPaletteEditor
        :model-value="editorContent"
        @update:model-value="$emit('update:editorContent', $event)"
        placeholder="@可以引用相关资源"
        @keydown="handleKeyDown"
        ref="editorRef"
        :disabled="isProcessing"
        :paste-handler="pasteHandler"
        class="editor-area"
      />
      
      <!-- 底部控制栏 -->
      <div
        class="flex items-center gap-2 px-3 py-2.5 bg-transparent"
      >
        <!-- 联网搜索按钮 (仅简单模式显示) -->
        <Button
          v-if="selectedMode === 'Simple'"
          variant="ghost"
          size="sm"
          :class="[
            'rounded-full text-xs h-8 px-3 bg-base-100 text-base-content hover:bg-base-200 inline-flex items-center gap-1 shrink-0',
            { 'bg-primary/10 text-primary': selectedDeepSearch }
          ]"
          @click="$emit('toggleDeepSearch')"
        >
          <Search class="w-3.5 h-3.5" />
          联网搜索
        </Button>

        <div class="flex-grow"></div>

        <!-- 模式选择器 -->
        <SelectRoot :model-value="selectedMode" @update:model-value="$emit('updateMode', $event)">
          <SelectTrigger
            :class="[
              'inline-flex items-center justify-between rounded-full text-xs h-8 px-2.5 gap-1 focus:ring-0 transition-all duration-200 min-w-[90px] max-w-[120px]',
              selectedMode === 'Smart'
                ? 'border border-primary/30 bg-primary/10 hover:bg-primary/15 text-primary shadow-sm'
                : 'border border-base-200 bg-base-100 hover:bg-base-200 text-base-content'
            ]"
          >
            <div class="flex items-center gap-1 overflow-hidden">
              <!-- 智能模式图标 -->
              <Brain v-if="selectedMode === 'Smart'" class="w-3 h-3 shrink-0" />
              <!-- 简单模式图标 -->
              <MessageSquare v-else class="w-3 h-3 shrink-0" />
              <span class="truncate">{{
                selectedMode === 'Smart' ? '智能模式' : '简单模式'
              }}</span>
            </div>
            <SelectIcon class="h-3 w-3 shrink-0 ml-1">
              <ChevronDown class="h-3 w-3" />
            </SelectIcon>
          </SelectTrigger>
          <SelectPortal>
            <SelectContent
              class="z-50 min-w-[9rem] overflow-hidden rounded-md border border-base-200 bg-base-background text-base-content shadow-lg backdrop-blur-sm"
              position="popper"
            >
              <SelectViewport class="p-1">
                <SelectGroup>
                  <SelectItem
                    value="Smart"
                    class="relative flex select-none items-center rounded-sm py-2 pl-9 pr-3 text-sm outline-none hover:bg-base-100 focus:bg-base-100 data-[highlighted]:bg-base-100 data-[highlighted]:text-primary cursor-pointer"
                  >
                    <SelectItemIndicator class="absolute left-2 inline-flex items-center">
                      <Check class="h-4 w-4 text-primary" />
                    </SelectItemIndicator>
                    <div class="flex items-center gap-2">
                      <Brain class="w-3.5 h-3.5 text-primary" />
                      <div class="flex flex-col">
                        <SelectItemText class="font-medium text-primary">智能模式</SelectItemText>
                        <span class="text-xs text-base-content/60">AI自主调用工具</span>
                      </div>
                    </div>
                  </SelectItem>
                  <SelectItem
                    value="Simple"
                    class="relative flex select-none items-center rounded-sm py-2 pl-9 pr-3 text-sm outline-none hover:bg-base-100 focus:bg-base-100 data-[highlighted]:bg-base-100 data-[highlighted]:text-base-content cursor-pointer"
                  >
                    <SelectItemIndicator class="absolute left-2 inline-flex items-center">
                      <Check class="h-4 w-4" />
                    </SelectItemIndicator>
                    <div class="flex items-center gap-2">
                      <MessageSquare class="w-3.5 h-3.5" />
                      <div class="flex flex-col">
                        <SelectItemText>简单模式</SelectItemText>
                        <span class="text-xs text-base-content/60">基础问答对话</span>
                      </div>
                    </div>
                  </SelectItem>
                </SelectGroup>
              </SelectViewport>
            </SelectContent>
          </SelectPortal>
        </SelectRoot>

        <!-- 发送按钮 -->
        <IconButton
          icon="ArrowUp"
          variant="default"
          size="sm"
          class="rounded-full bg-base-100 hover:bg-base-200"
          @click="$emit('sendMessage')"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import type { JSONContent } from '@tiptap/core'
import type { AIMode } from '@renderer/types/chronEngine'

// 导入UI组件
import { Button } from '@renderer/components/ui/button'
import { IconButton } from '@renderer/components/ui/v2'
import {
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectPortal,
  SelectRoot,
  SelectTrigger,
  SelectIcon,
  SelectItemText,
  SelectItemIndicator,
  SelectViewport
} from 'radix-vue'

// 导入图标
import {
  Search,
  ChevronDown,
  Check,
  Brain,
  MessageSquare
} from 'lucide-vue-next'

// 导入编辑器
import CommandPaletteEditor from '@renderer/components/Commands/CommandPaletteEditor.vue'

interface Props {
  editorContent: JSONContent
  isProcessing: boolean
  hideInput?: boolean
  hideTabs?: boolean
  selectedMode: AIMode
  selectedDeepSearch: boolean
  pasteHandler?: any
}

interface Emits {
  'update:editorContent': [content: JSONContent]
  sendMessage: []
  toggleDeepSearch: []
  updateMode: [mode: AIMode]
  keydown: [event: KeyboardEvent]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const editorRef = ref<InstanceType<typeof CommandPaletteEditor> | null>(null)

/**
 * 处理键盘事件
 */
const handleKeyDown = (e: KeyboardEvent) => {
  if (e.key === 'Enter' && !e.shiftKey && !e.isComposing) {
    e.preventDefault()
    emit('sendMessage')
  }
  emit('keydown', e)
}

/**
 * 聚焦编辑器
 */
const focusEditor = async () => {
  await nextTick()
  editorRef.value?.editor?.commands.focus()
}

/**
 * 清空编辑器
 */
const clearEditor = () => {
  editorRef.value?.editor?.commands.clearContent()
}

/**
 * 插入内容到编辑器
 */
const insertContent = (content: any) => {
  editorRef.value?.editor?.commands.insertContent(content)
}

// 暴露方法给父组件
defineExpose({
  focusEditor,
  clearEditor,
  insertContent,
  editor: editorRef
})
</script>

<style scoped>
/* 输入框容器样式 */
.input-container {
  position: relative;
  z-index: 10;
}

.input-container::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px;
  background: linear-gradient(to top, var(--b1) 40%, transparent);
  pointer-events: none;
  z-index: -1;
}

/* 聊天输入框包装器样式 */
.chat-input-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  position: relative;
  z-index: 20;
  border-radius: 0.75rem;
}

.chat-input-wrapper {
  background-color: var(--v2-input-bg);
}

.chat-input-wrapper :deep(.ProseMirror) {
  padding: 0.75rem 1rem 0.75rem 1rem !important;
  min-height: 2.75rem;
  max-height: 10rem;
  overflow-y: auto;
  border-radius: 0;
  background-color: transparent;
  color: var(--v2-input-text);
  border: none;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  line-height: 1.5;
  scrollbar-width: thin;
  scrollbar-color: var(--v2-scrollbar-thumb) transparent;
}

.chat-input-wrapper :deep(.ProseMirror:focus) {
  outline: none;
  border-color: var(--v2-input-border-focus);
  box-shadow: 0 0 0 2px var(--v2-input-shadow-focus);
}

.chat-input-wrapper :deep(.ProseMirror::-webkit-scrollbar) {
  width: 4px;
  background: transparent;
}

.chat-input-wrapper :deep(.ProseMirror::-webkit-scrollbar-track) {
  background: transparent;
}

.chat-input-wrapper :deep(.ProseMirror::-webkit-scrollbar-thumb) {
  background-color: var(--v2-scrollbar-thumb);
  border-radius: 9999px;
}

.chat-input-wrapper :deep(.ProseMirror:hover::-webkit-scrollbar-thumb) {
  background-color: var(--v2-scrollbar-thumb-hover);
}

.chat-input-wrapper :deep(.ProseMirror p.is-editor-empty:first-child::before) {
  content: attr(data-placeholder);
  color: var(--v2-input-placeholder);
  opacity: 0.7;
  float: left;
  pointer-events: none;
  height: 0;
  font-style: normal;
  font-size: 0.875rem;
}
</style>