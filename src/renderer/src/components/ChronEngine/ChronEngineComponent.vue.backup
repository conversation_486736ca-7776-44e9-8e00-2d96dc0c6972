<template>
  <div
    class="flex w-full flex-col h-[calc(100%-1.5rem)] relative chron-engine-container"
  >
    <!-- 顶部工具栏 - 可通过hideTabs控制显示 -->
    <ChronEngineTabs
      v-if="!hideTabs"
      :panel-id="panel_id"
      :pane-node="node"
      :conversation="aiConversation"
      @close-pane="handleClosePane"
      @split-horizontal="handleSplitHorizontal"
      @split-vertical="handleSplitVertical"
      @clear-conversation="clearConversation"
    />

    <!-- 空状态 - 独立组件，不在滚动容器内 -->
    <ChronEngineEmptyView
      v-if="!aiConversation.length"
      :hide-tabs="hideTabs"
      class="flex-1 relative"
    >
      <!-- 背景网格 - 只应用到内容区域 -->
      <div class="absolute inset-0 bg-grid-pattern opacity-[0.08] pointer-events-none"></div>
    </ChronEngineEmptyView>

    <!-- 中间内容区域 - 对话历史 -->
    <div
      v-else
      class="flex-1 overflow-y-auto scrollbar-thin scrollbar-track-transparent scrollbar-thumb-transparent hover:scrollbar-thumb-base-content/25 scrollbar-thumb-rounded-full ai-conversation-container relative"
      ref="aiConversationContainer"
    >
      <div class="max-w-4xl mx-auto px-6 py-4" :class="{ 'max-w-full px-4': hideTabs }">
        <!-- 对话内容 -->
        <div class="space-y-8 py-2 w-full">
          <div
            v-for="(message, index) in aiConversation"
            :key="index"
            class="message-container w-full"
            :class="{ 'animate-fade-in': index === aiConversation.length - 1 }"
          >
            <!-- 用户消息 - 右侧显示 -->
            <template v-if="message.role === 'user'">
              <div class="flex flex-col items-end mb-4 w-full">
                <div class="text-sm text-primary font-medium user-message-container">
                  <MarkdownRenderer :content="message.formattedContent || message.content" />
                </div>
              </div>
            </template>

            <!-- AI助手消息 - 左侧显示 -->
            <template v-else>
              <div class="flex flex-col items-start mb-4 w-full">
                <div class="flex items-start w-full">
                  <GlobalPaletteContextMenu
                    @copyMessage="copyAiMessage(message.content)"
                    @deleteMessage="deleteAiMessage(index)"
                    @saveToNote="saveAiMessageToNote(message.content)"
                    @shareMessage="shareAiMessage(message.content)"
                    :items="aiChatMenuItems"
                    :includeDefaultItems="false"
                    class="w-full"
                  >
                    <div class="text-sm ai-message-container w-full">
                      <!-- 思考中状态 -->
                      <div v-if="message.isThinking" class="w-full">
                        <AIThinkingStepper :thinkingContent="message.thinkingContent" />
                      </div>

                      <!-- 思考完成状态 - 显示思考结果和内容 -->
                      <div v-else-if="message.thinkingCompleted" class="w-full">
                        <!-- 先显示思考结果 -->
                        <AIThinkingStepper
                          :thinkingContent="message.thinkingContent"
                          :completed="true"
                        />
                        <!-- 内容为空时显示占位动画 -->
                        <div
                          v-if="!message.content"
                          class="w-full mt-4 flex items-center justify-center"
                        >
                          <div class="typing-indicator">
                            <span></span>
                            <span></span>
                            <span></span>
                          </div>
                        </div>
                        <!-- 否则显示回答内容 -->
                        <div v-else class="w-full mt-4">
                          <MarkdownRenderer :content="message.content" />
                        </div>
                      </div>

                      <!-- 工具调用加载器 -->
                      <div v-else-if="message.isProcessingToolCall" class="w-full">
                        <MarkdownRenderer :content="message.content" />
                        <ToolCallingLoader :toolCode="message.toolCode" />
                      </div>

                      <!-- 正常内容显示 -->
                      <div v-else class="w-full">
                        <!-- 如果有思考内容但不在思考状态，先显示思考内容再显示正常内容 -->
                        <div
                          v-if="message.thinkingContent && !message.isThinking"
                          class="w-full mb-4"
                        >
                          <AIThinkingStepper
                            :thinkingContent="message.thinkingContent"
                            :completed="true"
                          />
                        </div>
                        <MarkdownRenderer :content="message.content" />
                      </div>
                    </div>
                  </GlobalPaletteContextMenu>
                </div>
                <!-- AI标识和操作按钮 -->
                <div class="text-xs text-base-content/50 mt-1 mb-1 flex items-center gap-2">
                  <span class="flex items-center gap-1">
                    <svg
                      class="w-3 h-3 text-primary"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M12 16V12"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M12 8H12.01"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                    Chron Engine
                  </span>
                  <!-- 快捷操作按钮 -->
                  <div
                    class="chat-actions flex gap-2 items-center opacity-0 transition-opacity duration-200 hover:opacity-100"
                  >
                    <!-- 保存到笔记按钮 -->
                    <!-- <Button
                      variant="ghost"
                      size="xs"
                      title="保存到笔记"
                      @click="saveAiMessageToNote(message.content)"
                      class="hover:bg-base-300/50 hover:text-primary p-1"
                    >
                      <Save class="w-4 h-4" />
                    </Button> -->
                    <!-- 复制按钮 -->
                    <Button
                      variant="ghost"
                      size="xs"
                      title="复制消息"
                      @click="copyMessage(index)"
                      class="hover:bg-base-300/50 hover:text-primary p-1"
                    >
                      <Copy class="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </template>
          </div>

          <!-- 清除对话按钮 -->
          <div v-if="aiConversation.length > 0 && !isAiProcessing" class="flex justify-center mt-4">
            <Button
              @click="clearConversation"
              variant="ghost"
              size="sm"
              class="gap-2 text-base-content/60 hover:text-base-content hover:bg-base-200/50"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path d="M3 6h18"></path>
                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
              </svg>
              <span class="text-xs">清除对话</span>
            </Button>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部聊天输入框 -->
    <div
      v-show="!isAiProcessing && !hideInput"
      class="w-full max-w-4xl mx-auto px-6 pb-5 pt-3 mb-4 relative input-container"
      :class="{ 'max-w-full px-4': hideTabs }"
    >
      <div class="chat-input-wrapper relative">
        <div
          class="absolute bottom-0 left-0 right-0 z-10 flex items-center gap-2 px-3 pb-2.5 pt-2 bg-transparent rounded-b-md"
        >
          <Button
            v-if="selectedMode === 'Simple'"
            variant="ghost"
            size="sm"
            :class="[
              'rounded-full text-xs h-8 px-3 bg-base-100 text-base-content hover:bg-base-200 inline-flex items-center gap-1 shrink-0',
              { 'bg-primary/10 text-primary': selectedDeepSearch }
            ]"
            @click="selectedDeepSearch = !selectedDeepSearch"
          >
            <Search class="w-3.5 h-3.5" />
            联网搜索
          </Button>

          <div class="flex-grow"></div>

          <SelectRoot v-model="selectedMode">
            <SelectTrigger
              :class="[
                'inline-flex items-center justify-between rounded-full text-xs h-8 px-2.5 gap-1 focus:ring-0 transition-all duration-200 min-w-[90px] max-w-[120px]',
                selectedMode === 'Smart'
                  ? 'border border-primary/30 bg-primary/10 hover:bg-primary/15 text-primary shadow-sm'
                  : 'border border-base-200 bg-base-100 hover:bg-base-200 text-base-content'
              ]"
            >
              <div class="flex items-center gap-1 overflow-hidden">
                <!-- 智能模式图标 -->
                <Brain v-if="selectedMode === 'Smart'" class="w-3 h-3 shrink-0" />
                <!-- 简单模式图标 -->
                <MessageSquare v-else class="w-3 h-3 shrink-0" />
                <span class="truncate">{{
                  selectedMode === 'Smart' ? '智能模式' : '简单模式'
                }}</span>
              </div>
              <SelectIcon class="h-3 w-3 shrink-0 ml-1">
                <ChevronDown class="h-3 w-3" />
              </SelectIcon>
            </SelectTrigger>
            <SelectPortal>
              <SelectContent
                class="z-50 min-w-[9rem] overflow-hidden rounded-md border border-base-200 bg-base-background text-base-content shadow-lg backdrop-blur-sm"
                position="popper"
              >
                <SelectViewport class="p-1">
                  <SelectGroup>
                    <SelectItem
                      value="Smart"
                      class="relative flex select-none items-center rounded-sm py-2 pl-9 pr-3 text-sm outline-none hover:bg-base-100 focus:bg-base-100 data-[highlighted]:bg-base-100 data-[highlighted]:text-primary cursor-pointer"
                    >
                      <SelectItemIndicator class="absolute left-2 inline-flex items-center">
                        <Check class="h-4 w-4 text-primary" />
                      </SelectItemIndicator>
                      <div class="flex items-center gap-2">
                        <Brain class="w-3.5 h-3.5 text-primary" />
                        <div class="flex flex-col">
                          <SelectItemText class="font-medium text-primary">智能模式</SelectItemText>
                          <span class="text-xs text-base-content/60">AI自主调用工具</span>
                        </div>
                      </div>
                    </SelectItem>
                    <SelectItem
                      value="Simple"
                      class="relative flex select-none items-center rounded-sm py-2 pl-9 pr-3 text-sm outline-none hover:bg-base-100 focus:bg-base-100 data-[highlighted]:bg-base-100 data-[highlighted]:text-base-content cursor-pointer"
                    >
                      <SelectItemIndicator class="absolute left-2 inline-flex items-center">
                        <Check class="h-4 w-4" />
                      </SelectItemIndicator>
                      <div class="flex items-center gap-2">
                        <MessageSquare class="w-3.5 h-3.5" />
                        <div class="flex flex-col">
                          <SelectItemText>简单模式</SelectItemText>
                          <span class="text-xs text-base-content/60">基础问答对话</span>
                        </div>
                      </div>
                    </SelectItem>
                  </SelectGroup>
                </SelectViewport>
              </SelectContent>
            </SelectPortal>
          </SelectRoot>

          <IconButton
            icon="ArrowUp"
            variant="default"
            size="sm"
            class="rounded-full bg-base-100 hover:bg-base-200"
            @click="sendAiMessage"
          />
        </div>

        <CommandPaletteEditor
          v-model="aiEditorContent"
          placeholder="@可以引用相关资源"
          @keydown="handleKeyDown"
          ref="aiInputRef"
          :disabled="isAiProcessing"
          :paste-handler="ChronEnginePasteHandler"
          class="rounded-md border border-base-500/50 focus-within:border-primary/50 transition-all duration-300 backdrop-blur-sm"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useBlockCreation } from '@renderer/composables/useBlockCreation'
import { useBlockService } from '@renderer/composables/useBlockService'
import { ChronEnginePasteHandler } from '@renderer/editor/plugins/function/pasteHandler'
import { mitter } from '@renderer/plugins/mitter'
import { usePanelCoordinateStore } from '@renderer/stores/panelCoordinateStore'
import { useDockviewStore } from '@renderer/stores/dockviewStore'
import { useUserStore } from '@renderer/stores/user'
import { CozeMessage, CozeUtils } from '@renderer/utils/CozeUtils'
import { showToast } from '@renderer/utils/toast'
import {
  detectToolCalling,
  parseToolCall,
  processToolCallingInResponse
} from '@renderer/utils/toolCalling'
import { registerAllToolHandlers } from '@renderer/utils/toolCalling/registerTools'
import { JSONContent } from '@tiptap/core'
import {
  ArrowUp,
  Mic,
  Search,
  Copy,
  Save,
  Trash,
  Share,
  ChevronDown,
  Check,
  Brain,
  MessageSquare
} from 'lucide-vue-next'
import TurndownService from 'turndown'
import { computed, nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue'
import CommandPaletteEditor from '../Commands/CommandPaletteEditor.vue'
import type { MenuItem } from '../Commands/GlobalPaletteContextMenu.vue'
import GlobalPaletteContextMenu from '../Commands/GlobalPaletteContextMenu.vue'
import AIThinkingStepper from './AIThinkingStepper.vue'
import ChronEngineEmptyView from './ChronEngineEmptyView.vue'
import ChronEngineTabs from './ChronEngineTabs.vue'
import MarkdownRenderer from './MarkdownRenderer.vue'
import ToolCallingLoader from './ToolCallingLoader.vue'
// 导入 v2 组件库和按钮组件
import { Button } from '@renderer/components/ui/button'
import { IconButton } from '@renderer/components/ui/v2'
import {
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectPortal,
  SelectRoot,
  SelectTrigger,
  SelectValue,
  SelectIcon,
  SelectItemText,
  SelectItemIndicator,
  SelectViewport
} from 'radix-vue'

class TypeWriter {
  private queue: string[] = []
  private isTyping = false
  private frameId: number | null = null
  private lastCharTime = 0
  private readonly typingInterval: number
  private appendChar: (char: string) => void
  public onAllCharsTyped: (() => void) | null = null

  constructor(
    appendChar: (char: string) => void,
    typingInterval = 10, // ms
    onAllCharsTyped: (() => void) | null = null
  ) {
    this.appendChar = appendChar
    this.typingInterval = typingInterval
    this.onAllCharsTyped = onAllCharsTyped
  }

  enqueue(text: string) {
    this.queue.push(...text.split(''))
    if (!this.isTyping) {
      this.type()
    }
  }

  private type = () => {
    this.isTyping = true
    const now = performance.now()

    if (now - this.lastCharTime >= this.typingInterval) {
      if (this.queue.length > 0) {
        const char = this.queue.shift()!
        this.appendChar(char)
        this.lastCharTime = now
      }
    }

    if (this.queue.length > 0) {
      this.frameId = requestAnimationFrame(this.type)
    } else {
      this.isTyping = false
      if (this.frameId) {
        cancelAnimationFrame(this.frameId)
        this.frameId = null
      }
      if (this.onAllCharsTyped) {
        this.onAllCharsTyped()
      }
    }
  }

  stop() {
    if (this.frameId) {
      cancelAnimationFrame(this.frameId)
      this.frameId = null
    }
    this.isTyping = false
    // 不清除队列，允许稍后继续
  }

  clear() {
    this.stop()
    this.queue = []
  }

  get isBusy(): boolean {
    return this.isTyping || this.queue.length > 0
  }
}

const props = defineProps<{
  node: any // 使用 any 类型避免类型检查错误
  panel_id: string
  hideInput?: boolean // 是否隐藏内置输入框
  hideTabs?: boolean // 是否隐藏顶部功能栏
  externalInput?: string // 外部输入内容
}>()
const userStore = useUserStore()
const selectedMode = ref('Smart')
const selectedDeepSearch = ref(false)

const emit = defineEmits(['close-pane', 'split-horizontal', 'split-vertical', 'send-message'])

// 添加hideInput和hideTabs的计算属性
const hideInput = computed(() => props.hideInput || false)
const hideTabs = computed(() => props.hideTabs || false)

// AI 对话相关状态
const aiEditorContent = ref<JSONContent>({
  type: 'doc',
  content: [
    {
      type: 'paragraph',
      content: []
    }
  ]
})
const aiPrompt = ref('')
const aiConversation = ref<
  {
    role: 'user' | 'assistant'
    content: string
    formattedContent?: string
    isProcessingToolCall?: boolean
    toolCode?: string
    isThinking?: boolean
    thinkingContent?: string
    thinkingCompleted?: boolean
  }[]
>([])
const currentAiRequest = ref<(() => void) | null>(null)
const isAiProcessing = ref(false)
const currentConversationId = ref<string>('')
const aiConversationContainer = ref<HTMLElement | null>(null)
const aiInputRef = ref<InstanceType<typeof CommandPaletteEditor> | null>(null)
const isUserScrolling = ref(false)
const lastScrollTop = ref(0)

// 本地存储上传的图片与Coze文件ID的映射关系
const imageCozeFileIdMap = new Map<string, string>()

// 获取图片URL对应的Coze文件ID
const getCozeFileIdForImage = (imageUrl: string): string | undefined => {
  return imageCozeFileIdMap.get(imageUrl)
}

// 获取编辑器内容中所有图片的Coze文件ID
const getAllCozeFileIds = (): string[] => {
  return Array.from(imageCozeFileIdMap.values())
}

// 上下文菜单项
const aiChatMenuItems = ref<MenuItem[]>([
  {
    id: 'copy',
    label: '复制消息',
    icon: Copy,
    action: () => {} // 这里使用空函数，实际操作由GlobalPaletteContextMenu组件的emit处理
  },
  // {
  //   id: 'save',
  //   label: '保存到笔记',
  //   icon: Save,
  //   action: () => {} // 这里使用空函数，实际操作由GlobalPaletteContextMenu组件的emit处理
  // },
  {
    id: 'delete',
    label: '删除消息',
    icon: Trash,
    action: () => {} // 这里使用空函数，实际操作由GlobalPaletteContextMenu组件的emit处理
  },
  {
    id: 'share',
    label: '分享消息',
    icon: Share,
    action: () => {} // 这里使用空函数，实际操作由GlobalPaletteContextMenu组件的emit处理
  }
])

// 添加setAIPrompt方法来处理serializedContent更新
const setAIPrompt = async (serializedContent: JSONContent | undefined) => {
  if (!serializedContent) {
    aiPrompt.value = ''
    return {
      displayContent: '',
      aiPrompt: '',
      files: []
    }
  }
  aiPrompt.value = ''
  let displayContent = ''
  const files: { type: string; transfer_method: string; url: string }[] = []

  // 处理内容
  if (serializedContent.content) {
    for (const node of serializedContent.content) {
      console.log('node', node)
      if (node.type === 'paragraph' && node.content) {
        for (const innerNode of node.content) {
          if (innerNode.type === 'text') {
            aiPrompt.value += innerNode.text
            displayContent += innerNode.text
          } else if (innerNode.type === 'image' || innerNode.type === 'simpleImage') {
            // 处理图片
            const src = innerNode.attrs?.src as string
            if (src) {
              // 检查是否有对应的Coze文件ID
              const cozeFileId = getCozeFileIdForImage(src)

              // 添加到文件数组，使用新的格式
              files.push({
                type: 'image',
                transfer_method: 'remote_url',
                url: src
              })

              // 如果有Coze文件ID，记录在控制台
              if (cozeFileId) {
                console.log(`Image ${src} has Coze file ID: ${cozeFileId}`)
              }
            }
            aiPrompt.value += '[图片]'
            displayContent += '[图片]'
          } else if (innerNode.type === 'mention') {
            // 查询对应的 markdown 内容
            const content = await useBlockService().getBlockByUuid(innerNode.attrs?.id)
            const turndownService = new TurndownService()
            // 确保内容存在，否则使用空字符串
            const contents = turndownService.turndown(content?.contents || '')

            // 为 AI 准备完整内容
            aiPrompt.value += `用户提到了文章：${content?.title || '未知文章'},它的内容是：<quote>${contents}</quote>`
            // 在显示版本中保留 @ 格式
            displayContent += `@${content?.title || innerNode.attrs?.id}`
          }
        }
      }
    }
  }

  return {
    displayContent,
    aiPrompt: aiPrompt.value,
    files
  }
}

// 处理键盘事件
const handleKeyDown = (e: KeyboardEvent) => {
  if (e.key === 'Enter' && !e.shiftKey && !e.isComposing) {
    sendAiMessage()
  }
  // 这里可以添加其他键盘事件的处理逻辑
}

// 滚动到AI对话底部
const scrollToAIBottom = async () => {
  await nextTick()
  if (aiConversationContainer.value && !isUserScrolling.value) {
    aiConversationContainer.value.scrollTop = aiConversationContainer.value.scrollHeight
  }
}

// 处理用户滚动
const handleScroll = () => {
  if (!aiConversationContainer.value) return

  const { scrollTop, scrollHeight, clientHeight } = aiConversationContainer.value
  const isScrollingUp = scrollTop < lastScrollTop.value
  const isNotAtBottom = scrollHeight - scrollTop - clientHeight > 50 // 50px的阈值

  // 如果用户向上滚动或距离底部较远，标记为用户滚动
  isUserScrolling.value = isScrollingUp || isNotAtBottom

  // 如果滚动到底部附近，重置用户滚动标记
  if (!isNotAtBottom) {
    isUserScrolling.value = false
  }

  lastScrollTop.value = scrollTop
}

// 监听AI对话内容变化自动滚动到底部
watch(
  () => aiConversation.value.length,
  () => {
    scrollToAIBottom()
  }
)

// 处理外部消息发送
const sendExternalMessage = async (message: string) => {
  console.log('sendExternalMessage called with:', message)
  console.log('isAiProcessing.value:', isAiProcessing.value)

  if (!message || message.trim().length === 0 || isAiProcessing.value) {
    console.log('Message rejected - empty or processing')
    return
  }

  console.log('Adding user message to conversation')
  // 添加用户消息
  aiConversation.value.push({
    role: 'user',
    content: message.trim(),
    formattedContent: message.trim()
  })

  scrollToAIBottom()

  const promptText = message.trim()
  isAiProcessing.value = true
  console.log('Starting AI processing with prompt:', promptText)

  // 继续使用现有的AI处理逻辑
  await processAiRequest(promptText, [])
}

// 发送AI消息
const sendAiMessage = async () => {
  // 获取显示版本的消息内容和文件数组
  const { displayContent, aiPrompt: a, files } = await setAIPrompt(aiEditorContent.value)
  if (aiPrompt.value.trim().length === 0 && files.length === 0) return
  aiPrompt.value = a

  if (
    ((!aiPrompt || aiPrompt.value.trim().length === 0) && files.length === 0) ||
    isAiProcessing.value
  ) {
    return
  }

  // 添加用户消息
  aiConversation.value.push({
    role: 'user',
    content: aiPrompt.value,
    formattedContent: displayContent
  })

  scrollToAIBottom()

  const promptText = aiPrompt.value
  aiPrompt.value = ''
  // 使用编辑器命令清空内容
  aiInputRef.value?.editor?.commands.clearContent()
  isAiProcessing.value = true

  // 继续使用现有的AI处理逻辑
  await processAiRequest(promptText, files)
}

// 处理AI请求的核心逻辑
const processAiRequest = async (promptText: string, files: any[]) => {
  // 根据选择的模式决定是否启用思考状态
  const enableThinking = selectedMode.value === 'Smart'

  const tempResponseIndex =
    aiConversation.value.push({
      role: 'assistant',
      content: '', // 初始化为空
      formattedContent: '',
      isThinking: enableThinking,
      thinkingContent: '',
      thinkingCompleted: false,
      isProcessingToolCall: false,
      toolCode: undefined
    }) - 1

  scrollToAIBottom()

  // 为当前AI消息创建一个TypeWriter实例
  let currentTypewriter: TypeWriter | null = null
  const resetAndCreateTypewriter = (onAllCharsTypedCallback?: () => void): TypeWriter => {
    if (currentTypewriter) {
      currentTypewriter.clear()
    }
    currentTypewriter = new TypeWriter(
      (char: string) => {
        // 确保在更新之前 thinkingCompleted 为 false
        if (aiConversation.value[tempResponseIndex]) {
          // 简单模式下直接添加到内容
          if (!enableThinking) {
            aiConversation.value[tempResponseIndex].content += char
          } else {
            // 智能模式下的复杂逻辑
            // 如果 thinkingCompleted 为 true，表示思考内容已完全显示，现在开始显示实际内容
            if (aiConversation.value[tempResponseIndex].thinkingCompleted) {
              aiConversation.value[tempResponseIndex].content += char
            } else if (aiConversation.value[tempResponseIndex].isThinking) {
              // 如果仍在思考阶段，但是 thinkingContent 已经包含结束标签，说明思考内容已完整
              // 此时不应该再通过打字机添加字符到 thinkingContent
              // 而是等待 isThinking 变为 false， thinkingCompleted 变为 true 后，将字符添加到 content
              // 这里暂时不追加，等待 thinkingCompleted 状态切换
            } else {
              // 如果不在思考状态且思考未完成，也添加到内容（用于处理非思考内容）
              aiConversation.value[tempResponseIndex].content += char
            }
          }
        }
      },
      10, // 打字间隔
      onAllCharsTypedCallback
    )
    return currentTypewriter!
  }

  resetAndCreateTypewriter(() => {
    // 所有字符打印完毕后的回调
    // isAiProcessing.value = false; // 移动到 CozeUtils.chat 的 onCompleted 中管理
    nextTick(() => {
      aiInputRef.value?.editor?.commands.focus()
    })
  })

  try {
    // 不需要重复设置思考状态，因为上面已经设置了
    // aiConversation.value[tempResponseIndex].isThinking = true // 初始设置为思考中
    // aiConversation.value[tempResponseIndex].thinkingCompleted = false
    aiConversation.value[tempResponseIndex].content = ''

    const historyMessages = aiConversation.value.slice(0, -1).map((msg) => ({
      role: msg.role,
      content: msg.content
    })) as CozeMessage[]

    if (!currentConversationId.value) {
      try {
        const conversation = await CozeUtils.createConversation({
          botId: '7505382249266233370',
          metaData: { source: 'ChronEngine' }
        })
        currentConversationId.value = conversation.id
        console.log('创建了新的会话:', currentConversationId.value)
      } catch (error) {
        console.error('创建会话失败:', error)
      }
    }

    const cozeFileIds = getAllCozeFileIds()
    console.log('Using Coze file IDs for images:', cozeFileIds)

    let accumulatedRawStream = ''
    let lastProcessedStreamLength = 0 // 用于跟踪已处理的流的长度，避免重复处理
    let thinkingContentBuffer = '' // 用于缓冲思考内容，直到遇到结束标签
    let inThinkingBlock = false // 标记是否在 <thinking>...</thinking> 块内

    currentAiRequest.value = await CozeUtils.chat(promptText, {
      messages: historyMessages.slice(0, -1),
      conversationId: currentConversationId.value,
      parameters: {
        TOKEN: userStore.token,
        IMAGE:
          cozeFileIds.length > 0
            ? cozeFileIds.map((id) => JSON.stringify({ file_id: id }))
            : undefined,
        HISTORY_MESSAGES: historyMessages,
        AUTO: selectedMode.value === 'Smart',
        WEBSEARCH: selectedMode.value === 'Simple' && selectedDeepSearch.value
      },
      onStart: () => {
        console.log('开始处理请求')
        // 根据模式初始化思考状态
        aiConversation.value[tempResponseIndex].isThinking = enableThinking
        aiConversation.value[tempResponseIndex].thinkingCompleted = false
        aiConversation.value[tempResponseIndex].content = ''
        aiConversation.value[tempResponseIndex].thinkingContent = ''
        lastProcessedStreamLength = 0
        accumulatedRawStream = ''
        thinkingContentBuffer = ''
        inThinkingBlock = false

        // 清空文件ID映射
        imageCozeFileIdMap.clear()
        console.log('已清空文件ID映射')
      },
      onText: async (chunk) => {
        accumulatedRawStream += chunk
        let streamToProcess = accumulatedRawStream.substring(lastProcessedStreamLength)

        const thinkingStartTag = '<thinking>'
        const thinkingEndTag = '</thinking>'
        const stepStartTag = '<step>'
        const stepEndTag = '</step>'

        let currentPos = 0
        let textToType = ''

        while (currentPos < streamToProcess.length) {
          if (!inThinkingBlock) {
            const thinkStartIdx = streamToProcess.indexOf(thinkingStartTag, currentPos)
            const toolCallMarkerIdx = detectToolCalling(streamToProcess.substring(currentPos))
            const actualToolCallMarkerIdx =
              toolCallMarkerIdx !== -1 ? currentPos + toolCallMarkerIdx : -1

            let nextSpecialIdx = -1
            if (thinkStartIdx !== -1 && actualToolCallMarkerIdx !== -1) {
              nextSpecialIdx = Math.min(thinkStartIdx, actualToolCallMarkerIdx)
            } else if (thinkStartIdx !== -1) {
              nextSpecialIdx = thinkStartIdx
            } else if (actualToolCallMarkerIdx !== -1) {
              nextSpecialIdx = actualToolCallMarkerIdx
            }

            if (nextSpecialIdx !== -1) {
              textToType += streamToProcess.substring(currentPos, nextSpecialIdx)
              currentPos = nextSpecialIdx
            } else {
              textToType += streamToProcess.substring(currentPos)
              currentPos = streamToProcess.length
            }

            if (textToType) {
              // 简单模式下直接输出所有文本，不需要复杂的思考状态判断
              if (!enableThinking) {
                currentTypewriter?.enqueue(textToType)
              } else {
                // 智能模式下的复杂判断逻辑
                // 如果当前是 thinkingCompleted 状态，说明思考内容已渲染完，这些文本是实际内容
                if (
                  aiConversation.value[tempResponseIndex] &&
                  aiConversation.value[tempResponseIndex].thinkingCompleted
                ) {
                  currentTypewriter?.enqueue(textToType)
                } else if (
                  aiConversation.value[tempResponseIndex] &&
                  !aiConversation.value[tempResponseIndex].isThinking
                ) {
                  // 如果 isThinking 为 false 且 thinkingCompleted 也为 false，这通常是 tool call 之后的内容
                  currentTypewriter?.enqueue(textToType)
                }
                // 如果 isThinking 为 true，这些文本不应被 enqueue 到主 content，因为它们可能是思考块之外的意外文本
                // 或者在思考块开始之前。思考块内部文本由下面的逻辑处理。
              }
              textToType = ''
            }

            if (currentPos < streamToProcess.length) {
              if (streamToProcess.startsWith(thinkingStartTag, currentPos)) {
                inThinkingBlock = true
                // 无论什么模式，遇到thinking标签都显示思考状态
                aiConversation.value[tempResponseIndex].isThinking = true
                aiConversation.value[tempResponseIndex].thinkingCompleted = false
                thinkingContentBuffer = thinkingStartTag // 开始缓冲
                currentPos += thinkingStartTag.length
              } else if (
                actualToolCallMarkerIdx === currentPos &&
                !aiConversation.value[tempResponseIndex].isProcessingToolCall
              ) {
                // 进入工具调用处理逻辑
                const remainingStreamForToolCall = accumulatedRawStream.substring(
                  lastProcessedStreamLength + currentPos
                )
                const toolCall = parseToolCall(remainingStreamForToolCall)

                if (toolCall) {
                  aiConversation.value[tempResponseIndex].isProcessingToolCall = true
                  aiConversation.value[tempResponseIndex].toolCode = toolCall.code
                  aiConversation.value[tempResponseIndex].isThinking = false // 工具调用时，关闭思考状态
                  aiConversation.value[tempResponseIndex].thinkingCompleted = false // 并重置 thinkingCompleted

                  // 更新 lastProcessedStreamLength 以包含工具调用 JSON 的部分
                  // 注意：这里的长度是相对于 streamToProcess 的，需要转换回 accumulatedRawStream
                  const toolCallJsonStartIndex = lastProcessedStreamLength + currentPos
                  const toolCallJsonEndIndex =
                    accumulatedRawStream.indexOf('```', toolCallJsonStartIndex + 3) + 3 // 找到工具调用JSON块的结束
                  // currentPos 需要跳过整个工具调用标记 + JSON内容
                  currentPos = streamToProcess.length // 假设工具调用是当前chunk的最后部分或需要完整解析

                  try {
                    const result = await processToolCallingInResponse(remainingStreamForToolCall)
                    if (result.hasToolCall) {
                      nextTick(() => {
                        aiConversation.value[tempResponseIndex].isProcessingToolCall = false
                        if (result.error) {
                          currentTypewriter?.enqueue(`\n\n*工具调用失败: ${result.error}*`)
                        } else {
                          currentTypewriter?.enqueue(`\n\n*工具调用成功*`)
                        }
                        // 工具调用处理完后，尝试处理剩余的流
                        // accumulatedRawStream 保持不变，让下一个 onText 事件或此循环的下一轮处理
                      })
                    }
                  } catch (error) {
                    console.error('工具调用处理失败:', error)
                    aiConversation.value[tempResponseIndex].isProcessingToolCall = false
                    currentTypewriter?.enqueue(
                      `\n\n*工具调用处理失败: ${error instanceof Error ? error.message : '未知错误'}*`
                    )
                  }
                } else {
                  // 如果toolCall未解析成功，暂时不移动currentPos，等待更多数据
                  currentPos = streamToProcess.length // 消耗掉当前无法完整解析的部分，避免死循环
                }
              }
            }
          } else {
            // 在 thinking 块内部
            const thinkEndIdx = streamToProcess.indexOf(thinkingEndTag, currentPos)
            if (thinkEndIdx !== -1) {
              // 无论什么模式，都处理思考内容的显示
              thinkingContentBuffer += streamToProcess.substring(
                currentPos,
                thinkEndIdx + thinkingEndTag.length
              )
              aiConversation.value[tempResponseIndex].thinkingContent = thinkingContentBuffer
              aiConversation.value[tempResponseIndex].isThinking = false // 思考结束
              aiConversation.value[tempResponseIndex].thinkingCompleted = true // 标记思考内容已完整显示
              thinkingContentBuffer = '' // 清空缓冲区

              inThinkingBlock = false
              currentPos = thinkEndIdx + thinkingEndTag.length

              // 思考结束后，处理 thinkingEndTag 之后可能紧跟着的实际内容
              const remainingAfterThinking = streamToProcess.substring(currentPos)
              if (remainingAfterThinking) {
                currentTypewriter?.enqueue(remainingAfterThinking)
              }
              currentPos = streamToProcess.length // 确保跳出当前chunk的处理
            } else {
              // 思考内容还未结束，继续缓冲
              thinkingContentBuffer += streamToProcess.substring(currentPos)
              // 提取并更新步骤到 thinkingContent，但不通过打字机
              const steps: string[] = []
              let stepMatch
              const stepRegex = new RegExp(`${stepStartTag}(.*?)${stepEndTag}`, 'gs')
              let tempThinkingContent = thinkingStartTag //总是以thinkingStartTag开头
              while ((stepMatch = stepRegex.exec(thinkingContentBuffer)) !== null) {
                steps.push(stepMatch[1].trim())
                tempThinkingContent += `${stepStartTag}${stepMatch[1].trim()}${stepEndTag}`
              }
              aiConversation.value[tempResponseIndex].thinkingContent = tempThinkingContent

              currentPos = streamToProcess.length
            }
          }
        }
        lastProcessedStreamLength = accumulatedRawStream.length // 更新已处理的流长度

        // 滚动到底部
        nextTick(() => scrollToAIBottom())
      },
      onError: (error) => {
        console.error('Coze API错误:', error)
        const errorMessage = `抱歉，发生了错误: ${error}`
        const currentTypwriterLocal = currentTypewriter as TypeWriter | null
        if (currentTypwriterLocal && currentTypwriterLocal.isBusy) {
          currentTypwriterLocal.clear()
        }
        const newErrorTypewriter = resetAndCreateTypewriter(() => {
          isAiProcessing.value = false
          nextTick(() => aiInputRef.value?.editor?.commands.focus())
        })
        newErrorTypewriter?.enqueue(errorMessage)

        if (aiConversation.value[tempResponseIndex]) {
          aiConversation.value[tempResponseIndex].isThinking = false
          aiConversation.value[tempResponseIndex].thinkingCompleted = false
        }
      },
      onCompleted: () => {
        console.log('对话完成')
        const completeProcessing = () => {
          if (!aiConversation.value[tempResponseIndex]) {
            isAiProcessing.value = false
            return
          }

          if (
            !aiConversation.value[tempResponseIndex]?.content?.trim() &&
            !aiConversation.value[tempResponseIndex]?.thinkingContent?.trim() &&
            !aiConversation.value[tempResponseIndex]?.toolCode
          ) {
            if (currentTypewriter && currentTypewriter.isBusy) {
              currentTypewriter.clear()
            }
            const newEmptyResponseTypewriter = resetAndCreateTypewriter(() => {
              isAiProcessing.value = false
              nextTick(() => aiInputRef.value?.editor?.commands.focus())
            })
            newEmptyResponseTypewriter?.enqueue('抱歉，我无法生成回答。请尝试重新提问。')
          }

          if (aiConversation.value.length === 2 && aiConversation.value[tempResponseIndex]) {
            if (currentTypewriter && currentTypewriter.isBusy) {
              currentTypewriter.onAllCharsTyped = () => {
                isAiProcessing.value = false
                nextTick(() => aiInputRef.value?.editor?.commands.focus())
              }
            } else {
              isAiProcessing.value = false
              nextTick(() => aiInputRef.value?.editor?.commands.focus())
            }
          } else {
            if (currentTypewriter && currentTypewriter.isBusy) {
              currentTypewriter.onAllCharsTyped = () => {
                isAiProcessing.value = false
                nextTick(() => aiInputRef.value?.editor?.commands.focus())
              }
            } else {
              isAiProcessing.value = false
              nextTick(() => aiInputRef.value?.editor?.commands.focus())
            }
          }

          if (aiConversation.value[tempResponseIndex]) {
            if (
              !aiConversation.value[tempResponseIndex].thinkingCompleted &&
              aiConversation.value[tempResponseIndex].thinkingContent
            ) {
              aiConversation.value[tempResponseIndex].thinkingCompleted = true
            }
            aiConversation.value[tempResponseIndex].isThinking = false
          }
        }

        if (currentTypewriter && currentTypewriter.isBusy) {
          currentTypewriter.onAllCharsTyped = completeProcessing
        } else {
          completeProcessing()
        }
      },
      onConversationId: (id) => {
        currentConversationId.value = id
      }
    })
  } catch (error) {
    console.error('发送消息错误:', error)
    const errorMessage = '抱歉，处理您的请求时出现了错误。请检查网络连接或稍后再试。'
    const currentTypwriterLocal = currentTypewriter as TypeWriter | null
    if (currentTypwriterLocal && currentTypwriterLocal.isBusy) {
      currentTypwriterLocal.clear()
    }
    const newCatchErrorTypewriter = resetAndCreateTypewriter(() => {
      isAiProcessing.value = false
      nextTick(() => aiInputRef.value?.editor?.commands.focus())
    })
    newCatchErrorTypewriter?.enqueue(errorMessage)

    if (aiConversation.value[tempResponseIndex]) {
      aiConversation.value[tempResponseIndex].isThinking = false
      aiConversation.value[tempResponseIndex].thinkingCompleted = false
    }

    if (aiConversation.value.length === 2 && aiConversation.value[tempResponseIndex]) {
      const currentTypwriterLocal2 = currentTypewriter as TypeWriter | null
      if (currentTypwriterLocal2 && currentTypwriterLocal2.isBusy) {
        currentTypwriterLocal2.onAllCharsTyped = () => {
          isAiProcessing.value = false
          nextTick(() => aiInputRef.value?.editor?.commands.focus())
        }
      } else {
        isAiProcessing.value = false
        nextTick(() => aiInputRef.value?.editor?.commands.focus())
      }
    }
  }
}

// 复制消息
const copyMessage = (index: number) => {
  const message = aiConversation.value[index]
  if (message) {
    navigator.clipboard.writeText(message.content)
    showToast('提示', '已复制到剪贴板', 2000)
  }
}

// 复制AI消息
const copyAiMessage = (content: string) => {
  navigator.clipboard.writeText(content)
  showToast('提示', '已复制到剪贴板', 2000)
}

// 删除AI消息
const deleteAiMessage = (index: number) => {
  aiConversation.value.splice(index, 1)
}

// 保存AI消息到笔记
const saveAiMessageToNote = async (content: string) => {
  const { createBlock } = useBlockCreation()
  // 创建一个新的笔记类型区块
  const newNote = await createBlock('note')

  if (newNote) {
    // 使用区块服务更新内容
    const blockService = useBlockService()
    await blockService.updateBlock({
      uuid: newNote.uuid,
      title: '来自Chron Engine的回答',
      contents: content,
      updatedAt: Date.now()
    })
    showToast('提示', '已保存到笔记', 2000)
  }
}

// 分享AI消息
const shareAiMessage = (content: string) => {
  navigator.clipboard.writeText(content)
  showToast('提示', '已复制到剪贴板，可以分享给他人', 2000)
}

// 清空对话
const clearConversation = async () => {
  aiConversation.value = []
  currentConversationId.value = ''

  // 清除本地的Coze文件ID映射
  imageCozeFileIdMap.clear()

  // 创建新的会话
  try {
    const conversation = await CozeUtils.createConversation({
      botId: '7505382249266233370', // 使用默认的botId
      metaData: { source: 'ChronEngine' }
    })

    currentConversationId.value = conversation.id
    console.log('创建了新的会话:', currentConversationId.value)
  } catch (error) {
    console.error('创建会话失败:', error)
  }
}

// 处理水平分屏事件 - 使用 dockview API (左右分屏)
const handleSplitHorizontal = () => {
  const dockviewStore = useDockviewStore()
  const api = dockviewStore.getApi()
  
  if (api && props.panel_id) {
    const currentPanel = api.getPanel(props.panel_id)
    if (currentPanel) {
      // 在当前面板右侧创建新面板（水平分屏）
      const newPanel = dockviewStore.addPanel({
        id: `${props.panel_id}-split-h-${Date.now()}`,
        title: 'Chron Engine',
        component: 'panel',
        params: {
          type: 'chronengine',
          node: props.node
        },
        position: {
          direction: 'right',
          referencePanel: props.panel_id
        }
      })
    }
  }
}

// 处理垂直分屏事件 - 使用 dockview API (上下分屏)
const handleSplitVertical = () => {
  const dockviewStore = useDockviewStore()
  const api = dockviewStore.getApi()
  
  if (api && props.panel_id) {
    const currentPanel = api.getPanel(props.panel_id)
    if (currentPanel) {
      // 在当前面板下方创建新面板（垂直分屏）
      const newPanel = dockviewStore.addPanel({
        id: `${props.panel_id}-split-v-${Date.now()}`,
        title: 'Chron Engine',
        component: 'panel',
        params: {
          type: 'chronengine',
          node: props.node
        },
        position: {
          direction: 'below',
          referencePanel: props.panel_id
        }
      })
    }
  }
}

// 处理关闭面板事件 - 使用 dockview API
const handleClosePane = () => {
  const dockviewStore = useDockviewStore()
  
  if (props.panel_id) {
    dockviewStore.removePanel(props.panel_id)
  }
}

// 添加图片到输入框
const addImageToEditor = (imageUrl: string, cozeFileId?: string) => {
  // 如果编辑器存在
  if (aiInputRef.value?.editor) {
    // 转换为使用FileReader读取图片，创建本地URL
    if (imageUrl.startsWith('http') || imageUrl.startsWith('chron://')) {
      // 对于远程URL或Chron协议URL，尝试获取图片并创建本地Blob URL
      fetch(imageUrl)
        .then((response) => response.blob())
        .then((blob) => {
          const localUrl = URL.createObjectURL(blob)
          insertImageToEditor(localUrl, cozeFileId)
        })
        .catch((error) => {
          console.error('无法获取图片:', error)
          // 如果获取失败，仍然使用原始URL
          insertImageToEditor(imageUrl, cozeFileId)
        })
    } else {
      // 对于已经是本地URL的情况，直接使用
      insertImageToEditor(imageUrl, cozeFileId)
    }
  }
}

// 辅助函数：实际将图片插入编辑器
const insertImageToEditor = (imageUrl: string, cozeFileId?: string) => {
  if (!aiInputRef.value?.editor) return

  // 将图片添加到编辑器中
  aiInputRef.value.editor.commands.insertContent({
    type: 'simpleImage',
    attrs: {
      src: imageUrl,
      width: '400px'
    }
  })

  // 如果有Coze文件ID，将URL和文件ID的映射关系存储起来
  if (cozeFileId) {
    imageCozeFileIdMap.set(imageUrl, cozeFileId)
    console.log('图片已关联Coze文件ID:', {
      imageUrl,
      cozeFileId
    })
  }

  // 更新提示文本
  aiPrompt.value = `请分析这张图片`

  // 聚焦编辑器
  nextTick(() => {
    aiInputRef.value?.editor?.commands.focus()
  })
}

// 添加文本到输入框
const addTextToEditor = (text: string) => {
  // 如果编辑器存在
  if (aiInputRef.value?.editor) {
    // 清空编辑器内容
    aiInputRef.value.editor.commands.clearContent()

    // 将文本添加到编辑器中
    aiInputRef.value.editor.commands.insertContent(text)

    // 更新提示文本
    aiPrompt.value = text

    // 聚焦编辑器
    nextTick(() => {
      aiInputRef.value?.editor?.commands.focus()
    })

    // 自动发送消息
    // nextTick(() => {
    //   sendAiMessage()
    // })
  }
}

// 定义Coze图片上传事件处理函数
const handleCozeImageUploaded = (event: CustomEvent) => {
  const { imageUrl, cozeFileId } = event.detail
  // 将图片URL和Coze文件ID的映射关系存储在本地Map中
  if (imageUrl && cozeFileId) {
    imageCozeFileIdMap.set(imageUrl, cozeFileId)
    console.log('ChronEngine收到Coze图片上传事件:', { imageUrl, cozeFileId })
  }
}

// 定义事件处理函数，以便在组件销毁时正确移除
const handleAddImage = (data: { panelId: string; imageUrl: string; cozeFileId?: string }) => {
  console.log(`[ChronEngine] 面板 ${props.panel_id} 收到 chronengine:add-image 事件`)
  console.log('我的id', props.panel_id, '需要接受的id', data.panelId)
  console.log('事件数据:', data)
  // 确保事件是发给当前面板的
  if (data.panelId === props.panel_id) {
    console.log(`[ChronEngine] 面板 ${props.panel_id} 匹配成功，添加图片到编辑器`)
    // 添加图片到编辑器，同时传递Coze文件ID
    addImageToEditor(data.imageUrl, data.cozeFileId)
  } else {
    console.log(`[ChronEngine] 面板 ${props.panel_id} 不匹配，忽略事件`)
  }
}

const handleAddText = (data: { panelId: string; text: string }) => {
  console.log(`[ChronEngine] 面板 ${props.panel_id} 收到 chronengine:add-text 事件`)
  console.log(data, 'DATA', props.panel_id, data.panelId)
  // 确保事件是发给当前面板的
  console.log('我的id', props.panel_id, '需要接受的id', data.panelId)
  console.log(data.panelId === props.panel_id, '是否相等')
  if (data.panelId === props.panel_id) {
    console.log(`[ChronEngine] 面板 ${props.panel_id} 匹配成功，添加文本到编辑器`)
    // 添加文本到编辑器并自动发送
    addTextToEditor(data.text)
  } else {
    console.log(`[ChronEngine] 面板 ${props.panel_id} 不匹配，忽略事件`)
  }
}

// 暴露方法给父组件
defineExpose({
  sendExternalMessage
})

onMounted(() => {
  // 注册所有工具处理器
  try {
    registerAllToolHandlers()
    console.log('工具处理器已注册')
  } catch (error) {
    console.error('注册工具处理器失败:', error)
  }

  // 注册自定义事件监听器
  document.addEventListener('coze-image-uploaded', handleCozeImageUploaded as EventListener)

  // 监听截图事件
  console.log(`[ChronEngine] 面板 ${props.panel_id} 注册 chronengine:add-image 事件监听器`)
  mitter.on('chronengine:add-image', handleAddImage)

  // 监听添加文本事件 - 用于PDF中的"询问AI"功能
  console.log(`[ChronEngine] 面板 ${props.panel_id} 注册 chronengine:add-text 事件监听器`)
  mitter.on('chronengine:add-text', handleAddText)
  // 为对话容器添加事件监听器
  // 链接点击处理现在由 MarkdownRenderer 组件处理

  // 总是创建新的会话
  currentConversationId.value = '' // 重置会话ID
  aiConversation.value = [] // 清空对话历史

  // 创建新会话
  CozeUtils.createConversation({
    botId: '7505382249266233370', // 使用默认的botId
    metaData: { source: 'ChronEngine' }
  })
    .then((conversation) => {
      currentConversationId.value = conversation.id
      console.log('新会话已创建:', currentConversationId.value)
    })
    .catch((error) => {
      console.error('创建新会话失败:', error)
    })

  // 聚焦输入框
  nextTick(() => {
    if (!hideInput.value) {
      aiInputRef.value?.editor?.commands.focus()
    }
  })

  // 添加滚动事件监听
  aiConversationContainer.value?.addEventListener('scroll', handleScroll)
})

// 组件销毁前取消请求和移除事件监听
onBeforeUnmount(() => {
  // 取消当前请求
  if (currentAiRequest.value) {
    currentAiRequest.value()
    currentAiRequest.value = null
  }

  // 清除会话ID和对话历史
  currentConversationId.value = ''
  aiConversation.value = []

  // 清除本地的Coze文件ID映射
  imageCozeFileIdMap.clear()

  // 移除滚动事件监听
  aiConversationContainer.value?.removeEventListener('scroll', handleScroll)

  // 移除链接点击事件监听器
  // 链接点击处理现在由 MarkdownRenderer 组件处理

  // 移除事件监听
  console.log(`[ChronEngine] 面板 ${props.panel_id} 移除事件监听器`)
  mitter.off('chronengine:add-image', handleAddImage)
  mitter.off('chronengine:add-text', handleAddText)

  // 移除自定义事件监听器
  document.removeEventListener('coze-image-uploaded', handleCozeImageUploaded as EventListener)

  // 清理全局函数
  if (typeof window !== 'undefined') {
    delete (window as any).copyCodeBlock
  }
})
</script>

<style lang="scss" scoped>
@import 'tailwindcss';
@plugin 'tailwind-scrollbar' {
  nocompatible: true;
}
.chron-engine-container {
  --message-spacing: 2rem;
}

.ai-conversation-container {
  height: calc(100% - 120px); /* 调整高度以适应顶部工具栏和底部输入框 */
  scroll-behavior: smooth;
}

/* 空状态样式 */
.empty-state {
  height: calc(100vh - 200px); /* 使用视口高度计算，减去顶部和底部空间 */
  opacity: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto; /* 水平居中 */
}

/* 响应式调整 */
@media (max-width: 640px) {
  .empty-state .grid {
    grid-template-columns: 1fr;
  }
}

/* 功能卡片样式 */
.empty-state .grid > div {
  transition: all 0.3s ease;
}

.empty-state .grid > div svg {
  transition: transform 0.3s ease;
}

.empty-state .grid > div:hover svg {
  transform: scale(1.2);
}

.empty-state .grid > div span {
  transition: color 0.3s ease;
}

/* 消息容器样式 */
.message-container {
  position: relative;
  transition: all 0.3s ease;
}

.message-container:hover .chat-actions {
  opacity: 1 !important;
}

/* 用户消息和AI消息共享样式 */
.user-message-container,
.ai-message-container {
  word-break: normal;
  white-space: normal;
  width: 100%;
}

/* 用户消息特定样式 */
.user-message-container {
  min-width: 40px;
  display: inline-block;
  max-width: 90%;
  text-align: right;
}

/* 打字指示器样式 */
.typing-indicator {
  display: flex;
  align-items: center;
  column-gap: 6px;
  padding: 4px 0;
}

.typing-indicator span {
  height: 8px;
  width: 8px;
  background-color: var(--p);
  opacity: 0.4;
  border-radius: 50%;
  display: inline-block;
  animation: typing 1.5s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: 0s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.3s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes typing {
  0% {
    transform: translateY(0px);
    opacity: 0.4;
  }
  50% {
    transform: translateY(-5px);
    opacity: 0.8;
  }
  100% {
    transform: translateY(0px);
    opacity: 0.4;
  }
}

/* 输入框容器样式 */
.input-container {
  position: relative;
  z-index: 10;
}

.input-container::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px;
  background: linear-gradient(to top, var(--b1) 40%, transparent);
  pointer-events: none;
  z-index: -1;
}

/* 聊天输入框包装器样式 */
.chat-input-wrapper {
  display: flex;
  width: 100%;
  position: relative;
  z-index: 20;
  border-radius: 0.75rem;
}

.chat-input-wrapper :deep(.ProseMirror) {
  padding: 0.75rem 3.5rem 3.25rem 1rem !important;
  min-height: 2.75rem;
  max-height: 10rem;
  overflow-y: auto;
  border-radius: 0.75rem;
  background-color: var(--v2-input-bg);
  color: var(--v2-input-text);
  border: 1px solid var(--v2-input-border);
  transition: all 0.2s ease;
  font-size: 0.875rem;
  line-height: 1.5;
  scrollbar-width: thin;
  scrollbar-color: var(--v2-scrollbar-thumb) transparent;
}

.chat-input-wrapper :deep(.ProseMirror:focus) {
  outline: none;
  border-color: var(--v2-input-border-focus);
  box-shadow: 0 0 0 2px var(--v2-input-shadow-focus);
}

.chat-input-wrapper :deep(.ProseMirror::-webkit-scrollbar) {
  width: 4px;
  background: transparent;
}

.chat-input-wrapper :deep(.ProseMirror::-webkit-scrollbar-track) {
  background: transparent;
}

.chat-input-wrapper :deep(.ProseMirror::-webkit-scrollbar-thumb) {
  background-color: var(--v2-scrollbar-thumb);
  border-radius: 9999px;
}

.chat-input-wrapper :deep(.ProseMirror:hover::-webkit-scrollbar-thumb) {
  background-color: var(--v2-scrollbar-thumb-hover);
}

.chat-input-wrapper :deep(.ProseMirror p.is-editor-empty:first-child::before) {
  content: attr(data-placeholder);
  color: var(--v2-input-placeholder);
  opacity: 0.7;
  float: left;
  pointer-events: none;
  height: 0;
  font-style: normal;
  font-size: 0.875rem;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

/* 直接在滚动容器上应用网格背景，确保网格跟随滚动 */
.ai-conversation-container {
  background-size: 32px 32px;
  background-image:
    linear-gradient(to right, rgba(128, 128, 128, 0.08) 0.5px, transparent 0.5px),
    linear-gradient(to bottom, rgba(128, 128, 128, 0.08) 0.5px, transparent 0.5px);
  background-attachment: local;
}
</style>
