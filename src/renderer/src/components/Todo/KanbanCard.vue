<template>
  <ContextMenu>
    <ContextMenuTrigger as-child>
      <div
        class="kanban-card"
        :class="{
          'kanban-card--selected': selected,
          'kanban-card--completed': todo.isCompleted === 1,
          'kanban-card--overdue': isOverdue,
          'kanban-card--disabled': isOverdue
        }"
        :draggable="isDraggable"
        @dragstart="handleDragStart"
        @click="handleClick"
      >
        <!-- Priority indicator -->
        <div class="kanban-card__priority" v-if="todo.priority >= 0">
          <component
            :is="getPriorityIcon(todo.priority)"
            :class="getPriorityClass(todo.priority)"
            class="w-4 h-4"
          />
        </div>

        <!-- Card content -->
        <div class="kanban-card__content">
          <!-- Title -->
          <div class="kanban-card__title">
            <TodoTitleEditor
              :node="todo"
              :panel_id="todo.uuid"
              :readonly="true"
              :auto-focus="false"
              :placeholder="'待办标题'"
            />
          </div>

          <!-- Notes preview -->
          <div v-if="todo.notes && cleanNotes" class="kanban-card__notes -ml-2">
            <TodoEditor :model-value="cleanNotes" :readonly="true" :placeholder="'备注内容'" />
          </div>

          <!-- Due date -->
          <div v-if="todo.dueDate" class="kanban-card__due-date" :class="getDueDateClass()">
            <component :is="getIconComponent('Calendar')" class="w-3 h-3" />
            <span>{{ formatDueDate(todo.dueDate) }}</span>
          </div>

          <!-- Tags -->
          <div v-if="todo.tags" class="kanban-card__tags">
            <span v-for="tag in parsedTags" :key="tag" class="kanban-card__tag">
              {{ tag }}
            </span>
          </div>

          <!-- Parent Todo (if exists) - moved to bottom -->
          <div v-if="parentTodo" class="kanban-card__parent mt-2 pt-2 border-t border-base-200">
            <div class="flex items-center text-xs" style="color: rgba(var(--base-content), 0.5)">
              <component :is="getIconComponent('ChevronUp')" class="w-3 h-3 mr-1" />
              <span class="truncate">父任务: {{ parentTodo.title }}</span>
            </div>
          </div>

          <!-- Debug info - temporary -->
          <div v-if="allTodos && allTodos.length > 0" class="text-xs text-red-500 mt-1">
            调试: 共{{ allTodos.length }}个todos, 当前: {{ todo.title }}
          </div>
        </div>
      </div>
    </ContextMenuTrigger>

    <ContextMenuContent
      class="w-48 bg-actual-background border border-base-300 shadow-md rounded-md"
    >
      <ContextMenuItem
        class="hover:bg-base-50 focus:bg-base-200 cursor-pointer"
        @click="toggleCompletion"
      >
        <component
          :is="todo.isCompleted === 1 ? getIconComponent('RotateCcw') : getIconComponent('Check')"
          class="w-4 h-4 mr-2"
        />
        {{ todo.isCompleted === 1 ? '标记为未完成' : '标记为已完成' }}
      </ContextMenuItem>

      <ContextMenuItem
        class="hover:bg-base-50 focus:bg-base-200 cursor-pointer"
        @click="showDetail"
      >
        <component :is="getIconComponent('Eye')" class="w-4 h-4 mr-2" />
        查看详情
      </ContextMenuItem>

      <ContextMenuItem
        class="hover:bg-base-50 focus:bg-base-200 cursor-pointer text-red-600"
        @click="deleteTodo"
      >
        <component :is="getIconComponent('Trash2')" class="w-4 h-4 mr-2" />
        删除
      </ContextMenuItem>
    </ContextMenuContent>
  </ContextMenu>
</template>

<script setup lang="ts">
import { computed, inject, type Ref } from 'vue'
import { type ExtendedTodo } from '@renderer/stores/todoStore'
import { type Todo } from '@renderer/services/TodoService'
import moment from 'moment'
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuTrigger,
  ContextMenuItem
} from '@renderer/components/ui/context-menu'
import { getIconComponent } from '@renderer/utils/iconComponents'
import TodoTitleEditor from '@renderer/editors/TodoTitleEditor.vue'
import TodoEditor from '@renderer/editors/TodoEditor.vue'

const props = defineProps<{
  todo: ExtendedTodo
  selected?: boolean
  allTodos?: ExtendedTodo[] // 新增：所有todos列表，用于查找父任务
}>()

// 添加调试信息查看props
console.log('KanbanCard props for', props.todo.title, ':', {
  hasAllTodos: !!props.allTodos,
  allTodosLength: props.allTodos?.length || 0
})

const emit = defineEmits<{
  (e: 'update', uuid: string, updates: Partial<Todo>): void
  (e: 'select', uuid: string): void
  (e: 'delete', uuid: string): void
  (e: 'show-detail', uuid: string): void
  (e: 'dragstart', event: DragEvent, todo: ExtendedTodo): void
}>()

// 查找父任务的函数
const findParentTodo = (todoUuid: string, todoList: ExtendedTodo[]): ExtendedTodo | null => {
  console.log('KanbanCard: findParentTodo searching for', todoUuid, 'in', todoList.length, 'todos')

  for (const todo of todoList) {
    console.log(
      'KanbanCard: checking todo',
      todo.title,
      'with',
      todo.children?.length || 0,
      'children'
    )

    if (todo.children && todo.children.some((child) => child.uuid === todoUuid)) {
      console.log('KanbanCard: Found parent!', todo.title, 'for', todoUuid)
      return todo
    }
    // 递归查找子待办事项中的父任务
    if (todo.children && todo.children.length > 0) {
      const found = findParentTodo(todoUuid, todo.children)
      if (found) return found
    }
  }

  console.log('KanbanCard: No parent found for', todoUuid)
  return null
}

// 获取父任务
const parentTodo = computed(() => {
  if (!props.allTodos) {
    console.log('KanbanCard: allTodos not provided for', props.todo.title)
    return null
  }
  const parent = findParentTodo(props.todo.uuid, props.allTodos)
  if (parent) {
    console.log(`KanbanCard: Found parent "${parent.title}" for "${props.todo.title}"`)
  } else {
    console.log(`KanbanCard: No parent found for "${props.todo.title}"`)
  }
  return parent
})

// 清理备注文本（移除看板状态标记）
const cleanNotes = computed(() => {
  if (!props.todo.notes) return ''
  return props.todo.notes.replace('__KANBAN_STATUS_IN_PROGRESS__', '').trim()
})

// 解析标签
const parsedTags = computed(() => {
  if (!props.todo.tags) return []
  try {
    // 尝试解析为 JSON 数组
    return JSON.parse(props.todo.tags)
  } catch {
    // 如果不是 JSON，则按逗号分割
    return props.todo.tags
      .split(',')
      .map((tag) => tag.trim())
      .filter((tag) => tag)
  }
})

// 获取优先级图标
const getPriorityIcon = (priority: number) => {
  switch (priority) {
    case 2:
      return getIconComponent('Signal')
    case 1:
      return getIconComponent('SignalHigh')
    default:
      return getIconComponent('SignalMedium')
  }
}

// 获取优先级样式类
const getPriorityClass = (priority: number) => {
  switch (priority) {
    case 2:
      return 'text-red-500'
    case 1:
      return 'text-orange-500'
    default:
      return 'text-base-content'
  }
}

// 格式化截止日期
const formatDueDate = (dueDate: string) => {
  const date = moment(dueDate)
  const now = moment()

  if (date.isSame(now, 'day')) {
    return '今天'
  } else if (date.isSame(now.clone().add(1, 'day'), 'day')) {
    return '明天'
  } else if (date.isSame(now.clone().subtract(1, 'day'), 'day')) {
    return '昨天'
  } else if (date.isSame(now, 'year')) {
    return date.format('M月D日')
  } else {
    return date.format('YYYY年M月D日')
  }
}

// 获取截止日期样式类
const getDueDateClass = () => {
  if (!props.todo.dueDate) return ''

  // 如果待办事项已完成，显示为灰色
  if (props.todo.isCompleted === 1) {
    return 'kanban-card__due-date--completed'
  }

  const date = moment(props.todo.dueDate)
  const now = moment()

  if (date.isBefore(now, 'day')) {
    return 'kanban-card__due-date--overdue'
  } else if (date.isSame(now, 'day')) {
    return 'kanban-card__due-date--today'
  } else if (date.isSame(now.clone().add(1, 'day'), 'day')) {
    return 'kanban-card__due-date--tomorrow'
  }

  return ''
}

// 检查待办事项是否已逾期
const isOverdue = computed(() => {
  if (!props.todo.dueDate || props.todo.isCompleted === 1) return false
  const now = moment()
  const dueDate = moment(props.todo.dueDate)
  return dueDate.isBefore(now, 'day')
})

// 计算是否可拖拽
const isDraggable = computed(() => {
  return !isOverdue.value
})

// 处理拖拽开始
const handleDragStart = (event: DragEvent) => {
  // 如果是逾期的待办事项，阻止拖拽
  if (isOverdue.value) {
    event.preventDefault()
    event.stopPropagation()
    return
  }
  emit('dragstart', event, props.todo)
}

// 处理点击
const handleClick = () => {
  emit('show-detail', props.todo.uuid)
}

// 切换完成状态
const toggleCompletion = () => {
  const newStatus = props.todo.isCompleted === 1 ? 0 : 1
  emit('update', props.todo.uuid, { isCompleted: newStatus })
}

// 显示详情
const showDetail = () => {
  emit('show-detail', props.todo.uuid)
}

// 删除待办事项
const deleteTodo = () => {
  emit('delete', props.todo.uuid)
}
</script>
