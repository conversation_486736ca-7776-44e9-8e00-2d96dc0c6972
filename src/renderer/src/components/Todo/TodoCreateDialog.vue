<template>
  <SimpleDialog v-model:open="isOpen" content-class="max-w-2xl">
    <div class="space-y-6">
      <!-- Title and Content Section - Like an Article -->
      <div class="space-y-4">
        <!-- Title Row -->
        <div class="flex items-center gap-4">
          <SimpleTodoTitleEditor
            v-model="todoData.title"
            :readonly="false"
            :autoFocus="true"
            :placeholder="titlePlaceholder"
            class="text-2xl font-semibold flex-1 border-none outline-none bg-transparent resize-none text-base-content"
            @enter="focusNotesEditor"
          />
        </div>

        <!-- Notes - Directly connected to title -->
        <div class="min-h-[60px] -ml-2 max-h-[150px] overflow-y-auto">
          <SimpleTodoEditor
            ref="notesEditor"
            v-model="todoData.notes"
            :readonly="false"
            :placeholder="notesPlaceholder"
            class="w-full border-none outline-none bg-transparent"
          />
        </div>
      </div>

      <!-- Properties Section -->
      <div class="space-y-3 mt-8 select-none">
        <!-- Priority Row -->
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <SignalHigh class="w-4 h-4 text-base-content/50" :stroke-width="2.5" />
            <span class="text-sm text-base-content/50">优先级</span>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger class="outline-hidden">
              <div
                class="flex items-center gap-2 text-sm text-base-content/70 hover:bg-base-200 focus:bg-base-200 rounded-md p-1.5 transition-colors"
              >
                <Signal v-if="todoData.priority === 2" class="w-4 h-4 text-red-500" />
                <SignalHigh v-else-if="todoData.priority === 1" class="w-4 h-4 text-orange-500" />
                <SignalMedium v-else class="w-4 h-4 text-base-content" />
                <span>{{
                  todoData.priority === 2
                    ? '高优先级'
                    : todoData.priority === 1
                      ? '中优先级'
                      : '低优先级'
                }}</span>
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              class="bg-base-100 border border-base-300 shadow-md rounded-md w-32 p-1.5"
              :no-close-animation="true"
            >
              <DropdownMenuItem
                class="hover:bg-base-200 focus:bg-base-200 rounded-md p-1.5 mb-0.5 h-8 flex items-center"
                @select="todoData.priority = 0"
              >
                <div class="flex items-center w-full gap-2">
                  <SignalMedium class="w-4 h-4 text-base-content" />
                  <span class="text-base-content">低优先级</span>
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem
                class="hover:bg-base-200 focus:bg-base-200 rounded-md p-1.5 mb-0.5 h-8 flex items-center"
                @select="todoData.priority = 1"
              >
                <div class="flex items-center w-full gap-2">
                  <SignalHigh class="w-4 h-4 text-orange-500" />
                  <span class="text-base-content">中优先级</span>
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem
                class="hover:bg-base-200 focus:bg-base-200 rounded-md p-1.5 h-8 flex items-center"
                @select="todoData.priority = 2"
              >
                <div class="flex items-center w-full gap-2">
                  <Signal class="w-4 h-4 text-red-500" />
                  <span class="text-base-content">高优先级</span>
                </div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <!-- Due Date Row -->
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <Calendar class="w-4 h-4 text-base-content/50" :stroke-width="2.5" />
            <span class="text-sm text-base-content/50">截止时间</span>
          </div>
          <div
            class="flex items-center gap-2 text-sm text-base-content/70 hover:bg-base-200 focus:bg-base-200 rounded-md p-1.5 transition-colors cursor-pointer"
            @click="openDueDateDialog"
          >
            <Calendar v-if="todoData.dueDate" class="w-4 h-4 text-blue-500" />
            <span>{{
              todoData.dueDate ? formatDueDateForDisplay(todoData.dueDate) : '设置截止时间'
            }}</span>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex justify-end gap-3 pt-4 border-t border-base-100">
        <button
          class="cursor-pointer hover:bg-base-200 rounded-md px-4 py-2 text-sm text-base-content/70 hover:text-base-content transition-colors"
          @click="handleCancel"
        >
          取消
        </button>
        <button
          @click="createTodo"
          :disabled="!todoData.title.trim()"
          class="cursor-pointer px-4 py-2 text-sm bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {{ createButtonText }}
        </button>
      </div>
    </div>

    <!-- Date Picker Dialog -->
    <Dialog v-model:open="showDueDateDialog" class="z-[9999999999999]">
      <DialogContent class="sm:max-w-lg bg-base-background">
        <div class="space-y-4">
          <h3 class="text-lg font-medium text-base-content">设置截止日期和时间</h3>
          <DatePicker v-model="dueDateValue" placeholder="选择日期和时间" />
          <div class="flex justify-end gap-3 pt-4 border-t border-base-100">
            <button
              v-if="dueDateValue"
              class="px-3 py-2 text-sm text-base-content hover:bg-base-200 rounded-md transition-colors"
              @click="clearDueDate"
            >
              清除
            </button>
            <button
              class="px-3 py-2 text-sm text-base-content hover:bg-base-200 rounded-md transition-colors"
              @click="cancelDueDate"
            >
              取消
            </button>
            <button
              class="px-3 py-2 text-sm bg-primary/80 text-white hover:bg-primary/70 rounded-md transition-colors"
              @click="saveDueDate"
            >
              保存
            </button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  </SimpleDialog>
</template>

<script setup lang="ts">
import { Calendar, SignalHigh, SignalMedium, Signal } from 'lucide-vue-next'
import SimpleTodoTitleEditor from '@renderer/editors/SimpleTodoTitleEditor.vue'
import SimpleTodoEditor from '@renderer/editors/SimpleTodoEditor.vue'
import moment from 'moment'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem
} from '@renderer/components/ui/dropdown-menu'
import { Dialog, DialogContent } from '@renderer/components/ui/dialog'
import SimpleDialog from './SimpleDialog.vue'
import DatePicker from './DatePicker.vue'
import { ref, watch, nextTick } from 'vue'

interface TodoData {
  title: string
  notes: string
  priority: number
  dueDate: string
}

const props = defineProps<{
  open: boolean
  titlePlaceholder?: string
  notesPlaceholder?: string
  createButtonText?: string
  defaultDueDate?: string
}>()

const emit = defineEmits<{
  (e: 'update:open', value: boolean): void
  (e: 'create', todoData: TodoData): void
}>()

// Dialog state
const isOpen = ref(props.open)

// Notes editor reference
const notesEditor = ref()

// Todo form data
const todoData = ref<TodoData>({
  title: '',
  notes: '',
  priority: 0,
  dueDate: ''
})

// Date Picker Dialog state
const showDueDateDialog = ref(false)
const dueDateValue = ref<string | null>(null)

// Watch for open prop changes
watch(
  () => props.open,
  (newValue) => {
    isOpen.value = newValue
    // 当对话框打开时，设置默认截止日期
    if (newValue && props.defaultDueDate) {
      todoData.value.dueDate = props.defaultDueDate
      dueDateValue.value = props.defaultDueDate
    }
  }
)

// Watch for isOpen changes
watch(isOpen, (newValue) => {
  emit('update:open', newValue)
  if (!newValue) {
    resetForm()
  }
})

// Reset form data
const resetForm = () => {
  todoData.value = {
    title: '',
    notes: '',
    priority: 0,
    dueDate: ''
  }
  dueDateValue.value = null
}

// Focus notes editor when enter is pressed in title
const focusNotesEditor = () => {
  nextTick(() => {
    notesEditor.value?.focus()
  })
}

// Create todo
const createTodo = () => {
  if (!todoData.value.title.trim()) {
    return
  }

  emit('create', { ...todoData.value })
  isOpen.value = false
}

// Open due date dialog
const openDueDateDialog = () => {
  dueDateValue.value = todoData.value.dueDate || null
  showDueDateDialog.value = true
}

// Cancel due date
const cancelDueDate = () => {
  dueDateValue.value = todoData.value.dueDate || null
  showDueDateDialog.value = false
}

// Save due date
const saveDueDate = () => {
  todoData.value.dueDate = dueDateValue.value || ''
  showDueDateDialog.value = false
}

// Clear due date
const clearDueDate = () => {
  dueDateValue.value = null
  todoData.value.dueDate = ''
  showDueDateDialog.value = false
}

// Format due date for display
const formatDueDateForDisplay = (dateString: string): string => {
  if (!dateString) return '设置截止时间'
  try {
    const date = moment(dateString)
    if (!date.isValid()) return '设置截止时间'

    const now = moment()
    const tomorrow = moment().add(1, 'day')
    const nextWeek = moment().add(7, 'days')

    if (date.isSame(now, 'day')) {
      return `今天 ${date.format('HH:mm')}`
    } else if (date.isSame(tomorrow, 'day')) {
      return `明天 ${date.format('HH:mm')}`
    } else if (date.isBefore(nextWeek)) {
      return date.format('dddd HH:mm')
    } else {
      return date.format('M月D日 HH:mm')
    }
  } catch (error) {
    console.error('Error formatting due date for display:', error)
    return '设置截止时间'
  }
}

// Handle cancel
const handleCancel = () => {
  isOpen.value = false
  // 不需要在这里调用 resetForm，watch 会延迟调用
}
</script>
