<template>
  <div class="flex-1 flex flex-col">
    <!-- Title Section - Fixed at top -->
    <div class="px-6 pt-6 pb-3 flex-shrink-0">
      <!-- Title Row -->
      <div class="flex items-center gap-4">
        <TodoTitleEditor
          :node="todo"
          :panel_id="todo.uuid"
          :readonly="false"
          :autoFocus="true"
          :placeholder="'输入待办事项...'"
          class="text-2xl font-semibold flex-1 border-none outline-none bg-transparent resize-none text-base-content -ml-2"
          @update:title="(newTitle: string) => emit('update', todo.uuid, { title: newTitle })"
        />
      </div>
    </div>

    <!-- Notes Editor - Flexible content area -->
    <div
      class="flex-1 px-6 min-h-0 overflow-y-auto scrollbar-gutter scrollbar-thin scrollbar-track-transparent scrollbar-thumb-transparent hover:scrollbar-thumb-base-content/25"
    >
      <TodoEditor
        v-model="todo.notes"
        :readonly="false"
        class="w-full h-full border-none outline-none bg-transparent"
        @update:modelValue="handleNotesUpdate"
      />
    </div>

    <!-- Bottom Section - Properties and Sub-tasks -->
    <div class="flex-shrink-0">
      <!-- Properties Section -->
      <div class="px-6 pt-3 pb-6 border-t border-base-200">
        <div class="space-y-3 select-none">
          <!-- Status Row -->
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <Loader class="w-4 h-4 text-base-content/50" :stroke-width="2.5" />
              <span class="text-sm text-base-content/50">状态</span>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger class="outline-hidden">
                <div
                  class="flex items-center gap-2 text-sm text-base-content/70 hover:bg-base-100 focus:bg-base-100 rounded-md p-1.5 transition-colors"
                >
                  <Check v-if="todo.isCompleted === 1" class="w-4 h-4 text-green-500" />
                  <CircleDot v-else class="w-4 h-4 text-blue-500" />
                  <span>{{ todo.isCompleted === 1 ? '已完成' : '进行中' }}</span>
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                class="bg-base-background border border-base-300 shadow-md rounded-md w-32 p-1.5 mr-2"
                :no-close-animation="true"
              >
                <DropdownMenuItem
                  class="hover:bg-base-100 focus:bg-base-100 cursor-pointer rounded-md p-1.5 mb-0.5 h-8 flex items-center"
                  @select="updateTodo({ isCompleted: 0 })"
                >
                  <div class="flex items-center w-full gap-2">
                    <CircleDot class="w-4 h-4 text-blue-500" />
                    <span class="text-base-content">进行中</span>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem
                  class="hover:bg-base-100 focus:bg-base-100 cursor-pointer rounded-md p-1.5 h-8 flex items-center"
                  @select="updateTodo({ isCompleted: 1 })"
                >
                  <div class="flex items-center w-full gap-2">
                    <Check class="w-4 h-4 text-green-500" />
                    <span class="text-base-content">已完成</span>
                  </div>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <!-- Priority Row -->
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <SignalHigh class="w-4 h-4 text-base-content/50" :stroke-width="2.5" />
              <span class="text-sm text-base-content/50">优先级</span>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger class="outline-hidden">
                <div
                  class="flex items-center gap-2 text-sm text-base-content/70 hover:bg-base-100 focus:bg-base-100 rounded-md p-1.5 transition-colors"
                >
                  <Signal v-if="todo.priority === 2" class="w-4 h-4 text-red-500" />
                  <SignalHigh v-else-if="todo.priority === 1" class="w-4 h-4 text-orange-500" />
                  <SignalMedium v-else class="w-4 h-4 text-base-content" />
                  <span>{{
                    todo.priority === 2 ? '高优先级' : todo.priority === 1 ? '中优先级' : '低优先级'
                  }}</span>
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                class="bg-base-background border border-base-300 shadow-md rounded-md w-32 p-1.5"
                :no-close-animation="true"
              >
                <DropdownMenuItem
                  class="hover:bg-base-100 focus:bg-base-100 cursor-pointer rounded-md p-1.5 mb-0.5 h-8 flex items-center"
                  @select="updateTodo({ priority: 0 })"
                >
                  <div class="flex items-center w-full gap-2">
                    <SignalMedium class="w-4 h-4 text-base-content" />
                    <span class="text-base-content">低优先级</span>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem
                  class="hover:bg-base-100 focus:bg-base-100 cursor-pointer rounded-md p-1.5 mb-0.5 h-8 flex items-center"
                  @select="updateTodo({ priority: 1 })"
                >
                  <div class="flex items-center w-full gap-2">
                    <SignalHigh class="w-4 h-4 text-orange-500" />
                    <span class="text-base-content">中优先级</span>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem
                  class="hover:bg-base-100 focus:bg-base-100 cursor-pointer rounded-md p-1.5 h-8 flex items-center"
                  @select="updateTodo({ priority: 2 })"
                >
                  <div class="flex items-center w-full gap-2">
                    <Signal class="w-4 h-4 text-red-500" />
                    <span class="text-base-content">高优先级</span>
                  </div>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <!-- Due Date Row -->
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <Calendar class="w-4 h-4 text-base-content/50" :stroke-width="2.5" />
              <span class="text-sm text-base-content/50">截止时间</span>
            </div>
            <div
              class="flex items-center gap-2 text-sm text-base-content/70 hover:bg-base-100 focus:bg-base-100 rounded-md p-1.5 transition-colors"
              @click="openDueDateDialog"
            >
              <Calendar v-if="todo.dueDate" class="w-4 h-4 text-blue-500" />
              <span>{{ todo.dueDate ? formatDueDate(todo.dueDate) : '设置截止时间' }}</span>
            </div>
          </div>

          <!-- Created Date Row -->
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <Clock class="w-4 h-4 text-base-content/50" :stroke-width="2.5" />
              <span class="text-sm text-base-content/50">创建</span>
            </div>
            <span class="text-sm text-base-content/70">{{
              formatCreatedDate(todo.createdAt)
            }}</span>
          </div>

          <!-- Updated Date Row -->
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <Clock class="w-4 h-4 text-base-content/50" :stroke-width="2.5" />
              <span class="text-sm text-base-content/50">更新</span>
            </div>
            <span class="text-sm text-base-content/70">{{
              formatUpdatedDate(todo.updatedAt)
            }}</span>
          </div>
        </div>
      </div>

      <!-- Add Sub-issues Section -->
      <div class="px-6 pb-6">
        <button
          @click="showSubTodoDialog"
          class="w-full flex items-center justify-center gap-2 py-3 px-4 border border-dashed border-base-250 rounded-lg text-base-content/60 hover:text-base-content hover:border-base-500 transition-colors"
        >
          <!-- Always show Plus icon for add functionality -->
          <Plus class="w-4 h-4" :stroke-width="2.5" />
          <span class="text-sm">添加子任务</span>
          <!-- Progress indicator (only show when there are child todos) -->
          <div v-if="childTodos && childTodos.length > 0" class="flex items-center gap-1 ml-auto">
            <div class="relative w-4 h-4">
              <svg class="w-4 h-4 transform -rotate-90" viewBox="0 0 16 16">
                <!-- Background circle -->
                <circle
                  cx="8"
                  cy="8"
                  r="6"
                  stroke="currentColor"
                  stroke-width="1.5"
                  fill="none"
                  class="text-base-content/20"
                />
                <!-- Progress circle -->
                <circle
                  cx="8"
                  cy="8"
                  r="6"
                  stroke="currentColor"
                  stroke-width="1.5"
                  fill="none"
                  class="text-blue-500"
                  :stroke-dasharray="progressCircumference"
                  :stroke-dashoffset="progressOffset"
                  stroke-linecap="round"
                />
              </svg>
            </div>
            <span class="text-xs text-base-content/70 font-medium">
              {{ completedChildTodosCount }}/{{ childTodos.length }}
            </span>
          </div>
        </button>

        <!-- Sub-todo Create Dialog -->
        <TodoCreateDialog
          v-model:open="isSubTodoDialogOpen"
          :titlePlaceholder="'输入子待办事项标题...'"
          :notesPlaceholder="'键入任务的详细内容...'"
          :createButtonText="'创建'"
          @create="handleCreateSubTodo"
        />

        <!-- Child Todos List -->
        <div
          v-if="childTodos && childTodos.length > 0"
          class="mt-4 max-h-48 overflow-y-auto scrollbar-gutter scrollbar-thin scrollbar-track-transparent scrollbar-thumb-transparent hover:scrollbar-thumb-base-content/25"
        >
          <div class="space-y-2 pr-2">
            <div
              v-for="childTodo in childTodos"
              :key="childTodo.uuid"
              class="flex items-center gap-3 p-3 rounded-lg border border-base-300 hover:bg-base-100/50 hover:border-base-400 transition-colors"
            >
              <!-- Completion Checkbox -->
              <Checkbox
                :id="childTodo.uuid"
                :model-value="childTodo.isCompleted === 1"
                @update:model-value="
                  (checked) => updateChildTodo(childTodo.uuid, { isCompleted: checked ? 1 : 0 })
                "
                @click.stop
              />

              <!-- Priority Display -->
              <div class="flex items-center">
                <Signal
                  v-if="childTodo.priority === 2"
                  class="w-4 h-4 text-red-500"
                  :stroke-width="2.5"
                />
                <SignalHigh
                  v-else-if="childTodo.priority === 1"
                  class="w-4 h-4 text-orange-500"
                  :stroke-width="2.5"
                />
                <SignalMedium v-else class="w-4 h-4 text-base-content" :stroke-width="2.5" />
              </div>

              <!-- Title and Content -->
              <div
                class="flex-1 min-w-0 cursor-pointer"
                @click="showChildTodoDetail(childTodo.uuid)"
              >
                <div class="flex items-center justify-between">
                  <!-- Title -->
                  <div
                    class="flex-1 min-w-0 text-sm font-normal"
                    :class="{ 'text-base-content/70 line-through': childTodo.isCompleted === 1 }"
                  >
                    <div class="truncate" v-html="childTodo.title || '新待办事项'"></div>
                  </div>
                  <!-- Due Date Display -->
                  <div
                    v-if="childTodo.dueDate"
                    class="ml-2 text-xs flex items-center"
                    :class="getChildDueDateColorClass(childTodo.dueDate, childTodo)"
                  >
                    {{ formatChildDueDate(childTodo.dueDate) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Date Picker Dialog -->
    <Dialog v-model:open="showDueDateDialog">
      <DialogContent class="sm:max-w-lg bg-base-background">
        <div class="space-y-4">
          <h3 class="text-lg font-medium text-base-content">设置截止日期和时间</h3>
          <DatePicker v-model="dueDateValue" placeholder="选择日期和时间" />
          <div class="flex justify-end gap-3 pt-4 border-t border-base-100">
            <button
              v-if="dueDateValue"
              class="px-3 py-2 text-sm text-base-content hover:bg-base-100 rounded-md transition-colors"
              @click="clearDueDate"
            >
              清除
            </button>
            <button
              class="px-3 py-2 text-sm text-base-content hover:bg-base-100 rounded-md transition-colors"
              @click="cancelDueDate"
            >
              取消
            </button>
            <button
              class="px-3 py-2 text-sm bg-primary/80 text-white hover:bg-primary/70 rounded-md transition-colors"
              @click="saveDueDate"
            >
              保存
            </button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import {
  ChevronLeft,
  Clock,
  Calendar,
  SignalHigh,
  SignalMedium,
  Signal,
  Plus,
  Loader,
  Check,
  CircleDot,
  Copy,
  FileText,
  Share2
} from 'lucide-vue-next'
import TodoEditor from '@renderer/editors/TodoEditor.vue'
import TodoTitleEditor from '@renderer/editors/TodoTitleEditor.vue'
import TodoCreateDialog from './TodoCreateDialog.vue'
import moment from 'moment'
import type { Todo } from '@renderer/services/TodoService'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem
} from '@renderer/components/ui/dropdown-menu'
import { HeaderActions } from '@renderer/components/ui/v2'
import type { ActionItem } from '@renderer/components/ui/v2/header-actions/HeaderActions.vue'
import { ref, computed, watch } from 'vue'
import Checkbox from '@renderer/components/ui/v2/Checkbox.vue'
import DatePicker from './DatePicker.vue'
import { Dialog, DialogContent } from '@renderer/components/ui/dialog'
import { showToast } from '@renderer/utils/toast'

// 响应式的 Todo 接口，包含子待办事项
interface ReactiveTodo extends Omit<Todo, 'children'> {
  children: ReactiveTodo[]
}

const props = defineProps<{
  todo: Todo
  childTodos?: ReactiveTodo[]
}>()

const emit = defineEmits<{
  (e: 'update', uuid: string, updates: Partial<Todo>): void
  (e: 'close'): void
  (e: 'subTodoCreated', subTodo: Todo): void
  (e: 'showChildDetail', uuid: string): void
}>()

// 菜单操作配置
const menuActions = computed<ActionItem[]>(() => [
  {
    id: 'copy-todo',
    label: '复制待办',
    icon: Copy,
    action: copyTodo
  },
  {
    id: 'export-todo',
    label: '导出待办',
    icon: FileText,
    action: exportTodo
  },
  {
    id: 'share-todo',
    label: '分享待办',
    icon: Share2,
    action: shareTodo
  }
])

// 处理操作事件
const handleAction = (action: ActionItem) => {
  // 操作已经在action函数中处理
}

// 复制待办
const copyTodo = async () => {
  try {
    const todoText = `${props.todo.title}\n${props.todo.notes || ''}`
    await navigator.clipboard.writeText(todoText)
    showToast('成功', '已复制到剪贴板', 2000)
  } catch (err) {
    showToast('错误', '复制失败', 2000)
  }
}

// 导出待办
const exportTodo = () => {
  const todoData = {
    title: props.todo.title,
    notes: props.todo.notes,
    priority: props.todo.priority,
    dueDate: props.todo.dueDate,
    isCompleted: props.todo.isCompleted,
    createdAt: props.todo.createdAt,
    updatedAt: props.todo.updatedAt
  }

  const dataStr = JSON.stringify(todoData, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)

  const link = document.createElement('a')
  link.href = url
  link.download = `todo-${props.todo.title || 'untitled'}.json`
  link.click()

  URL.revokeObjectURL(url)
  showToast('成功', '待办已导出', 2000)
}

// 分享待办
const shareTodo = async () => {
  const shareText = `待办事项: ${props.todo.title}\n${props.todo.notes || ''}`

  if (navigator.share) {
    try {
      await navigator.share({
        title: '待办事项',
        text: shareText
      })
    } catch (err) {
      // 用户取消分享或其他错误
      console.log('分享取消或失败:', err)
    }
  } else {
    // 降级到复制到剪贴板
    try {
      await navigator.clipboard.writeText(shareText)
      showToast('成功', '已复制到剪贴板，可手动分享', 2000)
    } catch (err) {
      showToast('错误', '分享失败', 2000)
    }
  }
}

// Sub-todo dialog state
const isSubTodoDialogOpen = ref(false)

// Due date reactive value
const dueDateValue = ref<string | null>(props.todo.dueDate || null)

// Date Picker Dialog state
const showDueDateDialog = ref(false)

// Watch for changes in props.todo.dueDate to sync dueDateValue
watch(
  () => props.todo.dueDate,
  (newDueDate) => {
    dueDateValue.value = newDueDate || null
  }
)

// Computed property for completed child todos count
const completedChildTodosCount = computed(() => {
  if (!props.childTodos) return 0
  return props.childTodos.filter((todo) => todo.isCompleted === 1).length
})

// Progress circle calculations
const progressCircumference = computed(() => {
  const radius = 6 // radius of the circle (updated for smaller button icon)
  return 2 * Math.PI * radius
})

const progressOffset = computed(() => {
  if (!props.childTodos || props.childTodos.length === 0) return progressCircumference.value
  const progress = completedChildTodosCount.value / props.childTodos.length
  return progressCircumference.value * (1 - progress)
})

// Show sub-todo dialog
const showSubTodoDialog = () => {
  isSubTodoDialogOpen.value = true
}

// Handle create sub-todo
const handleCreateSubTodo = async (todoData: any) => {
  try {
    // Import TodoService dynamically to avoid circular dependency
    const { TodoService } = await import('@renderer/services/TodoService')
    const todoService = new TodoService()

    // Prepare todo data
    const newTodoData = {
      title: todoData.title.trim(),
      notes: todoData.notes || '',
      priority: todoData.priority || 0,
      dueDate: todoData.dueDate ? moment(todoData.dueDate).toISOString() : undefined,
      isCompleted: 0
    }

    // Create the new sub-todo
    const newTodoItem = await todoService.createTodo(newTodoData)

    // Add it as a child to the current todo
    const success = await todoService.addChildTodo(props.todo.uuid, newTodoItem.uuid)

    if (success) {
      // Emit event to notify parent component
      emit('subTodoCreated', newTodoItem)

      // Close dialog
      isSubTodoDialogOpen.value = false
    } else {
      console.error('Failed to add child todo')
    }
  } catch (error) {
    console.error('Error creating sub-todo:', error)
  }
}

// Handle notes update
const handleNotesUpdate = (newNotes: string) => {
  emit('update', props.todo.uuid, { notes: newNotes })
}

// Update todo
const updateTodo = (updates: Partial<Todo>) => {
  emit('update', props.todo.uuid, updates)
}

// Update due date
const updateDueDate = (dateTimeValue: string | null) => {
  if (!dateTimeValue) {
    updateTodo({ dueDate: undefined })
    dueDateValue.value = null
    return
  }

  try {
    const date = moment(dateTimeValue)
    if (date.isValid()) {
      updateTodo({ dueDate: date.toISOString() })
      dueDateValue.value = dateTimeValue
    }
  } catch (error) {
    console.error('Error updating due date:', error)
  }
}

// Open due date dialog
const openDueDateDialog = () => {
  dueDateValue.value = props.todo.dueDate || null
  showDueDateDialog.value = true
}

// Cancel due date
const cancelDueDate = () => {
  dueDateValue.value = props.todo.dueDate || null
  showDueDateDialog.value = false
}

// Save due date
const saveDueDate = () => {
  updateDueDate(dueDateValue.value)
  showDueDateDialog.value = false
}

// Clear due date (updated to also close dialog)
const clearDueDate = () => {
  updateTodo({ dueDate: undefined })
  dueDateValue.value = null
  showDueDateDialog.value = false
}

// Format created date
const formatCreatedDate = (dateString: string | null | undefined): string => {
  if (!dateString) return '未知'
  try {
    const date = moment(dateString)
    if (!date.isValid()) return '未知'

    const now = moment()
    const diffDays = now.diff(date, 'days')

    if (diffDays === 0) {
      return date.format('今日 HH:mm')
    } else if (diffDays === 1) {
      return '昨日'
    } else if (diffDays < 7) {
      return `${diffDays} 天前`
    } else {
      return date.format('YYYY年M月D日')
    }
  } catch (error) {
    console.error('Error formatting created date:', error)
    return '未知'
  }
}

// Format updated date
const formatUpdatedDate = (dateString: string | null | undefined): string => {
  if (!dateString) return '未知'
  try {
    const date = moment(dateString)
    if (!date.isValid()) return '未知'

    const now = moment()
    const diffMinutes = now.diff(date, 'minutes')
    const diffHours = now.diff(date, 'hours')
    const diffDays = now.diff(date, 'days')

    if (diffMinutes < 1) {
      return '刚刚'
    } else if (diffMinutes < 60) {
      return `${diffMinutes} 分钟前`
    } else if (diffHours < 24) {
      return `${diffHours} 小时前`
    } else if (diffDays === 1) {
      return '昨日'
    } else if (diffDays < 7) {
      return `${diffDays} 天前`
    } else {
      return date.format('YYYY年M月D日')
    }
  } catch (error) {
    console.error('Error formatting updated date:', error)
    return '未知'
  }
}

// Format due date for display
const formatDueDate = (dateString: string): string => {
  if (!dateString) return '设置截止时间'
  try {
    const date = moment(dateString)
    if (!date.isValid()) return '设置截止时间'

    const now = moment()
    const tomorrow = moment().add(1, 'day')
    const nextWeek = moment().add(7, 'days')

    if (date.isSame(now, 'day')) {
      return `今天 ${date.format('HH:mm')}`
    } else if (date.isSame(tomorrow, 'day')) {
      return `明天 ${date.format('HH:mm')}`
    } else if (date.isBefore(nextWeek)) {
      return date.format('dddd HH:mm')
    } else {
      return date.format('M月D日 HH:mm')
    }
  } catch (error) {
    console.error('Error formatting due date:', error)
    return '设置截止时间'
  }
}

// Update child todo
const updateChildTodo = (uuid: string, updates: Partial<Todo>) => {
  emit('update', uuid, updates)
}

// Show child todo detail
const showChildTodoDetail = (uuid: string) => {
  emit('showChildDetail', uuid)
}

// Format due date for child todos
const formatChildDueDate = (dateString: string): string => {
  return formatDueDate(dateString)
}

// Get due date color class for child todos
const getChildDueDateColorClass = (dateString: string, childTodo?: any): string => {
  if (!dateString) return ''

  // 如果子待办事项已完成，显示为灰色
  if (childTodo && childTodo.isCompleted === 1) {
    return 'text-base-content/50'
  }

  try {
    const date = moment(dateString)
    if (!date.isValid()) return ''

    const now = moment()
    if (date.isBefore(now)) {
      return 'text-red-500'
    } else if (date.isSame(now, 'day')) {
      return 'text-orange-500'
    } else {
      return 'text-base-content/70'
    }
  } catch (error) {
    console.error('Error getting due date color class:', error)
    return ''
  }
}
</script>
