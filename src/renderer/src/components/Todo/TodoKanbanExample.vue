<template>
  <div class="todo-kanban-example p-4">
    <h1 class="text-2xl font-bold mb-4">待办事项看板</h1>

    <!-- Toggle between List and Kanban view -->
    <div class="mb-4 flex gap-2">
      <button
        @click="viewMode = 'list'"
        :class="[
          'px-4 py-2 rounded',
          viewMode === 'list' ? 'bg-blue-500 text-white' : 'bg-gray-200'
        ]"
      >
        列表视图
      </button>
      <button
        @click="viewMode = 'kanban'"
        :class="[
          'px-4 py-2 rounded',
          viewMode === 'kanban' ? 'bg-blue-500 text-white' : 'bg-gray-200'
        ]"
      >
        看板视图
      </button>
    </div>

    <!-- Conditional rendering based on view mode -->
    <TodoList v-if="viewMode === 'list' && listUuid" :listUuid="listUuid" />

    <TodoKanbanView v-else-if="viewMode === 'kanban' && listUuid" :listUuid="listUuid" />

    <div v-else class="text-center text-gray-500 py-8">请先选择一个待办清单</div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import TodoList from './TodoList.vue'
import TodoKanbanView from './TodoKanbanView.vue'
import { useTodoService } from '@renderer/services/TodoService'

const viewMode = ref<'list' | 'kanban'>('kanban')
const listUuid = ref<string>('')

const todoService = useTodoService()

// 在组件挂载时创建一个示例清单（如果还没有的话）
onMounted(async () => {
  try {
    // 获取所有待办清单
    const lists = await todoService.getAllTodoLists()

    if (lists.length > 0) {
      // 使用第一个清单
      listUuid.value = lists[0].uuid
    } else {
      // 创建一个示例清单
      console.log('No todo lists found, you may need to create one first')
    }
  } catch (error) {
    console.error('Failed to load todo lists:', error)
  }
})
</script>

<style scoped>
.todo-kanban-example {
  height: 100vh;
  overflow: hidden;
}
</style>
