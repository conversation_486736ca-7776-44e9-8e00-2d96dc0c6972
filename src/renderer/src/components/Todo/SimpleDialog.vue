<template>
  <Teleport to="body">
    <div
      v-if="open"
      class="fixed inset-0 z-[999999999]"
      @keydown.esc="$emit('update:open', false)"
      tabindex="-1"
    >
      <!-- Backdrop -->
      <div
        class="fixed inset-0 z-[] bg-black/50 backdrop-blur-sm"
        @click="handleOverlayClick"
      ></div>

      <!-- Dialog Content -->
      <div
        ref="dialogContent"
        :class="[
          'fixed left-1/2 top-1/2 z-[9999] grid w-full max-w-lg -translate-x-1/2 -translate-y-1/2 gap-4 border border-base-300 bg-base-100 p-6 shadow-lg duration-200 sm:rounded-lg',
          contentClass
        ]"
        @click.stop
      >
        <slot />

        <!-- Close Button - 完全复刻原样式 -->
        <!-- Close Button - 完全复刻原样式 -->
        <button
          @click="$emit('update:open', false)"
          class="absolute p-1 hover:bg-base-200 right-4 top-4 rounded-sm opacity-70 ring-offset-base-100 transition-opacity hover:opacity-100 focus:outline-hidden disabled:pointer-events-none"
        >
          <!-- Cross2Icon 的 SVG -->
          <svg
            class="w-4 h-4 text-base-content-secondary"
            viewBox="0 0 15 15"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M11.7816 4.03157C12.0062 3.80702 12.0062 3.44295 11.7816 3.2184C11.5571 2.99385 11.193 2.99385 10.9685 3.2184L7.50005 6.68682L4.03164 3.2184C3.80708 2.99385 3.44301 2.99385 3.21846 3.2184C2.99391 3.44295 2.99391 3.80702 3.21846 4.03157L6.68688 7.49999L3.21846 10.9684C2.99391 11.193 2.99391 11.557 3.21846 11.7816C3.44301 12.0061 3.80708 12.0061 4.03164 11.7816L7.50005 8.31316L10.9685 11.7816C11.193 12.0061 11.5571 12.0061 11.7816 11.7816C12.0062 11.557 12.0062 11.193 11.7816 10.9684L8.31322 7.49999L11.7816 4.03157Z"
              fill="currentColor"
              fill-rule="evenodd"
              clip-rule="evenodd"
            />
          </svg>
          <span class="sr-only">Close</span>
        </button>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, watch, onUnmounted } from 'vue'

interface Props {
  open: boolean
  contentClass?: string
  closeOnOverlay?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  contentClass: '',
  closeOnOverlay: true
})

const emit = defineEmits<{
  (e: 'update:open', value: boolean): void
}>()

const dialogContent = ref<HTMLElement>()

const handleOverlayClick = (event: MouseEvent) => {
  if (props.closeOnOverlay && event.target === event.currentTarget) {
    emit('update:open', false)
  }
}

// 监听 ESC 键
const handleEscKey = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    emit('update:open', false)
  }
}

watch(
  () => props.open,
  (isOpen) => {
    if (isOpen) {
      document.addEventListener('keydown', handleEscKey)
    } else {
      document.removeEventListener('keydown', handleEscKey)
    }
  }
)

onUnmounted(() => {
  document.removeEventListener('keydown', handleEscKey)
})
</script>
