<template>
  <div class="todo-kanban h-full flex flex-col text-base-content">
    <!-- Detail View -->
    <TodoDetail
      v-if="showDetailView && detailTodo"
      :todo="detailTodo"
      :childTodos="getChildTodos(detailTodo.uuid) as any"
      @update="updateTodo"
      @close="closeDetailView"
      @subTodoCreated="handleSubTodoCreated"
      @showChildDetail="showTodoDetail"
      class="flex-1 overflow-y-auto"
    />

    <!-- Kanban View -->
    <div v-else class="w-full h-full flex flex-col">
      <!-- Filter Bar -->
      <FilterBar
        :filters="currentFilters"
        :display="currentDisplay"
        @filter-change="handleFilterChange"
        @display-change="handleDisplayChange"
      />

      <!-- Stats and Actions Bar -->
      <div
        class="px-5 py-2 border-b"
        style="background-color: var(--base-100); border-color: var(--base-300)"
      >
        <div class="flex items-center justify-between">
          <!-- Left: Statistics -->
          <div
            class="flex items-center space-x-4 text-xs"
            style="color: rgba(var(--base-content), 0.6)"
          >
            <span v-if="!isLoading">{{ getResultsText() }}</span>
            <span v-if="!isLoading">
              {{ getActiveTodosCount() }} 个未完成，{{ getCompletedTodosCount() }} 个已完成
            </span>
          </div>

          <!-- Right: Add button -->
          <IconButton
            class="cursor-pointer rounded-md"
            style="--hover-bg: var(--base-300)"
            icon="Plus"
            variant="ghost"
            size="sm"
            @click="createNewTodo"
            title="添加待办事项"
          />
        </div>
      </div>

      <!-- Kanban Board -->
      <div
        class="flex-1 overflow-hidden scrollbar-gutter scrollbar-thin scrollbar-track-transparent scrollbar-thumb-transparent hover:scrollbar-thumb-base-content/25"
      >
        <!-- Empty state -->
        <TodoEmptyView
          v-if="filteredTodos.length === 0"
          :emptyStateText="emptyStateText"
          @create-todo="createNewTodo"
        />

        <!-- Debug info - always visible -->
        <div class="fixed top-0 left-0 bg-red-500 text-white p-2 z-50 text-xs">
          调试: todoStore.todos长度 = {{ todoStore.todos.length }}, allTodosFlattened长度 =
          {{ allTodosFlattened.length }}
        </div>

        <!-- Kanban columns -->
        <div v-if="filteredTodos.length > 0" class="h-full grid grid-cols-2 gap-4 p-4">
          <!-- 待办 Column -->
          <div class="kanban-column">
            <div class="kanban-column-header">
              <div class="flex items-center justify-between">
                <h3 class="font-medium" style="color: var(--base-content)">
                  <component :is="getIconComponent('Circle')" class="w-4 h-4 mr-2 inline" />
                  待办
                </h3>
                <span
                  class="text-xs px-2 py-1 rounded-full"
                  style="color: rgba(var(--base-content), 0.6); background-color: var(--base-200)"
                >
                  {{ todosByStatus.todo.length }}
                </span>
              </div>
            </div>
            <div
              class="kanban-column-content scrollbar-gutter scrollbar-thin scrollbar-track-transparent scrollbar-thumb-transparent hover:scrollbar-thumb-base-content/25"
              data-status="todo"
              @drop="handleDrop($event, 'todo')"
              @dragover="handleDragOver"
            >
              <KanbanCard
                v-for="todo in todosByStatus.todo"
                :key="todo.uuid"
                :todo="todo"
                :selected="selectedTodoId === todo.uuid"
                :all-todos="allTodosFlattened"
                @update="updateTodo"
                @delete="confirmDeleteTodo"
                @select="selectTodo"
                @show-detail="showTodoDetail"
                @dragstart="handleDragStart($event, todo)"
              />
            </div>
          </div>

          <!-- 已完成 Column -->
          <div class="kanban-column">
            <div class="kanban-column-header">
              <div class="flex items-center justify-between">
                <h3 class="font-medium" style="color: var(--base-content)">
                  <component :is="getIconComponent('CheckCircle')" class="w-4 h-4 mr-2 inline" />
                  已完成
                </h3>
                <span
                  class="text-xs px-2 py-1 rounded-full"
                  style="color: rgba(var(--base-content), 0.6); background-color: var(--base-200)"
                >
                  {{ todosByStatus.completed.length }}
                </span>
              </div>
            </div>
            <div
              class="kanban-column-content scrollbar-gutter scrollbar-thin scrollbar-track-transparent scrollbar-thumb-transparent hover:scrollbar-thumb-base-content/25"
              data-status="completed"
              @drop="handleDrop($event, 'completed')"
              @dragover="handleDragOver"
            >
              <KanbanCard
                v-for="todo in todosByStatus.completed"
                :key="todo.uuid"
                :todo="todo"
                :selected="selectedTodoId === todo.uuid"
                :all-todos="allTodosFlattened"
                @update="updateTodo"
                @delete="confirmDeleteTodo"
                @select="selectTodo"
                @show-detail="showTodoDetail"
                @dragstart="handleDragStart($event, todo)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Create Todo Dialog -->
    <TodoCreateDialog
      v-model:open="showCreateDialog"
      :titlePlaceholder="'输入待办事项标题...'"
      :notesPlaceholder="'键入任务的详细内容...'"
      :createButtonText="'创建'"
      @create="handleCreateTodo"
    />

    <!-- Delete Confirmation Dialog -->
    <Dialog v-model:open="showDeleteDialog">
      <DialogContent class="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>确认删除</DialogTitle>
          <DialogDescription> 您确定要删除这个待办事项吗？此操作无法撤销。 </DialogDescription>
        </DialogHeader>
        <DialogFooter class="flex justify-end gap-2">
          <button
            class="inline-flex items-center justify-center px-3 py-2 text-sm font-medium rounded-md border border-base-300 bg-base-100 hover:bg-base-200 hover:text-base-content transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            @click="showDeleteDialog = false"
          >
            <component :is="getIconComponent('X')" class="w-4 h-4 mr-1.5" />
            取消
          </button>
          <button
            class="inline-flex items-center justify-center px-3 py-2 text-sm font-medium rounded-md bg-red-500 hover:bg-red-600 text-white transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            @click="deleteTodo"
          >
            <component :is="getIconComponent('Trash2')" class="w-4 h-4 mr-1.5" />
            删除
          </button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, reactive } from 'vue'
import { Todo } from '@renderer/services/TodoService'
import { useTodoStore, type ExtendedTodo } from '@renderer/stores/todoStore'
import KanbanCard from './KanbanCard.vue'
import { IconButton, FilterBar } from '@renderer/components/ui/v2'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@renderer/components/ui/dialog'

import TodoDetail from './TodoDetail.vue'
import TodoCreateDialog from './TodoCreateDialog.vue'
import TodoEmptyView from './TodoEmptyView.vue'
import moment from 'moment'

import { showToast } from '@renderer/utils/toast'
import { getIconComponent } from '@renderer/utils/iconComponents'

const props = defineProps<{
  listUuid: string
  filter?: string
  sort?: string
}>()

// 待办事项的拖拽状态
const draggedTodo = ref<ExtendedTodo | null>(null)

const todoStore = useTodoStore()

// 扁平化待办事项函数（直接处理 todoStore 的数据）
const flattenTodosFromStore = (todoArray: ExtendedTodo[]): ExtendedTodo[] => {
  const result: ExtendedTodo[] = []

  for (const todo of todoArray) {
    result.push(todo)

    // 递归处理子待办事项
    if (todo.children && todo.children.length > 0) {
      result.push(...flattenTodosFromStore(todo.children))
    }
  }

  return result
}

// 从 todoStore 获取扁平化的 todos
const allTodosFlattened = computed(() => {
  const flattened = flattenTodosFromStore(todoStore.todos)
  console.log('TodoKanbanView: Using todoStore data, flattened', flattened.length, 'todos')
  console.log('TodoKanbanView: todoStore.todos:', todoStore.todos)
  return flattened
})

// 初始化 todoStore 数据
onMounted(async () => {
  console.log('TodoKanbanView: onMounted, loading todoList')
  await todoStore.loadTodoList(props.listUuid)
})

// Create Todo Dialog
const showCreateDialog = ref(false)

// Delete Confirmation Dialog
const showDeleteDialog = ref(false)
const todoToDelete = ref<string | null>(null)

// Selected todo
const selectedTodoId = ref<string | null>(null)

// Detail view state
const showDetailView = ref(false)
const detailTodo = ref<Todo | null>(null)

// Filter and Display states
const currentFilters = reactive({
  status: 'all',
  priority: 'all',
  dueDate: 'all'
})

const currentDisplay = reactive({
  sort: 'priority',
  showCompletedTodos: true,
  showSubTodos: true,
  showDueDates: true,
  showPriority: true,
  viewMode: 'kanban'
})

// 使用 todoStore 的状态
const isLoading = computed(() => todoStore.isLoading)
const hasInitialLoad = computed(() => todoStore.hasInitialLoad)

// 使用 todoStore 的筛选后数据，但应用额外的筛选逻辑
const filteredTodos = computed(() => {
  let result = allTodosFlattened.value

  // Apply filters
  if (currentFilters.status !== 'all') {
    if (currentFilters.status === 'active') {
      result = result.filter((todo) => todo.isCompleted !== 1)
    } else if (currentFilters.status === 'completed') {
      result = result.filter((todo) => todo.isCompleted === 1)
    }
  }

  if (currentFilters.priority !== 'all') {
    const priorityMap = { high: 2, medium: 1, low: 0 }
    const targetPriority = priorityMap[currentFilters.priority as keyof typeof priorityMap]
    if (targetPriority !== undefined) {
      result = result.filter((todo) => todo.priority === targetPriority)
    }
  }

  if (currentFilters.dueDate !== 'all') {
    const now = moment()
    const today = moment().startOf('day')
    const tomorrow = moment().add(1, 'day').startOf('day')
    const weekEnd = moment().endOf('week')

    result = result.filter((todo) => {
      if (!todo.dueDate && currentFilters.dueDate === 'no-due-date') return true
      if (!todo.dueDate && currentFilters.dueDate !== 'no-due-date') return false

      const dueDate = moment(todo.dueDate)

      switch (currentFilters.dueDate) {
        case 'overdue':
          return dueDate.isBefore(now)
        case 'today':
          return dueDate.isSame(today, 'day')
        case 'tomorrow':
          return dueDate.isSame(tomorrow, 'day')
        case 'this-week':
          return dueDate.isBetween(today, weekEnd, 'day', '[]')
        case 'no-due-date':
          return false
        default:
          return true
      }
    })
  }

  // Apply display filters
  if (!currentDisplay.showCompletedTodos) {
    result = result.filter((todo) => todo.isCompleted !== 1)
  }

  return result
})

// 根据状态获取待办事项的计算属性（扁平化显示）
const todosByStatus = computed(() => {
  const result = {
    todo: [] as ExtendedTodo[],
    completed: [] as ExtendedTodo[]
  }

  // 使用扁平化的数据
  const flattenedTodos = allTodosFlattened.value

  for (const todo of flattenedTodos) {
    if (todo.isCompleted === 1) {
      result.completed.push(todo)
    } else {
      result.todo.push(todo)
    }
  }

  return result
})

// 拖拽处理函数
const handleDragStart = (event: DragEvent, todo: ExtendedTodo) => {
  draggedTodo.value = todo
  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'move'
    event.dataTransfer.setData('text/plain', todo.uuid)
  }
}

const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'move'
  }
}

const handleDrop = async (event: DragEvent, targetStatus: string) => {
  event.preventDefault()

  if (!draggedTodo.value) return

  const todo = draggedTodo.value
  let updates: Partial<Todo> = {}

  // 根据目标状态更新待办事项
  switch (targetStatus) {
    case 'todo':
      updates = {
        isCompleted: 0
      }
      break
    case 'completed':
      updates = {
        isCompleted: 1
      }
      break
  }

  // 更新待办事项
  await updateTodo(todo.uuid, updates)

  // 清除拖拽状态
  draggedTodo.value = null
}

// Empty state text
const emptyStateText = computed(() => {
  if (currentFilters.status === 'active') return '没有符合条件的未完成待办事项'
  if (currentFilters.status === 'completed') return '没有符合条件的已完成待办事项'
  if (currentFilters.priority !== 'all' || currentFilters.dueDate !== 'all') {
    return '没有符合筛选条件的待办事项'
  }
  return '没有待办事项'
})

// Check if any filters are active
const hasActiveFilters = computed(() => {
  return (
    currentFilters.status !== 'all' ||
    currentFilters.priority !== 'all' ||
    currentFilters.dueDate !== 'all'
  )
})

// Check if any display options are affecting visibility
const hasActiveDisplayFilters = computed(() => {
  return !currentDisplay.showCompletedTodos || !currentDisplay.showSubTodos
})

// Get results text
const getResultsText = () => {
  // 递归统计筛选结果中的所有待办事项（包括子待办事项）
  const total = countTodosRecursively(filteredTodos.value, () => true)
  if (hasActiveFilters.value) {
    return `筛选结果 (${total} 个)`
  }
  return `待办事项 (${total} 个)`
}

// 递归统计待办事项数量的辅助函数
const countTodosRecursively = (
  todoArray: ExtendedTodo[],
  filterFn: (todo: ExtendedTodo) => boolean
): number => {
  let count = 0
  for (const todo of todoArray) {
    if (filterFn(todo)) {
      count++
    }
    // 递归统计子待办事项
    if (todo.children && todo.children.length > 0) {
      count += countTodosRecursively(todo.children, filterFn)
    }
  }
  return count
}

// Get active todos count
const getActiveTodosCount = () => {
  return countTodosRecursively(todoStore.todos, (todo) => todo.isCompleted === 0)
}

// Get completed todos count
const getCompletedTodosCount = () => {
  return countTodosRecursively(todoStore.todos, (todo) => todo.isCompleted === 1)
}

// Update todo - 使用 todoStore 的方法
const updateTodo = async (uuid: string, updates: Partial<Todo>) => {
  const success = await todoStore.updateTodo(uuid, updates)

  // 更新详情视图中的todo（如果正在显示）
  if (success && detailTodo.value && detailTodo.value.uuid === uuid) {
    // 从 todoStore 中获取更新后的 todo
    const updatedTodo = todoStore.findTodoRecursively(todoStore.todos, uuid)
    if (updatedTodo) {
      detailTodo.value = {
        ...updatedTodo,
        children: JSON.stringify(updatedTodo.children.map((child: ExtendedTodo) => child.uuid))
      }
    }
  }
}

// 创建新待办事项 - 打开对话框
const createNewTodo = () => {
  showCreateDialog.value = true
}

// 处理创建待办事项 - 使用 todoStore 的方法
const handleCreateTodo = async (todoData: any) => {
  const newTodo = await todoStore.createTodo(todoData, props.listUuid)

  if (newTodo) {
    // 选中新创建的待办事项
    selectedTodoId.value = newTodo.uuid
  }
}

// 获取指定待办事项的子待办事项
const getChildTodos = (parentUuid: string): ExtendedTodo[] => {
  const parentTodo = todoStore.findTodoRecursively(todoStore.todos, parentUuid)
  return parentTodo ? parentTodo.children : []
}

// Select todo
const selectTodo = async (uuid: string) => {
  // If clicking the already selected item, deselect it
  if (selectedTodoId.value === uuid) {
    selectedTodoId.value = null
  } else {
    // Otherwise select the new item
    selectedTodoId.value = uuid
  }
}

// Show todo detail view
const showTodoDetail = async (uuid: string) => {
  // 在响应式结构中查找待办事项
  const todo = todoStore.findTodoRecursively(todoStore.todos, uuid)

  if (todo) {
    detailTodo.value = {
      ...todo,
      children: JSON.stringify(todo.children.map((child: ExtendedTodo) => child.uuid))
    }
    showDetailView.value = true
  }
}

// Close detail view
const closeDetailView = async () => {
  showDetailView.value = false
  detailTodo.value = null
}

// Handle sub-todo created
const handleSubTodoCreated = async (subTodo: Todo) => {
  try {
    if (detailTodo.value) {
      const parentUuid = detailTodo.value.uuid

      // 使用 todoStore 的方法添加子待办事项
      await todoStore.addSubTodo(parentUuid, subTodo)

      console.log(`Sub-todo created: ${subTodo.title} for parent: ${parentUuid}`)
    }
  } catch (error) {
    console.error('Error handling sub-todo creation:', error)
  }
}

// Confirm delete todo
const confirmDeleteTodo = (uuid: string) => {
  todoToDelete.value = uuid
  showDeleteDialog.value = true
}

// Delete todo - 使用 todoStore 的方法
const deleteTodo = async () => {
  if (!todoToDelete.value) return

  try {
    const success = await todoStore.deleteTodo(todoToDelete.value, props.listUuid)

    if (success) {
      console.log(`Successfully deleted todo: ${todoToDelete.value}`)
    }
  } catch (error) {
    console.error('Failed to delete todo:', error)
  } finally {
    todoToDelete.value = null
    showDeleteDialog.value = false
  }
}

// Filter and Display event handlers
const handleFilterChange = (filters: any) => {
  Object.assign(currentFilters, filters)
}

const handleDisplayChange = (display: any) => {
  Object.assign(currentDisplay, display)

  // 更新 todoStore 的显示选项
  if (display.sort) {
    todoStore.updateDisplayOptions({ sort: display.sort })
  }
}

// Watch for list UUID changes
watch(
  () => props.listUuid,
  (newUuid) => {
    if (newUuid) {
      todoStore.loadTodoList(newUuid)
    }
  }
)

// 暴露方法给父组件使用
defineExpose({
  loadTodoList: () => todoStore.loadTodoList(props.listUuid),
  todos: computed(() => todoStore.todos),
  showTodoDetail
})
</script>

<style scoped>
/* Ensure dropdown menu doesn't affect layout */
:deep([data-radix-popper-content-wrapper]) {
  position: fixed !important;
  z-index: 9999 !important;
}
</style>
