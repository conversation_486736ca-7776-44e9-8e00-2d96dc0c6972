<template>
  <div class="h-full items-center justify-center flex flex-col">
    <!-- Empty state content -->
    <div class="flex-1 flex h-full w-full flex-col items-center justify-center px-6 py-10">
      <!-- Icon container -->
      <div class="mb-4 flex items-center justify-center">
        <svg
          class="w-20 h-20 fill-base-content/70"
          enable-background="new 0 0 68 68"
          viewBox="0 0 68 68"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g>
            <path
              d="m19.7127399 21.7962475h18.4682598c.4140625 0 .75-.3359375.75-.75s-.3359375-.75-.75-.75h-18.4682598c-.4140625 0-.75.3359375-.75.75s.3359375.75.75.75z"
            />
            <path
              d="m19.7127399 25.9754467h8.2978516c.4140625 0 .75-.3359375.75-.75s-.3359375-.75-.75-.75h-8.2978516c-.4140625 0-.75.3359375-.75.75s.3359375.75.75.75z"
            />
            <path
              d="m9.8064899 20.182478c-1.4194336 1.034668-2.3022461 3.0742188-.887207 5.1069336 1.7441359 2.5077686 6.3008833 2.1407738 6.8662109-1.5942383.1567383-1.0361328-.0913086-2.0737305-.6811523-2.8461914-1.4580078-1.9116211-3.7685547-1.7822266-5.2978516-.6665039zm4.4956055 3.2880859c-.3561907 2.3555126-3.1638441 2.3823338-4.1513672.9624023 0 0 0 0 0-.0004883-.9018555-1.2949219-.2729492-2.4458008.5390625-3.0375977 1.01301-.7382946 2.3716736-.7509384 3.222168.3637695.3442383.451172.4868164 1.0756837.3901367 1.7119142z"
            />
            <path
              d="m19.7127399 33.0218353h18.4682598c.4140625 0 .75-.3359375.75-.75 0-.4140644-.3359375-.7500019-.75-.7500019h-18.4682598c-.4140625 0-.75.3359375-.75.7500019 0 .4140625.3359375.75.75.75z"
            />
            <path
              d="m19.7127399 37.2015228h8.2978516c.4140625 0 .75-.3359375.75-.75s-.3359375-.75-.75-.75h-8.2978516c-.4140625 0-.75.3359375-.75.75s.3359375.75.75.75z"
            />
            <path
              d="m8.9192829 36.5154877c1.7479877 2.5133057 6.3017712 2.1336479 6.8662109-1.59375.1567383-1.0366211-.0913086-2.0742188-.6811523-2.847168-2.887414-3.7888832-8.9904184.4100266-6.1850586 4.440918zm4.4926758-.2822266c-.987278.7020874-2.5753136.4120407-3.2612305-.5742188-1.6804123-2.4137154 2.0375843-4.9349518 3.7612305-2.675293.7255897.953045.4793568 2.5517198-.5 3.2495118z"
            />
            <path
              d="m19.7127399 44.2479095h18.4682598c.4140625 0 .75-.3359375.75-.75s-.3359375-.75-.75-.75h-18.4682598c-.4140625 0-.75.3359375-.75.75s.3359375.75.75.75z"
            />
            <path
              d="m28.0105915 46.9271088h-8.2978516c-.4140625 0-.75.3359375-.75.75s.3359375.75.75.75h8.2978516c.4140625 0 .75-.3359375.75-.75s-.3359375-.75-.75-.75z"
            />
            <path
              d="m8.9192829 47.7415619c.7079477 1.0179062 1.9471331 1.6040039 3.2045898 1.6040039 3.3277864 0 4.6673603-3.8343697 2.9804688-6.0449219-2.8828087-3.7838058-8.9931488.4061051-6.1850586 4.440918zm4.4926758-.2822266c-.9824057.6986198-2.5725441.4160233-3.2612305-.5742188-1.6804123-2.4137154 2.0375843-4.9349518 3.7612305-2.675293.7255897.953045.4793568 2.5517198-.5 3.2495118z"
            />
            <path
              d="m64.763031 39.0843353c-.4272461-.4858398-.8549805-.9726563-1.1791992-1.3334961-.3950195-.4399414-1.0795898-.4755859-1.5253906-.0810547-.1107254.0978699-13.2293167 10.9970856-13.3352051 11.0841064.0500145-33.0718384.2786064-31.0962448-.0493164-31.6426411-.0004272-.0037231.0012207-.0070801.0007324-.0108032-.6650391-4.9560536-4.6181641-9.6054677-9.1953125-10.8154286-.1820679-.0472412-.3627319-.0166016-.5192261.0601807-1.3629837-.449935-2.1349602-.0981498-33.6316528-.5177002-.1240234-.0079956-.2483521.0376587-.3640747.102478.0031738-.0471802.0050049-.0866089.0050049-.114502-1.0010357.7919922-1.8937359 1.440979-2.7224102 2.4859009-.1506348.1899414-.2299195.4325561-.2312012.6749878-.0999147 18.7850942-.2171022 37.9469003.2443848 56.2323618.0084229.3342285.2831421.5980835.6174316.5966797l43.4447021-.1810303c.5936279-.0025024 1.1304932-.3404541 1.4113159-.8634644.449646-.8376465.8155098-1.7549362.9475708-2.1622925-.0089111.0006104-.0178223.0007935-.0267334.0014038.0359383-.2751694.0161934-1.7550316.0380249-4.8646851 5.463562-4.9052734 11.0304565-9.8310547 16.645813-14.512207.5865402-.4889297.5786743-1.0654907.5838623-1.5533447.0826416-.1566162.1342773-.3293457.1484985-.5078125.0671292-.8414382-.5915393-1.1542701-1.307619-2.0776367zm-2.0180664-.0205078c.0783539.0882492 1.7245712 1.955513 1.7426758 1.9760742-7.8496094 6.4038086-13.215332 11.1259766-19.5136719 17.1806641-3.5132561-3.285183-4.7523155-3.9892578-8.1430664-7.7260742.9386406-.8727531 1.6708794-1.584549 2.5708008-2.4208984 4.6179466 4.7969933 4.9104767 5.8035355 5.6337891 5.8193359.3754883.0117188.3759766.0102539 2.4619141-2.0234375 1.9362334-1.8862267 13.6017646-11.3599472 15.2475585-12.8056641zm-15.6846313-22.4873066h-5.4115601c-.5543747 0-.9751663.1462288-1.1049805-.0444336-.0356445-.0541992-.0405273-.1113281-.0292969-.3574219.0048828-.1137695.0097656-.2314444.0063477-.3496084-.1300278-5.5291462.186264-6.2966957-.0744629-7.6012573 3.1738892 1.4372559 5.8644409 4.7948608 6.6139527 8.3527212zm.1157836 45.1792011h-41.1074199v-54.4179697c30.5422992.3990322 28.7366771.0675822 32.1293926.3691407 1.3115425.1161799.6702385 1.7183237.8232422 8.1572266.0100021.3460512-.1199951.9004946.2768555 1.503417.709034 1.0371361 1.8304138.7089844 2.3505859.7089844h5.6914063l-.1218872 31.9750385c-.3553543.3401337-.8072319.7839127-1.3643799 1.324585-3.3681717-3.5318336-3.8005791-4.7971954-5.4259644-4.8052368-.3835449-.0019531-.758606-.0344849-1.1833496-.1311646.0194092.0212402.0366211.0394897.0559692.0606689-.6989021.0617294-.6135483.23843-3.6698608 3.0609741-.5.4624023-.5361328 1.2490234-.0810547 1.7524414 3.769352 4.1787453 4.973793 4.7505417 8.9140625 8.4868164.1450195.137207.3305664.2055664.5161133.2055664.0328979 0 .0640869-.0141602.0966797-.0184326.5266724.1387329 1.0892334.0161133 1.4987183-.3527832.2027588-.1826172.4086914-.3661499.6117554-.5488892z"
            />
            <path
              d="m49.7063904 2.265975c-.3740234-.1767578-.8217773-.0136719-.9970703.3613281l-1.5678711 3.3476563c-.1757813.3754883-.0141602.8217773.3608398.9975586.3722382.1746502.8210564.0158362.9975586-.3608398l1.5678711-3.3486328c.1757813-.3750001.0136719-.8217774-.3613281-.9970704z"
            />
            <path
              d="m51.0613708 9.4070883 3.8071289-2.9238281c.328125-.2519531.3901367-.7231445.1376953-1.0517578-.2514648-.328125-.7226563-.3891602-1.0517578-.1376953l-3.8071289 2.9238281c-.328125.2519531-.3901367.7231445-.1376953 1.0517578.2514725.3272428.7218438.390748 1.0517578.1376953z"
            />
            <path
              d="m52.2547302 11.5364828c-.4086914-.0620117-.7910156.2241211-.8500977.6342773-.0595703.4101563.2246094.7905273.6342773.8500977l5.0664063.7363281c.4074707.059762.7903786-.2227001.8500977-.6342773.0595703-.409668-.2241249-.7905273-.6342773-.8500977z"
            />
          </g>
        </svg>
      </div>

      <!-- Text content -->
      <h2 class="text-xl font-medium text-title mb-2">{{ emptyStateText }}</h2>
      <p class="text-subtitle text-md mb-8">开始创建您的第一个待办事项，让工作变得更有条理</p>

      <!-- Action button -->
      <button
        @click="$emit('create-todo')"
        class="bg-accent hover:bg-accent/90 text-accent-content px-4 py-2 rounded-md flex items-center cursor-pointer transition-colors"
      >
        <PlusIcon :stroke-width="2.5" class="w-4 h-4 mr-2" />
        <span>添加待办事项</span>
      </button>

      <!-- Hint -->
      <div class="mt-8 bg-base-100 text-base-content-secondary px-4 py-3 rounded-md text-sm">
        <span>提示：您也可以使用快捷键 Ctrl+N 快速创建</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Plus as PlusIcon } from 'lucide-vue-next'

defineProps<{
  emptyStateText: string
}>()

defineEmits<{
  'create-todo': []
}>()
</script>

<style scoped>
/* 按钮悬停效果增强 */
button {
  transition: all 0.2s ease;
}
</style>
