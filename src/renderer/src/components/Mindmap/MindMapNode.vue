<template>
  <ContextMenu>
    <ContextMenuTrigger as-child>
      <div
        :style="{
          left: position.x + 'px',
          top: position.y + 'px',
          width: nodeWidth + 'px',
          height: nodeHeight + 'px'
        }"
        :draggable="(isTextNode || (isNoteNode && isCollapsed)) && !!data.parentId"
        @dragstart="handleDragStart"
        @dragend="handleDragEnd"
        @dragover="handleDragOver"
        @drop="handleDrop"
        @mousedown="handleMainMouseDown"
        @dblclick.stop="handleDoubleClick"
        @click="handleClick"
        :class="[
          'absolute font-inherit select-none overflow-hidden',
          'bg-base-background border-2 border-base-300 rounded-lg shadow-sm',
          'transition-all duration-200',
          'hover:border-blue-400 hover:shadow-md',
          {
            'cursor-move': !isNoteNode || isCollapsed,
            'border-blue-600 shadow-lg': isSelected || isEditing,
            'opacity-50 z-[1000] transition-none [&_*]:transition-none': isDragging,
            'pl-6': hasChildren,
            'transition-none [&_*]:transition-none': isResizing,
          }
        ]"
      >
        <!-- Drag Handle for Expanded Note Nodes -->
        <div
            v-if="isNoteNode && !isCollapsed"
            :draggable="!!data.parentId"
            @dragstart="handleDragStart"
            @mousedown.stop="handleDragHandleMouseDown"
            class="absolute top-0 left-0 w-full h-8 cursor-move z-20"
        ></div>

        <!-- 展开/收起按钮 -->
        <button
          v-if="hasChildren"
          @click.stop="toggleCollapse"
          @mousedown.stop
          title="展开/收起"
          :class="[
            'absolute left-0.5 top-1/2 -translate-y-1/2 w-4 h-4 rounded-full bg-base-100 border border-base-300',
            'flex items-center justify-center cursor-pointer transition-all duration-200 z-10',
            'hover:bg-base-200 hover:border-blue-400',
            { 'bg-blue-600 border-blue-600 text-white': isCollapsed }
          ]"
        >
          <svg class="w-3 h-3 transition-transform duration-200" :class="{ 'rotate-180': isCollapsed }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
        </button>

        <!-- 节点类型图标 -->
        <div class="absolute top-1 right-1 z-[5] opacity-70">
          <FileText v-if="isNoteNode" class="w-4 h-4 text-blue-500" />
          <Type v-else class="w-4 h-4 text-gray-500" />
        </div>

        <!-- 文本节点内容 -->
        <div v-if="isTextNode" class="px-3 py-2 w-full h-full flex items-center justify-center">
          <!-- 编辑模式 -->
          <textarea
            v-if="isEditing"
            ref="editInput"
            v-model="editingLabel"
            @blur="handleBlur"
            @keydown.enter.exact="handleEnter"
            @keydown.esc="handleEscape"
            @input="adjustTextareaSize"
            class="bg-transparent border-none outline-none resize-none font-inherit text-sm text-center break-words text-base-content w-full"
            :style="textareaStyle"
          />

          <!-- 显示模式 -->
          <div
            v-else
            class="text-sm cursor-pointer select-none whitespace-nowrap overflow-hidden text-ellipsis text-center w-full text-base-content"
            @dblclick="startEdit"
          >
            {{ data.label }}
          </div>
        </div>

        <!-- 笔记节点内容 -->
        <div 
          v-if="isNoteNode && !isCollapsed" 
          class="w-full h-full px-4 rounded-md"
          :class="{
            'overflow-hidden': shouldAutoResize,
            'overflow-auto': !shouldAutoResize
          }"
        >
          <NoteCore
            ref="noteEditorRef"
            v-if="noteBlock"
            :key="noteBlock.uuid"
            :node="noteBlock"
            class="w-full cursor-text"
            :class="{
              'h-auto min-h-full': shouldAutoResize,
              'h-full': !shouldAutoResize
            }"
            container-class="flex-1 relative scrollbar-gutter scrollbar-thin scrollbar-track-transparent scrollbar-thumb-transparent hover:scrollbar-thumb-base-content/25 scrollbar-h-[10px] w-full duration-300 transition-all overflow-y-auto"
            content-class="chron w-full mx-auto prose prose-sm text-base-content !px-0 whitespace-pre-wrap"
            :is-embedded="true"
            @title-changed="handleNoteContentChanged"
          />
          <div v-else class="flex items-center justify-center gap-2 h-full text-base-content-secondary text-xs">
            <div class="animate-spin w-4 h-4">⏳</div>
            <span>加载中...</span>
          </div>
        </div>

        <!-- 笔记节点折叠状态的预览 -->
        <div v-if="isNoteNode && isCollapsed" class="p-3 h-full flex flex-col">
          <div class="font-semibold text-sm mb-2 text-base-content">{{ data.label }}</div>
          <div class="text-xs text-base-content-secondary leading-normal flex-1">{{ noteExcerpt }}</div>
        </div>

        <!-- 调整大小手柄（所有节点都支持） -->
        <div
          v-if="isSelected && !isDragging"
          class="absolute bottom-0 right-0 w-3 h-3 cursor-nw-resize bg-transparent opacity-0 pointer-events-auto z-50"
          @mousedown.stop="handleResizeStart('corner', $event)"
          :title="`当前尺寸: ${nodeWidth}×${nodeHeight}`"
        />

        <!-- 四条边手柄：上、下、左、右 -->
        <div
          v-if="isSelected && !isDragging"
          class="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 w-4 h-4 cursor-ns-resize bg-transparent opacity-0 pointer-events-auto z-50"
          @mousedown.stop="handleResizeStart('top', $event)"
        />
        <div
          v-if="isSelected && !isDragging"
          class="absolute bottom-0 left-1/2 -translate-x-1/2 translate-y-1/2 w-4 h-4 cursor-ns-resize bg-transparent opacity-0 pointer-events-auto z-50"
          @mousedown.stop="handleResizeStart('bottom', $event)"
        />
        <div
          v-if="isSelected && !isDragging"
          class="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1/2 w-4 h-4 cursor-ew-resize bg-transparent opacity-0 pointer-events-auto z-50"
          @mousedown.stop="handleResizeStart('left', $event)"
        />
        <div
          v-if="isSelected && !isDragging"
          class="absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 w-4 h-4 cursor-ew-resize bg-transparent opacity-0 pointer-events-auto z-50"
          @mousedown.stop="handleResizeStart('right', $event)"
        />
      </div>
    </ContextMenuTrigger>

    <ContextMenuContent class="w-48 bg-actual-background border border-base-300 shadow-md rounded-md">
      <ContextMenuItem
        v-if="isTextNode"
        class="hover:bg-base-50 focus:bg-base-200 cursor-pointer"
        @click="handleConvertToNote"
      >
        <FileText class="w-4 h-4 mr-2" />
        转换为笔记节点
      </ContextMenuItem>

      <ContextMenuItem
        v-if="isNoteNode"
        class="hover:bg-base-50 focus:bg-base-200 cursor-pointer"
        @click="handleConvertToText"
      >
        <Type class="w-4 h-4 mr-2" />
        转换为文本节点
      </ContextMenuItem>

      <ContextMenuItem
        class="hover:bg-base-50 focus:bg-base-200 cursor-pointer"
        @click="handleAddTextChild"
      >
        <Plus class="w-4 h-4 mr-2" />
        添加文本子节点
      </ContextMenuItem>

      <ContextMenuItem
        class="hover:bg-base-50 focus:bg-base-200 cursor-pointer"
        @click="handleAddNoteChild"
      >
        <FileText class="w-4 h-4 mr-2" />
        添加笔记子节点
      </ContextMenuItem>

      <ContextMenuItem
        class="hover:bg-base-50 focus:bg-base-200 cursor-pointer text-red-600"
        @click="handleDelete"
      >
        <Trash2 class="w-4 h-4 mr-2" />
        删除节点
      </ContextMenuItem>
    </ContextMenuContent>
  </ContextMenu>
</template>

<script setup lang="ts">
import { ref, nextTick, computed, onMounted, onUnmounted, watch } from 'vue'
import { useDebounceFn } from '@vueuse/core'
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger
} from '@renderer/components/ui/context-menu'
import { Plus, Trash2, FileText, Type } from 'lucide-vue-next'
import NoteCore from '../Editor/NoteCore.vue'
import { useBlockService } from '@renderer/composables/useBlockService'

interface Props {
  id: string
  data: {
    label: string
    parentId?: string | null
    nodeType: 'text' | 'note'
    blockId?: string
    dimensions: { width: number; height: number }
    isManuallyResized?: boolean // 添加手动调整标记
  }
  position: {
    x: number
    y: number
  }
  isEditing: boolean
  hasChildren: boolean
  isCollapsed: boolean
  isDragging: boolean
  isSelected?: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update-label': [nodeId: string, newLabel: string]
  'start-edit': [nodeId: string]
  'end-edit': []
  'toggle-collapse': [nodeId: string]
  'drag-start': [nodeId: string, event: DragEvent | MouseEvent]
  'drag-end': []
  'drag-over': [nodeId: string, event: DragEvent, position: 'before' | 'after' | 'child']
  'drop': [nodeId: string, position: 'before' | 'after' | 'child']
  'add-text-child': [nodeId: string]
  'add-note-child': [nodeId: string]
  'delete-node': [nodeId: string]
  'select-node': [nodeId: string]
  'convert-node': [nodeId: string, targetType: 'text' | 'note']
  'resize-node': [nodeId: string, width: number, height: number]
  'auto-resize-node': [nodeId: string, width: number, height: number] // 新增：自动调整事件
  'note-editor-focus': [nodeId: string]
  'note-editor-blur': [nodeId: string]
}>()

// 基础状态
const editingLabel = ref(props.data.label)
const editInput = ref<HTMLTextAreaElement>()
const noteBlock = ref<any>(null)
const noteExcerpt = ref('')
const isNoteEditorFocused = ref(false) // 新增：跟踪笔记编辑器焦点状态
const noteEditorRef = ref<InstanceType<typeof NoteCore>>()

// 计算属性
const isSelected = computed(() => props.isSelected || false)
const isNoteNode = computed(() => props.data.nodeType === 'note')
const isTextNode = computed(() => props.data.nodeType === 'text')

const nodeWidth = computed(() => props.data.dimensions.width)
const nodeHeight = computed(() => props.data.dimensions.height)

// 新增：判断是否应该自动调整高度（只有笔记节点且未被手动调整过才自动调整）
const shouldAutoResize = computed(() => {
  const result = isNoteNode.value && !props.data.isManuallyResized
  return result
})

// 新增：自动调整笔记节点高度
const autoResizeNoteHeight = async () => {
  if (!shouldAutoResize.value || !noteEditorRef.value) {
    return
  }

  await nextTick()

  try {
    // 获取笔记编辑器的实际内容高度
    const editorElement = noteEditorRef.value.$el as HTMLElement
    if (!editorElement) return

    const contentElement = editorElement.querySelector('.ProseMirror') as HTMLElement
    if (!contentElement) return

    // 计算内容的实际高度
    const contentHeight = contentElement.scrollHeight
    const containerPadding = 32 // px-4 的左右内边距 (16px * 2) - 注意：这不影响高度计算
    const embeddedBottomPadding = 16 // NoteCore嵌入模式下的底部内边距 (1rem)
    const totalPadding = embeddedBottomPadding + 8 // 底部内边距 + 一些额外空间确保不出现滚动条
    const defaultHeight = 200 // 使用与创建时相同的默认高度
    
    // 计算需要的节点高度（内容高度 + 内边距，但不小于默认高度）
    const newHeight = Math.max(defaultHeight, contentHeight + totalPadding)
    
    // 自动双向调整：扩大或缩小（确保差异明显避免频繁调整）
    const heightDifference = Math.abs(newHeight - nodeHeight.value)
    const shouldResize = heightDifference > 5 // 差异大于5px才调整
    
    if (shouldResize) {
      console.log(`[笔记节点${props.id}] 自动调整高度: ${nodeHeight.value} -> ${newHeight} (${newHeight > nodeHeight.value ? '扩大' : '缩小'})`)
      emit('auto-resize-node', props.id, nodeWidth.value, newHeight)
    }
  } catch (error) {
    console.warn('自动调整笔记高度失败:', error)
  }
}

// 新增：防抖的自动调整函数 - 减少延迟以提高响应速度
const debouncedAutoResize = useDebounceFn(autoResizeNoteHeight, 100) // 从300ms减少到100ms

// 新增：快速响应的自动调整函数（用于实时输入）
const quickAutoResize = useDebounceFn(autoResizeNoteHeight, 50) // 50ms快速响应

// 新增：内容变化观察器
let contentObserver: MutationObserver | null = null

// 新增：设置内容变化监听
const setupContentObserver = () => {
  if (!shouldAutoResize.value || !noteEditorRef.value) return

  // 清理之前的观察器和事件监听器
  if (contentObserver) {
    contentObserver.disconnect()
    contentObserver = null
  }

  nextTick(() => {
    const editorElement = noteEditorRef.value?.$el as HTMLElement
    if (!editorElement) return

    const contentElement = editorElement.querySelector('.ProseMirror') as HTMLElement
    if (!contentElement) return

    // 先移除可能存在的事件监听器，避免重复绑定
    contentElement.removeEventListener('keydown', handleEditorKeydown)
    contentElement.removeEventListener('input', handleEditorInput)

    // 创建观察器监听内容变化
    contentObserver = new MutationObserver(() => {
      if (shouldAutoResize.value) {
        quickAutoResize() // 使用快速响应版本
      }
    })

    // 开始观察
    contentObserver.observe(contentElement, {
      childList: true,        // 监听子节点变化
      subtree: true,          // 监听子树变化
      characterData: true,    // 监听文本变化
      attributes: false       // 不监听属性变化
    })

    // 重新绑定键盘和输入事件
    contentElement.addEventListener('keydown', handleEditorKeydown)
    contentElement.addEventListener('input', handleEditorInput)
  })
}

// 新增：处理编辑器键盘事件
const handleEditorKeydown = (event: KeyboardEvent) => {
  if (!shouldAutoResize.value) return
  
  // 特别处理可能改变内容高度的按键
  if (event.key === 'Enter') {
    // 回车键：立即触发调整
    setTimeout(() => {
      quickAutoResize()
    }, 10) // 很短的延迟让DOM更新完成
  } else if (event.key === 'Backspace' || event.key === 'Delete') {
    // 删除键：延迟触发调整，让内容删除完成
    setTimeout(() => {
      quickAutoResize()
    }, 50) // 稍长的延迟确保删除操作完成
  }
}

// 新增：处理编辑器输入事件
const handleEditorInput = () => {
  if (shouldAutoResize.value) {
    quickAutoResize()
  }
}
const isEditing = computed(() => {
  return props.isEditing || (isNoteNode.value && isNoteEditorFocused.value)
})

// 文本框自适应样式
const textareaStyle = computed(() => ({
  minHeight: '20px',
}))

// 服务
const blockService = useBlockService()

// 新增：处理笔记编辑器焦点状态变化
const handleNoteEditorFocus = () => {
  isNoteEditorFocused.value = true
  // 通知父组件有节点进入编辑状态
  emit('note-editor-focus', props.id)
  
  // 在获得焦点时延迟触发自动调整并设置观察器（如果需要的话）
  if (shouldAutoResize.value) {
    setTimeout(() => {
      debouncedAutoResize()
      setupContentObserver() // 确保观察器已设置
    }, 200) // 短延迟，避免立即调整
  }
}

const handleNoteEditorBlur = () => {
  isNoteEditorFocused.value = false
  // 通知父组件节点退出编辑状态
  emit('note-editor-blur', props.id)
}

// 监听笔记编辑器的焦点状态
const checkNoteEditorFocus = () => {
  if (noteEditorRef.value?.$el) {
    const editorElement = noteEditorRef.value.$el.querySelector('[contenteditable]') as HTMLElement
    if (editorElement) {
      isNoteEditorFocused.value = document.activeElement === editorElement
    }
  }
}

// 定期检查焦点状态
let focusCheckInterval: number | null = null

// 自动调整文本框大小
const adjustTextareaSize = () => {
  if (editInput.value) {
    const textarea = editInput.value
    const lines = editingLabel.value.split('\n').length
    const lineHeight = 16
    const padding = 16
    const minHeight = 30
    const newHeight = Math.max(minHeight, lines * lineHeight + padding)

    // 自动调整节点高度以适应内容
    emit('auto-resize-node', props.id, nodeWidth.value, newHeight) // 改为自动调整事件
  }
}

// 加载笔记内容
const loadNoteContent = async () => {
  if (isNoteNode.value && props.data.blockId) {
    try {
      const block = await blockService.getBlockByUuid(props.data.blockId)
      if (block) {
        noteBlock.value = {
          uuid: block.uuid,
          title: block.title,
          type: block.type
        }

        // 提取文本摘要作为预览
        const content = block.contents || ''
        const textContent = content.replace(/<[^>]*>/g, '').substring(0, 50)
        noteExcerpt.value = textContent ? textContent + '...' : '空白笔记'
        
        // 内容加载完成后，如果需要自动调整高度，则延迟触发调整并设置观察器
        // 延迟是为了避免初始化时就缩小到最小尺寸
        if (shouldAutoResize.value) {
          setTimeout(() => {
            debouncedAutoResize()
            setupContentObserver() // 设置内容变化观察器
          }, 500) // 延迟500ms，让编辑器充分初始化
        }
      }
    } catch (error) {
      console.error('加载笔记内容失败:', error)
    }
  }
}

// 调整大小功能
const isResizing = ref(false)
const resizeDirection = ref<'corner' | 'right' | 'bottom' | 'left' | 'top'>('corner')
const resizeStartPos = ref({ x: 0, y: 0 })
const resizeStartSize = ref({ width: 0, height: 0 })

const handleResizeStart = (direction: 'corner' | 'right' | 'bottom' | 'left' | 'top', event: MouseEvent) => {
  resizeDirection.value = direction
  isResizing.value = true
  resizeStartPos.value = { x: event.clientX, y: event.clientY }
  resizeStartSize.value = {
    width: nodeWidth.value,
    height: nodeHeight.value
  }

  document.addEventListener('mousemove', handleResizeMove)
  document.addEventListener('mouseup', handleResizeEnd)
  event.preventDefault()
}

const handleResizeMove = (event: MouseEvent) => {
  if (!isResizing.value) return

  const deltaX = event.clientX - resizeStartPos.value.x
  const deltaY = event.clientY - resizeStartPos.value.y

  let newWidth = resizeStartSize.value.width
  let newHeight = resizeStartSize.value.height

  // 水平方向
  if (resizeDirection.value === 'corner' || resizeDirection.value === 'right') {
    newWidth = resizeStartSize.value.width + deltaX
  } else if (resizeDirection.value === 'left') {
    newWidth = resizeStartSize.value.width - deltaX
  }

  // 垂直方向
  if (resizeDirection.value === 'corner' || resizeDirection.value === 'bottom') {
    newHeight = resizeStartSize.value.height + deltaY
  } else if (resizeDirection.value === 'top') {
    newHeight = resizeStartSize.value.height - deltaY
  }

  // 应用最小尺寸限制
  const minWidth = isNoteNode.value ? 250 : 80
  const minHeight = isNoteNode.value ? 150 : 30

  newWidth = Math.max(minWidth, newWidth)
  newHeight = Math.max(minHeight, newHeight)

  emit('resize-node', props.id, newWidth, newHeight)
}

const handleResizeEnd = () => {
  isResizing.value = false
  document.removeEventListener('mousemove', handleResizeMove)
  document.removeEventListener('mouseup', handleResizeEnd)
}

// 开始编辑
const startEdit = () => {
  editingLabel.value = props.data.label
  emit('start-edit', props.id)

  nextTick(() => {
    if (editInput.value) {
      editInput.value.focus()
      editInput.value.select()
    }
  })
}

// 处理失焦
const handleBlur = () => {
  if (editingLabel.value.trim()) {
    emit('update-label', props.id, editingLabel.value.trim())
  } else {
    emit('end-edit')
  }
}

// 处理回车
const handleEnter = () => {
  if (editingLabel.value.trim()) {
    emit('update-label', props.id, editingLabel.value.trim())
  } else {
    emit('end-edit')
  }
}

// 处理ESC
const handleEscape = () => {
  editingLabel.value = props.data.label
  emit('end-edit')
}

// 切换展开/收起
const toggleCollapse = () => {
  emit('toggle-collapse', props.id)
}

const handleDragHandleMouseDown = (event: MouseEvent) => {
  // For the drag handle of an expanded note node
  if (event.button === 2) return
  if (event.detail > 1) return
  if (!props.data.parentId) emit('drag-start', props.id, event)
}

// 鼠标处理 - 用于根节点拖动
const handleMainMouseDown = (event: MouseEvent) => {
  if (isNoteNode.value && !props.isCollapsed) return

  // 右键点击不触发拖动
  if (event.button === 2) {
    return
  }

  // 如果是双击（event.detail > 1），直接让双击处理函数接管
  if (event.detail > 1) {
    return
  }

  // 只有根节点才使用鼠标拖动
  if (!props.data.parentId) {
    emit('drag-start', props.id, event) // 直接传递 MouseEvent
  }
}

// 拖拽处理
const handleDragStart = (event: DragEvent) => {
  emit('drag-start', props.id, event)
}

const handleDragEnd = () => {
  emit('drag-end')
}

const handleDragOver = (event: DragEvent) => {
  event.preventDefault()

  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
  const y = event.clientY - rect.top
  const height = rect.height
  const x = event.clientX - rect.left
  const width = rect.width

  let position: 'before' | 'after' | 'child'

  // 判断拖拽位置
  if (x > width * 0.7) {
    // 右侧70%区域 - 作为子节点
    position = 'child'
  } else if (y < height * 0.3) {
    // 上部30%区域 - 插入到前面
    position = 'before'
  } else if (y > height * 0.7) {
    // 下部30%区域 - 插入到后面
    position = 'after'
  } else {
    // 中间区域 - 作为子节点
    position = 'child'
  }

  emit('drag-over', props.id, event, position)
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()

  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
  const y = event.clientY - rect.top
  const height = rect.height
  const x = event.clientX - rect.left
  const width = rect.width

  let position: 'before' | 'after' | 'child'

  // 判断拖拽位置
  if (x > width * 0.7) {
    // 右侧70%区域 - 作为子节点
    position = 'child'
  } else if (y < height * 0.3) {
    // 上部30%区域 - 插入到前面
    position = 'before'
  } else if (y > height * 0.7) {
    // 下部30%区域 - 插入到后面
    position = 'after'
  } else {
    // 中间区域 - 作为子节点
    position = 'child'
  }

  emit('drop', props.id, position)
}

// 点击选择节点
const handleClick = (event: MouseEvent) => {
  event.stopPropagation()
  emit('select-node', props.id)
}

// 右键菜单操作
const handleAddTextChild = () => {
  emit('add-text-child', props.id)
}

const handleAddNoteChild = () => {
  emit('add-note-child', props.id)
}

const handleDelete = () => {
  emit('delete-node', props.id)
}

const handleConvertToNote = () => {
  emit('convert-node', props.id, 'note')
}

const handleConvertToText = () => {
  emit('convert-node', props.id, 'text')
}

const handleNoteContentChanged = (newTitle: string) => {
  emit('update-label', props.id, newTitle)
  
  // 在内容变化时自动调整高度并确保观察器正常
  if (shouldAutoResize.value) {
    quickAutoResize() // 使用快速响应版本
    // 确保观察器仍然有效
    if (!contentObserver) {
      setupContentObserver()
    }
  }
}

// 新增：统一的双击处理函数
const focusNoteEditorContent = () => {
  nextTick(() => {
    const el = noteEditorRef.value?.$el as HTMLElement | undefined
    const editable = el?.querySelector('[contenteditable]') as HTMLElement | null
    if (editable) {
      editable.focus()
      // 将光标移动到内容末尾
      const range = document.createRange()
      const selection = window.getSelection()
      range.selectNodeContents(editable)
      range.collapse(false) // false means collapse to end
      selection?.removeAllRanges()
      selection?.addRange(range)
    }
  })
}

const handleDoubleClick = () => {
  if (isTextNode.value) {
    // 文本节点进入编辑
    startEdit()
  } else if (isNoteNode.value) {
    if (props.isCollapsed) {
      toggleCollapse()
      // 折叠状态下先展开再聚焦
      nextTick(() => focusNoteEditorContent())
    } else {
      focusNoteEditorContent()
    }
  }

  // 如果因为双击意外触发了拖动，立即通知父组件结束拖动
  emit('drag-end')
}

// 生命周期
onMounted(() => {
  if (isNoteNode.value) {
    loadNoteContent()

    // 设置定期检查焦点状态
    focusCheckInterval = window.setInterval(() => {
      const wasFocused = isNoteEditorFocused.value
      checkNoteEditorFocus()

      // 如果焦点状态发生变化，通知父组件
      if (wasFocused !== isNoteEditorFocused.value) {
        if (isNoteEditorFocused.value) {
          emit('note-editor-focus', props.id)
        } else {
          emit('note-editor-blur', props.id)
        }
      }
    }, 100) // 每100ms检查一次
  }
})

watch(() => props.data.blockId, () => {
  if (isNoteNode.value) {
    loadNoteContent()
  }
})

// 新增：监听自动调整状态变化
watch(shouldAutoResize, (newValue) => {
  if (newValue && noteBlock.value) {
    // 当变为自动调整模式时，延迟触发一次调整并设置观察器
    setTimeout(() => {
      debouncedAutoResize()
      setupContentObserver() // 重新设置观察器
    }, 300) // 延迟触发，避免初始化时的问题
  } else {
    // 如果不需要自动调整，清理观察器
    if (contentObserver) {
      contentObserver.disconnect()
      contentObserver = null
    }
  }
}, { immediate: false }) // 不要立即执行

onUnmounted(() => {
  if (isResizing.value) {
    document.removeEventListener('mousemove', handleResizeMove)
    document.removeEventListener('mouseup', handleResizeEnd)
  }

  // 清理焦点检查定时器
  if (focusCheckInterval) {
    clearInterval(focusCheckInterval)
    focusCheckInterval = null
  }

  // 清理内容观察器
  if (contentObserver) {
    contentObserver.disconnect()
    contentObserver = null
  }

  // 清理事件监听器
  if (noteEditorRef.value) {
    const editorElement = noteEditorRef.value.$el as HTMLElement
    if (editorElement) {
      const contentElement = editorElement.querySelector('.ProseMirror') as HTMLElement
      if (contentElement) {
        contentElement.removeEventListener('keydown', handleEditorKeydown)
        contentElement.removeEventListener('input', handleEditorInput)
      }
    }
  }
})
</script>

<style lang="scss" scoped>
</style>
