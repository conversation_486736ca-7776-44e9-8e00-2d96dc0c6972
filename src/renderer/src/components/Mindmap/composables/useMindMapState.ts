import { ref, computed, watch } from 'vue'
import { useBlockService } from '@renderer/composables/useBlockService'

// 思维导图节点数据结构
export interface MindMapNodeData {
  id: string
  label: string
  children: string[]
  collapsed: boolean
  order: number
  level: number
  parentId: string | null
}

// 节点移动操作类型
export interface NodeMoveOperation {
  sourceId: string
  targetId: string
  position: 'before' | 'after' | 'child'
  type: 'hierarchy-change' | 'sibling-reorder'
}

// 移动验证结果
export interface MoveValidationResult {
  isValid: boolean
  errorType?: 'circular-dependency' | 'invalid-target' | 'same-position' | 'root-constraint'
  errorMessage?: string
}

/**
 * 思维导图状态管理组合式函数
 */
export function useMindMapState(nodeUuid: string) {
  const blockService = useBlockService()

  // 核心数据状态
  const mindMapData = ref<Map<string, MindMapNodeData>>(new Map())
  const rootNodeIds = ref<string[]>([])
  const nodeCounter = ref(1)
  const editingNodeId = ref<string | null>(null)
  const selectedNodeId = ref<string | null>(null)

  // 计算属性
  const visibleNodeIds = computed(() => {
    const result: string[] = []

    const addVisibleNodes = (nodeId: string) => {
      const node = mindMapData.value.get(nodeId)
      if (!node) return

      result.push(nodeId)

      if (!node.collapsed) {
        node.children.forEach(childId => addVisibleNodes(childId))
      }
    }

    rootNodeIds.value.forEach(rootId => addVisibleNodes(rootId))
    return result
  })

  // 辅助方法
  const hasChildren = (nodeId: string): boolean => {
    const node = mindMapData.value.get(nodeId)
    return node ? node.children.length > 0 : false
  }

  const isCollapsed = (nodeId: string): boolean => {
    const node = mindMapData.value.get(nodeId)
    return node ? node.collapsed : false
  }

  const isDescendant = (ancestorId: string, nodeId: string): boolean => {
    const node = mindMapData.value.get(nodeId)
    if (!node) return false

    for (const childId of node.children) {
      if (childId === ancestorId || isDescendant(ancestorId, childId)) {
        return true
      }
    }
    return false
  }

  // 节点操作
  const updateNodeLabel = (nodeId: string, newLabel: string) => {
    const node = mindMapData.value.get(nodeId)
    if (node) {
      node.label = newLabel
    }
  }

  const toggleCollapse = (nodeId: string) => {
    const node = mindMapData.value.get(nodeId)
    if (node && node.children.length > 0) {
      node.collapsed = !node.collapsed
    }
  }

  const addRootNode = () => {
    const newNodeId = `node-${nodeCounter.value++}`
    const newNode: MindMapNodeData = {
      id: newNodeId,
      label: '新根节点',
      children: [],
      collapsed: false,
      order: rootNodeIds.value.length,
      level: 0,
      parentId: null
    }

    mindMapData.value.set(newNodeId, newNode)
    rootNodeIds.value.push(newNodeId)
  }

  const addChildNode = (parentId: string) => {
    const parentNode = mindMapData.value.get(parentId)
    if (!parentNode) return

    const newNodeId = `node-${nodeCounter.value++}`
    const newNode: MindMapNodeData = {
      id: newNodeId,
      label: '新子节点',
      children: [],
      collapsed: false,
      order: parentNode.children.length,
      level: parentNode.level + 1,
      parentId: parentId
    }

    mindMapData.value.set(newNodeId, newNode)
    parentNode.children.push(newNodeId)

    // 如果父节点是收起状态，自动展开
    if (parentNode.collapsed) {
      parentNode.collapsed = false
    }
  }

  const deleteNode = (nodeId: string) => {
    const node = mindMapData.value.get(nodeId)
    if (!node) return

    // 递归删除所有子节点
    const deleteNodeRecursive = (id: string) => {
      const nodeToDelete = mindMapData.value.get(id)
      if (!nodeToDelete) return

      // 先删除所有子节点
      for (const childId of nodeToDelete.children) {
        deleteNodeRecursive(childId)
      }

      // 从父节点的children中移除
      if (nodeToDelete.parentId) {
        const parentNode = mindMapData.value.get(nodeToDelete.parentId)
        if (parentNode) {
          const index = parentNode.children.indexOf(id)
          if (index > -1) {
            parentNode.children.splice(index, 1)
            // 重新排序剩余子节点
            parentNode.children.forEach((childId, idx) => {
              const child = mindMapData.value.get(childId)
              if (child) {
                child.order = idx
              }
            })
          }
        }
      } else {
        // 从根节点列表中移除
        const rootIndex = rootNodeIds.value.indexOf(id)
        if (rootIndex > -1) {
          rootNodeIds.value.splice(rootIndex, 1)
        }
      }

      // 删除节点数据
      mindMapData.value.delete(id)
    }

    deleteNodeRecursive(nodeId)
  }

  // 编辑状态管理
  const startEditNode = (nodeId: string) => {
    editingNodeId.value = nodeId
  }

  const endEditNode = () => {
    editingNodeId.value = null
  }

  // 选择状态管理
  const selectNode = (nodeId: string | null) => {
    selectedNodeId.value = nodeId
  }

  const isNodeSelected = (nodeId: string): boolean => {
    return selectedNodeId.value === nodeId
  }

  // 数据持久化
  const saveMindMapData = async () => {
    try {
      const data = {
        mindMapData: Object.fromEntries(mindMapData.value),
        rootNodeIds: rootNodeIds.value,
        nodeCounter: nodeCounter.value
      }

      await blockService.updateBlock({
        uuid: nodeUuid,
        contents: JSON.stringify(data),
        updatedAt: Date.now()
      })
    } catch (error) {
      console.error('保存思维导图数据失败:', error)
    }
  }

  const loadMindMapData = async () => {
    try {
      const block = await blockService.getBlockByUuid(nodeUuid)
      if (block && block.contents) {
        const data = JSON.parse(block.contents)

        // 转换旧格式数据到新格式
        if (data.nodes && Array.isArray(data.nodes)) {
          convertLegacyData(data)
        } else if (data.mindMapData) {
          // 新格式数据
          mindMapData.value = new Map(Object.entries(data.mindMapData))
          rootNodeIds.value = data.rootNodeIds || []
          nodeCounter.value = data.nodeCounter || 1
        }
      } else {
        // 创建默认的根节点
        createDefaultRootNode()
      }
    } catch (error) {
      console.error('加载思维导图数据失败:', error)
      createDefaultRootNode()
    }
  }

  const createDefaultRootNode = () => {
    const rootId = 'root'
    const rootNode: MindMapNodeData = {
      id: rootId,
      label: '中心主题',
      children: [],
      collapsed: false,
      order: 0,
      level: 0,
      parentId: null
    }

    mindMapData.value.set(rootId, rootNode)
    rootNodeIds.value = [rootId]
    nodeCounter.value = 1
  }

  const convertLegacyData = (data: any) => {
    const newMindMapData = new Map<string, MindMapNodeData>()
    const newRootNodeIds: string[] = []

    // 构建父子关系映射
    const childrenMap = new Map<string, string[]>()
    const parentMap = new Map<string, string>()

    if (data.edges) {
      for (const edge of data.edges) {
        if (!childrenMap.has(edge.source)) {
          childrenMap.set(edge.source, [])
        }
        childrenMap.get(edge.source)!.push(edge.target)
        parentMap.set(edge.target, edge.source)
      }
    }

    // 转换节点数据
    if (data.nodes) {
      for (let i = 0; i < data.nodes.length; i++) {
        const oldNode = data.nodes[i]
        const nodeData: MindMapNodeData = {
          id: oldNode.id,
          label: oldNode.data?.label || '未命名节点',
          children: childrenMap.get(oldNode.id) || [],
          collapsed: false,
          order: i,
          level: 0, // 将在后面计算
          parentId: parentMap.get(oldNode.id) || null
        }

        newMindMapData.set(oldNode.id, nodeData)

        if (!nodeData.parentId) {
          newRootNodeIds.push(oldNode.id)
        }
      }
    }

    // 计算层级
    const calculateLevel = (nodeId: string, level: number = 0) => {
      const node = newMindMapData.get(nodeId)
      if (node) {
        node.level = level
        for (const childId of node.children) {
          calculateLevel(childId, level + 1)
        }
      }
    }

    for (const rootId of newRootNodeIds) {
      calculateLevel(rootId)
    }

    mindMapData.value = newMindMapData
    rootNodeIds.value = newRootNodeIds

    // 更新节点计数器
    const maxId = Math.max(...Array.from(newMindMapData.keys()).map(id => {
      const match = id.match(/node-(\d+)/)
      return match ? parseInt(match[1]) : 0
    }))
    nodeCounter.value = maxId + 1
  }

  // 自动保存监听
  watch([mindMapData, rootNodeIds], () => {
    saveMindMapData()
  }, { deep: true })

  return {
    // 状态
    mindMapData,
    rootNodeIds,
    nodeCounter,
    editingNodeId,
    selectedNodeId,
    visibleNodeIds,

    // 辅助方法
    hasChildren,
    isCollapsed,
    isDescendant,

    // 节点操作
    updateNodeLabel,
    toggleCollapse,
    addRootNode,
    addChildNode,
    deleteNode,

    // 编辑状态
    startEditNode,
    endEditNode,

    // 选择状态
    selectNode,
    isNodeSelected,

    // 数据持久化
    loadMindMapData,
    saveMindMapData
  }
}
