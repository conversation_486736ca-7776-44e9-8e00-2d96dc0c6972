import { ref, computed, type Ref } from 'vue'
import type { VisibleNodeData } from './useMindMapLayout'
import { LAYOUT_CONFIG } from './useMindMapLayout'

// 缩放和平移配置
export const ZOOM_CONFIG = {
  MIN_ZOOM: 0.1,
  MAX_ZOOM: 3,
  ZOOM_STEP: 0.1,
  ZOOM_WHEEL_FACTOR: 0.001,
  PAN_SPEED: 1,
  KEYBOARD_PAN_STEP: 50
}

/**
 * 思维导图视口控制组合式函数
 */
export function useMindMapViewport(
  canvasContainer: Ref<HTMLElement | undefined>,
  visibleNodes: Ref<VisibleNodeData[]>
) {
  // 视图状态
  const zoomLevel = ref(1)
  const canvasSize = ref({ width: 2000, height: 1500 })
  const panOffset = ref({ x: 0, y: 0 })
  const isPanning = ref(false)
  const isSpacePanning = ref(false)
  const lastPanPoint = ref({ x: 0, y: 0 })

  // 计算画布样式
  const canvasStyle = computed(() => ({
    width: canvasSize.value.width + 'px',
    height: canvasSize.value.height + 'px',
    transform: `scale(${zoomLevel.value}) translate(${panOffset.value.x}px, ${panOffset.value.y}px)`,
    transformOrigin: '0 0',
    cursor: isPanning.value || isSpacePanning.value ? 'grabbing' : 'default'
  }))

  // 缩放功能
  const setZoom = (newZoom: number) => {
    zoomLevel.value = Math.max(ZOOM_CONFIG.MIN_ZOOM, Math.min(ZOOM_CONFIG.MAX_ZOOM, newZoom))
  }

  const zoomIn = () => {
    const newZoom = Math.min(zoomLevel.value + ZOOM_CONFIG.ZOOM_STEP, ZOOM_CONFIG.MAX_ZOOM)
    setZoom(newZoom)
  }

  const zoomOut = () => {
    const newZoom = Math.max(zoomLevel.value - ZOOM_CONFIG.ZOOM_STEP, ZOOM_CONFIG.MIN_ZOOM)
    setZoom(newZoom)
  }

  // 重置视图
  const resetView = () => {
    zoomLevel.value = 1
    panOffset.value = { x: 0, y: 0 }
  }

  // 适应画布内容
  const fitToContent = () => {
    if (visibleNodes.value.length === 0) return

    // 计算所有节点的边界
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity

    visibleNodes.value.forEach(node => {
      minX = Math.min(minX, node.position.x)
      minY = Math.min(minY, node.position.y)
      maxX = Math.max(maxX, node.position.x + LAYOUT_CONFIG.NODE_WIDTH)
      maxY = Math.max(maxY, node.position.y + LAYOUT_CONFIG.NODE_HEIGHT)
    })

    if (!canvasContainer.value) return

    const containerRect = canvasContainer.value.getBoundingClientRect()
    const contentWidth = maxX - minX
    const contentHeight = maxY - minY

    // 计算合适的缩放比例，留出一些边距
    const padding = 100
    const scaleX = (containerRect.width - padding * 2) / contentWidth
    const scaleY = (containerRect.height - padding * 2) / contentHeight
    const newZoom = Math.min(scaleX, scaleY, ZOOM_CONFIG.MAX_ZOOM)

    // 计算居中的偏移量
    const centerX = (containerRect.width / newZoom - contentWidth) / 2 - minX + padding / newZoom
    const centerY = (containerRect.height / newZoom - contentHeight) / 2 - minY + padding / newZoom

    zoomLevel.value = Math.max(ZOOM_CONFIG.MIN_ZOOM, newZoom)
    panOffset.value = { x: centerX, y: centerY }
  }

  // 鼠标滚轮缩放
  const handleWheel = (event: WheelEvent) => {
    if (event.ctrlKey || event.metaKey) {
      event.preventDefault()

      const delta = -event.deltaY * ZOOM_CONFIG.ZOOM_WHEEL_FACTOR
      const newZoom = zoomLevel.value * (1 + delta)
      setZoom(newZoom)
    }
  }

  // 双击重置视图
  const handleDoubleClick = (event: MouseEvent) => {
    // 只有在空白区域双击才重置视图
    if (event.target === canvasContainer.value || (event.target as HTMLElement).classList.contains('mindmap-canvas')) {
      resetView()
    }
  }

  // 鼠标拖拽平移
  const handleMouseDown = (event: MouseEvent) => {
    // 只有在空白区域或者按住空格键时才开始平移
    if (event.target === canvasContainer.value ||
        (event.target as HTMLElement).classList.contains('mindmap-canvas') ||
        isSpacePanning.value) {

      event.preventDefault()
      isPanning.value = true
      lastPanPoint.value = { x: event.clientX, y: event.clientY }

      // 改变鼠标样式
      if (canvasContainer.value) {
        canvasContainer.value.style.cursor = 'grabbing'
      }
    }
  }

  const handleMouseMove = (event: MouseEvent) => {
    if (isPanning.value) {
      event.preventDefault()

      const deltaX = (event.clientX - lastPanPoint.value.x) / zoomLevel.value
      const deltaY = (event.clientY - lastPanPoint.value.y) / zoomLevel.value

      panOffset.value = {
        x: panOffset.value.x + deltaX,
        y: panOffset.value.y + deltaY
      }

      lastPanPoint.value = { x: event.clientX, y: event.clientY }
    }
  }

  const handleMouseUp = () => {
    if (isPanning.value) {
      isPanning.value = false

      // 恢复鼠标样式
      if (canvasContainer.value) {
        canvasContainer.value.style.cursor = isSpacePanning.value ? 'grab' : 'default'
      }
    }
  }

  const handleMouseLeave = () => {
    if (isPanning.value) {
      isPanning.value = false

      // 恢复鼠标样式
      if (canvasContainer.value) {
        canvasContainer.value.style.cursor = 'default'
      }
    }
  }

  // 键盘事件处理
  const handleKeyDown = (event: KeyboardEvent, editingNodeId: Ref<string | null>) => {
    // 如果正在编辑节点，不处理画布快捷键
    if (editingNodeId.value) return

    // 方向键移动画布
    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
      event.preventDefault()

      const step = ZOOM_CONFIG.KEYBOARD_PAN_STEP / zoomLevel.value

      switch (event.key) {
        case 'ArrowUp':
          panOffset.value.y += step
          break
        case 'ArrowDown':
          panOffset.value.y -= step
          break
        case 'ArrowLeft':
          panOffset.value.x += step
          break
        case 'ArrowRight':
          panOffset.value.x -= step
          break
      }
    }

    // 缩放快捷键
    if (event.ctrlKey || event.metaKey) {
      if (event.key === '=' || event.key === '+') {
        event.preventDefault()
        zoomIn()
      } else if (event.key === '-') {
        event.preventDefault()
        zoomOut()
      } else if (event.key === '0') {
        event.preventDefault()
        resetView()
      }
    }
  }

  // 全局键盘事件处理（空格键平移模式）
  const handleGlobalKeyDown = (event: KeyboardEvent, editingNodeId: Ref<string | null>) => {
    if (event.code === 'Space' && !isSpacePanning.value && !editingNodeId.value) {
      event.preventDefault()
      isSpacePanning.value = true

      // 改变鼠标样式
      if (canvasContainer.value) {
        canvasContainer.value.style.cursor = 'grab'
      }
    }
  }

  const handleGlobalKeyUp = (event: KeyboardEvent) => {
    if (event.code === 'Space' && isSpacePanning.value) {
      isSpacePanning.value = false
      isPanning.value = false

      // 恢复鼠标样式
      if (canvasContainer.value) {
        canvasContainer.value.style.cursor = 'default'
      }
    }
  }

  return {
    // 状态
    zoomLevel,
    canvasSize,
    panOffset,
    isPanning,
    isSpacePanning,

    // 计算属性
    canvasStyle,

    // 缩放控制
    zoomIn,
    zoomOut,
    setZoom,
    resetView,
    fitToContent,

    // 事件处理
    handleWheel,
    handleDoubleClick,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    handleMouseLeave,
    handleKeyDown,
    handleGlobalKeyDown,
    handleGlobalKeyUp
  }
}
