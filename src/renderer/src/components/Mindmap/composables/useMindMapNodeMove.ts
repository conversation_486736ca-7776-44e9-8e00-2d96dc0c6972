import { nextTick, type Ref } from 'vue'
import type { MindMapNodeData, NodeMoveOperation, MoveValidationResult } from './useMindMapState'

/**
 * 思维导图节点移动操作组合式函数
 */
export function useMindMapNodeMove(
  mindMapData: Ref<Map<string, MindMapNodeData>>,
  rootNodeIds: Ref<string[]>
) {
  // 验证数据完整性
  const validateDataIntegrity = (): { isValid: boolean; errorMessage?: string } => {
    try {
      // 检查所有节点的父子关系是否一致
      for (const [nodeId, node] of mindMapData.value) {
        // 检查父节点引用
        if (node.parentId) {
          const parentNode = mindMapData.value.get(node.parentId)
          if (!parentNode) {
            return {
              isValid: false,
              errorMessage: `节点 ${nodeId} 的父节点 ${node.parentId} 不存在`
            }
          }

          if (!parentNode.children.includes(nodeId)) {
            return {
              isValid: false,
              errorMessage: `父节点 ${node.parentId} 的children数组中缺少子节点 ${nodeId}`
            }
          }
        } else {
          // 根节点应该在rootNodeIds中
          if (!rootNodeIds.value.includes(nodeId)) {
            return {
              isValid: false,
              errorMessage: `根节点 ${nodeId} 不在rootNodeIds数组中`
            }
          }
        }

        // 检查子节点引用
        for (const childId of node.children) {
          const childNode = mindMapData.value.get(childId)
          if (!childNode) {
            return {
              isValid: false,
              errorMessage: `节点 ${nodeId} 的子节点 ${childId} 不存在`
            }
          }

          if (childNode.parentId !== nodeId) {
            return {
              isValid: false,
              errorMessage: `子节点 ${childId} 的parentId与实际父节点 ${nodeId} 不匹配`
            }
          }
        }
      }

      // 检查rootNodeIds中的节点是否都存在且为根节点
      for (const rootId of rootNodeIds.value) {
        const rootNode = mindMapData.value.get(rootId)
        if (!rootNode) {
          return {
            isValid: false,
            errorMessage: `根节点列表中的节点 ${rootId} 不存在`
          }
        }

        if (rootNode.parentId !== null) {
          return {
            isValid: false,
            errorMessage: `根节点列表中的节点 ${rootId} 不是真正的根节点`
          }
        }
      }

      return { isValid: true }
    } catch (error) {
      return {
        isValid: false,
        errorMessage: `完整性验证过程中发生错误: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  }

  // 创建数据快照
  const createDataSnapshot = () => {
    return {
      mindMapData: new Map(mindMapData.value),
      rootNodeIds: [...rootNodeIds.value],
      timestamp: Date.now()
    }
  }

  // 恢复数据快照
  const restoreDataSnapshot = (snapshot: any) => {
    try {
      mindMapData.value = new Map(snapshot.mindMapData)
      rootNodeIds.value = [...snapshot.rootNodeIds]
      console.log('数据已回滚到移动操作前的状态')
    } catch (error) {
      console.error('数据回滚失败:', error)
    }
  }

  // 从父节点中移除节点
  const removeNodeFromParent = (nodeId: string) => {
    const node = mindMapData.value.get(nodeId)
    if (!node) return

    if (node.parentId) {
      const parentNode = mindMapData.value.get(node.parentId)
      if (parentNode) {
        const index = parentNode.children.indexOf(nodeId)
        if (index > -1) {
          parentNode.children.splice(index, 1)
          // 重新排序剩余子节点
          parentNode.children.forEach((childId, idx) => {
            const child = mindMapData.value.get(childId)
            if (child) child.order = idx
          })
        }
      }
    } else {
      // 从根节点列表中移除
      const index = rootNodeIds.value.indexOf(nodeId)
      if (index > -1) {
        rootNodeIds.value.splice(index, 1)
      }
    }
  }

  // 更新子树层级
  const updateSubtreeLevel = (nodeId: string) => {
    const node = mindMapData.value.get(nodeId)
    if (!node) return

    const parentLevel = node.parentId ?
      (mindMapData.value.get(node.parentId)?.level || 0) : -1

    node.level = parentLevel + 1

    for (const childId of node.children) {
      updateSubtreeLevel(childId)
    }
  }

  // 处理同级节点重排
  const processSiblingReorder = async (operation: NodeMoveOperation): Promise<void> => {
    const { sourceId, targetId, position } = operation
    const sourceNode = mindMapData.value.get(sourceId)
    const targetNode = mindMapData.value.get(targetId)

    if (!sourceNode || !targetNode) return

    const parentId = sourceNode.parentId

    if (parentId) {
      // 处理非根节点的同级重排
      const parentNode = mindMapData.value.get(parentId)
      if (!parentNode) return

      const children = [...parentNode.children]
      const sourceIndex = children.indexOf(sourceId)
      const targetIndex = children.indexOf(targetId)

      if (sourceIndex === -1 || targetIndex === -1) return

      // 移除源节点
      children.splice(sourceIndex, 1)

      // 计算新的插入位置
      const newTargetIndex = children.indexOf(targetId)
      const insertIndex = position === 'before' ? newTargetIndex : newTargetIndex + 1

      // 插入到新位置
      children.splice(insertIndex, 0, sourceId)

      // 更新父节点的children数组
      parentNode.children = children

      // 重新分配order
      children.forEach((childId, index) => {
        const child = mindMapData.value.get(childId)
        if (child) child.order = index
      })
    } else {
      // 处理根节点的重排
      const rootIds = [...rootNodeIds.value]
      const sourceIndex = rootIds.indexOf(sourceId)
      const targetIndex = rootIds.indexOf(targetId)

      if (sourceIndex === -1 || targetIndex === -1) return

      // 移除源节点
      rootIds.splice(sourceIndex, 1)

      // 计算新的插入位置
      const newTargetIndex = rootIds.indexOf(targetId)
      const insertIndex = position === 'before' ? newTargetIndex : newTargetIndex + 1

      // 插入到新位置
      rootIds.splice(insertIndex, 0, sourceId)

      // 更新根节点数组
      rootNodeIds.value = rootIds
    }
  }

  // 处理层级变更
  const processHierarchyChange = async (operation: NodeMoveOperation): Promise<void> => {
    const { sourceId, targetId, position } = operation
    const sourceNode = mindMapData.value.get(sourceId)
    const targetNode = mindMapData.value.get(targetId)

    if (!sourceNode || !targetNode) return

    // 1. 从原位置移除
    removeNodeFromParent(sourceId)

    // 2. 添加到新位置
    if (position === 'child') {
      // 作为子节点添加
      sourceNode.parentId = targetId
      sourceNode.level = targetNode.level + 1
      sourceNode.order = targetNode.children.length
      targetNode.children.push(sourceId)

      // 自动展开目标节点
      targetNode.collapsed = false
    } else {
      // 作为兄弟节点添加
      const targetParentId = targetNode.parentId
      sourceNode.parentId = targetParentId
      sourceNode.level = targetNode.level

      if (targetParentId) {
        const parentNode = mindMapData.value.get(targetParentId)
        if (parentNode) {
          const targetIndex = parentNode.children.indexOf(targetId)
          const insertIndex = position === 'before' ? targetIndex : targetIndex + 1
          parentNode.children.splice(insertIndex, 0, sourceId)

          // 重新排序
          parentNode.children.forEach((childId, idx) => {
            const child = mindMapData.value.get(childId)
            if (child) child.order = idx
          })
        }
      } else {
        // 目标是根节点
        const targetIndex = rootNodeIds.value.indexOf(targetId)
        const insertIndex = position === 'before' ? targetIndex : targetIndex + 1
        rootNodeIds.value.splice(insertIndex, 0, sourceId)
        sourceNode.level = 0
      }
    }

    // 3. 更新子树层级
    updateSubtreeLevel(sourceId)
  }

  // 计算移动操作的类型
  const getMoveOperationType = (operation: NodeMoveOperation): 'hierarchy-change' | 'sibling-reorder' => {
    const { sourceId, targetId, position } = operation
    const sourceNode = mindMapData.value.get(sourceId)
    const targetNode = mindMapData.value.get(targetId)

    if (!sourceNode || !targetNode) return 'hierarchy-change'

    if (position === 'child') {
      return 'hierarchy-change'
    }

    // 检查是否为同级调整
    return sourceNode.parentId === targetNode.parentId ? 'sibling-reorder' : 'hierarchy-change'
  }

  // 统一的节点移动处理函数
  const processNodeMove = async (operation: NodeMoveOperation): Promise<boolean> => {
    // 1. 确定操作类型
    operation.type = getMoveOperationType(operation)

    // 2. 创建数据快照用于回滚
    const dataSnapshot = createDataSnapshot()

    try {
      // 3. 执行移动操作
      if (operation.type === 'sibling-reorder') {
        await processSiblingReorder(operation)
      } else {
        await processHierarchyChange(operation)
      }

      // 4. 验证数据完整性
      const integrityCheck = validateDataIntegrity()
      if (!integrityCheck.isValid) {
        throw new Error(`数据完整性验证失败: ${integrityCheck.errorMessage}`)
      }

      // 5. 触发重新布局计算
      await nextTick()

      console.log(`节点移动成功: ${operation.sourceId} -> ${operation.targetId} (${operation.position})`)
      return true
    } catch (error) {
      console.error('节点移动失败:', error)

      // 回滚数据
      restoreDataSnapshot(dataSnapshot)

      console.warn(`移动失败: ${error instanceof Error ? error.message : '未知错误'}`)
      return false
    }
  }

  return {
    // 核心移动方法
    processNodeMove,
    
    // 辅助方法
    validateDataIntegrity,
    removeNodeFromParent,
    updateSubtreeLevel,
    
    // 数据快照
    createDataSnapshot,
    restoreDataSnapshot
  }
}
