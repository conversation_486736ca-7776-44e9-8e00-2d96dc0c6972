import { ref, computed, type Ref } from 'vue'
import type { MindMapNodeData } from './useMindMapState'

// 节点位置信息
export interface NodePosition {
  x: number
  y: number
}

// 可见节点信息
export interface VisibleNodeData {
  id: string
  data: MindMapNodeData
  position: NodePosition
}

// 连接线信息
export interface EdgeData {
  id: string
  path: string
}

// 布局常量
export const LAYOUT_CONFIG = {
  NODE_WIDTH: 120,
  NODE_HEIGHT: 40,
  HORIZONTAL_SPACING: 200,
  VERTICAL_SPACING: 80,
  ROOT_START_X: 100,
  ROOT_START_Y: 100
}

/**
 * 思维导图布局计算组合式函数
 */
export function useMindMapLayout(
  mindMapData: Ref<Map<string, MindMapNodeData>>,
  rootNodeIds: Ref<string[]>,
  visibleNodeIds: Ref<string[]>
) {
  // 缓存节点位置计算结果
  const cachedPositions = ref<Map<string, NodePosition>>(new Map())
  const positionsCacheKey = ref<string>('')

  // 计算位置缓存键
  const calculatePositionsCacheKey = (): string => {
    const dataKeys = Array.from(mindMapData.value.keys()).sort().join(',')
    const rootKeys = rootNodeIds.value.join(',')
    const visibleKeys = visibleNodeIds.value.join(',')
    return `${dataKeys}-${rootKeys}-${visibleKeys}`
  }

  // 计算子树高度
  const calculateSubtreeHeight = (nodeId: string): number => {
    const node = mindMapData.value.get(nodeId)
    if (!node || node.collapsed || node.children.length === 0) {
      return LAYOUT_CONFIG.NODE_HEIGHT
    }

    const childrenHeight = node.children.reduce((sum, childId) => {
      return sum + calculateSubtreeHeight(childId)
    }, 0)

    const spacingHeight = (node.children.length - 1) * LAYOUT_CONFIG.VERTICAL_SPACING

    return Math.max(LAYOUT_CONFIG.NODE_HEIGHT, childrenHeight + spacingHeight)
  }

  // 计算单个节点及其子树的位置
  const calculateNodePosition = (
    nodeId: string,
    x: number,
    y: number,
    positions: Map<string, NodePosition>
  ) => {
    const node = mindMapData.value.get(nodeId)
    if (!node) return

    positions.set(nodeId, { x, y })

    if (node.collapsed || node.children.length === 0) {
      return
    }

    // 计算子节点位置
    const childrenIds = [...node.children].sort((a, b) => {
      const nodeA = mindMapData.value.get(a)
      const nodeB = mindMapData.value.get(b)
      return (nodeA?.order || 0) - (nodeB?.order || 0)
    })

    const totalChildrenHeight = childrenIds.reduce((sum, childId) => {
      return sum + calculateSubtreeHeight(childId)
    }, 0) + (childrenIds.length - 1) * LAYOUT_CONFIG.VERTICAL_SPACING

    let currentChildY = y - totalChildrenHeight / 2

    for (const childId of childrenIds) {
      const childHeight = calculateSubtreeHeight(childId)
      const childY = currentChildY + childHeight / 2

      calculateNodePosition(
        childId,
        x + LAYOUT_CONFIG.HORIZONTAL_SPACING,
        childY,
        positions
      )

      currentChildY += childHeight + LAYOUT_CONFIG.VERTICAL_SPACING
    }
  }

  // 计算所有节点位置
  const calculateNodePositions = (): Map<string, NodePosition> => {
    const positions = new Map<string, NodePosition>()

    // 为每个根节点计算位置
    let currentRootY = LAYOUT_CONFIG.ROOT_START_Y

    for (const rootId of rootNodeIds.value) {
      const rootHeight = calculateSubtreeHeight(rootId)
      const rootY = currentRootY + rootHeight / 2

      calculateNodePosition(rootId, LAYOUT_CONFIG.ROOT_START_X, rootY, positions)
      currentRootY += rootHeight + LAYOUT_CONFIG.VERTICAL_SPACING * 2
    }

    return positions
  }

  // 获取节点位置（带缓存）
  const getNodePositions = (): Map<string, NodePosition> => {
    const currentCacheKey = calculatePositionsCacheKey()

    // 如果缓存键没有变化，直接返回缓存结果
    if (positionsCacheKey.value === currentCacheKey && cachedPositions.value.size > 0) {
      return cachedPositions.value
    }

    // 重新计算位置
    const positions = calculateNodePositions()
    cachedPositions.value = positions
    positionsCacheKey.value = currentCacheKey

    return positions
  }

  // 清除位置缓存
  const clearPositionCache = () => {
    cachedPositions.value.clear()
    positionsCacheKey.value = ''
  }

  // 创建连接线路径
  const createConnectionPath = (parentPos: NodePosition, childPos: NodePosition): string => {
    const startX = parentPos.x + LAYOUT_CONFIG.NODE_WIDTH
    const startY = parentPos.y + LAYOUT_CONFIG.NODE_HEIGHT / 2
    const endX = childPos.x
    const endY = childPos.y + LAYOUT_CONFIG.NODE_HEIGHT / 2

    const midX = startX + (endX - startX) / 2

    return `M ${startX} ${startY} C ${midX} ${startY} ${midX} ${endY} ${endX} ${endY}`
  }

  // 检查节点是否可见
  const isNodeVisible = (nodeId: string): boolean => {
    const node = mindMapData.value.get(nodeId)
    if (!node) return false

    // 根节点总是可见
    if (!node.parentId) return true

    // 检查所有祖先节点是否都展开
    let currentId: string | null = node.parentId
    while (currentId) {
      const currentNode = mindMapData.value.get(currentId)
      if (!currentNode) return false

      if (currentNode.collapsed) return false
      currentId = currentNode.parentId
    }

    return true
  }

  // 计算可见节点数据
  const visibleNodes = computed(() => {
    const result: VisibleNodeData[] = []
    const positions = getNodePositions()

    for (const [nodeId, nodeData] of mindMapData.value) {
      if (isNodeVisible(nodeId)) {
        const position = positions.get(nodeId)
        if (position) {
          result.push({
            id: nodeId,
            data: nodeData,
            position
          })
        }
      }
    }

    return result
  })

  // 计算可见连接线
  const visibleEdges = computed(() => {
    const result: EdgeData[] = []
    const positions = getNodePositions()

    for (const [nodeId, nodeData] of mindMapData.value) {
      if (nodeData.parentId && isNodeVisible(nodeId) && isNodeVisible(nodeData.parentId)) {
        const parentPos = positions.get(nodeData.parentId)
        const childPos = positions.get(nodeId)

        if (parentPos && childPos) {
          const path = createConnectionPath(parentPos, childPos)
          result.push({
            id: `edge-${nodeData.parentId}-${nodeId}`,
            path
          })
        }
      }
    }

    return result
  })

  // 计算画布边界
  const getCanvasBounds = () => {
    const positions = getNodePositions()
    if (positions.size === 0) {
      return {
        minX: 0,
        minY: 0,
        maxX: LAYOUT_CONFIG.NODE_WIDTH,
        maxY: LAYOUT_CONFIG.NODE_HEIGHT
      }
    }

    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity

    for (const position of positions.values()) {
      minX = Math.min(minX, position.x)
      minY = Math.min(minY, position.y)
      maxX = Math.max(maxX, position.x + LAYOUT_CONFIG.NODE_WIDTH)
      maxY = Math.max(maxY, position.y + LAYOUT_CONFIG.NODE_HEIGHT)
    }

    return { minX, minY, maxX, maxY }
  }

  // 计算所有节点的详细边界信息（包含节点实际尺寸）
  const calculateNodesBounds = (mindMapData: Map<string, any>) => {
    const positions = getNodePositions()
    if (positions.size === 0) {
      return {
        minX: 0,
        minY: 0,
        maxX: LAYOUT_CONFIG.NODE_WIDTH,
        maxY: LAYOUT_CONFIG.NODE_HEIGHT
      }
    }

    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity

    for (const [nodeId, position] of positions.entries()) {
      const nodeData = mindMapData.get(nodeId)
      const nodeWidth = nodeData?.dimensions?.width || LAYOUT_CONFIG.NODE_WIDTH
      const nodeHeight = nodeData?.dimensions?.height || LAYOUT_CONFIG.NODE_HEIGHT

      minX = Math.min(minX, position.x)
      minY = Math.min(minY, position.y)
      maxX = Math.max(maxX, position.x + nodeWidth)
      maxY = Math.max(maxY, position.y + nodeHeight)
    }

    return { minX, minY, maxX, maxY }
  }

  // 检查节点是否接近画布边界
  const isNodeNearBoundary = (
    nodeId: string,
    canvasWidth: number,
    canvasHeight: number,
    mindMapData: Map<string, any>,
    gap: number = 80
  ) => {
    const positions = getNodePositions()
    const position = positions.get(nodeId)
    if (!position) return { near: false, directions: [] }

    const nodeData = mindMapData.get(nodeId)
    const nodeWidth = nodeData?.dimensions?.width || LAYOUT_CONFIG.NODE_WIDTH
    const nodeHeight = nodeData?.dimensions?.height || LAYOUT_CONFIG.NODE_HEIGHT

    const directions: string[] = []

    // 检查各个方向的边界
    if (position.x <= gap) directions.push('left')
    if (position.y <= gap) directions.push('top')
    if (position.x + nodeWidth >= canvasWidth - gap) directions.push('right')
    if (position.y + nodeHeight >= canvasHeight - gap) directions.push('bottom')

    return {
      near: directions.length > 0,
      directions,
      position,
      nodeWidth,
      nodeHeight
    }
  }

  // 计算需要的画布扩展量
  const calculateCanvasExpansion = (
    mindMapData: Map<string, any>,
    currentCanvasWidth: number,
    currentCanvasHeight: number,
    gap: number = 80,
    expansionStep: number = 400
  ) => {
    const bounds = calculateNodesBounds(mindMapData)

    let newWidth = currentCanvasWidth
    let newHeight = currentCanvasHeight
    let needsExpansion = false

    // 检查是否需要向右扩展
    if (bounds.maxX >= currentCanvasWidth - gap) {
      newWidth = Math.max(currentCanvasWidth + expansionStep, bounds.maxX + gap * 2)
      needsExpansion = true
    }

    // 检查是否需要向下扩展
    if (bounds.maxY >= currentCanvasHeight - gap) {
      newHeight = Math.max(currentCanvasHeight + expansionStep, bounds.maxY + gap * 2)
      needsExpansion = true
    }

    // 检查是否需要向左扩展（较少见，但可能发生）
    if (bounds.minX <= gap) {
      newWidth = Math.max(currentCanvasWidth + expansionStep, newWidth)
      needsExpansion = true
    }

    // 检查是否需要向上扩展（较少见，但可能发生）
    if (bounds.minY <= gap) {
      newHeight = Math.max(currentCanvasHeight + expansionStep, newHeight)
      needsExpansion = true
    }

    return {
      needsExpansion,
      newWidth,
      newHeight,
      bounds
    }
  }

  return {
    // 常量
    LAYOUT_CONFIG,

    // 计算方法
    getNodePositions,
    clearPositionCache,
    calculateSubtreeHeight,
    createConnectionPath,
    isNodeVisible,
    getCanvasBounds,
    calculateNodesBounds,
    isNodeNearBoundary,
    calculateCanvasExpansion,

    // 计算属性
    visibleNodes,
    visibleEdges
  }
}
