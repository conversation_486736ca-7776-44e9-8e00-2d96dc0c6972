import { ref, nextTick, type Ref } from 'vue'
import type { MindMapNodeData, NodeMoveOperation, MoveValidationResult } from './useMindMapState'
import type { NodePosition } from './useMindMapLayout'
import { LAYOUT_CONFIG } from './useMindMapLayout'

// 拖拽指示器
export interface DropIndicator {
  visible: boolean
  x: number
  y: number
  width: number
  height: number
}

// 拖拽状态管理
export interface DragState {
  isDragging: boolean
  draggedNodeId: string | null
  dragOverNodeId: string | null
  dropPosition: 'before' | 'after' | 'child' | null
  previewPosition: NodePosition | null
  isValidDrop: boolean
}

/**
 * 思维导图拖拽功能组合式函数
 */
export function useMindMapDrag(
  mindMapData: Ref<Map<string, MindMapNodeData>>,
  rootNodeIds: Ref<string[]>,
  getNodePositions: () => Map<string, NodePosition>
) {
  // 拖拽状态
  const dragState = ref<DragState>({
    isDragging: false,
    draggedNodeId: null,
    dragOverNodeId: null,
    dropPosition: null,
    previewPosition: null,
    isValidDrop: false
  })

  const dropIndicator = ref<DropIndicator>({
    visible: false,
    x: 0,
    y: 0,
    width: 0,
    height: 0
  })

  // 验证节点移动操作的有效性
  const validateNodeMove = (operation: NodeMoveOperation): MoveValidationResult => {
    const { sourceId, targetId, position } = operation

    try {
      // 基础验证
      if (!sourceId || !targetId) {
        return {
          isValid: false,
          errorType: 'invalid-target',
          errorMessage: '节点ID不能为空'
        }
      }

      if (sourceId === targetId) {
        return {
          isValid: false,
          errorType: 'same-position',
          errorMessage: '不能将节点移动到自身'
        }
      }

      const sourceNode = mindMapData.value.get(sourceId)
      const targetNode = mindMapData.value.get(targetId)

      if (!sourceNode || !targetNode) {
        return {
          isValid: false,
          errorType: 'invalid-target',
          errorMessage: '源节点或目标节点不存在'
        }
      }

      // 检查循环依赖
      if (isDescendant(targetId, sourceId)) {
        return {
          isValid: false,
          errorType: 'circular-dependency',
          errorMessage: '不能将节点移动到其子节点中，这会造成循环依赖'
        }
      }

      // 检查根节点约束
      if (sourceId === 'root' && position === 'child') {
        return {
          isValid: false,
          errorType: 'root-constraint',
          errorMessage: '根节点不能作为其他节点的子节点'
        }
      }

      // 检查是否已经在目标位置
      if (isAlreadyInTargetPosition(sourceId, targetId, position)) {
        return {
          isValid: false,
          errorType: 'same-position',
          errorMessage: '节点已经在目标位置'
        }
      }

      return { isValid: true }
    } catch (error) {
      console.error('节点移动验证过程中发生错误:', error)
      return {
        isValid: false,
        errorType: 'invalid-target',
        errorMessage: '验证过程中发生未知错误'
      }
    }
  }

  // 检查是否为后代节点
  const isDescendant = (ancestorId: string, nodeId: string): boolean => {
    const node = mindMapData.value.get(nodeId)
    if (!node) return false

    for (const childId of node.children) {
      if (childId === ancestorId || isDescendant(ancestorId, childId)) {
        return true
      }
    }
    return false
  }

  // 检查节点是否已经在目标位置
  const isAlreadyInTargetPosition = (sourceId: string, targetId: string, position: 'before' | 'after' | 'child'): boolean => {
    const sourceNode = mindMapData.value.get(sourceId)
    const targetNode = mindMapData.value.get(targetId)

    if (!sourceNode || !targetNode) return false

    if (position === 'child') {
      return sourceNode.parentId === targetId
    } else {
      // 检查是否为相邻的兄弟节点
      if (sourceNode.parentId !== targetNode.parentId) return false

      const parentId = sourceNode.parentId
      let siblings: string[]

      if (parentId) {
        const parentNode = mindMapData.value.get(parentId)
        if (!parentNode) return false
        siblings = parentNode.children
      } else {
        siblings = rootNodeIds.value
      }

      const sourceIndex = siblings.indexOf(sourceId)
      const targetIndex = siblings.indexOf(targetId)

      if (sourceIndex === -1 || targetIndex === -1) return false

      // 检查是否已经在相邻位置
      if (position === 'before') {
        return sourceIndex === targetIndex - 1
      } else {
        return sourceIndex === targetIndex + 1
      }
    }
  }

  // 计算拖拽预览位置
  const calculatePreviewPosition = (targetNodeId: string, position: 'before' | 'after' | 'child'): NodePosition | null => {
    const positions = getNodePositions()
    const targetPos = positions.get(targetNodeId)

    if (!targetPos) return null

    const offset = 20 // 预览偏移量

    if (position === 'child') {
      // 作为子节点时，显示在目标节点右侧，但更靠近当前节点
      return {
        x: targetPos.x + LAYOUT_CONFIG.NODE_WIDTH + 40, // 减少间距，从 HORIZONTAL_SPACING/2 (100) 改为 40
        y: targetPos.y
      }
    } else {
      // 作为兄弟节点时，显示在目标节点上方或下方
      const yOffset = position === 'before' ? -LAYOUT_CONFIG.NODE_HEIGHT - offset : LAYOUT_CONFIG.NODE_HEIGHT + offset
      return {
        x: targetPos.x,
        y: targetPos.y + yOffset
      }
    }
  }

  // 更新拖拽指示器
  const updateDropIndicator = (nodeId: string, position: 'before' | 'after' | 'child', isValid: boolean = true) => {
    const positions = getNodePositions()
    const nodePos = positions.get(nodeId)

    if (!nodePos) {
      dropIndicator.value.visible = false
      return
    }

    dropIndicator.value.visible = true

    if (position === 'child') {
      dropIndicator.value.x = nodePos.x + LAYOUT_CONFIG.NODE_WIDTH + 40
      dropIndicator.value.y = nodePos.y
      dropIndicator.value.width = LAYOUT_CONFIG.NODE_WIDTH
      dropIndicator.value.height = LAYOUT_CONFIG.NODE_HEIGHT
    } else {
      dropIndicator.value.x = nodePos.x
      dropIndicator.value.y = position === 'before' ?
        nodePos.y - 2 : nodePos.y + LAYOUT_CONFIG.NODE_HEIGHT
      dropIndicator.value.width = LAYOUT_CONFIG.NODE_WIDTH
      dropIndicator.value.height = 4
    }
  }

  // 开始拖拽节点
  const handleNodeDragStart = (nodeId: string, event: DragEvent) => {
    // 更新拖拽状态
    dragState.value = {
      isDragging: true,
      draggedNodeId: nodeId,
      dragOverNodeId: null,
      dropPosition: null,
      previewPosition: null,
      isValidDrop: false
    }

    // 设置拖拽数据
    event.dataTransfer!.setData('text/plain', nodeId)
    event.dataTransfer!.effectAllowed = 'move'

    // 创建拖拽影子
    const dragImage = document.createElement('div')
    dragImage.className = 'drag-ghost'
    dragImage.textContent = mindMapData.value.get(nodeId)?.label || ''
    dragImage.style.cssText = `
      position: absolute;
      top: -1000px;
      background: rgba(59, 130, 246, 0.1);
      border: 2px dashed #3b82f6;
      padding: 8px 12px;
      border-radius: 8px;
      font-size: 14px;
      color: #3b82f6;
      pointer-events: none;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    `
    document.body.appendChild(dragImage)
    event.dataTransfer!.setDragImage(dragImage, 60, 20)

    // 清理拖拽影子
    setTimeout(() => {
      if (document.body.contains(dragImage)) {
        document.body.removeChild(dragImage)
      }
    }, 0)
  }

  // 结束拖拽
  const handleNodeDragEnd = () => {
    // 重置拖拽状态
    dragState.value = {
      isDragging: false,
      draggedNodeId: null,
      dragOverNodeId: null,
      dropPosition: null,
      previewPosition: null,
      isValidDrop: false
    }

    // 隐藏拖拽指示器
    dropIndicator.value.visible = false
  }

  // 拖拽悬停处理
  const handleNodeDragOver = (nodeId: string, event: DragEvent, position: 'before' | 'after' | 'child') => {
    if (dragState.value.draggedNodeId === nodeId) return

    event.preventDefault()
    event.dataTransfer!.dropEffect = 'move'

    // 验证拖拽操作
    const operation: NodeMoveOperation = {
      sourceId: dragState.value.draggedNodeId!,
      targetId: nodeId,
      position,
      type: 'hierarchy-change' // 临时值，会在processNodeMove中重新计算
    }

    const validation = validateNodeMove(operation)

    // 更新拖拽状态
    dragState.value.dragOverNodeId = nodeId
    dragState.value.dropPosition = position
    dragState.value.isValidDrop = validation.isValid

    // 计算预览位置
    if (validation.isValid) {
      dragState.value.previewPosition = calculatePreviewPosition(nodeId, position)
    }

    // 更新拖拽指示器
    updateDropIndicator(nodeId, position, validation.isValid)
  }

  return {
    // 状态
    dragState,
    dropIndicator,

    // 验证方法
    validateNodeMove,

    // 拖拽处理方法
    handleNodeDragStart,
    handleNodeDragEnd,
    handleNodeDragOver,

    // 辅助方法
    calculatePreviewPosition,
    updateDropIndicator
  }
}
