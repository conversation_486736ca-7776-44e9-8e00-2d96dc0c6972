<template>
  <div class="w-full h-full flex flex-col bg-base-background">
    <!-- 工具栏 -->
    <div class="flex items-center justify-between p-3 border-b border-base-200">
      <div class="flex items-center gap-3">
        <h2 class="text-base font-medium text-base-content">{{ headerTitle }}</h2>
      </div>
      <HeaderActions
        :left-actions="leftActions"
        :menu-actions="menuActions"
        :show-split-controls="true"
        @split-horizontal="handleSplitHorizontal"
        @split-vertical="handleSplitVertical"
        @close="handleClosePane"
        @action="handleAction"
      />
    </div>

    <!-- 思维导图画布 -->
    <div
      class="flex-1 relative overflow-hidden mindmap-canvas-container"
      ref="canvasContainer"
      @wheel="handleWheel"
      @mousedown="handleMouseDown"
      @mousemove="handleMouseMove"
      @mouseup="handleMouseUp"
      @mouseleave="handleMouseLeave"
      @keydown="handleKeyDown"
      tabindex="0"
      :style="{
        cursor: isPanning || isSpacePanning ? 'grabbing' : (dragState.isDragging ? 'grabbing' : 'default')
      }"
    >
      <!-- 画布内容容器 -->
      <div
        class="mindmap-canvas"
        :class="{ 'interacting': isInteracting }"
        :style="{
          width: dynamicCanvasSize.width + 'px',
          height: dynamicCanvasSize.height + 'px',
          transform: `scale(${zoomLevel}) translate(${panOffset.x}px, ${panOffset.y}px)`,
          transformOrigin: '0 0'
        }"
      >
      <!-- 连接线 SVG -->
      <svg
        class="absolute inset-0 pointer-events-none"
        :width="dynamicCanvasSize.width"
        :height="dynamicCanvasSize.height"
      >
        <path
          v-for="edge in visibleEdges"
          :key="edge.id"
          :d="edge.path"
          stroke="#94a3b8"
          stroke-width="2"
          fill="none"
          :class="{
            'transition-all duration-300': !rootDragState.isDragging && !isInteracting,
            'transition-none': rootDragState.isDragging || isInteracting
          }"
        />
      </svg>

      <!-- 节点渲染 -->
      <MindMapNode
        v-for="nodeData in visibleNodes"
        :key="nodeData.id"
        :id="nodeData.id"
        :data="nodeData.data"
        :position="nodeData.position"
        :is-editing="editingNodeId === nodeData.id"
        :is-selected="isNodeSelected(nodeData.id)"
        :has-children="hasChildren(nodeData.id)"
        :is-collapsed="isCollapsed(nodeData.id)"
        :is-dragging="dragState.draggedNodeId === nodeData.id || rootDragState.draggedRootId === nodeData.id || isNodeInDraggedSubtree(nodeData.id)"
        @update-label="updateNodeLabel"
        @start-edit="startEditNode"
        @end-edit="endEditNode"
        @add-text-child="addChildNode"
        @add-note-child="addNoteChildNode"
        @delete-node="deleteNode"
        @select-node="selectNode"
        @toggle-collapse="toggleCollapse"
        @convert-node="handleNodeConvert"
        @resize-node="handleNodeResize"
        @auto-resize-node="handleNodeAutoResize"
        @note-editor-focus="handleNoteEditorFocus"
        @note-editor-blur="handleNoteEditorBlur"
        @drag-start="handleNodeDragStart"
        @drag-end="handleNodeDragEnd"
        @drag-over="handleNodeDragOver"
        @drop="handleNodeDrop"
      />

      <!-- 拖拽预览节点 -->
      <div
        v-if="dragState.isDragging && dragState.previewPosition"
        class="absolute pointer-events-none z-40 opacity-60 transform scale-95"
        :class="{
          'transition-all duration-200': !isInteracting,
          'transition-none': isInteracting
        }"
        :style="{
          left: dragState.previewPosition.x + 'px',
          top: dragState.previewPosition.y + 'px'
        }"
      >
        <div class="animate-preview-pulse bg-base-100 border-2 border-blue-600 rounded-lg px-3 py-2 min-w-[80px] w-[120px] h-[40px] shadow-[0_8px_25px_rgba(59,130,246,0.3)] text-sm text-base-content flex items-center justify-center text-center whitespace-nowrap overflow-hidden text-ellipsis backdrop-blur-sm">
          {{ mindMapData.get(dragState.draggedNodeId!)?.label || '' }}
        </div>
      </div>
      </div>
    </div>

    <!-- 视图控制面板 -->
    <div class="absolute bottom-4 right-4 flex flex-col gap-2 bg-base-100 rounded-lg shadow-lg p-2 border border-base-200">
      <!-- 缩放比例显示 -->
      <div class="text-xs text-base-content text-center px-2 py-1 bg-base-50 rounded">
        {{ Math.round(zoomLevel * 100) }}%
      </div>

      <!-- 缩放控制 -->
      <button
        @click="zoomIn"
        class="w-8 h-8 flex items-center justify-center text-base-content hover:bg-base-200 rounded transition-colors"
        title="放大 (Ctrl/Cmd + +)"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
        </svg>
      </button>
      <button
        @click="zoomOut"
        class="w-8 h-8 flex items-center justify-center text-base-content hover:bg-base-200 rounded transition-colors"
        title="缩小 (Ctrl/Cmd + -)"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />
        </svg>
      </button>

      <!-- 分隔线 -->
      <div class="h-px bg-base-200 mx-1"></div>

      <!-- 适应画布 -->
      <button
        @click="fitToContent"
        class="w-8 h-8 flex items-center justify-center text-base-content hover:bg-base-200 rounded transition-colors"
        title="适应画布大小"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
        </svg>
      </button>

      <!-- 重置视图 -->
      <button
        @click="resetView"
        class="w-8 h-8 flex items-center justify-center text-base-content hover:bg-base-200 rounded transition-colors"
        title="重置视图 (双击画布)"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
      </button>

      <!-- 分隔线 -->
      <div class="h-px bg-base-200 mx-1"></div>

      <!-- 定位到中心 -->
      <button
        @click="centerView"
        class="w-8 h-8 flex items-center justify-center text-base-content hover:bg-base-200 rounded transition-colors"
        title="定位到中心"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
        </svg>
      </button>
    </div>

    <!-- 操作提示 -->
    <div class="absolute bottom-4 left-4 text-xs text-base-content-secondary bg-base-100 rounded-lg shadow-lg p-3 border border-base-200 max-w-xs">
      <div class="space-y-1">
        <div><kbd class="font-mono text-xs font-medium leading-none px-1 py-0.5 bg-base-200 rounded">滚轮</kbd> 缩放</div>
        <div><kbd class="font-mono text-xs font-medium leading-none px-1 py-0.5 bg-base-200 rounded">拖拽</kbd> 或 <kbd class="px-1 py-0.5 bg-base-200 rounded text-xs">空格+拖拽</kbd> 平移</div>
        <div><kbd class="font-mono text-xs font-medium leading-none px-1 py-0.5 bg-base-200 rounded">方向键</kbd> 移动画布</div>
        <div><kbd class="font-mono text-xs font-medium leading-none px-1 py-0.5 bg-base-200 rounded">双击</kbd> 重置视图</div>
      </div>

      <!-- 性能信息（开发模式） -->
      <div v-if="showDebugInfo" class="mt-2 pt-2 border-t border-base-200 space-y-1">
        <div>节点总数: {{ mindMapData.size }}</div>
        <div>可见节点: {{ visibleNodes.length }}</div>
        <div>可见连接线: {{ visibleEdges.length }}</div>
        <div>视口: {{ Math.round(viewport.width) }}×{{ Math.round(viewport.height) }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed, nextTick } from 'vue'
import { BaseNode } from '@renderer/componentSystem/common/BaseNode'
import { useBlockService } from '@renderer/composables/useBlockService'
import MindMapNode from './MindMapNode.vue'
import { HeaderActions } from '@renderer/components/ui/v2'
import type { ActionItem } from '@renderer/components/ui/v2/header-actions/HeaderActions.vue'
import {
  Download,
  Copy,
  Share2,
  Save,
  Plus,
  RotateCcw,
  FileText
} from 'lucide-vue-next'
import { v4 } from 'uuid'
import { useDebounceFn } from '@vueuse/core'

// 思维导图节点数据结构
interface MindMapNodeData {
  id: string
  label: string
  children: string[]
  collapsed: boolean
  order: number
  level: number
  parentId: string | null
  customPosition?: NodePosition // 根节点的自定义位置
  // 新增统一的节点属性
  nodeType: 'text' | 'note'    // 节点类型
  blockId?: string             // 笔记节点关联的Block ID
  dimensions: {                // 所有节点都有尺寸
    width: number
    height: number
  }
  isManuallyResized?: boolean  // 是否被手动调整过大小
}

// 节点位置信息
interface NodePosition {
  x: number
  y: number
}

// 内联类型定义，只保留必要的类型
type NodeMoveOperation = { sourceId: string; targetId: string; position: 'before' | 'after' | 'child'; type: 'hierarchy-change' | 'sibling-reorder' }
type MoveValidationResult = { isValid: boolean; errorType?: string; errorMessage?: string }

interface Props {
  node: BaseNode
  panel_id: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'title-changed': [title: string]
  'close-pane': []
  'split-horizontal': []
  'split-vertical': []
}>()

// 组件引用
const canvasContainer = ref<HTMLElement>()

// 数据状态
const mindMapData = ref<Map<string, MindMapNodeData>>(new Map())
const editingNodeId = ref<string | null>(null)
const noteEditingNodeIds = ref(new Set<string>()) // 跟踪正在编辑的笔记节点
const selectedNodeId = ref<string | null>(null)
const nodeCounter = ref(1)
const rootNodeIds = ref<string[]>([])

// 视图状态
const zoomLevel = ref(1)
const panOffset = ref({ x: 0, y: 0 })
const isPanning = ref(false)
const isSpacePanning = ref(false)
const lastPanPoint = ref({ x: 0, y: 0 })



// 视口信息
const viewport = ref({
  x: 0,
  y: 0,
  width: 0,
  height: 0
})

// 调试信息显示控制
const showDebugInfo = ref(false)

// 交互状态管理
const isInteracting = ref(false)

// 缩放和平移配置
const ZOOM_CONFIG = {
  MIN_ZOOM: 0.1,
  MAX_ZOOM: 3,
  ZOOM_STEP: 0.1,
  ZOOM_WHEEL_FACTOR: 0.001,
  PAN_SPEED: 1,
  KEYBOARD_PAN_STEP: 50
}

// 拖拽状态 - 使用内联类型
const dragState = ref({
  isDragging: false,
  draggedNodeId: null as string | null,
  dragOverNodeId: null as string | null,
  dropPosition: null as 'before' | 'after' | 'child' | null,
  previewPosition: null as NodePosition | null,
  isValidDrop: false
})

// 根节点拖动状态
const rootDragState = ref({
  isDragging: false,
  draggedRootId: null as string | null,
  startPosition: null as NodePosition | null,
  currentPosition: null as NodePosition | null,
  mouseOffset: null as NodePosition | null // 鼠标在节点内的相对位置
})



// 统一的布局常量
const LAYOUT_CONFIG = {
  // 通用间距
  HORIZONTAL_SPACING: 200,
  VERTICAL_SPACING: 80,
  ROOT_START_X: 0,
  ROOT_START_Y: 0,

  // 节点尺寸配置
  NODE_DEFAULTS: {
    text: {
      width: 120,
      height: 40,
      minWidth: 80,
      maxWidth: 300,
      minHeight: 30,
      maxHeight: 100
    },
    note: {
      width: 300,
      height: 200,
      minWidth: 250,
      maxWidth: 600,
      minHeight: 150,
      maxHeight: 400
    }
  }
}

// 服务
const blockService = useBlockService()

// 获取节点默认尺寸
const getDefaultNodeDimensions = (nodeType: 'text' | 'note'): { width: number; height: number } => {
  const config = LAYOUT_CONFIG.NODE_DEFAULTS[nodeType]
  return {
    width: config.width,
    height: config.height
  }
}

// 验证并修正节点尺寸（移除最大限制，只保留最小限制）
const validateNodeDimensions = (nodeType: 'text' | 'note', width: number, height: number): { width: number; height: number } => {
  const config = LAYOUT_CONFIG.NODE_DEFAULTS[nodeType]
  return {
    width: Math.max(config.minWidth, width),
    height: Math.max(config.minHeight, height)
  }
}

// 创建新节点（统一接口）
const createNode = async (nodeType: 'text' | 'note', parentId?: string, label?: string): Promise<string> => {
  const nodeId = `${nodeType}-${nodeCounter.value++}`
  let blockId: string | undefined = undefined

  // 如果是笔记节点，创建关联的Block记录
  if (nodeType === 'note') {
    blockId = v4()
    const nodeLabel = label || '新笔记节点'

    await blockService.createBlock({
      uuid: blockId,
      type: 'note',
      title: nodeLabel,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      contents: `<h1>${nodeLabel}</h1><p>在这里开始编写内容...</p>`,
      meta: JSON.stringify([])
    })
  }

  const newNode: MindMapNodeData = {
    id: nodeId,
    label: label || (nodeType === 'note' ? '新笔记节点' : '新节点'),
    children: [],
    collapsed: false,
    order: parentId ? (mindMapData.value.get(parentId)?.children.length || 0) : rootNodeIds.value.length,
    level: parentId ? ((mindMapData.value.get(parentId)?.level || 0) + 1) : 0,
    parentId: parentId || null,
    nodeType,
    blockId,
    dimensions: getDefaultNodeDimensions(nodeType),
    isManuallyResized: false  // 新节点默认没有被手动调整过大小
  }

  mindMapData.value.set(nodeId, newNode)

  if (parentId) {
    const parentNode = mindMapData.value.get(parentId)
    if (parentNode) {
      parentNode.children.push(nodeId)
      parentNode.collapsed = false
    }
  } else {
    rootNodeIds.value.push(nodeId)
  }

  return nodeId
}

// 统一的节点类型转换
const convertNodeType = async (nodeId: string, targetType: 'text' | 'note') => {
  const node = mindMapData.value.get(nodeId)
  if (!node || node.nodeType === targetType) return

  if (targetType === 'note' && node.nodeType === 'text') {
    // 文本节点转笔记节点
    const blockId = v4()
    await blockService.createBlock({
      uuid: blockId,
      type: 'note',
      title: node.label,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      contents: `<h1>${node.label}</h1><p>转换自文本节点</p>`,
      meta: JSON.stringify([])
    })

    node.nodeType = 'note'
    node.blockId = blockId
    node.dimensions = getDefaultNodeDimensions('note')
    node.isManuallyResized = false  // 转换后重置手动调整标记

  } else if (targetType === 'text' && node.nodeType === 'note') {
    // 笔记节点转文本节点
    if (node.blockId) {
      // 可选：删除关联的Block记录，或者保留作为历史
      // await blockService.deleteBlock(node.blockId)
    }

    node.nodeType = 'text'
    delete node.blockId
    node.dimensions = getDefaultNodeDimensions('text')
    node.isManuallyResized = false  // 转换后重置手动调整标记
  }

  // 清除布局缓存，触发重新计算
  clearAllCaches()
}

// 统一的节点尺寸更新
const updateNodeDimensions = (nodeId: string, width: number, height: number, isManualResize = false) => {
  const node = mindMapData.value.get(nodeId)
  if (!node) return

  const validatedDimensions = validateNodeDimensions(node.nodeType, width, height)
  node.dimensions = validatedDimensions

  // 如果是手动调整，标记节点为已手动调整过
  if (isManualResize) {
    node.isManuallyResized = true
    console.log(`[Editor] 节点${nodeId}被标记为手动调整，将禁用自动调整`)
  }

  // 清除布局缓存，触发重新计算
  clearAllCaches()
}

// 智能计算画布边界
const calculateCanvasBounds = () => {
  const positions = getNodePositions()
  if (positions.size === 0) return { x: 0, y: 0, width: 800, height: 600 }

  let minX = Infinity
  let minY = Infinity
  let maxX = -Infinity
  let maxY = -Infinity

  positions.forEach((pos, nodeId) => {
    const nodeData = mindMapData.value.get(nodeId)
    const nodeWidth = nodeData?.dimensions.width || LAYOUT_CONFIG.NODE_DEFAULTS.text.width
    const nodeHeight = nodeData?.dimensions.height || LAYOUT_CONFIG.NODE_DEFAULTS.text.height

    minX = Math.min(minX, pos.x)
    minY = Math.min(minY, pos.y)
    maxX = Math.max(maxX, pos.x + nodeWidth)
    maxY = Math.max(maxY, pos.y + nodeHeight)
  })

  const padding = 300
  return {
    x: minX - padding,
    y: minY - padding,
    width: maxX - minX + padding * 2,
    height: maxY - minY + padding * 2
  }
}

// 计算动态画布尺寸
const dynamicCanvasSize = computed(() => {
  const bounds = calculateCanvasBounds()
  return {
    width: Math.max(2000, bounds.width),
    height: Math.max(1500, bounds.height)
  }
})

// 防抖更新视口信息
let viewportUpdateTimer: number | null = null
const updateViewport = () => {
  if (!canvasContainer.value) return

  // 清除之前的定时器
  if (viewportUpdateTimer) {
    clearTimeout(viewportUpdateTimer)
  }

  // 设置新的定时器，防抖处理
  viewportUpdateTimer = window.setTimeout(() => {
    if (!canvasContainer.value) return

    const rect = canvasContainer.value.getBoundingClientRect()
    viewport.value = {
      x: -panOffset.value.x,
      y: -panOffset.value.y,
      width: rect.width / zoomLevel.value,
      height: rect.height / zoomLevel.value
    }
    viewportUpdateTimer = null
  }, 16) // 约60fps的更新频率
}



// 重构原有的添加子节点方法
const addChildNode = async (parentId: string) => {
  await createNode('text', parentId)
}

const addNoteChildNode = async (parentId: string) => {
  await createNode('note', parentId)
}

// 添加根节点
const addRootNode = async () => {
  await createNode('text')
}

const addRootNoteNode = async () => {
  await createNode('note')
}

// 处理节点类型转换
const handleNodeConvert = async (nodeId: string, targetType: 'text' | 'note') => {
  await convertNodeType(nodeId, targetType)
}

// 处理节点大小调整
const handleNodeResize = (nodeId: string, width: number, height: number) => {
  updateNodeDimensions(nodeId, width, height, true)  // 通过resize事件触发的都是手动调整
}

// 处理节点自动大小调整
const handleNodeAutoResize = (nodeId: string, width: number, height: number) => {
  updateNodeDimensions(nodeId, width, height, false)  // 自动调整不标记为手动调整
}

// 简化的 HeaderActions 配置
const leftActions = computed<ActionItem[]>(() => [
  { id: 'add-text-node', label: '添加文本节点', icon: Plus, title: '添加文本节点', action: addRootNode },
  { id: 'add-note-node', label: '添加笔记节点', icon: FileText, title: '添加笔记节点', action: addRootNoteNode },
  { id: 'reset-view', label: '重置视图', icon: RotateCcw, title: '重置视图', action: resetView }
])

const menuActions = computed<ActionItem[]>(() => [
  { id: 'add-text-child', label: '添加文本子节点', icon: Plus, action: () => selectedNodeId.value ? addChildNode(selectedNodeId.value) : addRootNode() },
  { id: 'add-note-child', label: '添加笔记子节点', icon: FileText, action: () => selectedNodeId.value ? addNoteChildNode(selectedNodeId.value) : addRootNoteNode() },
  { id: 'save-mindmap', label: '保存思维导图', icon: Save, action: handleSaveMindMap },
  { id: 'export-mindmap', label: '导出思维导图', icon: Download, action: handleExportMindMap },
  { id: 'copy-mindmap', label: '复制思维导图', icon: Copy, action: handleCopyMindMap },
  { id: 'share-mindmap', label: '分享思维导图', icon: Share2, action: handleShareMindMap }
])

// ===== 简化的位置计算 =====

// 获取节点位置（简化版本，移除复杂缓存）
const getNodePositions = (): Map<string, NodePosition> => {
  return calculateNodePositions()
}

// 缓存视口信息，避免频繁计算
let cachedViewport: { x: number; y: number; width: number; height: number } | null = null
let lastViewportUpdate = 0

// 检查节点是否在视口内（带缓冲区）- 优化版本
const isNodeInViewport = (position: NodePosition): boolean => {
  // 使用缓存的视口信息，避免频繁重新计算
  const now = Date.now()
  if (!cachedViewport || now - lastViewportUpdate > 16) { // 最多60fps更新频率
    if (canvasContainer.value) {
      const rect = canvasContainer.value.getBoundingClientRect()
      cachedViewport = {
        x: -panOffset.value.x,
        y: -panOffset.value.y,
        width: rect.width / zoomLevel.value,
        height: rect.height / zoomLevel.value
      }
      lastViewportUpdate = now
    }
  }

  if (!cachedViewport) return true // 如果无法获取视口信息，默认显示

  // 获取当前节点数据来确定尺寸
  let nodeWidth = LAYOUT_CONFIG.NODE_DEFAULTS.text.width
  let nodeHeight = LAYOUT_CONFIG.NODE_DEFAULTS.text.height

  // 尝试从现有数据中获取实际尺寸
  for (const [nodeId, nodeData] of mindMapData.value.entries()) {
    const nodePos = getNodePositions().get(nodeId)
    if (nodePos && nodePos.x === position.x && nodePos.y === position.y) {
      nodeWidth = nodeData.dimensions.width
      nodeHeight = nodeData.dimensions.height
      break
    }
  }

  const buffer = 300 // 增大缓冲区，减少频繁的显示/隐藏切换

  return position.x + nodeWidth >= cachedViewport.x - buffer &&
         position.x <= cachedViewport.x + cachedViewport.width + buffer &&
         position.y + nodeHeight >= cachedViewport.y - buffer &&
         position.y <= cachedViewport.y + cachedViewport.height + buffer
}

// 缓存可见节点计算结果
let cachedVisibleNodes: Array<{ id: string; data: any; position: NodePosition }> = []
let lastVisibleNodesUpdate = 0

// 优化的可见节点计算 - 结合层级可见性和视口裁剪
const visibleNodes = computed(() => {
  const now = Date.now()

  // 在根节点拖动时禁用缓存，确保实时更新
  if (rootDragState.value.isDragging) {
    const positions = getNodePositions()
    return Array.from(mindMapData.value.entries())
      .filter(([nodeId]) => isNodeVisible(nodeId)) // 层级可见性检查
      .map(([nodeId, nodeData]) => ({
        id: nodeId,
        data: nodeData,
        position: positions.get(nodeId)!
      }))
      .filter(node => node.position && isNodeInViewport(node.position)) // 视口裁剪
  }

  // 在交互时降低更新频率
  const updateInterval = isInteracting.value ? 50 : 16 // 交互时20fps，平时60fps

  if (now - lastVisibleNodesUpdate < updateInterval && cachedVisibleNodes.length > 0) {
    return cachedVisibleNodes
  }

  const positions = getNodePositions()
  const result = Array.from(mindMapData.value.entries())
    .filter(([nodeId]) => isNodeVisible(nodeId)) // 层级可见性检查
    .map(([nodeId, nodeData]) => ({
      id: nodeId,
      data: nodeData,
      position: positions.get(nodeId)!
    }))
    .filter(node => node.position && isNodeInViewport(node.position)) // 视口裁剪

  cachedVisibleNodes = result
  lastVisibleNodesUpdate = now
  return result
})

// 检查连接线是否在视口内 - 优化版本
const isEdgeInViewport = (parentPos: NodePosition, childPos: NodePosition): boolean => {
  // 复用缓存的视口信息
  if (!cachedViewport) return true

  const buffer = 100

  // 获取父子节点的实际尺寸
  let parentWidth = LAYOUT_CONFIG.NODE_DEFAULTS.text.width
  let parentHeight = LAYOUT_CONFIG.NODE_DEFAULTS.text.height
  let childWidth = LAYOUT_CONFIG.NODE_DEFAULTS.text.width
  let childHeight = LAYOUT_CONFIG.NODE_DEFAULTS.text.height

  for (const [nodeId, nodeData] of mindMapData.value.entries()) {
    const nodePos = getNodePositions().get(nodeId)
    if (nodePos) {
      if (nodePos.x === parentPos.x && nodePos.y === parentPos.y) {
        parentWidth = nodeData.dimensions.width
        parentHeight = nodeData.dimensions.height
      }
      if (nodePos.x === childPos.x && nodePos.y === childPos.y) {
        childWidth = nodeData.dimensions.width
        childHeight = nodeData.dimensions.height
      }
    }
  }

  const minX = Math.min(parentPos.x, childPos.x) - buffer
  const maxX = Math.max(parentPos.x + parentWidth, childPos.x + childWidth) + buffer
  const minY = Math.min(parentPos.y, childPos.y) - buffer
  const maxY = Math.max(parentPos.y + parentHeight, childPos.y + childHeight) + buffer

  return maxX >= cachedViewport.x &&
         minX <= cachedViewport.x + cachedViewport.width &&
         maxY >= cachedViewport.y &&
         minY <= cachedViewport.y + cachedViewport.height
}

// 缓存可见连接线计算结果
let cachedVisibleEdges: Array<{ id: string; path: string }> = []
let lastVisibleEdgesUpdate = 0

// 优化的可见连接线计算 - 结合层级可见性和视口裁剪
const visibleEdges = computed(() => {
  const now = Date.now()

  // 在根节点拖动时禁用缓存，确保实时更新
  if (rootDragState.value.isDragging) {
    const positions = getNodePositions()
    return Array.from(mindMapData.value.entries())
      .filter(([nodeId, nodeData]) =>
        nodeData.parentId &&
        isNodeVisible(nodeId) &&
        isNodeVisible(nodeData.parentId)
      )
      .map(([nodeId, nodeData]) => {
        const parentPos = positions.get(nodeData.parentId!)
        const childPos = positions.get(nodeId)
        if (!parentPos || !childPos) return null

        // 视口裁剪检查
        if (!isEdgeInViewport(parentPos, childPos)) return null

        return {
          id: `edge-${nodeData.parentId}-${nodeId}`,
          path: createConnectionPath(parentPos, childPos)
        }
      })
      .filter(edge => edge !== null) as { id: string; path: string }[]
  }

  // 在交互时降低更新频率
  const updateInterval = isInteracting.value ? 50 : 16 // 交互时20fps，平时60fps

  if (now - lastVisibleEdgesUpdate < updateInterval && cachedVisibleEdges.length > 0) {
    return cachedVisibleEdges
  }

  const positions = getNodePositions()
  const result = Array.from(mindMapData.value.entries())
    .filter(([nodeId, nodeData]) =>
      nodeData.parentId &&
      isNodeVisible(nodeId) &&
      isNodeVisible(nodeData.parentId)
    )
    .map(([nodeId, nodeData]) => {
      const parentPos = positions.get(nodeData.parentId!)
      const childPos = positions.get(nodeId)
      if (!parentPos || !childPos) return null

      // 视口裁剪检查
      if (!isEdgeInViewport(parentPos, childPos)) return null

      return {
        id: `edge-${nodeData.parentId}-${nodeId}`,
        path: createConnectionPath(parentPos, childPos)
      }
    })
    .filter(edge => edge !== null) as { id: string; path: string }[]

  cachedVisibleEdges = result
  lastVisibleEdgesUpdate = now
  return result
})

// 初始化数据
onMounted(async () => {
  await loadMindMapData()

  // 添加全局键盘事件监听
  document.addEventListener('keydown', handleGlobalKeyDown)
  document.addEventListener('keyup', handleGlobalKeyUp)
})

// 组件卸载时清理事件监听器
onUnmounted(() => {
  document.removeEventListener('keydown', handleGlobalKeyDown)
  document.removeEventListener('keyup', handleGlobalKeyUp)

  // 清理根节点拖动事件监听器
  document.removeEventListener('mousemove', handleRootNodeDrag)
  document.removeEventListener('mouseup', handleRootNodeDragEnd)

  // 清理动画帧
  if (dragAnimationFrame) {
    cancelAnimationFrame(dragAnimationFrame)
    dragAnimationFrame = null
  }

  window.removeEventListener('resize', handleResize)
})



// 简化的数据加载函数
const loadMindMapData = async () => {
  isLoadingData.value = true
  try {
    if (!props.node.uuid) return

    const nodeData = await blockService.getBlockByUuid(props.node.uuid)
    if (nodeData?.contents) {
      const parsedData = JSON.parse(nodeData.contents)

      // 直接使用新格式数据
      if (parsedData.mindMapData && parsedData.rootNodeIds) {
        mindMapData.value = new Map(Object.entries(parsedData.mindMapData))
        rootNodeIds.value = parsedData.rootNodeIds
        nodeCounter.value = parsedData.nodeCounter || 1

        // 恢复视口
        if (parsedData.viewport) {
          if (typeof parsedData.viewport.zoomLevel === 'number') {
            zoomLevel.value = parsedData.viewport.zoomLevel
          }
          if (parsedData.viewport.panOffset) {
            panOffset.value = parsedData.viewport.panOffset
          }
        }
      } else {
        await createDefaultNode()
      }
    } else {
      await createDefaultNode()
    }

    clearAllCaches()
  } catch (error) {
    console.error('加载思维导图数据失败:', error)
    await createDefaultNode()
  } finally {
    isLoadingData.value = false
  }
}

// 创建默认节点
const createDefaultNode = async () => {
  mindMapData.value.clear()
  rootNodeIds.value = []
  nodeCounter.value = 1

  // 创建默认根节点
  await createNode('text', undefined, '中心主题')
}

// 清除所有缓存的辅助函数
const clearAllCaches = () => {
  cachedVisibleNodes = []
  cachedVisibleEdges = []
  cachedViewport = null
  lastVisibleNodesUpdate = 0
  lastVisibleEdgesUpdate = 0
  lastViewportUpdate = 0
}



// 保存数据
const saveMindMapData = async () => {
  if (isLoadingData.value) return
  isSaving.value = true
  try {
    const payload = {
      mindMapData: Object.fromEntries(mindMapData.value),
      rootNodeIds: rootNodeIds.value,
      nodeCounter: nodeCounter.value,
      viewport: {
        zoomLevel: zoomLevel.value,
        panOffset: { ...panOffset.value }
      }
    }

    await blockService.updateBlock({
      uuid: props.node.uuid,
      contents: JSON.stringify(payload),
      updatedAt: Date.now()
    })
  } catch (error) {
    console.error('保存思维导图数据失败:', error)
  } finally {
    setTimeout(() => {
      isSaving.value = false
    }, 500) // 延迟关闭，避免闪烁
  }
}

// Helper function to get the vertical bounds of a subtree
interface SubtreeBounds {
  minY: number
  maxY: number
}

const getSubtreeBounds = (
  nodeId: string,
  positions: Map<string, NodePosition>
): SubtreeBounds => {
  const node = mindMapData.value.get(nodeId)
  const pos = positions.get(nodeId)

  if (!node || !pos) {
    return { minY: Infinity, maxY: -Infinity }
  }

  let minY = pos.y
  let maxY = pos.y + node.dimensions.height

  if (!node.collapsed && node.children.length > 0) {
    for (const childId of node.children) {
      const childBounds = getSubtreeBounds(childId, positions)
      minY = Math.min(minY, childBounds.minY)
      maxY = Math.max(maxY, childBounds.maxY)
    }
  }

  return { minY, maxY }
}

// Helper function to vertically shift an entire subtree
const shiftSubtree = (
  nodeId: string,
  positions: Map<string, NodePosition>,
  dy: number
) => {
  const node = mindMapData.value.get(nodeId)
  const pos = positions.get(nodeId)

  if (!node || !pos) return

  pos.y += dy

  if (!node.collapsed) {
    for (const childId of node.children) {
      shiftSubtree(childId, positions, dy)
    }
  }
}

// 计算节点位置 - 支持根节点自定义位置
const calculateNodePositions = (): Map<string, NodePosition> => {
  const positions = new Map<string, NodePosition>()

  // 为每个根节点计算位置
  let currentRootY = LAYOUT_CONFIG.ROOT_START_Y

  for (const rootId of rootNodeIds.value) {
    const rootNode = mindMapData.value.get(rootId)
    if (!rootNode) continue

    const rootHeight = calculateSubtreeHeight(rootId)

    // 检查是否有自定义位置
    let rootX: number, rootY: number
    if (rootNode.customPosition) {
      rootX = rootNode.customPosition.x
      rootY = rootNode.customPosition.y
    } else {
      rootX = LAYOUT_CONFIG.ROOT_START_X
      rootY = currentRootY + rootHeight / 2
      currentRootY += rootHeight + LAYOUT_CONFIG.VERTICAL_SPACING * 2
    }

    calculateNodePosition(rootId, rootX, rootY, positions)
  }

  return positions
}

// 计算子树高度
const calculateSubtreeHeight = (nodeId: string): number => {
  const node = mindMapData.value.get(nodeId)
  if (!node || node.collapsed) {
    return node?.dimensions.height || LAYOUT_CONFIG.NODE_DEFAULTS.text.height
  }

  if (node.children.length === 0) {
    return node.dimensions.height
  }

  const childrenHeight = node.children.reduce((sum, childId) => {
    return sum + calculateSubtreeHeight(childId)
  }, 0)

  const spacingHeight = (node.children.length - 1) * LAYOUT_CONFIG.VERTICAL_SPACING

  return Math.max(node.dimensions.height, childrenHeight + spacingHeight)
}

// 计算单个节点及其子树的位置
const calculateNodePosition = (
  nodeId: string,
  x: number,
  y: number,
  positions: Map<string, NodePosition>
) => {
  const node = mindMapData.value.get(nodeId)
  if (!node) return

  positions.set(nodeId, { x, y: y - node.dimensions.height / 2 })

  if (node.collapsed || node.children.length === 0) {
    return
  }

  const childrenIds = [...node.children].sort((a, b) => {
    const nodeA = mindMapData.value.get(a)
    const nodeB = mindMapData.value.get(b)
    return (nodeA?.order || 0) - (nodeB?.order || 0)
  })

  const childX = x + node.dimensions.width + LAYOUT_CONFIG.HORIZONTAL_SPACING

  // 1. 初始布局：仅基于节点自身高度进行理想化排列
  let currentY = 0
  for (const childId of childrenIds) {
    const childNode = mindMapData.value.get(childId)!
    const childY = currentY + childNode.dimensions.height / 2
    calculateNodePosition(childId, childX, childY, positions)
    currentY += childNode.dimensions.height + LAYOUT_CONFIG.VERTICAL_SPACING
  }

  // 2. 避让重叠：检查并修正子树间的重叠
  if (childrenIds.length > 1) {
    for (let i = 1; i < childrenIds.length; i++) {
      const prevChildId = childrenIds[i - 1]
      const currentChildId = childrenIds[i]
      const prevBounds = getSubtreeBounds(prevChildId, positions)
      const currentBounds = getSubtreeBounds(currentChildId, positions)
      const clearance = prevBounds.maxY + LAYOUT_CONFIG.VERTICAL_SPACING
      const overlap = clearance - currentBounds.minY
      if (overlap > 0) {
        for (let j = i; j < childrenIds.length; j++) {
          shiftSubtree(childrenIds[j], positions, overlap)
        }
      }
    }
  }

  // 3. 整体居中：将所有子节点作为一个整体，相对于父节点居中
  const firstChildId = childrenIds[0]
  const lastChildId = childrenIds[childrenIds.length - 1]
  const childrenGroupTop = getSubtreeBounds(firstChildId, positions).minY
  const childrenGroupBottom = getSubtreeBounds(lastChildId, positions).maxY
  const childrenGroupCenter = childrenGroupTop + (childrenGroupBottom - childrenGroupTop) / 2
  const parentCenter = positions.get(nodeId)!.y + node.dimensions.height / 2
  const finalShift = parentCenter - childrenGroupCenter

  if (Math.abs(finalShift) > 1) {
    for (const childId of childrenIds) {
      shiftSubtree(childId, positions, finalShift)
    }
  }
}

// 创建连接线路径
const createConnectionPath = (parentPos: NodePosition, childPos: NodePosition): string => {
  // 需要获取父节点和子节点的实际尺寸
  const getNodeByPosition = (pos: NodePosition): MindMapNodeData | null => {
    for (const [nodeId, nodeData] of mindMapData.value.entries()) {
      const nodePos = getNodePositions().get(nodeId)
      if (nodePos && nodePos.x === pos.x && nodePos.y === pos.y) {
        return nodeData
      }
    }
    return null
  }

  const parentNode = getNodeByPosition(parentPos)
  const childNode = getNodeByPosition(childPos)

  const parentWidth = parentNode?.dimensions.width || LAYOUT_CONFIG.NODE_DEFAULTS.text.width
  const parentHeight = parentNode?.dimensions.height || LAYOUT_CONFIG.NODE_DEFAULTS.text.height
  const childHeight = childNode?.dimensions.height || LAYOUT_CONFIG.NODE_DEFAULTS.text.height

  const startX = parentPos.x + parentWidth
  const startY = parentPos.y + parentHeight / 2
  const endX = childPos.x
  const endY = childPos.y + childHeight / 2

  // 计算中间转折点，创建三段直线
  const midX = startX + (endX - startX) / 2

  // 创建三段直线路径：水平线 -> 垂直线 -> 水平线
  return `M ${startX} ${startY} L ${midX} ${startY} L ${midX} ${endY} L ${endX} ${endY}`
}

// 检查节点是否可见
const isNodeVisible = (nodeId: string): boolean => {
  const node = mindMapData.value.get(nodeId)
  if (!node) return false

  // 根节点总是可见
  if (!node.parentId) return true

  // 检查所有祖先节点是否都展开
  let currentId: string | null = node.parentId
  while (currentId) {
    const currentNode = mindMapData.value.get(currentId)
    if (!currentNode) return false

    if (currentNode.collapsed) return false
    currentId = currentNode.parentId
  }

  return true
}

// ===== 核心节点移动逻辑 =====

/**
 * 简化的节点移动验证函数
 */
const validateNodeMove = (operation: NodeMoveOperation): MoveValidationResult => {
  const { sourceId, targetId, position } = operation

  // 基础验证
  if (!sourceId || !targetId || sourceId === targetId) {
    return { isValid: false, errorType: 'invalid-target', errorMessage: '无效的移动操作' }
  }

  const sourceNode = mindMapData.value.get(sourceId)
  const targetNode = mindMapData.value.get(targetId)

  if (!sourceNode || !targetNode) {
    return { isValid: false, errorType: 'invalid-target', errorMessage: '节点不存在' }
  }

  // 检查循环依赖
  if (isDescendant(targetId, sourceId)) {
    return { isValid: false, errorType: 'circular-dependency', errorMessage: '不能移动到子节点中' }
  }

  // 检查根节点约束
  if (sourceId === 'root' && position === 'child') {
    return { isValid: false, errorType: 'root-constraint', errorMessage: '根节点不能作为子节点' }
  }

  // 检查是否已经在目标位置
  if (isAlreadyInTargetPosition(sourceId, targetId, position)) {
    return { isValid: false, errorType: 'same-position', errorMessage: '节点已在目标位置' }
  }

  return { isValid: true }
}

/**
 * 检查节点是否已经在目标位置
 * @param sourceId 源节点ID
 * @param targetId 目标节点ID
 * @param position 目标位置
 * @returns 是否已经在目标位置
 */
const isAlreadyInTargetPosition = (sourceId: string, targetId: string, position: 'before' | 'after' | 'child'): boolean => {
  const sourceNode = mindMapData.value.get(sourceId)
  const targetNode = mindMapData.value.get(targetId)

  if (!sourceNode || !targetNode) return false

  if (position === 'child') {
    return sourceNode.parentId === targetId
  } else {
    // 检查是否为相邻的兄弟节点
    if (sourceNode.parentId !== targetNode.parentId) return false

    const parentId = sourceNode.parentId
    let siblings: string[]

    if (parentId) {
      const parentNode = mindMapData.value.get(parentId)
      if (!parentNode) return false
      siblings = parentNode.children
    } else {
      siblings = rootNodeIds.value
    }

    const sourceIndex = siblings.indexOf(sourceId)
    const targetIndex = siblings.indexOf(targetId)

    if (sourceIndex === -1 || targetIndex === -1) return false

    // 检查是否已经在相邻位置
    if (position === 'before') {
      return sourceIndex === targetIndex - 1
    } else {
      return sourceIndex === targetIndex + 1
    }
  }
}

/**
 * 计算移动操作的类型
 * @param operation 移动操作对象
 * @returns 操作类型
 */
const getMoveOperationType = (operation: NodeMoveOperation): 'hierarchy-change' | 'sibling-reorder' => {
  const { sourceId, targetId, position } = operation
  const sourceNode = mindMapData.value.get(sourceId)
  const targetNode = mindMapData.value.get(targetId)

  if (!sourceNode || !targetNode) return 'hierarchy-change'

  if (position === 'child') {
    return 'hierarchy-change'
  }

  // 检查是否为同级调整
  return sourceNode.parentId === targetNode.parentId ? 'sibling-reorder' : 'hierarchy-change'
}

/**
 * 简化的节点移动处理函数
 */
const processNodeMove = async (operation: NodeMoveOperation): Promise<boolean> => {
  // 验证移动操作
  const validation = validateNodeMove(operation)
  if (!validation.isValid) {
    console.warn('节点移动验证失败:', validation.errorMessage)
    return false
  }

  // 确定操作类型并执行
  operation.type = getMoveOperationType(operation)

  try {
    if (operation.type === 'sibling-reorder') {
      await processSiblingReorder(operation)
    } else {
      await processHierarchyChange(operation)
    }

    await nextTick()
    console.log(`节点移动成功: ${operation.sourceId} -> ${operation.targetId} (${operation.position})`)
    return true
  } catch (error) {
    console.error('节点移动失败:', error)
    return false
  }
}



/**
 * 处理同级节点重排
 * @param operation 移动操作对象
 */
const processSiblingReorder = async (operation: NodeMoveOperation): Promise<void> => {
  const { sourceId, targetId, position } = operation
  const sourceNode = mindMapData.value.get(sourceId)
  const targetNode = mindMapData.value.get(targetId)

  if (!sourceNode || !targetNode) return

  const parentId = sourceNode.parentId

  if (parentId) {
    // 处理非根节点的同级重排
    const parentNode = mindMapData.value.get(parentId)
    if (!parentNode) return

    const children = [...parentNode.children]
    const sourceIndex = children.indexOf(sourceId)
    const targetIndex = children.indexOf(targetId)

    if (sourceIndex === -1 || targetIndex === -1) return

    // 移除源节点
    children.splice(sourceIndex, 1)

    // 计算新的插入位置
    const newTargetIndex = children.indexOf(targetId)
    const insertIndex = position === 'before' ? newTargetIndex : newTargetIndex + 1

    // 插入到新位置
    children.splice(insertIndex, 0, sourceId)

    // 更新父节点的children数组
    parentNode.children = children

    // 重新分配order
    children.forEach((childId, index) => {
      const child = mindMapData.value.get(childId)
      if (child) child.order = index
    })
  } else {
    // 处理根节点的重排
    const rootIds = [...rootNodeIds.value]
    const sourceIndex = rootIds.indexOf(sourceId)
    const targetIndex = rootIds.indexOf(targetId)

    if (sourceIndex === -1 || targetIndex === -1) return

    // 移除源节点
    rootIds.splice(sourceIndex, 1)

    // 计算新的插入位置
    const newTargetIndex = rootIds.indexOf(targetId)
    const insertIndex = position === 'before' ? newTargetIndex : newTargetIndex + 1

    // 插入到新位置
    rootIds.splice(insertIndex, 0, sourceId)

    // 更新根节点数组
    rootNodeIds.value = rootIds
  }
}

/**
 * 处理层级变更
 * @param operation 移动操作对象
 */
const processHierarchyChange = async (operation: NodeMoveOperation): Promise<void> => {
  const { sourceId, targetId, position } = operation
  const sourceNode = mindMapData.value.get(sourceId)
  const targetNode = mindMapData.value.get(targetId)

  if (!sourceNode || !targetNode) return

  // 1. 从原位置移除
  removeNodeFromParent(sourceId)

  // 2. 添加到新位置
  if (position === 'child') {
    // 作为子节点添加
    sourceNode.parentId = targetId
    sourceNode.level = targetNode.level + 1
    sourceNode.order = targetNode.children.length
    targetNode.children.push(sourceId)

    // 自动展开目标节点
    targetNode.collapsed = false
  } else {
    // 作为兄弟节点添加
    const targetParentId = targetNode.parentId
    sourceNode.parentId = targetParentId
    sourceNode.level = targetNode.level

    if (targetParentId) {
      const parentNode = mindMapData.value.get(targetParentId)
      if (parentNode) {
        const targetIndex = parentNode.children.indexOf(targetId)
        const insertIndex = position === 'before' ? targetIndex : targetIndex + 1
        parentNode.children.splice(insertIndex, 0, sourceId)

        // 重新排序
        parentNode.children.forEach((childId, idx) => {
          const child = mindMapData.value.get(childId)
          if (child) child.order = idx
        })
      }
    } else {
      // 目标是根节点
      const targetIndex = rootNodeIds.value.indexOf(targetId)
      const insertIndex = position === 'before' ? targetIndex : targetIndex + 1
      rootNodeIds.value.splice(insertIndex, 0, sourceId)
      sourceNode.level = 0
    }
  }

  // 3. 更新子树层级
  updateSubtreeLevel(sourceId)
}

// ===== 性能优化的监听器 =====

// 标记是否正在加载数据，避免加载时触发保存
const isLoadingData = ref(true)
const isSaving = ref(false)

const headerTitle = computed(() => {
  const baseTitle = props.node.title || '思维导图'
  return isSaving.value ? `${baseTitle} (保存中...)` : baseTitle
})

// 监听数据变化并自动保存
const autoSaveMindMap = useDebounceFn(() => {
  saveMindMapData()
}, 2000)

watch(mindMapData, () => {
  if (!isLoadingData.value) {
    autoSaveMindMap()
  }
}, { deep: true })

// 监听拖拽状态变化，优化性能
watch(() => dragState.value.isDragging, (isDragging) => {
  if (isDragging) {
    // 拖拽开始时，可以暂停一些不必要的计算
    document.body.style.userSelect = 'none'
  } else {
    // 拖拽结束时，恢复正常状态
    document.body.style.userSelect = ''

    // 清理预览位置
    dragState.value.previewPosition = null
  }
})

// 监听节点标题变化
watch(() => props.node.title, (newTitle) => {
  emit('title-changed', newTitle)
})

// 监听节点UUID变化，立即重新加载数据
watch(() => props.node.uuid, async (newUuid, oldUuid) => {
  if (newUuid && newUuid !== oldUuid) {
    console.log('MindMapEditor: 检测到节点变化，立即重新加载数据', { newUuid, oldUuid })

    // 立即清除当前数据和缓存，避免显示旧内容
    mindMapData.value.clear()
    rootNodeIds.value = []
    clearAllCaches()

    // 重置所有状态
    editingNodeId.value = null
    selectedNodeId.value = null
    dragState.value = {
      isDragging: false,
      draggedNodeId: null,
      dragOverNodeId: null,
      dropPosition: null,
      previewPosition: null,
      isValidDrop: false
    }
    rootDragState.value = {
      isDragging: false,
      draggedRootId: null,
      startPosition: null,
      currentPosition: null,
      mouseOffset: null
    }

    // 加载新数据
    await loadMindMapData()
  }
}, { immediate: false })

// 监听视图变化，更新视口信息
watch([zoomLevel, panOffset], () => {
  updateViewport()
  if (!isLoadingData.value) {
    autoSaveMindMap()
  }
}, { immediate: true })

// 监听窗口大小变化
const handleResize = () => {
  updateViewport()
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
  updateViewport()
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 辅助方法
const hasChildren = (nodeId: string): boolean => {
  const node = mindMapData.value.get(nodeId)
  return node ? node.children.length > 0 : false
}

const isCollapsed = (nodeId: string): boolean => {
  const node = mindMapData.value.get(nodeId)
  return node ? node.collapsed : false
}

const isNodeSelected = (nodeId: string): boolean => {
  return selectedNodeId.value === nodeId
}

const selectNode = (nodeId: string) => {
  selectedNodeId.value = nodeId
}

// 检查节点是否在被拖动的子树中
const isNodeInDraggedSubtree = (nodeId: string): boolean => {
  if (!rootDragState.value.isDragging || !rootDragState.value.draggedRootId) {
    return false
  }

  // 检查是否为被拖动根节点的后代
  const draggedRootId = rootDragState.value.draggedRootId
  return isDescendantOf(nodeId, draggedRootId)
}

// 检查节点是否为指定节点的后代
const isDescendantOf = (nodeId: string, ancestorId: string): boolean => {
  if (nodeId === ancestorId) return false

  const node = mindMapData.value.get(nodeId)
  if (!node || !node.parentId) return false

  if (node.parentId === ancestorId) return true

  return isDescendantOf(node.parentId, ancestorId)
}

// 开始编辑节点
const startEditNode = (nodeId: string) => {
  editingNodeId.value = nodeId
}

// 结束编辑节点
const endEditNode = () => {
  editingNodeId.value = null
}

// 更新节点标签
const updateNodeLabel = (nodeId: string, newLabel: string) => {
  const node = mindMapData.value.get(nodeId)
  if (node) {
    node.label = newLabel
  }
  endEditNode()
}

// 切换节点展开/收起状态
const toggleCollapse = (nodeId: string) => {
  const node = mindMapData.value.get(nodeId)
  if (node && node.children.length > 0) {
    node.collapsed = !node.collapsed
  }
}

// 删除节点
const deleteNode = (nodeId: string) => {
  const node = mindMapData.value.get(nodeId)
  if (!node) return

  // 递归删除所有子节点
  const deleteNodeRecursive = (id: string) => {
    const nodeToDelete = mindMapData.value.get(id)
    if (!nodeToDelete) return

    // 先删除所有子节点
    for (const childId of nodeToDelete.children) {
      deleteNodeRecursive(childId)
    }

    // 从父节点的children中移除
    if (nodeToDelete.parentId) {
      const parentNode = mindMapData.value.get(nodeToDelete.parentId)
      if (parentNode) {
        const index = parentNode.children.indexOf(id)
        if (index > -1) {
          parentNode.children.splice(index, 1)
          // 重新排序剩余子节点
          parentNode.children.forEach((childId, idx) => {
            const child = mindMapData.value.get(childId)
            if (child) {
              child.order = idx
            }
          })
        }
      }
    } else {
      // 从根节点列表中移除
      const rootIndex = rootNodeIds.value.indexOf(id)
      if (rootIndex > -1) {
        rootNodeIds.value.splice(rootIndex, 1)
      }
    }

    // 删除节点数据
    mindMapData.value.delete(id)
  }

  deleteNodeRecursive(nodeId)
}

// ===== 拖拽处理 - 使用新的统一状态管理 =====

/**
 * 拖拽开始处理 - 区分根节点和普通节点
 */
const handleNodeDragStart = (nodeId: string, event: DragEvent | MouseEvent) => {
  const node = mindMapData.value.get(nodeId)
  if (!node) return

  // 检查是否为根节点
  const isRootNode = !node.parentId

  if (isRootNode) {
    // 根节点拖动 - 自由位置拖动
    const nodePosition = getNodePositions().get(nodeId) || { x: 0, y: 0 }

    // 计算鼠标在节点内的相对位置
    if (!canvasContainer.value) return
    const rect = canvasContainer.value.getBoundingClientRect()

    // 将屏幕坐标转换为画布坐标
    const mouseCanvasX = (event.clientX - rect.left) / zoomLevel.value - panOffset.value.x
    const mouseCanvasY = (event.clientY - rect.top) / zoomLevel.value - panOffset.value.y

    // 计算鼠标在节点内的相对偏移（鼠标点击位置相对于节点左上角的距离）
    const mouseOffset = {
      x: mouseCanvasX - nodePosition.x,
      y: mouseCanvasY - nodePosition.y
    }

    rootDragState.value = {
      isDragging: true,
      draggedRootId: nodeId,
      startPosition: nodePosition,
      currentPosition: null,
      mouseOffset: mouseOffset
    }

    // 设置交互状态，取消动画
    isInteracting.value = true

    // 阻止默认的拖拽行为，使用自定义拖拽
    event.preventDefault()

    // 添加全局鼠标事件监听
    document.addEventListener('mousemove', handleRootNodeDrag)
    document.addEventListener('mouseup', handleRootNodeDragEnd)

    return
  }

  // 普通节点拖动 - 层级结构拖动
  // 只有 DragEvent 才处理普通节点拖动
  if (event instanceof DragEvent) {
    dragState.value = {
      isDragging: true,
      draggedNodeId: nodeId,
      dragOverNodeId: null,
      dropPosition: null,
      previewPosition: null,
      isValidDrop: false
    }

    event.dataTransfer!.setData('text/plain', nodeId)
    event.dataTransfer!.effectAllowed = 'move'

    // 获取鼠标在节点内的相对位置（由 MindMapNode 计算并添加到事件中）
    const offsetX = (event as any).nodeOffsetX || 60
    const offsetY = (event as any).nodeOffsetY || 20

    // 创建拖拽影子，保持鼠标相对位置
    const dragImage = document.createElement('div')
    dragImage.textContent = mindMapData.value.get(nodeId)?.label || ''
    dragImage.style.cssText = `
      position: absolute;
      top: -1000px;
      padding: 8px 12px;
      background: rgba(59, 130, 246, 0.9);
      border: 2px solid #3b82f6;
      border-radius: 8px;
      color: white;
      font-size: 14px;
      font-weight: 500;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
      z-index: 9999;
    `
    document.body.appendChild(dragImage)

    // 使用从节点组件传递的精确偏移位置
    event.dataTransfer!.setDragImage(dragImage, offsetX, offsetY)
    setTimeout(() => document.body.removeChild(dragImage), 0)
  }
}

/**
 * 根节点拖动处理 - 使用requestAnimationFrame优化性能
 */
let dragAnimationFrame: number | null = null
const handleRootNodeDrag = (event: MouseEvent) => {
  if (!rootDragState.value.isDragging || !rootDragState.value.draggedRootId || !rootDragState.value.mouseOffset) return

  // 取消之前的动画帧
  if (dragAnimationFrame) {
    cancelAnimationFrame(dragAnimationFrame)
  }

  // 使用requestAnimationFrame确保流畅更新
  dragAnimationFrame = requestAnimationFrame(() => {
    if (!canvasContainer.value) return

    const rect = canvasContainer.value.getBoundingClientRect()

    // 计算鼠标在画布坐标系中的位置
    const mouseCanvasX = (event.clientX - rect.left) / zoomLevel.value - panOffset.value.x
    const mouseCanvasY = (event.clientY - rect.top) / zoomLevel.value - panOffset.value.y

    // 减去鼠标偏移量，得到节点应该在的位置
    const nodeX = mouseCanvasX - rootDragState.value.mouseOffset!.x
    const nodeY = mouseCanvasY - rootDragState.value.mouseOffset!.y

    // 立即更新根节点的自定义位置
    const rootNode = mindMapData.value.get(rootDragState.value.draggedRootId!)
    if (rootNode) {
      // customPosition.y 需要使用节点中心坐标，避免拖动时产生垂直偏移
      const centerY = nodeY + rootNode.dimensions.height / 2
      rootNode.customPosition = { x: nodeX, y: centerY }
      rootDragState.value.currentPosition = { x: nodeX, y: nodeY }
    }

    // 立即清除所有缓存，确保实时更新
    cachedVisibleNodes = []
    cachedVisibleEdges = []
    cachedViewport = null
    lastVisibleNodesUpdate = 0
    lastVisibleEdgesUpdate = 0
    lastViewportUpdate = 0

    dragAnimationFrame = null
  })
}

/**
 * 根节点拖动结束处理
 */
const handleRootNodeDragEnd = () => {
  if (rootDragState.value.isDragging) {
    // 取消待处理的动画帧
    if (dragAnimationFrame) {
      cancelAnimationFrame(dragAnimationFrame)
      dragAnimationFrame = null
    }

    // 移除全局事件监听
    document.removeEventListener('mousemove', handleRootNodeDrag)
    document.removeEventListener('mouseup', handleRootNodeDragEnd)

    // 重置状态
    rootDragState.value = {
      isDragging: false,
      draggedRootId: null,
      startPosition: null,
      currentPosition: null,
      mouseOffset: null
    }

    // 清除交互状态，恢复动画
    isInteracting.value = false

    // 保存数据
    saveMindMapData()
  }
}

/**
 * 结束拖拽 - 根据拖拽状态执行移动
 */
const handleNodeDragEnd = async () => {
  // 如果有有效的拖拽目标，执行移动操作
  if (dragState.value.isDragging &&
      dragState.value.isValidDrop &&
      dragState.value.draggedNodeId &&
      dragState.value.dragOverNodeId &&
      dragState.value.dropPosition) {

    const operation: NodeMoveOperation = {
      sourceId: dragState.value.draggedNodeId,
      targetId: dragState.value.dragOverNodeId,
      position: dragState.value.dropPosition,
      type: 'hierarchy-change' // 会在processNodeMove中重新计算
    }

    // 执行移动操作
    const success = await processNodeMove(operation)

    if (!success) {
      console.warn('节点移动失败')
    }
  }

  // 重置拖拽状态
  dragState.value = {
    isDragging: false,
    draggedNodeId: null,
    dragOverNodeId: null,
    dropPosition: null,
    previewPosition: null,
    isValidDrop: false
  }
}

/**
 * 拖拽悬停处理 - 更新拖拽状态
 * 这里更新的状态将在 handleNodeDragEnd 中用于执行实际的移动操作
 * @param nodeId 目标节点ID
 * @param event 拖拽事件
 * @param position 拖拽位置
 */
const handleNodeDragOver = (nodeId: string, event: DragEvent, position: 'before' | 'after' | 'child') => {
  if (dragState.value.draggedNodeId === nodeId) return

  event.preventDefault()
  event.dataTransfer!.dropEffect = 'move'

  // 验证拖拽操作
  const operation: NodeMoveOperation = {
    sourceId: dragState.value.draggedNodeId!,
    targetId: nodeId,
    position,
    type: 'hierarchy-change' // 临时值，会在processNodeMove中重新计算
  }

  const validation = validateNodeMove(operation)

  // 更新拖拽状态 - 这些状态将决定最终的移动操作
  dragState.value.dragOverNodeId = nodeId
  dragState.value.dropPosition = position
  dragState.value.isValidDrop = validation.isValid

  // 计算预览位置
  if (validation.isValid) {
    dragState.value.previewPosition = calculatePreviewPosition(nodeId, position)
  }


}

/**
 * 计算拖拽预览位置（使用缓存优化）
 * @param targetNodeId 目标节点ID
 * @param position 拖拽位置
 * @returns 预览位置坐标
 */
const calculatePreviewPosition = (
  targetNodeId: string,
  position: 'before' | 'after' | 'child'
): NodePosition | null => {
  const positions = getNodePositions()
  const targetPos = positions.get(targetNodeId)
  const targetNode = mindMapData.value.get(targetNodeId)
  const draggedNodeId = dragState.value.draggedNodeId

  if (!targetPos || !targetNode || !draggedNodeId) return null

  const draggedNode = mindMapData.value.get(draggedNodeId)
  if (!draggedNode) return null

  const offset = 20 // 视觉间隙

  if (position === 'child') {
    const hasVisibleChildren = targetNode.children.length > 0 && !targetNode.collapsed

    if (hasVisibleChildren) {
      // 如果目标节点有展开的子节点，则将预览显示在最后一个子节点的下方
      const lastChildId = targetNode.children[targetNode.children.length - 1]
      const lastChildNode = mindMapData.value.get(lastChildId)
      const lastChildPos = positions.get(lastChildId)

      if (lastChildNode && lastChildPos) {
        return {
          x: lastChildPos.x,
          y: lastChildPos.y + lastChildNode.dimensions.height + offset
        }
      }
    }

    // 如果没有子节点或子节点已折叠，则将预览显示在目标节点的右侧
    // 并使其与目标节点垂直居中对齐
    return {
      x: targetPos.x + targetNode.dimensions.width + 40, // 40是水平间距
      y: targetPos.y + targetNode.dimensions.height / 2 - draggedNode.dimensions.height / 2
    }
  } else {
    // 作为兄弟节点时，根据实际尺寸动态计算位置
    const draggedNodeHeight = draggedNode.dimensions.height
    let previewY: number

    if (position === 'before') {
      // 预览位置在目标节点上方
      // 修正：使用预览框的固定高度(40px)而不是被拖拽节点的实际高度
      const previewNodeHeight = 40
      previewY = targetPos.y - previewNodeHeight - offset
    } else {
      // 预览位置在目标节点下方
      previewY = targetPos.y + targetNode.dimensions.height + offset
    }

    return {
      x: targetPos.x,
      y: previewY
    }
  }
}

/**
 * 处理节点放置 - 现在只负责触发拖拽结束
 * 实际的移动逻辑在 handleNodeDragEnd 中根据拖拽状态执行
 */
const handleNodeDrop = async (targetNodeId: string, position: 'before' | 'after' | 'child') => {
  // 确保拖拽状态与drop事件一致（作为最后的确认）
  if (dragState.value.draggedNodeId &&
      dragState.value.dragOverNodeId === targetNodeId &&
      dragState.value.dropPosition === position) {
    // 状态一致，触发拖拽结束处理
    await handleNodeDragEnd()
  } else {
    // 状态不一致，只清理拖拽状态
    console.warn('Drop事件与拖拽状态不一致，取消移动操作')
    dragState.value = {
      isDragging: false,
      draggedNodeId: null,
      dragOverNodeId: null,
      dropPosition: null,
      previewPosition: null,
      isValidDrop: false
    }
  }
}



// 从父节点中移除节点
const removeNodeFromParent = (nodeId: string) => {
  const node = mindMapData.value.get(nodeId)
  if (!node) return

  if (node.parentId) {
    const parentNode = mindMapData.value.get(node.parentId)
    if (parentNode) {
      const index = parentNode.children.indexOf(nodeId)
      if (index > -1) {
        parentNode.children.splice(index, 1)
        // 重新排序剩余子节点
        parentNode.children.forEach((childId, idx) => {
          const child = mindMapData.value.get(childId)
          if (child) child.order = idx
        })
      }
    }
  } else {
    // 从根节点列表中移除
    const index = rootNodeIds.value.indexOf(nodeId)
    if (index > -1) {
      rootNodeIds.value.splice(index, 1)
    }
  }
}

// 检查是否为后代节点
const isDescendant = (ancestorId: string, nodeId: string): boolean => {
  const node = mindMapData.value.get(nodeId)
  if (!node) return false

  for (const childId of node.children) {
    if (childId === ancestorId || isDescendant(ancestorId, childId)) {
      return true
    }
  }

  return false
}

// 更新子树层级
const updateSubtreeLevel = (nodeId: string) => {
  const node = mindMapData.value.get(nodeId)
  if (!node) return

  const parentLevel = node.parentId ?
    (mindMapData.value.get(node.parentId)?.level || 0) : -1

  node.level = parentLevel + 1

  for (const childId of node.children) {
    updateSubtreeLevel(childId)
  }
}



// 缩放功能
const zoomIn = () => {
  const newZoom = Math.min(zoomLevel.value + ZOOM_CONFIG.ZOOM_STEP, ZOOM_CONFIG.MAX_ZOOM)
  setZoom(newZoom)
}

const zoomOut = () => {
  const newZoom = Math.max(zoomLevel.value - ZOOM_CONFIG.ZOOM_STEP, ZOOM_CONFIG.MIN_ZOOM)
  setZoom(newZoom)
}

const setZoom = (newZoom: number) => {
  const clampedZoom = Math.max(ZOOM_CONFIG.MIN_ZOOM, Math.min(ZOOM_CONFIG.MAX_ZOOM, newZoom))
  if (Math.abs(zoomLevel.value - clampedZoom) > 0.001) { // 只有在缩放级别真正改变时才更新
    zoomLevel.value = clampedZoom
    // 清除缓存的视口信息
    cachedViewport = null
  }
}

// 优化的鼠标滚轮缩放 - 支持以鼠标位置为中心的缩放，添加节流
let lastWheelTime = 0
const handleWheel = (event: WheelEvent) => {
  if (event.ctrlKey || event.metaKey) {
    event.preventDefault()

    // 节流处理，避免过于频繁的缩放
    const now = Date.now()
    if (now - lastWheelTime < 16) return // 最多60fps
    lastWheelTime = now

    if (!canvasContainer.value) return

    const rect = canvasContainer.value.getBoundingClientRect()
    const mouseX = event.clientX - rect.left
    const mouseY = event.clientY - rect.top

    // 计算鼠标在画布坐标系中的位置
    const worldX = (mouseX / zoomLevel.value) - panOffset.value.x
    const worldY = (mouseY / zoomLevel.value) - panOffset.value.y

    const delta = -event.deltaY * ZOOM_CONFIG.ZOOM_WHEEL_FACTOR
    const oldZoom = zoomLevel.value
    const newZoom = Math.max(ZOOM_CONFIG.MIN_ZOOM, Math.min(ZOOM_CONFIG.MAX_ZOOM, oldZoom * (1 + delta)))

    if (Math.abs(newZoom - oldZoom) > 0.001) { // 只有在缩放真正改变时才更新
      zoomLevel.value = newZoom

      // 调整平移偏移，使缩放以鼠标位置为中心
      const newWorldX = (mouseX / newZoom) - panOffset.value.x
      const newWorldY = (mouseY / newZoom) - panOffset.value.y

      panOffset.value.x += worldX - newWorldX
      panOffset.value.y += worldY - newWorldY

      // 清除缓存的视口信息
      cachedViewport = null
    }
  }
}

// 重置视图
const resetView = () => {
  zoomLevel.value = 1
  panOffset.value = { x: 0, y: 0 }
}

// 定位到画布中心
const centerView = () => {
  const positions = getNodePositions()
  const allNodes = Array.from(mindMapData.value.entries())
    .map(([nodeId]) => positions.get(nodeId))
    .filter(pos => pos !== undefined) as NodePosition[]

  if (allNodes.length === 0) {
    resetView()
    return
  }

  // 计算内容中心点
  let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity
  allNodes.forEach(pos => {
    minX = Math.min(minX, pos.x)
    minY = Math.min(minY, pos.y)
    maxX = Math.max(maxX, pos.x + LAYOUT_CONFIG.NODE_DEFAULTS.text.width)
    maxY = Math.max(maxY, pos.y + LAYOUT_CONFIG.NODE_DEFAULTS.text.height)
  })

  const centerX = (minX + maxX) / 2
  const centerY = (minY + maxY) / 2

  if (!canvasContainer.value) return

  const containerRect = canvasContainer.value.getBoundingClientRect()

  // 计算偏移量，使内容中心对齐到视口中心
  panOffset.value = {
    x: (containerRect.width / 2) / zoomLevel.value - centerX,
    y: (containerRect.height / 2) / zoomLevel.value - centerY
  }
}

// 优化的适应画布内容 - 支持无限画布
const fitToContent = () => {
  // 使用所有节点（包括折叠的）来计算边界，而不仅仅是可见节点
  const positions = getNodePositions()
  const allNodes = Array.from(mindMapData.value.entries())
    .map(([nodeId, nodeData]) => ({
      id: nodeId,
      data: nodeData,
      position: positions.get(nodeId)!
    }))
    .filter(node => node.position)

  if (allNodes.length === 0) return

  // 计算所有节点的边界
  let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity

  allNodes.forEach(node => {
    minX = Math.min(minX, node.position.x)
    minY = Math.min(minY, node.position.y)
    maxX = Math.max(maxX, node.position.x + LAYOUT_CONFIG.NODE_DEFAULTS.text.width)
    maxY = Math.max(maxY, node.position.y + LAYOUT_CONFIG.NODE_DEFAULTS.text.height)
  })

  if (!canvasContainer.value) return

  const containerRect = canvasContainer.value.getBoundingClientRect()
  const contentWidth = maxX - minX
  const contentHeight = maxY - minY

  // 计算合适的缩放比例，留出一些边距
  const padding = 100
  const scaleX = (containerRect.width - padding * 2) / contentWidth
  const scaleY = (containerRect.height - padding * 2) / contentHeight
  const newZoom = Math.min(scaleX, scaleY, ZOOM_CONFIG.MAX_ZOOM)

  // 计算居中的偏移量
  const centerX = (containerRect.width / newZoom - contentWidth) / 2 - minX + padding / newZoom
  const centerY = (containerRect.height / newZoom - contentHeight) / 2 - minY + padding / newZoom

  zoomLevel.value = Math.max(ZOOM_CONFIG.MIN_ZOOM, newZoom)
  panOffset.value = { x: centerX, y: centerY }
}

// 优化的鼠标事件处理
const handleMouseDown = (event: MouseEvent) => {
  const target = event.target as HTMLElement
  const isCanvasArea = target === canvasContainer.value ||
                      target.classList.contains('mindmap-canvas') ||
                      target.classList.contains('mindmap-canvas-container')

  // 点击画布空白区域取消节点选择
  if (isCanvasArea) {
    selectedNodeId.value = null
  }

  if (isCanvasArea || isSpacePanning.value) {
    event.preventDefault()
    isPanning.value = true
    isInteracting.value = true // 设置交互状态
    lastPanPoint.value = { x: event.clientX, y: event.clientY }
    if (canvasContainer.value) canvasContainer.value.style.cursor = 'grabbing'
  }
}

// 节流处理鼠标移动 - 根据操作类型调整频率
let lastMouseMoveTime = 0
const handleMouseMove = (event: MouseEvent) => {
  if (!isPanning.value) return

  // 根据是否在拖动根节点调整节流频率
  const throttleInterval = rootDragState.value.isDragging ? 8 : 16 // 拖动时更高频率
  const now = Date.now()
  if (now - lastMouseMoveTime < throttleInterval) return
  lastMouseMoveTime = now

  event.preventDefault()
  const deltaX = (event.clientX - lastPanPoint.value.x) / zoomLevel.value
  const deltaY = (event.clientY - lastPanPoint.value.y) / zoomLevel.value

  panOffset.value.x += deltaX
  panOffset.value.y += deltaY
  lastPanPoint.value = { x: event.clientX, y: event.clientY }

  // 清除缓存的视口信息，强制下次重新计算
  cachedViewport = null
}

const handleMouseUp = () => {
  if (isPanning.value) {
    isPanning.value = false
    isInteracting.value = false // 清除交互状态
    if (canvasContainer.value) {
      canvasContainer.value.style.cursor = isSpacePanning.value ? 'grab' : 'default'
    }
  }
}

const handleMouseLeave = handleMouseUp

// 简化的键盘事件处理
const handleKeyDown = (event: KeyboardEvent) => {
  // 如果有节点正在编辑，跳过全局快捷键处理
  if (editingNodeId.value || noteEditingNodeIds.value.size > 0) return

  const { key, ctrlKey, metaKey } = event

  // 退格键删除选中的节点（仅当节点内容为空，且焦点不在可编辑元素上时）
  if (key === 'Backspace' && selectedNodeId.value) {
    // 如果当前事件源自可编辑元素（input/textarea/contenteditable），则交由其默认行为处理
    const targetEl = event.target as HTMLElement
    if (targetEl && (targetEl.isContentEditable || targetEl.tagName === 'INPUT' || targetEl.tagName === 'TEXTAREA')) {
      return // 直接退出，让内部编辑器处理退格
    }

    const node = mindMapData.value.get(selectedNodeId.value)
    // 仅当节点文本内容为空时才允许删除
    if (node && node.label.trim() !== '') {
      return
    }

    event.preventDefault()
    deleteNode(selectedNodeId.value)
    selectedNodeId.value = null
    return
  }

  // 方向键移动画布
  const arrowKeys = { ArrowUp: [0, 1], ArrowDown: [0, -1], ArrowLeft: [1, 0], ArrowRight: [-1, 0] }
  if (key in arrowKeys) {
    event.preventDefault()
    const step = ZOOM_CONFIG.KEYBOARD_PAN_STEP / zoomLevel.value
    const [x, y] = arrowKeys[key as keyof typeof arrowKeys]
    panOffset.value.x += x * step
    panOffset.value.y += y * step
    return
  }

  // 缩放快捷键
  if (ctrlKey || metaKey) {
    event.preventDefault()
    if (key === '=' || key === '+') zoomIn()
    else if (key === '-') zoomOut()
    else if (key === '0') resetView()
    else if (key === 'd') showDebugInfo.value = !showDebugInfo.value // Ctrl/Cmd + D 切换调试信息
  }
}

// 全局键盘事件处理（空格键平移模式）
const handleGlobalKeyDown = (event: KeyboardEvent) => {
  // 如果有任何节点正在编辑，不处理空格键
  if (event.code === 'Space' && !isSpacePanning.value && !editingNodeId.value && noteEditingNodeIds.value.size === 0) {
    event.preventDefault()
    isSpacePanning.value = true

    // 改变鼠标样式
    if (canvasContainer.value) {
      canvasContainer.value.style.cursor = 'grab'
    }
  }
}

const handleGlobalKeyUp = (event: KeyboardEvent) => {
  if (event.code === 'Space' && isSpacePanning.value) {
    isSpacePanning.value = false
    isPanning.value = false

    // 恢复鼠标样式
    if (canvasContainer.value) {
      canvasContainer.value.style.cursor = 'default'
    }
  }
}

// HeaderActions 事件处理
const handleAction = (_action: ActionItem) => {
  // 操作已经在action函数中处理，这里保留接口一致性
}

const handleClosePane = () => {
  emit('close-pane')
}

const handleSplitHorizontal = () => {
  emit('split-horizontal')
}

const handleSplitVertical = () => {
  emit('split-vertical')
}

// 简化的思维导图操作处理函数
const handleSaveMindMap = () => saveMindMapData().then(() => console.log('保存成功')).catch(console.error)

const handleExportMindMap = () => {
  const data = {
    title: props.node.title || '思维导图',
    mindMapData: Object.fromEntries(mindMapData.value),
    rootNodeIds: rootNodeIds.value,
    nodeCounter: nodeCounter.value,
    exportTime: new Date().toISOString()
  }

  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const link = Object.assign(document.createElement('a'), {
    href: url,
    download: `${props.node.title || 'mindmap'}.json`
  })
  link.click()
  URL.revokeObjectURL(url)
}

const handleCopyMindMap = () => {
  const data = { mindMapData: Object.fromEntries(mindMapData.value), rootNodeIds: rootNodeIds.value, nodeCounter: nodeCounter.value }
  navigator.clipboard.writeText(JSON.stringify(data, null, 2))
    .then(() => console.log('已复制到剪贴板'))
    .catch(console.error)
}

const handleShareMindMap = () => {
  const shareText = `思维导图: ${props.node.title || '未命名思维导图'}`
  if (navigator.share) {
    navigator.share({ title: '思维导图', text: shareText }).catch(() => {})
  } else {
    navigator.clipboard.writeText(shareText).catch(console.error)
  }
}

// 处理笔记编辑器焦点状态
const handleNoteEditorFocus = (nodeId: string) => {
  noteEditingNodeIds.value.add(nodeId)
}

const handleNoteEditorBlur = (nodeId: string) => {
  noteEditingNodeIds.value.delete(nodeId)
}
</script>

<style scoped>
.mindmap-canvas-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.mindmap-canvas {
  position: relative;
  background-color: var(--base-background);
  background-image:
    radial-gradient(circle, #e5e7eb 1px, transparent 1px);
  background-size: 20px 20px;
  min-width: 100%;
  min-height: 100%;
  overflow: visible; /* 确保节点按钮不被裁剪 */
}

.drag-ghost {
  opacity: 0.8;
  transform: rotate(5deg);
}



/* 拖拽预览节点样式 */
.animate-preview-pulse {
  animation: preview-pulse 2s infinite;
}

@keyframes preview-pulse {
  0%, 100% {
    transform: scale(0.95);
    opacity: 0.6;
  }
  50% {
    transform: scale(1);
    opacity: 0.8;
  }
}

/* 滚动条样式 */
.mindmap-canvas::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.mindmap-canvas::-webkit-scrollbar-track {
  background: var(--base-200);
}

.mindmap-canvas::-webkit-scrollbar-thumb {
  background: var(--base-300);
  border-radius: 4px;
}

.mindmap-canvas::-webkit-scrollbar-thumb:hover {
  background: var(--base-400);
}

/* 画布容器样式 - 性能优化 */
.mindmap-canvas {
  /* 启用硬件加速 */
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;

  /* 优化渲染性能 */
  contain: layout style paint;

  /* 强制GPU加速 */
  transform-style: preserve-3d;
}

/* 画布容器在交互时的优化 */
.mindmap-canvas.interacting {
  /* 在交互时禁用一些昂贵的效果 */
  pointer-events: none;
}

/* 节点容器性能优化 */
.mindmap-node {
  /* 启用硬件加速 */
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;

  /* 优化渲染 */
  contain: layout style paint;
}

/* 禁用文本选择 */
.mindmap-canvas * {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 键盘快捷键样式 */
kbd {
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1;
}

/* 控制面板样式 */
.zoom-controls {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.9);
}

@media (prefers-color-scheme: dark) {
  .zoom-controls {
    background: rgba(0, 0, 0, 0.8);
  }
}

/* 平移时的鼠标样式 */
.panning {
  cursor: grabbing !important;
}

.space-panning {
  cursor: grab !important;
}
</style>
