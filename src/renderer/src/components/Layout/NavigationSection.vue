<template>
  <div>
    <SectionHeader
      :title="title"
      :collapsible="collapsible"
      :collapsed="collapsed"
      @toggle="$emit('toggle', $event)"
    />
    <div v-if="!collapsed" class="space-y-1 ml-2">
      <NavigationItem
        v-for="item in items"
        :key="item.id"
        v-bind="item"
        :active="activeItem === item.id"
        :color-indicator="colorIndicator?.(item)"
        @navigate="$emit('navigate', item)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { SectionHeader, NavigationItem } from '../ui/v2'
import type { NavigationItem as NavigationItemType } from '../../constants/navigation'

interface Props {
  title: string
  collapsible?: boolean
  collapsed: boolean
  items: NavigationItemType[]
  activeItem: string
  colorIndicator?: (item: NavigationItemType) => string | undefined
}

defineProps<Props>()

defineEmits<{
  (e: 'toggle', value: boolean): void
  (e: 'navigate', item: NavigationItemType): void
}>()
</script>
