<template>
  <div class="flex-1 flex flex-col">
    <!-- Header -->
    <div class="bg-white border-b border-gray-200 px-6 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <IconButton
            icon="Search"
            variant="ghost"
            size="sm"
            icon-class="w-5 h-5 text-gray-400"
          />
          <IconButton
            icon="Edit"
            variant="ghost"
            size="sm"
            icon-class="w-5 h-5 text-gray-400"
          />

          <TabButton
            v-for="tab in headerTabs"
            :key="tab.id"
            :label="tab.label"
            :icon="tab.icon"
            :active="activeTab === tab.id"
            @click="handleTabChange(tab.id)"
          />

          <div class="w-4 h-4 text-gray-400">
            <svg viewBox="0 0 16 16" fill="currentColor">
              <path
                d="M8 0a8 8 0 100 16A8 8 0 008 0zM1.5 8a6.5 6.5 0 1113 0 6.5 6.5 0 01-13 0z"
              />
            </svg>
          </div>
        </div>
        <div class="flex items-center space-x-4">
          <IconButton
            icon="Bell"
            variant="ghost"
            size="sm"
            icon-class="w-5 h-5 text-gray-400"
          />
          <div class="w-5 h-5 text-gray-400">
            <svg viewBox="0 0 16 16" fill="currentColor">
              <path d="M8 0a8 8 0 100 16A8 8 0 008 0z" />
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white border-b border-gray-200 px-6 py-3">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-2 cursor-pointer" @click="handleFilterClick">
          <IconButton
            icon="Filter"
            variant="ghost"
            size="xs"
            icon-class="w-4 h-4 text-gray-500"
          />
          <span class="text-[11px] text-gray-600">Filter</span>
        </div>
        <div class="flex items-center space-x-2 cursor-pointer" @click="handleDisplayClick">
          <IconButton
            icon="LayoutGrid"
            variant="ghost"
            size="xs"
            icon-class="w-3.5 h-3.5 text-gray-500"
          />
          <span class="text-[11px] text-gray-600">Display</span>
        </div>
      </div>
    </div>

    <!-- Issues List -->
    <div class="flex-1 bg-white">
      <!-- Todo Section -->
      <div class="px-6 py-4 border-b border-gray-100">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <IconButton
              icon="Circle"
              variant="ghost"
              size="xs"
              icon-class="w-3.5 h-3.5 text-gray-400"
            />
            <span class="font-medium text-[11px] text-gray-900">{{ currentStatusTitle }}</span>
            <span class="text-[11px] text-gray-500">{{ filteredIssues.length }}</span>
          </div>
          <IconButton
            icon="Plus"
            variant="ghost"
            size="xs"
            icon-class="w-3.5 h-3.5 text-gray-400"
            @click="handleAddIssue"
          />
        </div>
      </div>

      <!-- Issue Items -->
      <div class="divide-y divide-gray-100">
        <IssueCard
          v-for="issue in filteredIssues"
          :key="issue.id"
          :issue-id="issue.id"
          :title="issue.title"
          :type="issue.type"
          :assignee="issue.assignee"
          :date="issue.date"
          :avatar="issue.avatar"
          @click="handleIssueClick(issue)"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { IconButton, TabButton, IssueCard } from '../ui/v2'
import type { Issue } from '../../constants/issues'
import { MAIN_HEADER_TABS } from '../../constants/issues'

const props = defineProps<{
  issues: Issue[]
  activeTab: string
}>()

const emit = defineEmits<{
  (e: 'tabChange', tabId: string): void
  (e: 'issueClick', issue: Issue): void
  (e: 'addIssue'): void
  (e: 'filterClick'): void
  (e: 'displayClick'): void
}>()

const headerTabs = MAIN_HEADER_TABS

const currentStatusTitle = computed(() => {
  const tab = headerTabs.find(t => t.id === props.activeTab)
  return tab?.label || 'Todo'
})

const filteredIssues = computed(() => {
  switch (props.activeTab) {
    case 'active':
      return props.issues.filter(issue => issue.status === 'Todo' || issue.status === 'In Progress')
    case 'backlog':
      return props.issues.filter(issue => issue.status === 'Todo')
    case 'all-issues':
    default:
      return props.issues
  }
})

const handleTabChange = (tabId: string) => {
  emit('tabChange', tabId)
}

const handleIssueClick = (issue: Issue) => {
  emit('issueClick', issue)
}

const handleAddIssue = () => {
  emit('addIssue')
}

const handleFilterClick = () => {
  emit('filterClick')
}

const handleDisplayClick = () => {
  emit('displayClick')
}
</script>
