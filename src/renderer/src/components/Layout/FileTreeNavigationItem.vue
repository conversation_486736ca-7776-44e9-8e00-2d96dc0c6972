<template>
  <div class="list-none my-1 w-full relative" :data-note-id="item.uuid">
    <!-- 添加统一的间隙高亮 -->
    <div
      v-if="isGapHighlighted"
      class="absolute left-0 right-0 h-1 bg-primary/60 pointer-events-none z-10"
      :class="{
        'top-0': dropPosition === 'before',
        'bottom-0': dropPosition === 'after'
      }"
    ></div>

    <ContextMenu @update:open="isContextMenuOpen = $event">
      <ContextMenuTrigger>
        <!-- 主文件项 -->
        <div
          :class="[
            'flex items-center space-x-2 px-2 py-1.5 text-base-content hover:bg-base-100 rounded-md cursor-pointer group',
            {
              'bg-base-100':
                isNodeSelected || isNodeOpened || isAddDropdownOpen || isMoreDropdownOpen,
              'text-base-content':
                isNodeSelected || isNodeOpened || isAddDropdownOpen || isMoreDropdownOpen,
              'bg-base-content/20': isDragOver
            }
          ]"
          @click="handleClick"
          @dragover="handleDragOver"
          @dragleave="handleDragLeave"
          @drop="handleDrop"
          @dragstart="handleDragStart"
          @dragend="handleDragEnd"
          draggable="true"
        >
          <!-- 图标区域：展开箭头和文件图标的容器 -->
          <div
            class="w-4 h-4 flex items-center justify-center flex-shrink-0 relative"
            @click.stop="handleIconClick"
          >
            <!-- 展开箭头 - 仅在hover整行且有子节点时显示，替换原图标 -->
            <div
              v-if="hasChildren"
              class="w-4 h-4 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 absolute"
            >
              <ChevronRight
                class="w-3.5 h-3.5 text-base-content/60 transition-transform duration-200"
                :class="{ 'rotate-90': isNodeExpanded }"
              />
            </div>

            <!-- 原始图标 - hover整行时隐藏（如果有子节点） -->
            <div
              class="w-4 h-4 flex items-center justify-center transition-opacity duration-200"
              :class="{ 'group-hover:opacity-0': hasChildren }"
            >
              <!-- 自定义图标优先显示 -->
              <component
                v-if="
                  customIcon && !isCustomSvg(customIcon) && !getSvgContentByIconName(customIcon)
                "
                :is="getIconComponent(customIcon)"
                class="w-4 h-4 text-base-content/70"
              />
              <div
                v-else-if="
                  customIcon && (isCustomSvg(customIcon) || getSvgContentByIconName(customIcon))
                "
                v-html="getIconDisplayContent(customIcon)"
                class="w-4 h-4 flex items-center justify-center [&>svg]:w-full [&>svg]:h-full [&>svg]:fill-current text-base-content/70"
              ></div>

              <!-- 文件夹图标 -->
              <FolderIcon
                v-else-if="hasChildren && !isNodeExpanded"
                class="w-4 h-4 text-amber-500"
              />
              <FolderOpenIcon
                v-else-if="hasChildren && isNodeExpanded"
                class="w-4 h-4 text-amber-500"
              />

              <!-- PDF 文件图标 -->
              <img
                v-else-if="isPDF"
                src="@renderer/assets/images/reader.svg"
                class="w-4 h-4 text-red-400"
              />

              <!-- Markdown 文件图标 -->
              <FileTextIcon v-else-if="isNote" class="w-4 h-4 text-blue-500" />

              <!-- 星流节点图标 -->
              <Circle v-else-if="isXingliu" class="w-4 h-4 text-emerald-500" />

              <!-- 待办清单图标 -->
              <ListTodo v-else-if="isTodo" class="w-4 h-4 text-green-500" />

              <!-- 思维导图图标 -->
              <svg v-else-if="isMindmap" class="w-4 h-4 text-purple-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="3"/>
                <path d="M12 1v6m0 6v6"/>
                <path d="m21 12-6-3-6 3-6-3"/>
                <path d="m21 12-6 3-6-3-6 3"/>
                <path d="M12 1L9 9l3 3 3-3z"/>
                <path d="M12 23l-3-8 3-3 3 3z"/>
              </svg>

              <!-- 默认文件图标 -->
              <FileIcon v-else class="w-4 h-4 text-base-content/70" />
            </div>
          </div>

          <!-- 文件名 -->
          <div class="flex-1 min-w-0">
            <!-- 编辑模式 -->
            <input
              v-if="isEditing"
              ref="editInput"
              v-model="editingName"
              class="w-full text-xs bg-base-100 border border-base-content/20 rounded px-2 py-0.5 text-base-content/90 focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
              @keydown.enter.stop="handleEditKeydown"
              @keydown.esc="cancelEdit"
              @blur="confirmEdit"
              @compositionstart="handleCompositionStart"
              @compositionend="handleCompositionEnd"
              @click.stop
            />
            <!-- 显示模式 -->
            <span v-else class="font-normal text-base-content-secondary truncate block">
              {{ item.title }}
            </span>
          </div>

          <!-- 右侧操作按钮 - 在hover或下拉菜单打开时显示 -->
          <div
            :class="[
              'transition-opacity duration-200 flex items-center space-x-1',
              {
                'opacity-100 pointer-events-auto': isAddDropdownOpen || isMoreDropdownOpen,
                'opacity-0 group-hover:opacity-100 group-hover:pointer-events-auto pointer-events-none':
                  !isAddDropdownOpen && !isMoreDropdownOpen
              }
            ]"
          >
            <!-- 添加按钮 -->
            <DropdownMenu @update:open="isAddDropdownOpen = $event">
              <DropdownMenuTrigger as-child>
                <button
                  class="cursor-pointer w-5 h-5 flex items-center justify-center text-base-content/60 rounded transition-colors hover:bg-base-200"
                  @click.stop
                  title="添加子项"
                >
                  <Plus class="w-4 h-4" />
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent class="bg-base-background border-base-content/10">
                <DropdownMenuItem
                  class="hover:bg-base-100 focus:bg-base-100"
                  @select="addChildNote"
                >
                  <div class="flex items-center">
                    <FileTextIcon class="mr-2 h-3.5 w-3.5 text-indigo-500" />
                    <span class="text-base-content/90">新建子笔记</span>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem class="hover:bg-base-100 focus:bg-base-100" @select="addChildPDF">
                  <div class="flex items-center">
                    <img src="@renderer/assets/images/reader.svg" class="mr-2 h-3.5 w-3.5" />
                    <span class="text-base-content/90">新建子PDF</span>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem
                  class="hover:bg-base-100 focus:bg-base-100"
                  @select="addChildTodoList"
                >
                  <div class="flex items-center">
                    <ListTodo class="mr-2 h-3.5 w-3.5 text-green-500" />
                    <span class="text-base-content/90">新建子待办清单</span>
                  </div>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <!-- 更多操作按钮 -->
            <DropdownMenu @update:open="isMoreDropdownOpen = $event">
              <DropdownMenuTrigger as-child>
                <button
                  class="cursor-pointer w-5 h-5 flex items-center justify-center text-base-content/60 rounded transition-colors hover:bg-base-200"
                  @click.stop
                  title="更多选项"
                >
                  <MoreHorizontal class="w-4 h-4" />
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent class="bg-base-background border-base-content/10">
                <DropdownMenuItem
                  v-if="!isNote"
                  class="hover:bg-base-content/10 focus:bg-base-content/10"
                  @select="startEdit"
                >
                  <div class="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="mr-2 h-4 w-4 text-base-content/70"
                    >
                      <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z" />
                    </svg>
                    <span class="text-base-content/90">重命名</span>
                  </div>
                </DropdownMenuItem>

                <DropdownMenuItem
                  class="hover:bg-base-100 focus:bg-base-100"
                  @select="openIconDialog"
                >
                  <div class="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="mr-2 h-4 w-4 text-base-content/70"
                    >
                      <polygon
                        points="12,2 15.09,8.26 22,9 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9 8.91,8.26"
                      />
                    </svg>
                    <span class="text-base-content/90">设置图标</span>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem class="hover:bg-base-100 focus:bg-base-100" @select="deleteItem">
                  <div class="flex items-center">
                    <TrashIcon class="mr-2 h-3.5 w-3.5 text-title" />
                    <span class="text-base-content/90">删除</span>
                  </div>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </ContextMenuTrigger>
      <ContextMenuContent class="bg-base-background border-base-content/10">
        <ContextMenuItem class="hover:bg-base-100 focus:bg-base-100" @select="addChildNote">
          <div class="flex items-center">
            <FileTextIcon class="mr-2 h-3.5 w-3.5 text-indigo-500" />
            <span class="text-base-content/90">新建子笔记</span>
          </div>
        </ContextMenuItem>
        <ContextMenuItem class="hover:bg-base-100 focus:bg-base-100" @select="addChildPDF">
          <div class="flex items-center">
            <img src="@renderer/assets/images/reader.svg" class="mr-2 h-3.5 w-3.5" />
            <span class="text-base-content/90">新建子PDF</span>
          </div>
        </ContextMenuItem>
        <ContextMenuItem class="hover:bg-base-100 focus:bg-base-100" @select="addChildTodoList">
          <div class="flex items-center">
            <ListTodo class="mr-2 h-3.5 w-3.5 text-green-500" />
            <span class="text-base-content/90">新建子待办清单</span>
          </div>
        </ContextMenuItem>

        <ContextMenuItem class="hover:bg-base-100 focus:bg-base-100" @select="startEdit">
          <div class="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2.5"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="mr-2 h-4 w-4 text-base-content/70"
            >
              <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z" />
            </svg>
            <span class="text-base-content/90">重命名</span>
          </div>
        </ContextMenuItem>

        <ContextMenuItem class="hover:bg-base-100 focus:bg-base-100" @select="openIconDialog">
          <div class="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2.5"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="mr-2 h-4 w-4 text-base-content/70"
            >
              <polygon
                points="12,2 15.09,8.26 22,9 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9 8.91,8.26"
              />
            </svg>
            <span class="text-base-content/90">设置图标</span>
          </div>
        </ContextMenuItem>
        <ContextMenuItem class="hover:bg-base-100 focus:bg-base-100" @select="deleteItem">
          <TrashIcon class="mr-2 h-3.5 w-3.5 text-base-content/70" />
          <span class="text-base-content/90">删除</span>
        </ContextMenuItem>
      </ContextMenuContent>
    </ContextMenu>

    <!-- 子文件项 -->
    <div v-if="hasChildren && isNodeExpanded" class="ml-6 space-y-0.5">
      <FileTreeNavigationItem
        v-for="child in item.children"
        :key="child.uuid"
        :item="child"
        :level="level + 1"
        :active-item="activeItem"
      />
    </div>

    <!-- Icon Picker Dialog -->
    <Dialog v-model:open="isIconDialogOpen">
      <DialogContent class="bg-base-background border-base-content/10">
        <DialogHeader>
          <DialogTitle class="text-base-content/90">设置图标</DialogTitle>
        </DialogHeader>
        <div class="py-4">
          <IconPicker v-model="selectedIcon" />
        </div>
        <DialogFooter>
          <Button variant="ghost" class="text-base-content/90" @click="cancelIconSelection">
            取消
          </Button>
          <Button variant="ghost" class="text-base-content/90" @click="confirmIconSelection">
            确认
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- 删除确认对话框 -->
    <ConfirmDialog
      v-model:open="showDeleteDialog"
      title="确认删除"
      :description="
        itemToDelete
          ? `您确定要删除 '${itemToDelete.title}' 吗？项目将被移动到回收站。`
          : '您确定要删除选中的项目吗？项目将被移动到回收站。'
      "
      confirm-text="删除"
      cancel-text="取消"
      confirm-icon="Trash2"
      cancel-icon="X"
      variant="warning"
      @confirm="confirmDelete"
      @cancel="showDeleteDialog = false"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, defineAsyncComponent, nextTick, onMounted, onUnmounted, ref } from 'vue'
import {
  ChevronRight,
  FolderIcon,
  FolderOpenIcon,
  FileIcon,
  FileTextIcon,
  ListTodo,
  Circle,
  Plus,
  MoreHorizontal,
  TrashIcon
} from 'lucide-vue-next'
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger
} from '@renderer/components/ui/context-menu'
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@renderer/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@renderer/components/ui/dropdown-menu'
import { BaseNode } from '@renderer/componentSystem/common/BaseNode'
import { mitter } from '@renderer/plugins/mitter'
import { useTodoService } from '@renderer/services/TodoService'
import { useFileTreeStore } from '../../stores/fileTreeStore'
import { useNodeStore } from '@renderer/stores/nodeStore'
import { setCurrentResource } from '@renderer/utils/bus'
import { getIconComponent, isCustomSvg } from '../../utils/iconComponents'
import { getSvgContentByIconName } from '../../utils/svgIcons'
import { extractIconFromMeta } from '../../utils/iconUtils'
import { track } from '@renderer/utils/tracks'
import IconPicker from '../Todo/IconPicker.vue'
import { Button } from '@renderer/components/ui/button'
import { useRoute, useRouter } from 'vue-router'
import { useGlobalStore } from '@renderer/stores/globalStore'
import { useTrashStore } from '@renderer/stores/trashStore'
import ConfirmDialog from '@renderer/components/ui/ConfirmDialog.vue'
import { showToast } from '@renderer/utils/toast'

interface Props {
  item: any
  level?: number
  activeItem?: string
}

const props = withDefaults(defineProps<Props>(), {
  level: 0,
  activeItem: ''
})

const fileTreeStore = useFileTreeStore()
const nodeStore = useNodeStore()
const trashStore = useTrashStore()

// 状态变量
const isEditing = ref(false)
const editingName = ref('')
const editInput = ref<HTMLInputElement | null>(null)
const isContextMenuOpen = ref(false)
const isDragOver = ref(false)
const isGapHighlighted = ref(false)
const dropPosition = ref<'before' | 'after' | 'inside'>('inside')
const isIconDialogOpen = ref(false)
const selectedIcon = ref<string | null>(null)
const isAddDropdownOpen = ref(false)
const isMoreDropdownOpen = ref(false)
const showDeleteDialog = ref(false)
const itemToDelete = ref<any>(null)

// 计算属性
const hasChildren = computed(() => props.item.children?.length > 0)
const isPDF = computed(() => props.item.type === 'pdf')
const isNote = computed(() => props.item.type === 'note')
const isXingliu = computed(() => props.item.type === 'xingliu')
const isTodo = computed(() => props.item.type === 'todo')
const isMindmap = computed(() => props.item.type === 'mindmap')

const isNodeSelected = computed(() => {
  return fileTreeStore.selectedNodeIds.includes(props.item.uuid)
})

const isNodeOpened = computed(() => {
  return fileTreeStore.openedNodeIds.includes(props.item.uuid)
})

const isNodeExpanded = computed(() => {
  return fileTreeStore.expandedNodeIds.has(props.item.uuid)
})

const customIcon = computed(() => {
  const node = nodeStore.getNodeById(props.item.uuid)
  if (node && node.meta && Array.isArray(node.meta)) {
    const iconInfo = extractIconFromMeta(node.meta)
    return iconInfo?.value || null
  }
  return null
})

// 获取图标显示内容
const getIconDisplayContent = (icon: string) => {
  if (icon && icon.includes('<svg')) {
    return icon
  }
  if (icon) {
    const svgContent = getSvgContentByIconName(icon)
    if (svgContent) {
      return svgContent
    }
  }
  return icon
}

onMounted(() => {
  mitter.on('note-created', (event: any) => {
    if (event.parentId === props.item.uuid) {
      fileTreeStore.expandNode(props.item.uuid)
    }
  })
})

onUnmounted(() => {
  mitter.off('note-created')
})

const route = useRoute()
const router = useRouter()
const globalStore = useGlobalStore()

// 事件处理
const handleClick = (event: MouseEvent) => {
  if (route.name === 'Mail') {
    router.push({ name: 'SplitPanels' })
  } else {
    globalStore.isMessagePageOpen = false
    globalStore.isTrashOpen = false
  }
  // 只处理选择节点和打开
  fileTreeStore.selectNode(props.item.uuid, event)
}

// 添加新的图标点击处理函数
const handleIconClick = (event: MouseEvent) => {
  // 如果有子节点，切换展开状态
  if (hasChildren.value) {
    fileTreeStore.toggleNodeExpanded(props.item.uuid)
  }
}

// 添加子笔记
const addChildNote = async () => {
  const newNode = await props.item.addNode('新笔记', 'note')
  fileTreeStore.expandNode(props.item.uuid)
  mitter.emit('note-created', {
    noteId: newNode.uuid,
    parentId: props.item.uuid,
    shouldScroll: true
  })

  // 自动打开新创建的子笔记
  setCurrentResource(newNode.uuid, 'note')

  // 使用 dockview 系统打开新笔记
  const { useDockviewStore } = await import('../../stores/dockviewStore')
  const dockviewStore = useDockviewStore()

  const panel = dockviewStore.addPanel({
    id: `note-${newNode.uuid}`,
    title: newNode.title || '新笔记',
    component: 'panel',
    params: {
      type: 'note',
      node: newNode
    }
  })

  if (panel) {
    dockviewStore.setActivePanelId(panel.id)
  }

  track({
    action_type: 'SideBar',
    extra1: 'Create Kids Note'
  })
}

// 添加子PDF
const addChildPDF = async () => {
  const newNode = await props.item.addNode('新PDF', 'pdf')
  fileTreeStore.expandNode(props.item.uuid)
  mitter.emit('note-created', {
    noteId: newNode.uuid,
    parentId: props.item.uuid,
    shouldScroll: true
  })

  // 自动打开新创建的子PDF
  setCurrentResource(newNode.uuid, 'pdf')

  // 使用 dockview 系统打开新PDF
  const { useDockviewStore } = await import('../../stores/dockviewStore')
  const dockviewStore = useDockviewStore()

  const panel = dockviewStore.addPanel({
    id: `pdf-${newNode.uuid}`,
    title: newNode.title || '新PDF',
    component: 'panel',
    params: {
      type: 'pdf',
      node: newNode
    }
  })

  if (panel) {
    dockviewStore.setActivePanelId(panel.id)
  }

  track({
    action_type: 'SideBar',
    extra1: 'Create Kids PDF'
  })
}

// 添加子待办清单
const addChildTodoList = async () => {
  const todoService = useTodoService()
  const listUuid = await todoService.createTodoList('新待办清单', props.item.uuid)

  fileTreeStore.expandNode(props.item.uuid)

  mitter.emit('note-created', {
    noteId: listUuid,
    parentId: props.item.uuid,
    shouldScroll: true
  })

  // 自动打开新创建的子待办清单
  const resourceNode = nodeStore.getNodeById(listUuid) as BaseNode

  if (resourceNode) {
    // 使用 dockview 系统打开新待办清单
    const { useDockviewStore } = await import('../../stores/dockviewStore')
    const dockviewStore = useDockviewStore()

    const panel = dockviewStore.addPanel({
      id: `todo-${listUuid}`,
      title: resourceNode.title || '新待办清单',
      component: 'panel',
      params: {
        type: 'todo',
        node: resourceNode
      }
    })

    if (panel) {
      dockviewStore.setActivePanelId(panel.id)
    }
  }

  track({
    action_type: 'SideBar',
    extra1: 'Create Kids Todo List'
  })
}

// 内联编辑相关
const startEdit = () => {
  if (isEditing.value) return

  editingName.value = props.item.title
  isEditing.value = true

  // 下一帧聚焦并选中文本（不包括扩展名）
  nextTick(() => {
    if (editInput.value) {
      editInput.value.focus()

      // 选中文件名（不包括扩展名）
      const title = props.item.title
      const lastDotIndex = title.lastIndexOf('.')
      if (lastDotIndex > 0) {
        editInput.value.setSelectionRange(0, lastDotIndex)
      } else {
        editInput.value.select()
      }
    }
  })
}

const confirmEdit = async () => {
  if (!isEditing.value) return

  const newName = editingName.value.trim()
  if (newName && newName !== props.item.title) {
    try {
      await props.item.rename(newName)
    } catch (error) {
      console.error('重命名失败:', error)
      // 重命名失败时恢复原名称
      editingName.value = props.item.title
    }
  }

  isEditing.value = false
}

const cancelEdit = () => {
  editingName.value = props.item.title
  isEditing.value = false
}

// 中文输入法支持
const isComposing = ref(false)

const handleCompositionStart = () => {
  isComposing.value = true
}

const handleCompositionEnd = () => {
  isComposing.value = false
}

const handleEditKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !isComposing.value) {
    event.preventDefault()
    event.stopPropagation()
    confirmEdit()
  }
}

// 删除功能 - 显示确认对话框
const deleteItem = () => {
  itemToDelete.value = props.item
  showDeleteDialog.value = true
}

// 确认删除 - 移动到回收站
const confirmDelete = async () => {
  if (!itemToDelete.value) return

  try {
    if (fileTreeStore.selectedNodeIds.includes(itemToDelete.value.uuid)) {
      // 删除多个选中的节点
      for (const nodeId of fileTreeStore.selectedNodeIds) {
        const node = nodeStore.getNodeById(nodeId)
        if (node) {
          await moveToTrash(node)
        }
      }
      showToast('成功', `已将 ${fileTreeStore.selectedNodeIds.length} 个项目移动到回收站`, 2000)
    } else {
      // 删除单个节点
      await moveToTrash(itemToDelete.value)
      showToast('成功', `已将 "${itemToDelete.value.title}" 移动到回收站`, 2000)
    }
  } catch (error) {
    console.error('移动到回收站失败:', error)
    showToast('错误', '移动到回收站失败', 3000)
  } finally {
    showDeleteDialog.value = false
    itemToDelete.value = null
  }
}

// 移动单个项目到回收站
const moveToTrash = async (node: any) => {
  // 添加到回收站
  await trashStore.addToTrash(node)

  // 从面板中移除

  // 从 dockview 面板中移除
  const { useDockviewStore } = await import('../../stores/dockviewStore')
  const dockviewStore = useDockviewStore()
  const allPanels = dockviewStore.getAllPanels()

  // 移除所有与该资源相关的面板
  allPanels.forEach((panel) => {
    if (panel.params?.node?.uuid === node.uuid) {
      dockviewStore.removePanel(panel.id)
    }
  })

  // 从 selectedNodeIds 中移除该节点（因为节点已被删除）
  const index = fileTreeStore.selectedNodeIds.indexOf(node.uuid)
  if (index !== -1) {
    fileTreeStore.selectedNodeIds.splice(index, 1)
  }

  // 不再调用 node.removeNode()，让过滤机制自动隐藏节点
  // 节点数据保留在原始数据结构中，只是通过回收站状态来控制显示/隐藏

  // 发送删除事件
  mitter.emit('resource-deleted', {
    nodeId: node.uuid,
    nodeType: node.type
  })

  track({
    action_type: 'SideBar',
    extra1: 'Move to Trash'
  })
}

// 图标相关
const openIconDialog = () => {
  selectedIcon.value = customIcon.value
  isIconDialogOpen.value = true
}

const confirmIconSelection = async () => {
  try {
    const node = nodeStore.getNodeById(props.item.uuid)

    if (node) {
      const { updateIconInMeta } = await import('@renderer/utils/iconUtils')
      node.meta = updateIconInMeta(node.meta || [], selectedIcon.value)
      await node.saveChanges()
    }

    isIconDialogOpen.value = false
  } catch (error) {
    console.error('Failed to save icon:', error)
  }
}

const cancelIconSelection = () => {
  selectedIcon.value = null
  isIconDialogOpen.value = false
}

// 拖拽相关
const handleDragStart = (e: DragEvent) => {
  e.dataTransfer!.setData('id', props.item.uuid)
  e.dataTransfer!.setData('type', props.item.type.toString())

  const simplifiedNode = {
    uuid: props.item.uuid,
    type: props.item.type,
    title: props.item.title
  }
  e.dataTransfer!.setData('node', JSON.stringify(simplifiedNode))
  e.dataTransfer!.effectAllowed = 'move'

  const target = e.target as HTMLElement
  target.classList.add('dragging')

  mitter.emit('splitpanel:dragstart')
}

const handleDragEnd = (e: DragEvent) => {
  const target = e.target as HTMLElement
  target.classList.remove('dragging')

  mitter.emit('dragging-leave-item')
  isDragOver.value = false

  mitter.emit('splitpanel:dragend')
}

const handleDragOver = (e: DragEvent) => {
  e.preventDefault()
  e.dataTransfer!.dropEffect = 'move'

  const rect = (e.currentTarget as HTMLElement).getBoundingClientRect()
  const y = e.clientY - rect.top
  const height = rect.height
  const threshold = height * 0.3

  if (y < threshold) {
    dropPosition.value = 'before'
    isGapHighlighted.value = true
    isDragOver.value = false
  } else if (y > height - threshold) {
    dropPosition.value = 'after'
    isGapHighlighted.value = true
    isDragOver.value = false
  } else {
    dropPosition.value = 'inside'
    isGapHighlighted.value = false
    isDragOver.value = true
  }

  mitter.emit('dragging-over-item')
}

const handleDragLeave = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = false
  isGapHighlighted.value = false

  mitter.emit('dragging-leave-item')
}

const handleDrop = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = false
  isGapHighlighted.value = false

  const sourceId = e.dataTransfer?.getData('id')
  if (!sourceId) {
    e.stopPropagation()
    mitter.emit('dragging-leave-item')
    return
  }

  fileTreeStore.handleMultiSelectMove(sourceId, props.item.uuid, dropPosition.value)

  if (dropPosition.value === 'inside') {
    fileTreeStore.expandNode(props.item.uuid)
  }

  track({
    action_type: 'SideBar',
    extra1: fileTreeStore.selectedNodeIds.includes(sourceId)
      ? `Move Multiple Nodes ${dropPosition.value}`
      : `Move Node ${dropPosition.value}`
  })

  mitter.emit('dragging-leave-item')
  e.stopPropagation()
}

// 自引用组件声明
const FileTreeNavigationItem = defineAsyncComponent(() => import('./FileTreeNavigationItem.vue'))
</script>

<style scoped>
.dragging {
  opacity: 0.5;
}

.absolute {
  transition: all 0.2s ease-in-out;
}

.h-1 {
  margin-top: -2px;
  margin-bottom: -2px;
  height: 4px;
  border-radius: 2px;
  box-shadow: 0 0 4px rgba(var(--primary), 0.4);
}
</style>
