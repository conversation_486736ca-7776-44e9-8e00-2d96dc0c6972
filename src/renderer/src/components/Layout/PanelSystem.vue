<template>
  <div class="w-full h-full relative">
    <!-- 当没有面板时显示 Empty 组件 -->
    <Empty v-if="!hasPanels" class="absolute inset-0 z-10" @createNote="handleCreateNote" />

    <!-- Dockview 容器 -->
    <DockviewVue
      @ready="handleReady"
      class="w-full h-full"
      :class="{ 'opacity-0': !hasPanels }"
      :disable-tabs-overflow-list="true"
      :theme="currentDockviewTheme"
      single-tab-mode="fullwidth"
      :tab-components="tabComponents"
    />
  </div>
</template>

<script lang="ts" setup>
import { useDockviewStore } from '@renderer/stores/dockviewStore'
import { useFileTreeStore } from '@renderer/stores/fileTreeStore'
import { DockviewApi, DockviewReadyEvent, DockviewVue, DockviewTheme } from 'dockview-vue'
import { useBlockCreation } from '@renderer/composables/useBlockCreation'
import { useSettingsStore } from '@renderer/stores/settings'
import { ref, computed } from 'vue'
import Panel from './Panel.vue'
import Empty from './Empty.vue'

const dockviewStore = useDockviewStore()
const fileTreeStore = useFileTreeStore()
const { createBlock } = useBlockCreation()
const settingsStore = useSettingsStore()

// 跟踪面板数量
const panelCount = ref(0)
const hasPanels = computed(() => panelCount.value > 0)

// 创建自定义主题
const customTheme: DockviewTheme = {
  name: 'custom',
  className: 'dockview-theme-custom'
}

// 动态主题选择
const currentDockviewTheme = computed(() => {
  return customTheme
})

const tabComponents = {
  panel: Panel
}

// 处理创建笔记事件
const handleCreateNote = async () => {
  const newNote = await createBlock('note')
  if (newNote) {
    // 创建面板显示新笔记
    const panel = dockviewStore.addPanel({
      id: `note-${newNote.uuid}`,
      title: newNote.title,
      component: 'panel',
      params: {
        type: 'note',
        node: newNote
      }
    })
    if (panel) {
      dockviewStore.setActivePanelId(panel.id)
    }
  }
}

const handleReady = (event: DockviewReadyEvent) => {
  const api: DockviewApi = event.api
  dockviewStore.setApi(api)

  // 监听面板激活变化事件
  api.onDidActivePanelChange((activePanel) => {
    dockviewStore.setActivePanelId(activePanel?.id || null)
    
    // 同步文件树选中状态
    if (activePanel && activePanel.params) {
      const params = activePanel.params as any
      if (params.node && params.node.uuid) {
        // 仅更新选中状态，不触发打开操作
        fileTreeStore.selectNodeOnly(params.node.uuid)
      }
    }
  })

  // 监听面板添加事件
  api.onDidAddPanel(() => {
    panelCount.value = api.panels.length
  })

  // 监听面板移除事件
  api.onDidRemovePanel(() => {
    setTimeout(() => {
      panelCount.value = api.panels.length
    }, 0)
  })

  // 初始化面板数量
  panelCount.value = api.panels.length

  // 初始化激活面板状态
  const currentActivePanel = api.activePanel
  if (currentActivePanel) {
    dockviewStore.setActivePanelId(currentActivePanel.id)
    
    // 同步初始文件树选中状态
    if (currentActivePanel.params) {
      const params = currentActivePanel.params as any
      if (params.node && params.node.uuid) {
        // 仅更新选中状态，不触发打开操作
        fileTreeStore.selectNodeOnly(params.node.uuid)
      }
    }
  }
}
</script>
