<template>
  <div class="todo-loading h-full flex items-center justify-center">
    <div class="flex flex-col items-center space-y-4">
      <!-- Loading animation -->
      <div class="relative">
        <!-- Main spinner -->
        <div class="w-8 h-8 border-2 border-base-300 border-t-accent rounded-full animate-spin"></div>
        
        <!-- Pulse effect -->
        <div class="absolute inset-0 w-8 h-8 border-2 border-accent/20 rounded-full animate-ping"></div>
      </div>
      
      <!-- Loading text -->
      <div class="text-center">
        <p class="text-base-content/70 text-sm font-medium">{{ loadingText }}</p>
        <p class="text-base-content/50 text-xs mt-1">{{ subText }}</p>
      </div>
      
      <!-- Progress dots -->
      <div class="flex space-x-1">
        <div 
          v-for="i in 3" 
          :key="i"
          class="w-2 h-2 bg-accent/60 rounded-full animate-pulse"
          :style="{ animationDelay: `${(i - 1) * 0.2}s` }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = withDefaults(defineProps<{
  loadingText?: string
  subText?: string
  variant?: 'default' | 'minimal' | 'detailed'
}>(), {
  loadingText: '加载中...',
  subText: '正在获取待办事项',
  variant: 'default'
})

const loadingText = computed(() => {
  if (props.variant === 'minimal') return '加载中'
  if (props.variant === 'detailed') return '正在加载待办事项'
  return props.loadingText
})

const subText = computed(() => {
  if (props.variant === 'minimal') return ''
  if (props.variant === 'detailed') return '请稍候，正在从数据库获取数据...'
  return props.subText
})
</script>

<style scoped>
.todo-loading {
  min-height: 200px;
}

/* Custom animation for dots */
@keyframes pulse-delay {
  0%, 80%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  40% {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-pulse-delay {
  animation: pulse-delay 1.5s ease-in-out infinite;
}

/* Smooth spinner */
@keyframes smooth-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-smooth-spin {
  animation: smooth-spin 1s linear infinite;
}
</style>
