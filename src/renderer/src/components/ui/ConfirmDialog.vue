<template>
  <Teleport to="body">
    <Dialog v-if="open" :open="open" @update:open="$emit('update:open', $event)">
      <DialogContent class="sm:max-w-md confirm-dialog-content">
        <DialogHeader>
          <DialogTitle class="text-lg font-semibold text-base-content">{{ title }}</DialogTitle>
          <DialogDescription class="text-sm text-base-content/70 mt-2">{{
            description
          }}</DialogDescription>
        </DialogHeader>
        <DialogFooter class="flex justify-end gap-3 mt-6">
          <button
            class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-base-content rounded-md border border-base-300 bg-base-100 hover:bg-base-200 hover:text-base-content transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 min-w-[80px]"
            @click="handleCancel"
          >
            <component :is="getIconComponent(cancelIcon)" class="w-4 h-4 mr-1.5" />
            {{ cancelText }}
          </button>
          <button :class="confirmButtonClass" @click="handleConfirm">
            <component :is="getIconComponent(confirmIcon)" class="w-4 h-4 mr-1.5" />
            {{ confirmText }}
          </button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </Teleport>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@renderer/components/ui/dialog'
import { getIconComponent } from '@renderer/utils/iconComponents'

interface Props {
  open: boolean
  title?: string
  description?: string
  confirmText?: string
  cancelText?: string
  confirmIcon?: string
  cancelIcon?: string
  variant?: 'danger' | 'warning' | 'info' | 'success'
}

const props = withDefaults(defineProps<Props>(), {
  title: '确认操作',
  description: '您确定要执行此操作吗？',
  confirmText: '确认',
  cancelText: '取消',
  confirmIcon: 'Check',
  cancelIcon: 'X',
  variant: 'danger'
})

const emit = defineEmits<{
  'update:open': [value: boolean]
  confirm: []
  cancel: []
}>()

const confirmButtonClass = computed(() => {
  const baseClass =
    'inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 min-w-[80px] font-medium'

  switch (props.variant) {
    case 'danger':
      return `${baseClass} bg-red-500 hover:bg-red-600 text-white shadow-sm`
    case 'warning':
      return `${baseClass} bg-orange-500 hover:bg-orange-600 text-white shadow-sm`
    case 'info':
      return `${baseClass} bg-blue-500 hover:bg-blue-600 text-white shadow-sm`
    case 'success':
      return `${baseClass} bg-green-500 hover:bg-green-600 text-white shadow-sm`
    default:
      return `${baseClass} bg-red-500 hover:bg-red-600 text-white shadow-sm`
  }
})

const handleConfirm = () => {
  emit('confirm')
  emit('update:open', false)
}

const handleCancel = () => {
  emit('cancel')
  emit('update:open', false)
}
</script>

<style scoped>
/* 确保确认对话框有正确的层级和样式 */
:deep(.confirm-dialog-content) {
  background: rgb(var(--background));
  border: 1px solid rgb(var(--border));
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 确保对话框覆盖层有正确的 z-index */
:deep([data-radix-dialog-overlay]) {
  z-index: 9998 !important;
}

:deep([data-radix-dialog-content]) {
  z-index: 9999 !important;
}

/* 确保按钮有正确的焦点样式 */
:deep(button:focus-visible) {
  outline: 2px solid rgb(var(--primary));
  outline-offset: 2px;
}
</style>
