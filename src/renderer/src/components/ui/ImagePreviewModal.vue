<template>
  <Teleport to="body">
    <Transition
      enter-active-class="transition-all duration-200 ease-out"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition-all duration-150 ease-in"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="isOpen"
        class="fixed inset-0 z-[9999] flex items-center justify-center"
        style="-webkit-app-region: no-drag"
        @click="close"
        @keydown.escape="close"
        tabindex="0"
      >
        <!-- 背景蒙层 -->
        <div class="absolute inset-0 bg-black/80"></div>

        <!-- 图片容器 -->
        <div
          class="relative z-10 max-w-[95vw] max-h-[95vh] flex items-center justify-center"
          @click.stop
        >
          <!-- 关闭按钮 -->
          <button
            class="absolute -top-12 right-0 p-2 text-white/80 hover:text-white transition-colors rounded-lg hover:bg-white/10"
            @click="close"
            title="关闭 (ESC)"
          >
            <XIcon class="w-6 h-6" />
          </button>

          <!-- 图片 -->
          <img
            :src="imageSrc"
            :alt="alt"
            class="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
            @click.stop
            @load="onImageLoad"
            @error="onImageError"
          />

          <!-- 加载状态 -->
          <div
            v-if="isLoading"
            class="absolute inset-0 flex items-center justify-center bg-black/20 rounded-lg"
          >
            <LoaderIcon class="w-8 h-8 text-white animate-spin" />
          </div>

          <!-- 错误状态 -->
          <div
            v-if="hasError"
            class="absolute inset-0 flex flex-col items-center justify-center bg-black/20 rounded-lg text-white"
          >
            <TriangleAlertIcon class="w-8 h-8 mb-2" />
            <span class="text-sm">图片加载失败</span>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { XIcon, LoaderIcon, TriangleAlertIcon } from 'lucide-vue-next'

interface Props {
  isOpen: boolean
  imageSrc: string
  alt?: string
}

interface Emits {
  (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  alt: '图片预览'
})

const emit = defineEmits<Emits>()

const isLoading = ref(false)
const hasError = ref(false)

const close = () => {
  emit('close')
}

const onImageLoad = () => {
  isLoading.value = false
  hasError.value = false
}

const onImageError = () => {
  isLoading.value = false
  hasError.value = true
}

// 监听图片源变化，重置状态
watch(() => props.imageSrc, () => {
  if (props.imageSrc) {
    isLoading.value = true
    hasError.value = false
  }
})

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.isOpen) {
    close()
  }
}

// 阻止背景滚动
const preventScroll = (event: Event) => {
  if (props.isOpen) {
    event.preventDefault()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
  document.addEventListener('wheel', preventScroll, { passive: false })
  document.addEventListener('touchmove', preventScroll, { passive: false })
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  document.removeEventListener('wheel', preventScroll)
  document.removeEventListener('touchmove', preventScroll)
})

// 监听modal打开状态，管理body滚动
watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    document.body.style.overflow = 'hidden'
  } else {
    document.body.style.overflow = ''
  }
})
</script>

<style scoped>
/* 确保modal在最顶层 */
.fixed {
  position: fixed !important;
}
</style>
