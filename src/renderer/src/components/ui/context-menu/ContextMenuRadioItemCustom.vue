<script setup lang="ts">
import { cn } from '@renderer/lib/utils'
import {
  ContextMenuRadioItem,
  type ContextMenuRadioItemEmits,
  type ContextMenuRadioItemProps,
  useForwardPropsEmits
} from 'radix-vue'
import { computed, type HTMLAttributes } from 'vue'

const props = defineProps<ContextMenuRadioItemProps & { class?: HTMLAttributes['class'] }>()
const emits = defineEmits<ContextMenuRadioItemEmits>()

const delegatedProps = computed(() => {
  const { class: _, ...delegated } = props

  return delegated
})

const forwarded = useForwardPropsEmits(delegatedProps, emits)
</script>

<template>
  <ContextMenuRadioItem
    v-bind="forwarded"
    :class="
      cn(
        'relative flex cursor-default select-none items-center justify-between rounded-sm py-1.5 pl-2 pr-2 text-sm outline-hidden focus:bg-base-300 focus:text-accent-foreground data-disabled:pointer-events-none data-disabled:opacity-50 w-full',
        props.class
      )
    "
  >
    <slot />
  </ContextMenuRadioItem>
</template>
