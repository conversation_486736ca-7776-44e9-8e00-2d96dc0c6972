<template>
  <div class="kanban-loading h-full p-4">
    <div class="h-full grid grid-cols-2 gap-4">
      <!-- Todo Column Skeleton -->
      <div class="kanban-column-skeleton">
        <!-- Column Header Skeleton -->
        <div class="kanban-column-header-skeleton mb-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-4 h-4 bg-base-300 rounded-full animate-pulse mr-2"></div>
              <div class="w-12 h-4 bg-base-300 rounded animate-pulse"></div>
            </div>
            <div class="w-6 h-4 bg-base-300 rounded-full animate-pulse"></div>
          </div>
        </div>

        <!-- Cards Skeleton -->
        <div class="space-y-3">
          <div
            v-for="i in 3"
            :key="`todo-${i}`"
            class="kanban-card-skeleton"
            :style="{ animationDelay: `${i * 0.1}s` }"
          >
            <!-- Priority indicator -->
            <div class="w-3 h-3 bg-base-300 rounded-full animate-pulse mb-2"></div>

            <!-- Title -->
            <div class="space-y-2 mb-3">
              <div class="w-full h-4 bg-base-300 rounded animate-pulse"></div>
              <div class="w-3/4 h-4 bg-base-300 rounded animate-pulse"></div>
            </div>

            <!-- Notes -->
            <div class="space-y-1 mb-3">
              <div class="w-full h-3 bg-base-200 rounded animate-pulse"></div>
              <div class="w-2/3 h-3 bg-base-200 rounded animate-pulse"></div>
            </div>

            <!-- Due date and tags -->
            <div class="flex justify-between items-center">
              <div class="w-16 h-3 bg-base-200 rounded animate-pulse"></div>
              <div class="w-8 h-3 bg-base-200 rounded animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Completed Column Skeleton -->
      <div class="kanban-column-skeleton">
        <!-- Column Header Skeleton -->
        <div class="kanban-column-header-skeleton mb-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-4 h-4 bg-base-300 rounded-full animate-pulse mr-2"></div>
              <div class="w-16 h-4 bg-base-300 rounded animate-pulse"></div>
            </div>
            <div class="w-6 h-4 bg-base-300 rounded-full animate-pulse"></div>
          </div>
        </div>

        <!-- Cards Skeleton -->
        <div class="space-y-3">
          <div
            v-for="i in 2"
            :key="`completed-${i}`"
            class="kanban-card-skeleton opacity-60"
            :style="{ animationDelay: `${(i + 3) * 0.1}s` }"
          >
            <!-- Priority indicator -->
            <div class="w-3 h-3 bg-base-300 rounded-full animate-pulse mb-2"></div>

            <!-- Title -->
            <div class="space-y-2 mb-3">
              <div class="w-full h-4 bg-base-300 rounded animate-pulse"></div>
              <div class="w-1/2 h-4 bg-base-300 rounded animate-pulse"></div>
            </div>

            <!-- Due date and tags -->
            <div class="flex justify-between items-center">
              <div class="w-12 h-3 bg-base-200 rounded animate-pulse"></div>
              <div class="w-6 h-3 bg-base-200 rounded animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading indicator overlay -->
    <div class="absolute inset-0 flex items-center justify-center pointer-events-none">
      <div class="bg-base-100/90 backdrop-blur-sm rounded-lg p-6 shadow-lg border border-base-200">
        <div class="flex flex-col items-center space-y-4">
          <!-- Spinner -->
          <div class="relative">
            <div
              class="w-8 h-8 border-3 border-base-300 border-t-accent rounded-full animate-spin"
            ></div>
            <div
              class="absolute inset-0 w-8 h-8 border-3 border-accent/20 rounded-full animate-ping"
            ></div>
          </div>

          <!-- Loading text -->
          <div class="text-center">
            <span class="text-sm text-base-content/80 font-medium">{{ loadingText }}</span>
            <div class="flex justify-center mt-2 space-x-1">
              <div
                v-for="i in 3"
                :key="i"
                class="w-1.5 h-1.5 bg-accent/60 rounded-full animate-bounce"
                :style="{ animationDelay: `${(i - 1) * 0.15}s` }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
withDefaults(
  defineProps<{
    loadingText?: string
  }>(),
  {
    loadingText: '加载看板数据...'
  }
)
</script>

<style scoped>
.kanban-loading {
  position: relative;
}

.kanban-column-skeleton {
  background: var(--base-100);
  border: 1px solid var(--base-200);
  border-radius: 8px;
  padding: 16px;
  height: fit-content;
}

.kanban-card-skeleton {
  background: var(--base-100);
  border: 1px solid var(--base-200);
  border-radius: 6px;
  padding: 12px;
  animation: skeleton-fade 1.5s ease-in-out infinite;
}

@keyframes skeleton-fade {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Staggered animation for cards */
.kanban-card-skeleton:nth-child(1) {
  animation-delay: 0s;
}
.kanban-card-skeleton:nth-child(2) {
  animation-delay: 0.1s;
}
.kanban-card-skeleton:nth-child(3) {
  animation-delay: 0.2s;
}
.kanban-card-skeleton:nth-child(4) {
  animation-delay: 0.3s;
}

/* Pulse animation for skeleton elements */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
