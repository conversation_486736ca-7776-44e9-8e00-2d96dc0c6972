<template>
  <Teleport to="body">
    <Transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0 scale-95"
      enter-to-class="opacity-100 scale-100"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100 scale-100"
      leave-to-class="opacity-0 scale-95"
    >
      <div
        v-if="isOpen"
        class="fixed inset-0 z-[9999] flex items-center justify-center p-4"
        style="-webkit-app-region: no-drag"
        @click="handleClose"
        @keydown.escape="handleClose"
        tabindex="0"
      >
        <!-- 背景蒙层 -->
        <div class="absolute inset-0 bg-base-900/60 backdrop-blur-sm"></div>

        <!-- 模态框内容 -->
        <div
          class="relative z-10 w-[85%] max-w-[1100px] bg-base-900 rounded-xl shadow-2xl overflow-hidden max-h-[80vh]"
          @click.stop
        >
          <!-- 关闭按钮 -->
          <button
            class="absolute top-4 right-4 z-20 p-2 text-base-400 hover:text-base-300 transition-colors rounded-lg hover:bg-base-700"
            @click="handleClose"
            title="关闭"
          >
            <XIcon class="w-5 h-5" />
          </button>

          <!-- 主要内容区域 -->
          <div class="flex h-full">
            <!-- 左侧媒体区域 -->
            <div class="w-1/2 relative bg-base-900 flex items-center justify-center p-4">
              <div class="w-full aspect-square relative">
                <!-- 视频文件 -->
                <video
                  v-if="isVideoFile && config?.gifUrl && !hasMediaError"
                  :src="config.gifUrl"
                  class="w-full h-full object-cover rounded-lg shadow-lg cursor-pointer"
                  :class="{ 'cursor-pointer': config.gifLink }"
                  autoplay
                  loop
                  muted
                  playsinline
                  @click="handleMediaClick"
                  @loadeddata="onMediaLoad"
                  @error="onMediaError"
                />
                
                <!-- GIF/图片文件 -->
                <img
                  v-else-if="!isVideoFile && config?.gifUrl && !hasMediaError"
                  :src="config.gifUrl"
                  alt="功能介绍"
                  class="w-full h-full object-cover rounded-lg shadow-lg cursor-pointer"
                  :class="{ 'cursor-pointer': config.gifLink }"
                  @click="handleMediaClick"
                  @load="onMediaLoad"
                  @error="onMediaError"
                />
                
                <!-- 占位符 -->
                <div
                  v-if="!config?.gifUrl || hasMediaError"
                  class="w-full h-full bg-black rounded-lg shadow-lg flex items-center justify-center text-white"
                >
                  <div class="text-center">
                    <div class="text-4xl font-bold mb-2 tracking-tight" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;">Simple</div>
                    <div class="text-sm opacity-60">[ Demo Preview ]</div>
                  </div>
                </div>

                <!-- 加载状态 -->
                <div
                  v-if="isMediaLoading"
                  class="absolute inset-0 flex items-center justify-center bg-black/80 rounded-lg"
                >
                  <LoaderIcon class="w-8 h-8 text-white animate-spin" />
                </div>

                <!-- 错误状态 -->
                <div
                  v-if="hasMediaError"
                  class="absolute inset-0 flex flex-col items-center justify-center bg-black text-white rounded-lg"
                >
                  <TriangleAlertIcon class="w-8 h-8 mb-2" />
                  <span class="text-sm">加载失败</span>
                </div>

                <!-- 点击提示 -->
                <div
                  v-if="config?.gifLink && !isMediaLoading && !hasMediaError"
                  class="absolute bottom-2 right-2 bg-black/60 text-white text-xs px-3 py-1.5 rounded-full opacity-0 hover:opacity-100 transition-opacity backdrop-blur-sm"
                >
                  点击了解更多
                </div>
              </div>
            </div>

            <!-- 右侧内容区域 -->
            <div class="w-1/2 flex flex-col justify-center p-4">
              <div class="space-y-6">
                <!-- 动态内容 -->
                <div>
                  <component 
                    :is="contentComponent" 
                    v-if="contentComponent"
                    class="prose prose-gray dark:prose-invert max-w-none prose-sm"
                  />
                  <div v-else class="text-center text-base-500">
                    <h3 class="text-lg font-medium mb-2">功能介绍</h3>
                    <p>暂无内容</p>
                  </div>
                </div>

                <!-- 按钮区域 -->
                <div>
                  <button
                    class="w-full px-6 py-3 bg-orange-500 hover:bg-orange-600 text-white font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2"
                    @click="handleAcknowledge"
                  >
                    {{ config?.buttonText || '继续' }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { XIcon, LoaderIcon, TriangleAlertIcon, ImageIcon } from 'lucide-vue-next'

export interface FeatureIntroConfig {
  id: string
  gifUrl?: string
  gifLink?: string
  content?: () => any // 支持 JSX 渲染函数
  buttonText?: string
  onAcknowledge?: () => void
  onClose?: () => void
}

interface Props {
  isOpen: boolean
  config?: FeatureIntroConfig
}

interface Emits {
  (e: 'close'): void
  (e: 'acknowledge', id: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 媒体文件加载状态
const isMediaLoading = ref(false)
const hasMediaError = ref(false)

// 判断是否为视频文件
const isVideoFile = computed(() => {
  if (!props.config?.gifUrl) return false
  const url = props.config.gifUrl.toLowerCase()
  return url.endsWith('.mp4') || url.endsWith('.webm') || url.endsWith('.mov') || url.endsWith('.avi')
})

// 动态内容组件
const contentComponent = computed(() => {
  if (props.config?.content && typeof props.config.content === 'function') {
    return props.config.content
  }
  return null
})

// 处理关闭
const handleClose = () => {
  props.config?.onClose?.()
  emit('close')
}

// 处理确认
const handleAcknowledge = () => {
  if (props.config?.id) {
    emit('acknowledge', props.config.id)
  }
  props.config?.onAcknowledge?.()
  emit('close')
}

// 处理媒体文件点击
const handleMediaClick = () => {
  if (props.config?.gifLink) {
    window.open(props.config.gifLink, '_blank')
  }
}

// 媒体文件加载事件
const onMediaLoad = () => {
  isMediaLoading.value = false
  hasMediaError.value = false
}

const onMediaError = () => {
  isMediaLoading.value = false
  hasMediaError.value = true
}

// 监听媒体文件 URL 变化
watch(() => props.config?.gifUrl, (newUrl) => {
  if (newUrl) {
    isMediaLoading.value = true
    hasMediaError.value = false
  }
}, { immediate: true })

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.isOpen) {
    handleClose()
  }
}

// 阻止背景滚动
const preventScroll = (event: Event) => {
  if (props.isOpen) {
    event.preventDefault()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
  document.addEventListener('wheel', preventScroll, { passive: false })
  document.addEventListener('touchmove', preventScroll, { passive: false })
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  document.removeEventListener('wheel', preventScroll)
  document.removeEventListener('touchmove', preventScroll)
})

// 监听 modal 打开状态，管理 body 滚动
watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    document.body.style.overflow = 'hidden'
  } else {
    document.body.style.overflow = ''
  }
})
</script>

<style scoped>
/* 确保 modal 在最顶层 */
.fixed {
  position: fixed !important;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .prose {
    font-size: 0.875rem;
  }
}

@media (max-width: 640px) {
  .prose h1, .prose h2, .prose h3 {
    font-size: 1.25rem;
  }
  
  .prose p, .prose li {
    font-size: 0.875rem;
  }
}
</style>