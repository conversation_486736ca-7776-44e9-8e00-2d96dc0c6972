<template>
  <label
    :class="[
      'inline-flex border border-base-400 rounded-md items-center cursor-pointer',
      disabled && 'cursor-not-allowed opacity-60',
      labelClass
    ]"
  >
    <input
      :id="id"
      ref="inputRef"
      type="checkbox"
      :checked="modelValue"
      :disabled="disabled"
      :required="required"
      :name="name"
      :value="value"
      :class="[
        'checkbox-input',
        size === 'sm' && 'checkbox-sm',
        size === 'lg' && 'checkbox-lg',
        variant === 'primary' && 'checkbox-primary',
        variant === 'secondary' && 'checkbox-secondary',
        variant === 'accent' && 'checkbox-accent',
        variant === 'success' && 'checkbox-success',
        variant === 'warning' && 'checkbox-warning',
        variant === 'error' && 'checkbox-error',
        inputClass
      ]"
      @change="handleChange"
      @focus="$emit('focus', $event)"
      @blur="$emit('blur', $event)"
    />
    <span v-if="$slots.default || label" :class="['ml-2 select-none', textSize]">
      <slot>{{ label }}</slot>
    </span>
  </label>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Props {
  modelValue?: boolean
  id?: string
  name?: string
  value?: string | number
  label?: string
  disabled?: boolean
  required?: boolean
  size?: 'sm' | 'md' | 'lg'
  variant?: 'default' | 'primary' | 'secondary' | 'accent' | 'success' | 'warning' | 'error'
  labelClass?: string
  inputClass?: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'change', value: boolean, event: Event): void
  (e: 'focus', event: FocusEvent): void
  (e: 'blur', event: FocusEvent): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  size: 'md',
  variant: 'default',
  disabled: false,
  required: false
})

const emit = defineEmits<Emits>()

const inputRef = ref<HTMLInputElement>()

const textSize = computed(() => {
  switch (props.size) {
    case 'sm':
      return 'text-sm'
    case 'lg':
      return 'text-lg'
    default:
      return 'text-base'
  }
})

const handleChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const checked = target.checked
  emit('update:modelValue', checked)
  emit('change', checked, event)
}

// 暴露方法供父组件调用
defineExpose({
  focus: () => inputRef.value?.focus(),
  blur: () => inputRef.value?.blur()
})
</script>

<style scoped>
/* 基础 checkbox 样式 */
.checkbox-input {
  appearance: none;
  -webkit-appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  border: 1px solid hsl(var(--bc) / 0.2);
  border-radius: 0.375rem;
  background-color: hsl(var(--b1));
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.checkbox-input:hover:not(:disabled) {
  border-color: hsl(var(--bc) / 0.4);
}

.checkbox-input:focus {
  outline: none;
  border-color: hsl(var(--p));
  box-shadow: 0 0 0 2px hsl(var(--p) / 0.2);
}

.checkbox-input:checked {
  background-color: hsl(var(--p));
  border-color: hsl(var(--p));
}

.checkbox-input:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: hsl(var(--pc));
  font-size: 0.875rem;
  font-weight: 600;
  line-height: 1;
}

.checkbox-input:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* 尺寸变体 */
.checkbox-sm {
  width: 1rem;
  height: 1rem;
  border-radius: 0.25rem;
}

.checkbox-sm:checked::after {
  font-size: 0.75rem;
}

.checkbox-lg {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 0.5rem;
}

.checkbox-lg:checked::after {
  font-size: 1rem;
}

/* 颜色变体 */
.checkbox-primary:checked {
  background-color: hsl(var(--p));
  border-color: hsl(var(--p));
}

.checkbox-primary:focus {
  border-color: hsl(var(--p));
  box-shadow: 0 0 0 2px hsl(var(--p) / 0.2);
}

.checkbox-secondary:checked {
  background-color: hsl(var(--s));
  border-color: hsl(var(--s));
}

.checkbox-secondary:focus {
  border-color: hsl(var(--s));
  box-shadow: 0 0 0 2px hsl(var(--s) / 0.2);
}

.checkbox-accent:checked {
  background-color: hsl(var(--a));
  border-color: hsl(var(--a));
}

.checkbox-accent:focus {
  border-color: hsl(var(--a));
  box-shadow: 0 0 0 2px hsl(var(--a) / 0.2);
}

.checkbox-success:checked {
  background-color: hsl(var(--su));
  border-color: hsl(var(--su));
}

.checkbox-success:focus {
  border-color: hsl(var(--su));
  box-shadow: 0 0 0 2px hsl(var(--su) / 0.2);
}

.checkbox-warning:checked {
  background-color: hsl(var(--wa));
  border-color: hsl(var(--wa));
}

.checkbox-warning:focus {
  border-color: hsl(var(--wa));
  box-shadow: 0 0 0 2px hsl(var(--wa) / 0.2);
}

.checkbox-error:checked {
  background-color: hsl(var(--er));
  border-color: hsl(var(--er));
}

.checkbox-error:focus {
  border-color: hsl(var(--er));
  box-shadow: 0 0 0 2px hsl(var(--er) / 0.2);
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .checkbox-input {
    border-color: hsl(var(--bc) / 0.3);
  }

  .checkbox-input:hover:not(:disabled) {
    border-color: hsl(var(--bc) / 0.5);
  }
}
</style>
