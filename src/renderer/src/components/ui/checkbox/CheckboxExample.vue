<template>
  <div class="space-y-6 p-6">
    <h2 class="text-2xl font-bold text-base-content">Checkbox 组件示例</h2>

    <!-- 基础用法 -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold text-base-content">基础用法</h3>
      <div class="space-y-2">
        <SimpleCheckbox v-model="basic" label="基础 checkbox" />
        <SimpleCheckbox v-model="withSlot">
          <span class="font-medium">使用 slot 的 checkbox</span>
        </SimpleCheckbox>
        <SimpleCheckbox v-model="disabled" label="禁用状态" disabled />
      </div>
      <div class="text-sm text-base-content/60">
        状态: basic={{ basic }}, withSlot={{ withSlot }}, disabled={{ disabled }}
      </div>
    </div>

    <!-- 尺寸变体 -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold text-base-content">尺寸变体</h3>
      <div class="space-y-2">
        <SimpleCheckbox v-model="sizeSmall" size="sm" label="小尺寸 (sm)" />
        <SimpleCheckbox v-model="sizeMedium" size="md" label="中等尺寸 (md)" />
        <SimpleCheckbox v-model="sizeLarge" size="lg" label="大尺寸 (lg)" />
      </div>
    </div>

    <!-- 颜色变体 -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold text-base-content">颜色变体</h3>
      <div class="space-y-2">
        <SimpleCheckbox v-model="colorDefault" variant="default" label="默认颜色" />
        <SimpleCheckbox v-model="colorPrimary" variant="primary" label="主要颜色" />
        <SimpleCheckbox v-model="colorSecondary" variant="secondary" label="次要颜色" />
        <SimpleCheckbox v-model="colorAccent" variant="accent" label="强调颜色" />
        <SimpleCheckbox v-model="colorSuccess" variant="success" label="成功颜色" />
        <SimpleCheckbox v-model="colorWarning" variant="warning" label="警告颜色" />
        <SimpleCheckbox v-model="colorError" variant="error" label="错误颜色" />
      </div>
    </div>

    <!-- 事件处理 -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold text-base-content">事件处理</h3>
      <SimpleCheckbox
        v-model="eventExample"
        label="带事件处理的 checkbox"
        @change="handleChange"
        @focus="handleFocus"
        @blur="handleBlur"
      />
      <div class="text-sm text-base-content/60">最后一个事件: {{ lastEvent }}</div>
    </div>

    <!-- 表单集成 -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold text-base-content">表单集成</h3>
      <form @submit.prevent="handleSubmit" class="space-y-2">
        <SimpleCheckbox
          v-model="formData.newsletter"
          name="newsletter"
          label="订阅邮件通知"
          required
        />
        <SimpleCheckbox v-model="formData.terms" name="terms" label="同意服务条款" required />
        <SimpleCheckbox
          v-model="formData.marketing"
          name="marketing"
          label="接收营销信息（可选）"
        />
        <button
          type="submit"
          class="btn btn-primary"
          :disabled="!formData.newsletter || !formData.terms"
        >
          提交表单
        </button>
      </form>
      <div class="text-sm text-base-content/60">
        表单数据: {{ JSON.stringify(formData, null, 2) }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { SimpleCheckbox } from './index'

// 基础用法
const basic = ref(false)
const withSlot = ref(true)
const disabled = ref(true)

// 尺寸变体
const sizeSmall = ref(false)
const sizeMedium = ref(true)
const sizeLarge = ref(false)

// 颜色变体
const colorDefault = ref(false)
const colorPrimary = ref(true)
const colorSecondary = ref(false)
const colorAccent = ref(true)
const colorSuccess = ref(false)
const colorWarning = ref(true)
const colorError = ref(false)

// 事件处理
const eventExample = ref(false)
const lastEvent = ref('')

// 表单集成
const formData = reactive({
  newsletter: false,
  terms: false,
  marketing: false
})

const handleChange = (value: boolean, event: Event) => {
  lastEvent.value = `change: ${value}`
}

const handleFocus = (event: FocusEvent) => {
  lastEvent.value = 'focus'
}

const handleBlur = (event: FocusEvent) => {
  lastEvent.value = 'blur'
}

const handleSubmit = () => {
  alert('表单提交成功！数据：' + JSON.stringify(formData, null, 2))
}
</script>
