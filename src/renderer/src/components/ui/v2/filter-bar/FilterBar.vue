<template>
  <div class="border-b border-base-100 px-5 py-2 text-sm">
    <div class="flex items-center justify-between">
      <!-- Filter Button -->
      <div
        v-if="showFilter"
        class="flex items-center space-x-2"
        @click="handleFilterClick"
      >
        <div
          class="flex text-base-content items-center justify-center w-6 h-6 hover:bg-base-100 rounded-md transition-colors"
        >
          <ListFilter class="w-4 h-4 " />
        </div>
        <span class="select-none">过滤</span>
      </div>

      <!-- Spacer when filter is hidden -->
      <div v-else></div>

      <!-- Display Button -->
      <div class="flex items-center space-x-2 select-none cursor-default" @click="handleDisplayClick">
        <div
          class="flex text-base-content items-center justify-center w-6 h-6 hover:bg-base-100 rounded-md transition-colors"
        >
          <LayoutGrid class="w-3.5 h-3.5 " />
        </div>
        <span>显示</span>
      </div>
    </div>

    <!-- Filter Dropdown -->
    <DropdownMenu v-model:open="showFilterDropdown">
      <DropdownMenuTrigger as-child>
        <div ref="filterTrigger" class="hidden"></div>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        class="bg-base-background border border-base-200 shadow-md rounded-md w-56 p-1.5"
        align="start"
        :no-close-animation="true"
      >
        <DropdownMenuLabel class="select-none text-base-content font-medium px-1.5 py-1"
          >筛选</DropdownMenuLabel
        >

        <!-- Status Sub-menu -->
        <DropdownMenuSub>
          <DropdownMenuSubTrigger
            class="hover:bg-base-100 focus:bg-base-100 rounded-md p-1.5 mb-0.5 h-8 flex items-center justify-between"
          >
            <div class="flex items-center">
              <Circle class="w-3.5 h-3.5 text-base-content-secondary mr-3" />
              <span class="text-base-content">状态</span>
            </div>
          </DropdownMenuSubTrigger>
          <DropdownMenuSubContent
            class="bg-base-background border border-base-200 shadow-md rounded-md p-1.5"
          >
            <DropdownMenuRadioGroup v-model:value="selectedStatus">
              <DropdownMenuRadioItem
                value="all"
                class="hover:bg-base-100 focus:bg-base-100 rounded-md p-1.5 mb-0.5 h-8 flex items-center"
                @click="selectedStatus = 'all'"
              >
                <span class="text-base-content">全部</span>
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem
                value="active"
                class="hover:bg-base-100 focus:bg-base-100 rounded-md p-1.5 mb-0.5 h-8 flex items-center"
                @click="selectedStatus = 'active'"
              >
                <span class="text-base-content">未完成</span>
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem
                value="completed"
                class="hover:bg-base-100 focus:bg-base-100 rounded-md p-1.5 mb-0.5 h-8 flex items-center"
                @click="selectedStatus = 'completed'"
              >
                <span class="text-base-content">已完成</span>
              </DropdownMenuRadioItem>
            </DropdownMenuRadioGroup>
          </DropdownMenuSubContent>
        </DropdownMenuSub>

        <!-- Priority Sub-menu -->
        <DropdownMenuSub>
          <DropdownMenuSubTrigger
            class="dropdown-menu-item-override rounded-md p-1.5 mb-0.5 h-8 flex items-center justify-between"
          >
            <div class="flex items-center">
              <AlertTriangle class="w-3.5 h-3.5 text-base-content-secondary mr-3" />
              <span class="text-base-content">优先级</span>
            </div>
          </DropdownMenuSubTrigger>
          <DropdownMenuSubContent
            class="bg-base-background border border-base-300 shadow-md rounded-md p-1.5"
          >
            <DropdownMenuRadioGroup v-model:value="selectedPriority">
              <DropdownMenuRadioItem
                value="all"
                class="dropdown-menu-item-override rounded-md p-1.5 mb-0.5 h-8 flex items-center"
                @click="selectedPriority = 'all'"
              >
                <span class="text-base-content">全部</span>
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem
                value="high"
                class="dropdown-menu-item-override rounded-md p-1.5 mb-0.5 h-8 flex items-center"
                @click="selectedPriority = 'high'"
              >
                <span class="text-base-content">高优先级</span>
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem
                value="medium"
                class="dropdown-menu-item-override rounded-md p-1.5 mb-0.5 h-8 flex items-center"
                @click="selectedPriority = 'medium'"
              >
                <span class="text-base-content">中优先级</span>
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem
                value="low"
                class="dropdown-menu-item-override rounded-md p-1.5 mb-0.5 h-8 flex items-center"
                @click="selectedPriority = 'low'"
              >
                <span class="text-base-content">低优先级</span>
              </DropdownMenuRadioItem>
            </DropdownMenuRadioGroup>
          </DropdownMenuSubContent>
        </DropdownMenuSub>

        <!-- Due Date Sub-menu -->
        <DropdownMenuSub>
          <DropdownMenuSubTrigger
            class="dropdown-menu-item-override rounded-md p-1.5 mb-0.5 h-8 flex items-center justify-between"
          >
            <div class="flex items-center">
              <Calendar class="w-3.5 h-3.5 text-base-content-secondary mr-3" />
              <span class="text-base-content">截止日期</span>
            </div>
          </DropdownMenuSubTrigger>
          <DropdownMenuSubContent
            class="bg-base-background border border-base-200 shadow-md rounded-md p-1.5"
          >
            <DropdownMenuRadioGroup v-model:value="selectedDueDate">
              <DropdownMenuRadioItem
                value="all"
                class="dropdown-menu-item-override rounded-md p-1.5 mb-0.5 h-8 flex items-center"
                @click="selectedDueDate = 'all'"
              >
                <span class="text-base-content">全部</span>
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem
                value="overdue"
                class="dropdown-menu-item-override rounded-md p-1.5 mb-0.5 h-8 flex items-center"
                @click="selectedDueDate = 'overdue'"
              >
                <span class="text-base-content">已过期</span>
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem
                value="today"
                class="dropdown-menu-item-override rounded-md p-1.5 mb-0.5 h-8 flex items-center"
                @click="selectedDueDate = 'today'"
              >
                <span class="text-base-content">今天</span>
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem
                value="tomorrow"
                class="dropdown-menu-item-override rounded-md p-1.5 mb-0.5 h-8 flex items-center"
                @click="selectedDueDate = 'tomorrow'"
              >
                <span class="text-base-content">明天</span>
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem
                value="this-week"
                class="dropdown-menu-item-override rounded-md p-1.5 mb-0.5 h-8 flex items-center"
                @click="selectedDueDate = 'this-week'"
              >
                <span class="text-base-content">本周</span>
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem
                value="no-due-date"
                class="dropdown-menu-item-override rounded-md p-1.5 mb-0.5 h-8 flex items-center"
                @click="selectedDueDate = 'no-due-date'"
              >
                <span class="text-base-content">无截止日期</span>
              </DropdownMenuRadioItem>
            </DropdownMenuRadioGroup>
          </DropdownMenuSubContent>
        </DropdownMenuSub>

        <div class="border-t border-base-200 my-1.5"></div>

        <!-- Clear Filters -->
        <DropdownMenuItem
          class="dropdown-menu-item-override rounded-md p-1.5 h-8 flex items-center"
          @click="clearFilters"
        >
          <span class="text-base-content">清除筛选</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>

    <!-- Display Dropdown -->
    <DropdownMenu v-model:open="showDisplayDropdown">
      <DropdownMenuTrigger as-child>
        <div ref="displayTrigger" class="hidden"></div>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        class="bg-base-background border border-base-200 shadow-md rounded-md w-48 p-1.5"
        align="end"
        :no-close-animation="true"
      >
        <DropdownMenuLabel class="select-none text-base-content font-medium px-1.5 py-1"
          >显示</DropdownMenuLabel
        >

        <!-- View Mode Sub-menu -->
        <DropdownMenuSub>
          <DropdownMenuSubTrigger
            class="dropdown-menu-item-override rounded-md p-1.5 mb-0.5 h-8 flex items-center justify-between"
          >
            <div class="flex items-center">
              <LayoutGrid class="w-3.5 h-3.5 text-base-content-secondary mr-3" />
              <span class="text-base-content">视图模式</span>
            </div>
          </DropdownMenuSubTrigger>
          <DropdownMenuSubContent
            class="bg-base-background border border-base-200 shadow-md rounded-md p-1.5"
          >
            <DropdownMenuRadioGroup v-model:value="selectedViewMode">
              <DropdownMenuRadioItem
                value="list"
                class="dropdown-menu-item-override rounded-md p-1.5 mb-0.5 h-8 flex items-center"
                @click="selectedViewMode = 'list'"
              >
                <span class="text-base-content">列表</span>
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem
                value="kanban"
                class="dropdown-menu-item-override rounded-md p-1.5 mb-0.5 h-8 flex items-center"
                @click="selectedViewMode = 'kanban'"
              >
                <span class="text-base-content">看板</span>
              </DropdownMenuRadioItem>
            </DropdownMenuRadioGroup>
          </DropdownMenuSubContent>
        </DropdownMenuSub>

        <!-- Sort Sub-menu -->
        <DropdownMenuSub>
          <DropdownMenuSubTrigger
            class="dropdown-menu-item-override rounded-md p-1.5 mb-0.5 h-8 flex items-center justify-between"
          >
            <div class="flex items-center">
              <ArrowUpDown class="w-3.5 h-3.5 text-base-content-secondary mr-3" />
              <span class="text-base-content">排序</span>
            </div>
          </DropdownMenuSubTrigger>
          <DropdownMenuSubContent
            class="bg-base-background border border-base-200 shadow-md rounded-md p-1.5"
          >
            <DropdownMenuRadioGroup v-model:value="selectedSort">
              <DropdownMenuRadioItem
                value="dueDate"
                class="dropdown-menu-item-override rounded-md p-1.5 mb-0.5 h-8 flex items-center"
                @click="selectedSort = 'dueDate'"
              >
                <span class="text-base-content">按截止日期</span>
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem
                value="createdAt"
                class="dropdown-menu-item-override rounded-md p-1.5 mb-0.5 h-8 flex items-center"
                @click="selectedSort = 'createdAt'"
              >
                <span class="text-base-content">按创建时间</span>
              </DropdownMenuRadioItem>
            </DropdownMenuRadioGroup>
          </DropdownMenuSubContent>
        </DropdownMenuSub>

        <!-- View Options Sub-menu -->
        <DropdownMenuSub>
          <DropdownMenuSubTrigger
            class="!hover:bg-base-100 !focus:bg-base-100 !data-[state=open]:bg-base-100 rounded-md p-1.5 mb-0.5 h-8 flex items-center justify-between"
          >
            <div class="flex items-center">
              <Eye class="w-3.5 h-3.5 text-base-content-secondary mr-3" />
              <span class="text-base-content">视图选项</span>
            </div>
          </DropdownMenuSubTrigger>
          <DropdownMenuSubContent
            class="bg-base-background border border-base-300 shadow-md rounded-md p-1.5"
          >
            <DropdownMenuCheckboxItem
              v-model:checked="showCompletedTodos"
              class="hover:bg-base-100 focus:bg-base-100 rounded-md mb-0.5 h-8"
              @select="(e) => e.preventDefault()"
            >
              <span class="text-base-content">显示已完成项目</span>
            </DropdownMenuCheckboxItem>
            <DropdownMenuCheckboxItem
              v-model:checked="showSubTodos"
              class="hover:bg-base-100 focus:bg-base-100 rounded-md mb-0.5 h-8"
              @select="(e) => e.preventDefault()"
            >
              <span class="text-base-content">显示子任务</span>
            </DropdownMenuCheckboxItem>
            <DropdownMenuCheckboxItem
              v-model:checked="showDueDates"
              class="hover:bg-base-100 focus:bg-base-100 rounded-md mb-0.5 h-8"
              @select="(e) => e.preventDefault()"
            >
              <span class="text-base-content">显示截止日期</span>
            </DropdownMenuCheckboxItem>
            <DropdownMenuCheckboxItem
              v-model:checked="showPriority"
              class="hover:bg-base-100 focus:bg-base-100 rounded-md mb-0.5 h-8"
              @select="(e) => e.preventDefault()"
            >
              <span class="text-base-content">显示优先级</span>
            </DropdownMenuCheckboxItem>
          </DropdownMenuSubContent>
        </DropdownMenuSub>
      </DropdownMenuContent>
    </DropdownMenu>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import {
  ListFilter,
  LayoutGrid,
  Circle,
  AlertTriangle,
  Calendar,
  ArrowUpDown,
  Eye
} from 'lucide-vue-next'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuCheckboxItem,
  DropdownMenuItem,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuSubContent
} from '@renderer/components/ui/dropdown-menu'

interface FilterOptions {
  status: string
  priority: string
  dueDate: string
}

interface DisplayOptions {
  sort: string
  viewMode: string
  showCompletedTodos: boolean
  showSubTodos: boolean
  showDueDates: boolean
  showPriority: boolean
}

interface Props {
  filters?: FilterOptions
  display?: DisplayOptions
  showFilter?: boolean
}

interface Emits {
  (e: 'filter-change', filters: FilterOptions): void
  (e: 'display-change', display: DisplayOptions): void
  (e: 'filter-click'): void
  (e: 'display-click'): void
}

const props = withDefaults(defineProps<Props>(), {
  filters: () => ({
    status: 'all',
    priority: 'all',
    dueDate: 'all'
  }),
  display: () => ({
    sort: 'dueDate',
    viewMode: 'list',
    showCompletedTodos: true,
    showSubTodos: true,
    showDueDates: true,
    showPriority: true
  }),
  showFilter: true
})

const emit = defineEmits<Emits>()

// Dropdown states
const showFilterDropdown = ref(false)
const showDisplayDropdown = ref(false)

// Filter states
const selectedStatus = ref(props.filters.status)
const selectedPriority = ref(props.filters.priority)
const selectedDueDate = ref(props.filters.dueDate)

// Display states
const selectedSort = ref(props.display.sort)
const selectedViewMode = ref(props.display.viewMode)
const showCompletedTodos = ref(props.display.showCompletedTodos)
const showSubTodos = ref(props.display.showSubTodos)
const showDueDates = ref(props.display.showDueDates)
const showPriority = ref(props.display.showPriority)

// Handle filter button click
const handleFilterClick = () => {
  showFilterDropdown.value = true
  emit('filter-click')
}

// Handle display button click
const handleDisplayClick = () => {
  showDisplayDropdown.value = true
  emit('display-click')
}

// Clear all filters
const clearFilters = () => {
  selectedStatus.value = 'all'
  selectedPriority.value = 'all'
  selectedDueDate.value = 'all'
  showFilterDropdown.value = false
}

// Watch for props changes to sync internal state
watch(
  () => props.filters,
  (newFilters) => {
    selectedStatus.value = newFilters.status
    selectedPriority.value = newFilters.priority
    selectedDueDate.value = newFilters.dueDate
  },
  { deep: true }
)

watch(
  () => props.display,
  (newDisplay) => {
    selectedSort.value = newDisplay.sort
    selectedViewMode.value = newDisplay.viewMode
    showCompletedTodos.value = newDisplay.showCompletedTodos
    showSubTodos.value = newDisplay.showSubTodos
    showDueDates.value = newDisplay.showDueDates
    showPriority.value = newDisplay.showPriority
  },
  { deep: true }
)

// Watch for filter changes
watch([selectedStatus, selectedPriority, selectedDueDate], () => {
  emit('filter-change', {
    status: selectedStatus.value,
    priority: selectedPriority.value,
    dueDate: selectedDueDate.value
  })
})

// Watch for display changes
watch(
  [selectedSort, selectedViewMode, showCompletedTodos, showSubTodos, showDueDates, showPriority],
  () => {
    emit('display-change', {
      sort: selectedSort.value,
      viewMode: selectedViewMode.value,
      showCompletedTodos: showCompletedTodos.value,
      showSubTodos: showSubTodos.value,
      showDueDates: showDueDates.value,
      showPriority: showPriority.value
    })
  }
)
</script>

<style scoped>
/* Ensure dropdown menu doesn't affect layout */
:deep([data-radix-popper-content-wrapper]) {
  position: fixed !important;
  z-index: 9999 !important;
}
</style>
