<template>
  <div
    :class="[
      'relative flex shrink-0 overflow-hidden rounded-full bg-base-300',
      sizeClasses
    ]"
  >
    <img
      v-if="src"
      :src="src"
      :alt="alt"
      class="aspect-square h-full w-full object-cover"
    />
    <div
      v-else
      :class="[
        'flex h-full w-full items-center justify-center bg-base-300',
        textSizeClasses
      ]"
    >
      <span
        v-if="fallback"
        class="font-medium text-base-content-secondary"
      >
        {{ fallback }}
      </span>
      <span
        v-else-if="emoji"
        class="select-none"
      >
        {{ emoji }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  src?: string
  alt?: string
  fallback?: string
  emoji?: string
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md'
})

const sizeClasses = computed(() => {
  const sizes = {
    xs: 'w-4 h-4',
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-10 h-10',
    xl: 'w-12 h-12'
  }

  return sizes[props.size]
})

const textSizeClasses = computed(() => {
  const sizes = {
    xs: 'text-xs',
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
    xl: 'text-lg'
  }

  return sizes[props.size]
})
</script>
