<template>
  <div
    class="flex text-sm items-center space-x-2 rounded-md p-1 cursor-pointer transition-colors duration-200"
    :class="active ? 'bg-base-100' : 'hover:bg-base-100'"
  >
    <div
      :class="[
        avatarColor,
        'w-6 h-6 rounded flex items-center justify-center',
        { 'cursor-pointer': clickable }
      ]"
      @click="handleClick"
    >
      <span class="text-white text-xs font-bold">{{ avatarInitial }}</span>
    </div>
    <span
      :class="[
        'font-medium text-base-content select-none',
        textSize,
        { 'cursor-pointer': clickable }
      ]"
      @click="handleClick"
    >
      {{ displayEmail }}
    </span>
    <ChevronDown
      v-if="showDropdown"
      class="w-4 h-4 text-base-content-secondary"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ChevronDown } from 'lucide-vue-next'
import { useUserStore } from '@renderer/stores/user'
import { generateAvatarColor, getEmailInitial, truncateEmail } from '@renderer/utils/avatarUtils'

interface Props {
  appName?: string
  logo?: string
  logoColorClass?: string
  showDropdown?: boolean
  clickable?: boolean
  textSize?: string
  active?: boolean
}

interface Emits {
  (e: 'click'): void
}

const props = withDefaults(defineProps<Props>(), {
  appName: 'Chronnote',
  logo: 'CH',
  logoColorClass: 'bg-pink-500',
  showDropdown: true,
  clickable: false,
  textSize: 'text-[11px]',
  active: false
})

const emit = defineEmits<Emits>()

const userStore = useUserStore()

// 计算用户相关的显示内容
const displayEmail = computed(() => {
  const email = userStore.userInfo?.email
  return email ? truncateEmail(email) : props.appName
})

const avatarInitial = computed(() => {
  const email = userStore.userInfo?.email
  return email ? getEmailInitial(email) : props.logo
})

const avatarColor = computed(() => {
  const email = userStore.userInfo?.email
  return email ? generateAvatarColor(email) : props.logoColorClass
})

const handleClick = () => {
  if (props.clickable) {
    emit('click')
  }
}
</script>
