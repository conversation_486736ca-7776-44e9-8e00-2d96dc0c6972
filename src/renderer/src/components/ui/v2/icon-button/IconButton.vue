<template>
  <button
    :class="[
      'inline-flex items-center justify-center transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
      buttonClasses
    ]"
    :disabled="disabled"
    @click="$emit('click')"
  >
    <component :is="iconComponent" :class="iconClasses" />
    <span v-if="$slots.default" :class="textClasses">
      <slot />
    </span>
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { getIconComponent } from '../../../../utils/iconComponents'

interface Props {
  icon: string
  variant?: 'default' | 'ghost' | 'outline' | 'secondary'
  size?: 'xs' | 'sm' | 'md' | 'lg'
  disabled?: boolean
  iconClass?: string
}

interface Emits {
  (e: 'click'): void
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'ghost',
  size: 'md',
  disabled: false
})

defineEmits<Emits>()

const iconComponent = computed(() => {
  return getIconComponent(props.icon)
})

const buttonClasses = computed(() => {
  const variants = {
    default: 'bg-primary text-white hover:bg-primary/90',
    ghost: 'hover:bg-base-100 hover:text-base-content',
    outline: 'border border-base-300 bg-base-background hover:bg-base-100 hover:text-base-content',
    secondary: 'bg-base-100 text-base-content hover:bg-base-200'
  }

  const sizes = {
    xs: 'h-6 w-6 rounded-md',
    sm: 'h-8 w-8 rounded-md',
    md: 'h-10 w-10 rounded-md',
    lg: 'h-12 w-12 rounded-md'
  }

  return [variants[props.variant], sizes[props.size]]
})

const iconClasses = computed(() => {
  const sizes = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  }

  return [sizes[props.size], props.iconClass]
})

const textClasses = computed(() => {
  const sizes = {
    xs: 'text-xs ml-1',
    sm: 'text-sm ml-1.5',
    md: 'text-sm ml-2',
    lg: 'text-base ml-2'
  }

  return sizes[props.size]
})
</script>
