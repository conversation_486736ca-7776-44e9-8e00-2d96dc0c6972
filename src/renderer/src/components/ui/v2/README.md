# Home 组件

这是一个模块化的 Home 页面组件系统，已将原来的 NewHome.vue 组件拆分为多个可复用的子组件。

## 组件结构

```
Home/
├── Sidebar/                    # 侧边栏相关组件
│   ├── Sidebar.vue            # 主侧边栏组件
│   ├── SidebarHeader.vue      # 侧边栏头部
│   ├── SidebarNavigationItem.vue # 导航项组件
│   ├── SidebarSection.vue     # 导航节组件
│   └── SidebarFooter.vue      # 侧边栏底部
├── MainContent/               # 主内容区域组件
│   ├── MainContent.vue        # 主内容容器
│   ├── MainHeader.vue         # 主内容头部
│   ├── FilterBar.vue          # 过滤栏
│   ├── IssuesList.vue         # 问题列表
│   └── IssueItem.vue          # 问题项
└── index.ts                   # 组件导出索引
```

## 配置文件

### 导航配置 (`constants/navigation.ts`)

- `MAIN_NAVIGATION`: 主导航配置
- `WORKSPACE_NAVIGATION`: 工作空间导航配置
- `TEAMS_NAVIGATION`: 团队导航配置
- `TRY_NAVIGATION`: 尝试功能导航配置
- `APP_CONFIG`: 应用基本配置
- `FOOTER_CONFIG`: 底部配置

### 问题配置 (`constants/issues.ts`)

- `Issue`: 问题数据接口
- `IssueType`: 问题类型枚举
- `IssueStatus`: 问题状态枚举
- `ISSUE_TYPE_STYLES`: 问题类型样式映射
- `MAIN_HEADER_TABS`: 主头部标签页配置
- `SAMPLE_ISSUES`: 示例问题数据

## 使用方法

### 基本使用

```vue
<template>
  <div class="flex h-screen bg-gray-50">
    <Sidebar @navigate="handleNavigation" />
    <MainContent
      :issues="issues"
      @tab-change="handleTabChange"
      @issue-click="handleIssueClick"
      @add-issue="handleAddIssue"
      @filter-click="handleFilterClick"
      @display-click="handleDisplayClick"
    />
  </div>
</template>

<script setup lang="ts">
import { Sidebar, MainContent } from '@/components/Home'
import { SAMPLE_ISSUES } from '@/constants/issues'

const issues = ref(SAMPLE_ISSUES)

const handleNavigation = (item) => {
  // 处理导航点击
}

const handleTabChange = (tabId) => {
  // 处理标签页切换
}

// 其他事件处理...
</script>
```

### 自定义配置

#### 修改导航配置

```typescript
// 在 constants/navigation.ts 中修改或添加新的导航项
export const CUSTOM_NAVIGATION = [
  {
    id: 'custom-item',
    label: '自定义项目',
    icon: 'Star',
    path: '/custom'
  }
]
```

#### 添加新的问题类型

```typescript
// 在 constants/issues.ts 中添加新类型
export enum IssueType {
  FEATURE = 'Feature',
  IMPROVEMENT = 'Improvement',
  BUG = 'Bug',
  TASK = 'Task',
  EPIC = 'Epic' // 新增
}

export const ISSUE_TYPE_STYLES = {
  // ... 现有样式
  [IssueType.EPIC]: 'bg-yellow-100 text-yellow-800'
}
```

## 特性

### 1. 完全去除硬编码
- 所有文本、配置、样式都通过常量文件管理
- 便于国际化和主题定制

### 2. 模块化设计
- 每个组件职责单一，便于维护和测试
- 支持组件级别的复用

### 3. TypeScript 支持
- 完整的类型定义
- 更好的开发体验和代码安全性

### 4. 可扩展性
- 通过配置文件轻松添加新功能
- 支持自定义图标和样式

### 5. 事件驱动
- 通过事件系统实现组件间通信
- 支持灵活的业务逻辑集成

## 扩展指南

### 添加新的侧边栏节

1. 在 `constants/navigation.ts` 中添加新的节配置
2. 在 `Sidebar.vue` 中引用新配置
3. 根据需要添加对应的事件处理

### 添加新的主内容视图

1. 创建新的组件文件
2. 在 `MainContent.vue` 中集成新组件
3. 更新路由或状态管理逻辑

### 自定义样式主题

1. 修改 constants 文件中的样式配置
2. 或者通过 CSS 变量覆盖默认样式
3. 支持响应式和暗色主题

## 性能考虑

- 使用 Vue 3 的组合式 API 和响应式系统
- 支持按需加载和代码分割
- 图标组件按需导入，减少包体积

# UI v2 基础组件库

这个目录包含了从NewHome组件中提取出来的可复用基础UI组件。所有组件都经过重新设计，具有更好的可扩展性和复用性。

## 组件列表

### 1. AppLogo
应用程序标志组件，用于显示应用名称和图标。

```vue
<AppLogo
  app-name="Chronnote"
  logo="CH"
  logo-color-class="bg-pink-500"
  :show-dropdown="true"
  :clickable="false"
  text-size="text-sm"
  @click="handleLogoClick"
/>
```

**Props:**
- `appName`: 应用名称 (默认: 'Chronnote')
- `logo`: 图标文字 (默认: 'CH')
- `logoColorClass`: 图标背景色类 (默认: 'bg-pink-500')
- `showDropdown`: 是否显示下拉箭头 (默认: true)
- `clickable`: 是否可点击 (默认: false)
- `textSize`: 文字大小类 (默认: 'text-sm')

### 2. IconButton
通用图标按钮组件。

```vue
<IconButton
  icon="Search"
  variant="ghost"
  size="md"
  icon-class="w-5 h-5 text-gray-400"
  @click="handleClick"
>
  搜索
</IconButton>
```

**Props:**
- `icon`: 图标名称 (必需)
- `variant`: 按钮样式 ('default' | 'ghost' | 'outline' | 'secondary')
- `size`: 按钮大小 ('xs' | 'sm' | 'md' | 'lg')
- `disabled`: 是否禁用
- `iconClass`: 自定义图标样式类

### 3. NavigationItem
导航项组件，支持嵌套子项。

```vue
<NavigationItem
  id="inbox"
  label="Inbox"
  icon="Inbox"
  :active="activeItem === 'inbox'"
  color-indicator="bg-red-500"
  badge="5"
  :children="childItems"
  @navigate="handleNavigate"
  @click="handleClick"
/>
```

**Props:**
- `id`: 唯一标识 (必需)
- `label`: 显示文本 (必需)
- `icon`: 图标名称
- `colorIndicator`: 颜色指示器类
- `path`: 路径
- `badge`: 徽章文本或数字
- `children`: 子项数组
- `level`: 嵌套层级
- `active`: 是否激活状态
- `textClass`: 文本样式类
- `iconClass`: 图标样式类

### 4. StatusBadge
状态徽章组件，用于显示状态标签。

```vue
<StatusBadge
  text="Feature"
  variant="feature"
  icon="Star"
/>
```

**Props:**
- `text`: 徽章文本
- `variant`: 徽章样式 ('default' | 'success' | 'warning' | 'danger' | 'info' | 'feature' | 'improvement' | 'bug' | 'task')
- `icon`: 图标名称

### 5. TabButton
标签按钮组件，用于标签页导航。

```vue
<TabButton
  label="All issues"
  icon="Circle"
  :active="activeTab === 'all-issues'"
  badge="10"
  badge-variant="info"
  @click="handleTabClick"
/>
```

**Props:**
- `label`: 标签文本 (必需)
- `icon`: 图标名称
- `active`: 是否激活状态
- `badge`: 徽章文本或数字
- `badgeVariant`: 徽章样式

### 6. Avatar
头像组件，支持图片、文字后备和表情符号。

```vue
<Avatar
  src="/avatar.jpg"
  alt="用户头像"
  fallback="AB"
  emoji="🎯"
  size="md"
/>
```

**Props:**
- `src`: 图片地址
- `alt`: 图片替代文本
- `fallback`: 文字后备
- `emoji`: 表情符号
- `size`: 大小 ('xs' | 'sm' | 'md' | 'lg' | 'xl')

### 7. SectionHeader
节标题组件，支持折叠功能。

```vue
<SectionHeader
  title="Workspace"
  :collapsible="true"
  :collapsed="false"
  @toggle="handleToggle"
/>
```

**Props:**
- `title`: 标题文本 (必需)
- `collapsible`: 是否可折叠
- `collapsed`: 是否已折叠

### 8. IssueCard
问题卡片组件，用于显示问题信息。

```vue
<IssueCard
  issue-id="CHR-13"
  title="球状笔记"
  type="Feature"
  assignee="开发者"
  date="May 30"
  avatar="🎯"
  @click="handleIssueClick"
/>
```

**Props:**
- `issueId`: 问题ID (必需)
- `title`: 问题标题 (必需)
- `type`: 问题类型
- `assignee`: 分配者
- `date`: 日期 (必需)
- `avatar`: 头像表情符号

## 使用示例

### 完整的侧边栏示例

```vue
<template>
  <div class="w-64 bg-white border-r border-gray-200 flex flex-col">
    <!-- 头部 -->
    <div class="p-4 border-b border-gray-200">
      <AppLogo />
    </div>

    <!-- 导航 -->
    <div class="flex-1 p-4 space-y-6">
      <!-- 主导航 -->
      <div class="space-y-2">
        <NavigationItem
          v-for="item in mainNav"
          :key="item.id"
          v-bind="item"
          :active="activeItem === item.id"
          @navigate="handleNavigate"
        />
      </div>

      <!-- 工作空间节 -->
      <div>
        <SectionHeader
          title="Workspace"
          :collapsible="true"
          :collapsed="workspaceCollapsed"
          @toggle="workspaceCollapsed = $event"
        />
        <div v-if="!workspaceCollapsed" class="space-y-1 ml-2">
          <NavigationItem
            v-for="item in workspaceItems"
            :key="item.id"
            v-bind="item"
            :active="activeItem === item.id"
            @navigate="handleNavigate"
          />
        </div>
      </div>
    </div>

    <!-- 底部 -->
    <div class="p-4 border-t border-gray-200">
      <div class="flex items-center space-x-2 text-xs text-gray-600">
        <IconButton icon="HelpCircle" variant="ghost" size="xs" />
        <span>Free plan</span>
      </div>
    </div>
  </div>
</template>
```

## 设计原则

### 1. 原子化设计
每个组件都是独立的、可复用的原子单元，可以组合成更复杂的界面。

### 2. 一致性
所有组件使用统一的设计token和样式规范，确保视觉一致性。

### 3. 可扩展性
组件设计时考虑了未来的扩展需求，支持通过props自定义样式和行为。

### 4. 类型安全
所有组件都有完整的TypeScript类型定义，提供更好的开发体验。

### 5. 事件驱动
组件间通过事件系统通信，保持松耦合的架构。

## 样式系统

### 颜色变体
- `default`: 默认灰色系
- `success`: 绿色系，表示成功状态
- `warning`: 黄色系，表示警告状态
- `danger`: 红色系，表示危险状态
- `info`: 蓝色系，表示信息状态
- `feature`: 紫色系，表示新功能
- `improvement`: 蓝色系，表示改进
- `bug`: 红色系，表示缺陷
- `task`: 灰色系，表示任务

### 尺寸规格
- `xs`: 超小尺寸
- `sm`: 小尺寸
- `md`: 中等尺寸 (默认)
- `lg`: 大尺寸
- `xl`: 超大尺寸

## 最佳实践

1. **组件组合**: 优先使用基础组件组合而不是创建新的复杂组件
2. **props设计**: 保持props简单明确，避免过度配置
3. **事件命名**: 使用语义化的事件名称，如`navigate`、`toggle`等
4. **样式覆盖**: 通过class props而不是直接修改组件样式
5. **性能优化**: 合理使用computed和watch，避免不必要的重新渲染

这些基础组件可以灵活组合使用，构建出符合设计规范的复杂界面。
