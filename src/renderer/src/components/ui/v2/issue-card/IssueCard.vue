<template>
  <div
    class="px-6 py-4 hover:bg-base-200 cursor-pointer transition-colors"
    @click="$emit('click')"
  >
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-3">
        <IconButton
          icon="BarChart3"
          variant="ghost"
          size="xs"
          icon-class="w-4 h-4 text-base-content-tertiary"
        />
        <span class="text-xs font-medium text-base-content-secondary">{{ issueId }}</span>
        <span class="text-xs text-base-content">{{ title }}</span>
      </div>
      <div class="flex items-center space-x-3">
        <StatusBadge
          v-if="type"
          :text="type"
          :variant="getTypeVariant(type)"
        />
        <div v-if="assignee" class="flex items-center space-x-2 text-xs text-base-content-secondary">
          <IconButton
            icon="Circle"
            variant="ghost"
            size="xs"
            icon-class="w-3 h-3"
          />
          <span>{{ assignee }}</span>
        </div>
        <span class="text-xs text-base-content-secondary">{{ date }}</span>
        <Avatar
          :emoji="avatar"
          size="sm"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import IconButton from '../icon-button/IconButton.vue'
import StatusBadge from '../status-badge/StatusBadge.vue'
import Avatar from '../avatar/Avatar.vue'

interface Props {
  issueId: string
  title: string
  type?: string
  assignee?: string
  date: string
  avatar?: string
}

interface Emits {
  (e: 'click'): void
}

defineProps<Props>()
defineEmits<Emits>()

const getTypeVariant = (type: string): 'default' | 'success' | 'warning' | 'danger' | 'info' | 'feature' | 'improvement' | 'bug' | 'task' => {
  const variants: Record<string, 'default' | 'success' | 'warning' | 'danger' | 'info' | 'feature' | 'improvement' | 'bug' | 'task'> = {
    'Feature': 'feature',
    'Improvement': 'improvement',
    'Bug': 'bug',
    'Task': 'task'
  }

  return variants[type] || 'default'
}
</script>
