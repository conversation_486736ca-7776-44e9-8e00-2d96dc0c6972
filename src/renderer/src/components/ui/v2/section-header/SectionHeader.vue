<template>
  <div class="flex items-center space-x-2 mb-2">
    <span class="text-[11px] font-medium text-subtitle uppercase tracking-wide">
      {{ title }}
    </span>
    <IconButton
      v-if="collapsible"
      icon="ChevronDown"
      variant="ghost"
      size="xs"
      :icon-class="chevronClass"
      @click="toggleCollapse"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import IconButton from '../icon-button/IconButton.vue'

interface Props {
  title: string
  collapsible?: boolean
  collapsed?: boolean
}

interface Emits {
  (e: 'toggle', collapsed: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  collapsible: false,
  collapsed: false
})

const emit = defineEmits<Emits>()

const chevronClass = computed(() => {
  return [
    'w-3 h-3 text-base-content-tertiary transition-transform',
    props.collapsed ? '' : 'rotate-180'
  ].filter(<PERSON>olean).join(' ')
})

const toggleCollapse = () => {
  if (props.collapsible) {
    emit('toggle', !props.collapsed)
  }
}
</script>
