<template>
  <span
    :class="[
      'inline-flex items-center px-2 py-1 text-xs font-medium rounded-full',
      variantClasses
    ]"
  >
    <component
      v-if="icon"
      :is="iconComponent"
      class="w-3 h-3 mr-1"
    />
    <slot>{{ text }}</slot>
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { getIconComponent } from '../../../../utils/iconComponents'

interface Props {
  text?: string
  variant?: 'default' | 'success' | 'warning' | 'danger' | 'info' | 'feature' | 'improvement' | 'bug' | 'task'
  icon?: string
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default'
})

const iconComponent = computed(() => {
  return props.icon ? getIconComponent(props.icon) : null
})

const variantClasses = computed(() => {
  const variants = {
    default: 'bg-base-300 text-base-content',
    success: 'bg-success/20 text-success',
    warning: 'bg-warning/20 text-warning',
    danger: 'bg-error/20 text-error',
    info: 'bg-info/20 text-info',
    feature: 'bg-purple-500/20 text-purple-600',
    improvement: 'bg-info/20 text-info',
    bug: 'bg-error/20 text-error',
    task: 'bg-base-300 text-base-content'
  }

  return variants[props.variant]
})
</script>
