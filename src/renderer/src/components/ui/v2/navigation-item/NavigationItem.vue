<template>
  <div>
    <!-- 主导航项 -->
    <div
      :class="[
        'flex items-center space-x-3 px-2 py-1.5 text-base-content-secondary rounded-md cursor-pointer transition-colors select-none',
        {
          'hover:bg-base-100': !toggleable || (toggleable && !active),
          'bg-base-100': toggleable && active,
          'text-base-content': toggleable && active,
          'cursor-grab': draggable,
          'active:cursor-grabbing': draggable
        }
      ]"
      :draggable="draggable"
      @click="handleClick"
      @dragstart="handleDragStart"
      @dragend="handleDragEnd"
    >
      <component v-if="icon" :is="iconComponent" :class="iconClass" />
      <div v-if="colorIndicator" :class="[colorIndicator, 'w-3 h-3 rounded-full']" />
      <span :class="textClass">{{ label }}</span>
      <div class="flex-1" />
      <IconButton
        v-if="hasChildren"
        icon="ChevronDown"
        variant="ghost"
        size="sm"
        :icon-class="chevronClass"
      />
      <span v-if="badge" class="px-1.5 py-0.5 text-xs bg-base-100 text-base-content-secondary rounded-full">
        {{ badge }}
      </span>
    </div>

    <!-- 子导航项 -->
    <div v-if="hasChildren && expanded" class="space-y-1 ml-6 mt-1">
      <NavigationItem
        v-for="child in children"
        :key="child.id"
        v-bind="child"
        :level="level + 1"
        :toggleable="toggleable"
        @click="$emit('navigate', child)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { getIconComponent } from '../../../../utils/iconComponents'
import IconButton from '../icon-button/IconButton.vue'

interface NavigationItemData {
  id: string
  label: string
  icon?: string
  colorIndicator?: string
  path?: string
  badge?: string | number
  children?: NavigationItemData[]
  draggable?: boolean
}

interface Props extends NavigationItemData {
  level?: number
  active?: boolean
  toggleable?: boolean
  textClass?: string
  iconClass?: string
}

interface Emits {
  (e: 'navigate', item: NavigationItemData): void
  (e: 'click', item: NavigationItemData): void
}

const props = withDefaults(defineProps<Props>(), {
  level: 0,
  active: false,
  toggleable: true,
  textClass: 'text-sm',
  iconClass: 'w-3.5 h-3.5'
})

const emit = defineEmits<Emits>()

const expanded = ref(false)

const hasChildren = computed(() => {
  return props.children && props.children.length > 0
})

const iconComponent = computed(() => {
  return props.icon ? getIconComponent(props.icon) : null
})

const chevronClass = computed(() => {
  return ['w-3 h-3 text-base-content-tertiary transition-transform', { 'rotate-180': expanded.value }]
    .filter(Boolean)
    .join(' ')
})

const handleClick = () => {
  if (hasChildren.value) {
    expanded.value = !expanded.value
  } else {
    emit('navigate', props)
  }
  emit('click', props)
}

// 拖拽事件处理
const handleDragStart = (e: DragEvent) => {
  if (!props.draggable) return

  // 创建虚拟的 ChronEngine 节点数据
  const chronEngineData = {
    uuid: 'chronengine-' + Date.now(),
    type: 'chronengine',
    title: 'Chron Engine'
  }

  e.dataTransfer!.setData('id', chronEngineData.uuid)
  e.dataTransfer!.setData('type', chronEngineData.type)
  e.dataTransfer!.setData('node', JSON.stringify(chronEngineData))
  e.dataTransfer!.effectAllowed = 'copy' // 使用copy而不是move，因为这是可重复拖拽的

  const target = e.target as HTMLElement
  target.classList.add('dragging')

  // 发送拖拽开始事件
  // 这里可以添加 mitter 事件，但需要先导入 mitter
}

const handleDragEnd = (e: DragEvent) => {
  if (!props.draggable) return

  const target = e.target as HTMLElement
  target.classList.remove('dragging')

  // 发送拖拽结束事件
}
</script>

<style scoped>
/* 拖拽样式 */
.dragging {
  opacity: 0.5;
  transform: scale(0.95);
  transition: all 0.2s ease;
}

/* 拖拽时的光标样式 */
.cursor-grab {
  cursor: grab;
}

.cursor-grabbing {
  cursor: grabbing;
}
</style>
