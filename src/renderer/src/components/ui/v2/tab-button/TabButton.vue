<template>
  <button
    :class="[
      'flex items-center space-x-2 text-xs cursor-pointer transition-colors',
      active ? 'text-base-content' : 'text-base-content-secondary hover:text-base-content'
    ]"
    @click="$emit('click')"
  >
    <component
      v-if="icon"
      :is="iconComponent"
      class="w-4 h-4"
    />
    <span>{{ label }}</span>
    <StatusBadge
      v-if="badge"
      :text="badge.toString()"
      :variant="badgeVariant"
    />
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { getIconComponent } from '../../../../utils/iconComponents'
import StatusBadge from '../status-badge/StatusBadge.vue'

interface Props {
  label: string
  icon?: string
  active?: boolean
  badge?: string | number
  badgeVariant?: 'default' | 'success' | 'warning' | 'danger' | 'info'
}

interface Emits {
  (e: 'click'): void
}

const props = withDefaults(defineProps<Props>(), {
  active: false,
  badgeVariant: 'default'
})

defineEmits<Emits>()

const iconComponent = computed(() => {
  return props.icon ? getIconComponent(props.icon) : null
})
</script>
