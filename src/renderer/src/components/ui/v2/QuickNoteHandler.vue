<template>
  <!-- 这是一个无UI组件，仅用于处理快速笔记的事件 -->
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { toast } from '@renderer/components/ui/toast'

onMounted(() => {
  // 监听快速笔记保存通知
  window.electron.ipcRenderer.on('quick-note-saved-notification', () => {
    toast({
      title: '成功',
      description: '快速笔记已保存到本地',
      duration: 3000
    })
  })
})

onUnmounted(() => {
  // 移除事件监听
  window.electron.ipcRenderer.removeAllListeners('quick-note-saved-notification')
})
</script>
