<template>
  <div class="flex items-center gap-1">
    <!-- 左侧操作按钮 -->
    <div v-if="leftActions.length > 0" class="flex items-center gap-1 mr-2">
      <Button
        v-for="action in leftActions"
        :key="action.id"
        variant="ghost"
        size="icon"
        :class="[
          'h-6 w-6 text-base-content/70 hover:text-base-content/90',
          action.class
        ]"
        :title="action.title"
        @click="handleAction(action)"
      >
        <component :is="action.icon" class="h-4 w-4" />
      </Button>
    </div>

    <!-- 分屏控制按钮 -->
    <div v-if="showSplitControls" class="flex items-center gap-1 mr-1">
      <Button
        variant="ghost"
        size="icon"
        class="h-6 w-6 text-base-content/70 hover:text-base-content/90"
        @click="$emit('split-horizontal')"
        title="水平分割"
      >
        <Columns2 class="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="icon"
        class="h-6 w-6 text-base-content/70 hover:text-base-content/90"
        @click="$emit('split-vertical')"
        title="垂直分割"
      >
        <Rows2 class="h-4 w-4" />
      </Button>
    </div>

    <!-- 下拉菜单 -->
    <DropdownMenu v-if="menuActions.length > 0">
      <DropdownMenuTrigger as-child>
        <Button
          variant="ghost"
          size="icon"
          class="h-6 w-6 text-base-content/90 dark:text-base-content"
        >
          <Ellipsis class="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent class="-translate-x-3.5 w-52 bg-base-background border-base-content/10">
        <DropdownMenuItem
          v-for="action in menuActions"
          :key="action.id"
          class="text-base-content"
          @click="handleAction(action)"
        >
          <component :is="action.icon" class="h-4 w-4 mr-2 text-base-content" />
          {{ action.label }}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>

    <!-- 关闭按钮 -->
    <Button
      variant="ghost"
      size="icon"
      class="h-6 w-6 text-base-content/70 hover:text-base-content/90"
      @click="$emit('close')"
      title="关闭"
    >
      <X class="h-4 w-4" />
    </Button>

  </div>
</template>

<script setup lang="ts">
import { Button } from '@renderer/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@renderer/components/ui/dropdown-menu'
import {
  Columns2,
  Ellipsis,
  Rows2,
  X
} from 'lucide-vue-next'
import type { Component } from 'vue'

export interface ActionItem {
  id: string
  label: string
  icon: Component
  action?: () => void
  title?: string
  class?: string
}

interface Props {
  /** 左侧操作按钮列表 */
  leftActions?: ActionItem[]
  /** 下拉菜单操作列表 */
  menuActions?: ActionItem[]
  /** 是否显示分屏控制按钮 */
  showSplitControls?: boolean
}

interface Emits {
  (e: 'action', action: ActionItem): void
  (e: 'split-horizontal'): void
  (e: 'split-vertical'): void
  (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  leftActions: () => [],
  menuActions: () => [],
  showSplitControls: true
})

const emit = defineEmits<Emits>()

const handleAction = (action: ActionItem) => {
  if (action.action) {
    action.action()
  }
  emit('action', action)
}
</script>
