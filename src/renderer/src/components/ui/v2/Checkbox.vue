<template>
  <div class="checkbox-wrapper-15">
    <input
      :id="id"
      class="inp-cbx"
      type="checkbox"
      style="display: none"
      :checked="modelValue"
      @change="handleChange"
    />
    <label class="cbx" :for="id" :class="{ 'animate-enabled': shouldAnimate }">
      <span>
        <svg width="10px" height="7px" viewBox="0 0 10 7">
          <polyline points="1 3 4 6 9 1"></polyline>
        </svg>
      </span>
    </label>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, watch } from 'vue'

const props = defineProps<{
  modelValue?: boolean
  id: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
}>()

const shouldAnimate = ref(false)

const handleChange = (e: Event) => {
  const target = e.target as HTMLInputElement
  const newValue = target.checked

  // 只有在状态实际改变时才触发动画
  if (newValue !== props.modelValue) {
    shouldAnimate.value = true
    emit('update:modelValue', newValue)

    // 动画完成后重置动画状态
    setTimeout(() => {
      shouldAnimate.value = false
    }, 600)
  }
}

// 监听外部状态变化（比如批量更新），不触发动画
watch(
  () => props.modelValue,
  () => {
    // 外部更新不触发动画
  }
)
</script>

<style scoped>
.checkbox-wrapper-15 .cbx {
  -webkit-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}
.checkbox-wrapper-15 .cbx span {
  display: inline-block;
  vertical-align: middle;
  transform: translate3d(0, 0, 0);
}
.checkbox-wrapper-15 .cbx span:first-child {
  position: relative;
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  transform: scale(1);
  vertical-align: middle;
  border: 1px solid #b9b8c3;
  transition: all 0.2s ease;
}
.checkbox-wrapper-15 .cbx span:first-child svg {
  position: absolute;
  z-index: 1;
  top: 50%;
  left: 50%;
  fill: none;
  stroke: white;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-dasharray: 16px;
  stroke-dashoffset: 16px;
  transform: translate(-50%, -50%);
}

.checkbox-wrapper-15 .cbx span:first-child:before {
  content: '';
  width: 100%;
  height: 100%;
  background: #22a3e4;
  display: block;
  transform: scale(0);
  opacity: 1;
  border-radius: 50%;
  transition-delay: 0.2s;
}

.checkbox-wrapper-15 .cbx:hover span:first-child {
  border-color: #22a3e4;
}

/* 基础选中状态样式，无动画 */
.checkbox-wrapper-15 .inp-cbx:checked + .cbx span:first-child {
  border-color: #22a3e4;
  background: #22a3e4;
}

.checkbox-wrapper-15 .inp-cbx:checked + .cbx span:first-child svg {
  stroke-dashoffset: 0;
}

.checkbox-wrapper-15 .inp-cbx:checked + .cbx span:first-child:before {
  transform: scale(2.2);
  opacity: 0;
}

/* 只有在启用动画时才应用动画效果 */
.checkbox-wrapper-15 .cbx.animate-enabled span:first-child {
  animation: check-15 0.6s ease;
}

.checkbox-wrapper-15 .cbx.animate-enabled span:first-child svg polyline {
  animation: draw-check 0.3s ease forwards;
}

.checkbox-wrapper-15 .cbx.animate-enabled span:first-child:before {
  transition: all 0.6s ease;
}

@keyframes check-15 {
  50% {
    transform: scale(1.2);
  }
}

@keyframes draw-check {
  from {
    stroke-dashoffset: 16px;
  }
  to {
    stroke-dashoffset: 0;
  }
}
</style>
