# ConfirmDialog 组件

一个可复用的确认对话框组件，支持多种样式和自定义配置。

## 功能特性

- 🎨 **多种样式变体**：支持 danger、warning、info、success 四种预设样式
- 🔧 **高度可定制**：可自定义标题、描述、按钮文本和图标
- 📱 **响应式设计**：适配不同屏幕尺寸
- ♿ **无障碍支持**：支持键盘导航和屏幕阅读器
- 🎯 **TypeScript 支持**：完整的类型定义

## 基本用法

```vue
<template>
  <div>
    <button @click="showDialog = true">删除项目</button>
    
    <ConfirmDialog
      v-model:open="showDialog"
      title="确认删除"
      description="您确定要删除这个项目吗？此操作无法撤销。"
      confirm-text="删除"
      cancel-text="取消"
      variant="danger"
      @confirm="handleDelete"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ConfirmDialog from '@renderer/components/ui/ConfirmDialog.vue'

const showDialog = ref(false)

const handleDelete = () => {
  console.log('删除确认')
  // 执行删除逻辑
}

const handleCancel = () => {
  console.log('取消删除')
}
</script>
```

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `open` | `boolean` | `false` | 控制对话框显示/隐藏 |
| `title` | `string` | `'确认操作'` | 对话框标题 |
| `description` | `string` | `'您确定要执行此操作吗？'` | 对话框描述文本 |
| `confirmText` | `string` | `'确认'` | 确认按钮文本 |
| `cancelText` | `string` | `'取消'` | 取消按钮文本 |
| `confirmIcon` | `string` | `'Check'` | 确认按钮图标 |
| `cancelIcon` | `string` | `'X'` | 取消按钮图标 |
| `variant` | `'danger' \| 'warning' \| 'info' \| 'success'` | `'danger'` | 样式变体 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:open` | `boolean` | 对话框开关状态变化时触发 |
| `confirm` | - | 点击确认按钮时触发 |
| `cancel` | - | 点击取消按钮时触发 |

## 样式变体

### Danger (危险操作)
```vue
<ConfirmDialog
  variant="danger"
  title="确认删除"
  description="此操作无法撤销"
  confirm-icon="Trash2"
/>
```

### Warning (警告操作)
```vue
<ConfirmDialog
  variant="warning"
  title="警告"
  description="此操作可能会影响其他功能"
  confirm-icon="AlertTriangle"
/>
```

### Info (信息确认)
```vue
<ConfirmDialog
  variant="info"
  title="信息确认"
  description="您确定要执行此操作吗？"
  confirm-icon="Info"
/>
```

### Success (成功操作)
```vue
<ConfirmDialog
  variant="success"
  title="操作成功"
  description="操作已完成，继续下一步？"
  confirm-icon="ArrowRight"
/>
```

## 高级用法

### 自定义图标和文本
```vue
<ConfirmDialog
  v-model:open="showDialog"
  title="发布文章"
  description="文章将会公开发布，所有用户都能看到。"
  confirm-text="发布"
  cancel-text="稍后"
  confirm-icon="Send"
  cancel-icon="Clock"
  variant="info"
  @confirm="publishArticle"
/>
```

### 异步操作处理
```vue
<script setup>
const isDeleting = ref(false)

const handleDelete = async () => {
  isDeleting.value = true
  try {
    await deleteItem()
    showToast('删除成功')
  } catch (error) {
    showToast('删除失败')
  } finally {
    isDeleting.value = false
  }
}
</script>
```

## 注意事项

1. **图标名称**：确保使用的图标名称在 `iconComponents` 工具中已定义
2. **样式一致性**：建议在同一应用中保持一致的确认对话框样式
3. **无障碍性**：对话框会自动处理焦点管理和键盘导航
4. **响应式**：组件会自动适配移动端显示

## 与原有代码的对比

### 重构前
```vue
<!-- 需要重复编写大量模板代码 -->
<Dialog v-model:open="showDeleteDialog">
  <DialogContent class="sm:max-w-md">
    <DialogHeader>
      <DialogTitle>确认删除</DialogTitle>
      <DialogDescription>您确定要删除这个待办事项吗？此操作无法撤销。</DialogDescription>
    </DialogHeader>
    <DialogFooter class="flex justify-end gap-2">
      <button class="..." @click="showDeleteDialog = false">
        <component :is="getIconComponent('X')" class="w-4 h-4 mr-1.5" />
        取消
      </button>
      <button class="..." @click="deleteTodo">
        <component :is="getIconComponent('Trash2')" class="w-4 h-4 mr-1.5" />
        删除
      </button>
    </DialogFooter>
  </DialogContent>
</Dialog>
```

### 重构后
```vue
<!-- 简洁的声明式用法 -->
<ConfirmDialog
  v-model:open="showDeleteDialog"
  title="确认删除"
  description="您确定要删除这个待办事项吗？此操作无法撤销。"
  confirm-text="删除"
  confirm-icon="Trash2"
  variant="danger"
  @confirm="deleteTodo"
/>
```

## 优势

- ✅ **代码复用**：一次编写，多处使用
- ✅ **维护性**：统一的样式和行为，易于维护
- ✅ **一致性**：确保整个应用的确认对话框风格一致
- ✅ **可扩展**：易于添加新的样式变体和功能
- ✅ **类型安全**：完整的 TypeScript 支持
