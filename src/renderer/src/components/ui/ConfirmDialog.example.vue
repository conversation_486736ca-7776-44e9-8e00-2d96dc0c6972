<template>
  <div class="p-6 space-y-4">
    <h2 class="text-2xl font-bold">ConfirmDialog 使用示例</h2>
    
    <div class="grid grid-cols-2 gap-4">
      <!-- 删除确认 -->
      <button
        @click="showDeleteDialog = true"
        class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
      >
        删除操作
      </button>
      
      <!-- 警告确认 -->
      <button
        @click="showWarningDialog = true"
        class="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
      >
        警告操作
      </button>
      
      <!-- 信息确认 -->
      <button
        @click="showInfoDialog = true"
        class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        信息确认
      </button>
      
      <!-- 成功确认 -->
      <button
        @click="showSuccessDialog = true"
        class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
      >
        成功操作
      </button>
    </div>

    <!-- 删除确认对话框 -->
    <ConfirmDialog
      v-model:open="showDeleteDialog"
      title="确认删除"
      description="您确定要删除这个项目吗？此操作无法撤销。"
      confirm-text="删除"
      cancel-text="取消"
      confirm-icon="Trash2"
      cancel-icon="X"
      variant="danger"
      @confirm="handleDelete"
      @cancel="handleCancel"
    />

    <!-- 警告确认对话框 -->
    <ConfirmDialog
      v-model:open="showWarningDialog"
      title="警告"
      description="此操作可能会影响其他功能，您确定要继续吗？"
      confirm-text="继续"
      cancel-text="取消"
      confirm-icon="AlertTriangle"
      cancel-icon="X"
      variant="warning"
      @confirm="handleWarning"
      @cancel="handleCancel"
    />

    <!-- 信息确认对话框 -->
    <ConfirmDialog
      v-model:open="showInfoDialog"
      title="信息确认"
      description="您确定要执行此操作吗？"
      confirm-text="确认"
      cancel-text="取消"
      confirm-icon="Info"
      cancel-icon="X"
      variant="info"
      @confirm="handleInfo"
      @cancel="handleCancel"
    />

    <!-- 成功确认对话框 -->
    <ConfirmDialog
      v-model:open="showSuccessDialog"
      title="操作成功"
      description="操作已完成，您要继续下一步吗？"
      confirm-text="继续"
      cancel-text="完成"
      confirm-icon="ArrowRight"
      cancel-icon="Check"
      variant="success"
      @confirm="handleSuccess"
      @cancel="handleCancel"
    />

    <!-- 结果显示 -->
    <div v-if="lastAction" class="mt-4 p-4 bg-gray-100 rounded">
      <p><strong>最后操作:</strong> {{ lastAction }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ConfirmDialog from './ConfirmDialog.vue'

const showDeleteDialog = ref(false)
const showWarningDialog = ref(false)
const showInfoDialog = ref(false)
const showSuccessDialog = ref(false)
const lastAction = ref('')

const handleDelete = () => {
  lastAction.value = '删除操作已确认'
  console.log('删除操作已确认')
}

const handleWarning = () => {
  lastAction.value = '警告操作已确认'
  console.log('警告操作已确认')
}

const handleInfo = () => {
  lastAction.value = '信息操作已确认'
  console.log('信息操作已确认')
}

const handleSuccess = () => {
  lastAction.value = '成功操作已确认'
  console.log('成功操作已确认')
}

const handleCancel = () => {
  lastAction.value = '操作已取消'
  console.log('操作已取消')
}
</script>
