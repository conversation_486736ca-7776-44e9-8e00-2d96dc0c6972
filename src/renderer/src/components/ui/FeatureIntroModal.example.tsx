/**
 * FeatureIntroModal 使用示例
 * 
 * 这个文件展示了如何使用 FeatureIntroModal 组件和 useFeatureIntro composable
 */

import { useFeatureIntro } from '@renderer/composables/useFeatureIntro'

// 获取 composable 实例
const { show, isAcknowledged, resetAcknowledgment } = useFeatureIntro()

// 示例 1: 基本使用
export const showBasicIntro = () => {
  show({
    id: 'basic-intro',
    gifUrl: '/assets/gifs/basic-feature.gif',
    content: () => (
      <div>
        <h2 className="text-2xl font-bold mb-4">欢迎使用 ChronNote</h2>
        <p className="text-gray-600 mb-4">
          ChronNote 是一款强大的笔记应用，帮助您高效管理知识和想法。
        </p>
        <ul className="list-disc list-inside space-y-2">
          <li>智能笔记编辑</li>
          <li>实时协作功能</li>
          <li>多平台同步</li>
        </ul>
      </div>
    )
  })
}

// 示例 2: 带链接的 GIF
export const showProUpgradeIntro = () => {
  show({
    id: 'pro-upgrade-intro',
    gifUrl: '/assets/gifs/pro-features.gif',
    gifLink: 'https://chronnote.com/upgrade',
    buttonText: '了解更多',
    content: () => (
      <div>
        <h2 className="text-2xl font-bold mb-4 text-indigo-600">
          升级至 Pro+ 版全面解锁演说模式
        </h2>
        <p className="text-gray-600 mb-6">
          思维导图秒变幻灯片，即刻开始你的演说
        </p>
        
        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <span className="w-2 h-2 bg-green-500 rounded-full"></span>
            <span>工作汇报</span>
          </div>
          <div className="flex items-center space-x-3">
            <span className="w-2 h-2 bg-green-500 rounded-full"></span>
            <span>教育讲座</span>
          </div>
          <div className="flex items-center space-x-3">
            <span className="w-2 h-2 bg-green-500 rounded-full"></span>
            <span>创意展示</span>
          </div>
        </div>

        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <p className="text-sm text-gray-500">
            💰 ¥420/年，低至 ¥1.15/天
          </p>
        </div>
      </div>
    ),
    onAcknowledge: () => {
      console.log('用户确认了 Pro 升级介绍')
      // 可以在这里添加统计或其他逻辑
    }
  })
}

// 示例 3: AI 功能介绍
export const showAIIntro = () => {
  show({
    id: 'ai-features-intro',
    gifUrl: '/assets/gifs/ai-features.gif',
    content: () => (
      <div>
        <h2 className="text-2xl font-bold mb-4 flex items-center">
          <span className="mr-3">🤖</span>
          AI 助手已就绪
        </h2>
        <p className="text-gray-600 mb-6">
          全新的 AI 功能现已上线，让您的创作更加智能高效。
        </p>
        
        <div className="grid grid-cols-1 gap-4">
          <div className="p-4 border border-gray-200 rounded-lg">
            <h3 className="font-medium mb-2">✨ 智能续写</h3>
            <p className="text-sm text-gray-600">AI 理解您的思路，续写自然流畅</p>
          </div>
          <div className="p-4 border border-gray-200 rounded-lg">
            <h3 className="font-medium mb-2">🎯 内容精修</h3>
            <p className="text-sm text-gray-600">一键优化文档结构和表达</p>
          </div>
          <div className="p-4 border border-gray-200 rounded-lg">
            <h3 className="font-medium mb-2">🎨 图片生成</h3>
            <p className="text-sm text-gray-600">描述需求，AI 为您生成配图</p>
          </div>
        </div>
      </div>
    ),
    buttonText: '开始体验'
  })
}

// 示例 4: 新版本功能介绍
export const showVersionUpdateIntro = () => {
  show({
    id: 'version-2.0-intro',
    gifUrl: '/assets/gifs/version-update.gif',
    content: () => (
      <div>
        <div className="text-center mb-6">
          <h2 className="text-3xl font-bold mb-2">ChronNote 2.0</h2>
          <p className="text-indigo-600 font-medium">全新体验，重新定义笔记应用</p>
        </div>
        
        <div className="space-y-4">
          <div className="flex items-start space-x-4">
            <div className="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center flex-shrink-0">
              <span className="text-indigo-600 font-bold">1</span>
            </div>
            <div>
              <h3 className="font-medium">全新界面设计</h3>
              <p className="text-sm text-gray-600">更加简洁美观的用户界面</p>
            </div>
          </div>
          
          <div className="flex items-start space-x-4">
            <div className="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center flex-shrink-0">
              <span className="text-indigo-600 font-bold">2</span>
            </div>
            <div>
              <h3 className="font-medium">性能大幅提升</h3>
              <p className="text-sm text-gray-600">启动速度提升 50%，操作更流畅</p>
            </div>
          </div>
          
          <div className="flex items-start space-x-4">
            <div className="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center flex-shrink-0">
              <span className="text-indigo-600 font-bold">3</span>
            </div>
            <div>
              <h3 className="font-medium">协作功能增强</h3>
              <p className="text-sm text-gray-600">支持实时多人编辑和评论</p>
            </div>
          </div>
        </div>
      </div>
    )
  })
}

// 工具函数：检查介绍状态
export const checkIntroStatus = (id: string) => {
  console.log(`介绍 "${id}" 是否已确认:`, isAcknowledged(id))
}

// 工具函数：重置介绍状态（开发调试用）
export const resetIntro = (id?: string) => {
  resetAcknowledgment(id)
  console.log(`已重置介绍状态: ${id || '全部'}`)
}

// 使用说明
export const USAGE_GUIDE = `
# FeatureIntroModal 使用指南

## 基本使用

\`\`\`typescript
import { useFeatureIntro } from '@renderer/composables/useFeatureIntro'

const { show } = useFeatureIntro()

show({
  id: 'unique-intro-id',        // 必需：唯一标识符
  gifUrl: '/path/to/demo.gif',  // 可选：演示 GIF 地址
  gifLink: 'https://...',       // 可选：点击 GIF 跳转链接
  buttonText: '我知道了',        // 可选：按钮文字
  content: () => (              // 可选：JSX 内容
    <div>
      <h2>标题</h2>
      <p>描述内容</p>
    </div>
  ),
  onAcknowledge: () => {},      // 可选：确认回调
  onClose: () => {}             // 可选：关闭回调
})
\`\`\`

## 特性说明

1. **一次性显示**：相同 ID 的介绍只会显示一次，用户确认后永不再显示
2. **持久化记录**：确认状态保存在 localStorage，跨会话生效
3. **响应式设计**：适配桌面和移动端设备
4. **无障碍支持**：支持键盘导航和屏幕阅读器
5. **命令式调用**：无需在模板中声明，随时随地调用

## 最佳实践

1. 使用有意义的 ID，建议格式：\`feature-version-intro\`
2. GIF 文件尽量压缩，建议不超过 2MB
3. 内容简洁明了，重点突出核心功能
4. 合理使用回调函数进行数据统计
`