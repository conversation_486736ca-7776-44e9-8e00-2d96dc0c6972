<template>
  <div class="flex-1 flex w-full flex-col h-full relative">
    <!-- 保存动画 -->
    <div
      v-if="isSaving"
      class="absolute top-4 left-4 flex items-center gap-2 text-sm text-base-content/80"
    >
      <div class="animate-spin w-4 h-4">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle
            class="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
      </div>
      <span>保存中...</span>
    </div>

    <!-- 搜索对话框 - 移到最外层容器，不受滚动影响 -->
    <SearchDialog v-if="editor" :editor="editor" />

    <div
      ref="editorContainer"
      :class="containerClass"
      :style="{ paddingBottom: isEmbedded ? '1rem' : 'clamp(8rem, 25vh, 20rem)' }"
      @click="handleEditorContainerClick"
    >
      <EditorBubbleMenu v-if="editor" :editor="editor" :instance-id="node.uuid" :readonly="readonly" />
      <editor-content
        :editor="editor"
        :class="contentClass"
      />
    </div>

    <!-- 插入选择菜单 -->
    <InsertChoiceMenu
      ref="insertChoiceMenu"
      @select-ai-knowledge-block="handleInsertAIKnowledgeBlock"
      @select-manual-knowledge-block="handleInsertManualKnowledgeBlock"
      @select-anchor-link="handleInsertAnchorLink"
      @select-plain-link="handleInsertPlainLink"
      @select-bookmark="handleInsertBookmark"
      @select-embed="handleInsertEmbed"
      @cancel="handleInsertCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { computePosition, flip, offset, shift } from '@floating-ui/dom'
import { useBlockService } from '@renderer/composables/useBlockService'
import { useSearchDialog } from '@renderer/composables/useSearchDialog'
import { standardExtensions } from '@renderer/editor/plugins'
import { slashPluginKey } from '@renderer/editor/plugins/function/slash/sug'
import { SQLitePersistence } from '@renderer/editor/provider/local.sqlite'
import { LocalProvider } from '@renderer/editor/provider/LocalProvider'
import { Collaboration } from '@tiptap/extension-collaboration'
import { TextSelection } from '@tiptap/pm/state'
import { Editor, EditorContent, JSONContent } from '@tiptap/vue-3'
import { onBeforeMount, onBeforeUnmount, onMounted, ref, watch } from 'vue'
// 导入新的工具函数
// 导入性能测试工具（开发环境）
if (import.meta.env.DEV) {
  import('@renderer/utils/performanceTest')
}

import { useFocusTracker } from '@renderer/composables/useFocusTracker'
import { UpdateService } from '@renderer/editor/provider/UpdateService'
import { mitter } from '@renderer/plugins/mitter'
import { useNodeStore } from '@renderer/stores/nodeStore'
import { useMagicKeys } from '@vueuse/core'
import { v4 } from 'uuid'
import * as Y from 'yjs'
import EditorBubbleMenu from '../common/EditorBubbleMenu.vue'
import InsertChoiceMenu from './InsertChoiceMenu.vue'
import SearchDialog from './SearchDialog.vue'
import { deleteImageFile } from '@renderer/editor/plugins/node/image'

const props = withDefaults(defineProps<{
  node: any // 使用 any 类型避免类型检查错误
  containerClass?: string
  contentClass?: string
  collaborative?: boolean
  readonly?: boolean
  isEmbedded?: boolean // 新增：是否为嵌入模式（如思维导图节点中）
}>(), {
  containerClass: 'flex-1 relative scrollbar-gutter scrollbar-thin scrollbar-track-transparent scrollbar-thumb-transparent hover:scrollbar-thumb-base-content/25 scrollbar-h-[10px] w-full duration-300 transition-all overflow-y-auto',
  contentClass: 'chron w-full max-w-4xl lg:max-w-6xl xl:max-w-7xl 2xl:max-w-none mx-auto prose prose-sm text-base-content',
  collaborative: true,
  readonly: false,
  isEmbedded: false
})

const emit = defineEmits<{
  (e: 'title-changed', title: string): void
}>()

const canScroll = ref(true)
const editor = ref<Editor>()
const localProvider = ref<LocalProvider>()

const sqlitePersistence = ref<SQLitePersistence>()
const ydoc = new Y.Doc() // 创建一个ydoc实例，放在组件顶层作用域
const blockService = useBlockService() // 将blockService创建放在组件顶层作用域
const blockContent = ref<string | undefined>()
const isInitializing = ref(true)
const isSaving = ref(false)

// 存储待处理的drop数据
const pendingDropData = ref<{
  data: any
  position: { x: number; y: number }
  view: any
  event: DragEvent
} | null>(null)

const insertChoiceMenu = ref<InstanceType<typeof InsertChoiceMenu>>()

// 新增：编辑器容器引用
const editorContainer = ref<HTMLElement | null>(null)

// 添加焦点跟踪器
const { handleFocus, handleBlur } = useFocusTracker({
  label: 'Note Editor',
  thresholds: [1, 5, 30, 60, 120]
})

// 监听保存快捷键
const { ctrl_s, meta_s } = useMagicKeys()

// TODO 暂时是假效果，后面改为强制同步
watch([ctrl_s, meta_s], ([ctrlS, metaS]) => {
  if ((ctrlS || metaS) && !props.readonly) {
    showSavingAnimation()
  }
})

onBeforeMount(async () => {
  // 先从数据库获取文档内容
  const updateService = new UpdateService()
  const block = await blockService.getBlockByUuid(props.node.uuid)
  blockContent.value = block?.contents

  if (props.collaborative) {
    sqlitePersistence.value = new SQLitePersistence(props.node.uuid, ydoc)
    sqlitePersistence.value!.on('synced', () => {
      const { doc } = editor.value!.state
      if (doc.childCount === 2) {
        const node = doc.firstChild

        if (node && !node.attrs['id']) {
          // Create a transaction with the meta property set before dispatching
          const { tr } = editor.value!.state

          tr.setNodeMarkup(0, undefined, {
            ...node.attrs,
            id: v4()
          })
          tr.setMeta('addToHistory', false)

          // Dispatch the transaction only once
          editor.value?.view.dispatch(tr)
          console.log(node.attrs)
        }
        console.log(doc.child(1).attrs, 'ATTR')
        if (doc.child(1) && !doc.child(1).attrs['id']) {
          const { tr } = editor.value!.state
          tr.setNodeMarkup(2, undefined, {
            ...doc.child(1).attrs,
            id: v4()
          })
          tr.setMeta('addToHistory', false)
          editor.value?.view.dispatch(tr)
        }
      }
    })
    localProvider.value = new LocalProvider(props.node.uuid, ydoc)
  }
})

onMounted(async () => {
  // 确保所有数据加载完成后再初始化编辑器
  const extensions = [...standardExtensions]
  if (props.collaborative) {
    extensions.push(Collaboration.configure({
      document: ydoc
    }))
  }

  editor.value = new Editor({
    extensions,
    content: blockContent.value,
    editable: !props.readonly,
    onCreate: async () => {
      editor.value!.commands.setSearchTerm('')
      isInitializing.value = false
    },

    onUpdate: ({ editor }) => {
      // 只有在不是初始化状态时才更新存储
      if (isInitializing.value) {
        console.log('isInitializing')
        return
      }
      // 只读模式下不更新存储
      if (props.readonly) {
        return
      }
      const titleJSON = editor.getJSON()!.content![0]!
      if (titleJSON && titleJSON.content && titleJSON.content![0]) {
        const title = (editor.getJSON()!.content![0]!.content![0] as any).text
        const name = props.node?.title

        if (title === '') {
          props.node!.title = '新笔记'
        }
        if (title != name) {
          const newTitle = extractTextContent(titleJSON)
          props.node!.title = newTitle
          props.node.rename(newTitle)
          // 发送标题更改事件
          emit('title-changed', newTitle)
        }
      }
      const content = editor.getHTML()

      blockService.updateBlock({
        uuid: props.node.uuid,
        contents: content
      })
    },

    onTransaction: () => {
      // Transaction handling can be implemented here if needed
    },

    editorProps: {
      handleDrop: (view, event) => {
        if (!event.dataTransfer) return false
        if (props.readonly) return false

        const data = event.dataTransfer.getData('application/json')
        if (!data) return false

        try {
          const parsedData = JSON.parse(data)

          // 获取drop位置
          const dropPos = view.posAtCoords({ left: event.clientX, top: event.clientY })
          if (!dropPos) return false

          // 存储drop数据和位置信息
          pendingDropData.value = {
            data: parsedData,
            position: { x: event.clientX, y: event.clientY },
            view,
            event
          }

          // 设置光标位置
          const { pos } = dropPos
          // 确保位置有效 - ProseMirror中位置必须 >= 1
          const validPos = Math.max(1, Math.min(pos, view.state.doc.content.size))

          try {
            const tr = view.state.tr.setSelection(TextSelection.create(view.state.doc, validPos))
            tr.scrollIntoView()
            view.dispatch(tr)
          } catch (error) {
            console.warn('Failed to set cursor position:', error)
            // 如果设置位置失败，尝试设置到文档末尾
            try {
              const endPos = view.state.doc.content.size
              const tr = view.state.tr.setSelection(TextSelection.create(view.state.doc, endPos))
              view.dispatch(tr)
            } catch (fallbackError) {
              console.error('Failed to set fallback cursor position:', fallbackError)
            }
          }

          // 异步计算位置并显示菜单
          const calculatePositionAndShow = async () => {
            // 创建虚拟参考元素用于floating-ui计算
            const virtualReference = {
              getBoundingClientRect: () =>
                ({
                  width: 0,
                  height: 0,
                  x: event.clientX,
                  y: event.clientY,
                  top: event.clientY,
                  left: event.clientX,
                  right: event.clientX,
                  bottom: event.clientY,
                  toJSON: () => ({})
                }) as DOMRect
            }

            // 创建一个临时的菜单元素来计算位置
            const tempMenu = document.createElement('div')
            tempMenu.style.position = 'fixed'
            tempMenu.style.visibility = 'hidden'
            tempMenu.style.width = '256px' // min-w-64 = 16rem = 256px
            tempMenu.style.height = '200px' // 估算菜单高度
            document.body.appendChild(tempMenu)

            try {
              // 使用 floating-ui 计算最佳位置
              const { x, y } = await computePosition(virtualReference, tempMenu, {
                placement: 'bottom-start',
                middleware: [
                  offset(5), // 距离参考点5px
                  flip(), // 自动翻转避免边界溢出
                  shift({ padding: 8 }) // 保持8px的边距
                ],
                strategy: 'fixed'
              })

              // 显示菜单，传递计算好的位置
              insertChoiceMenu.value?.show(parsedData, { x, y })
            } catch (error) {
              console.error('Error computing position:', error)
              // 降级到原始位置
              insertChoiceMenu.value?.show(parsedData, { x: event.clientX, y: event.clientY })
            } finally {
              // 清理临时元素
              document.body.removeChild(tempMenu)
            }
          }

          // 立即执行异步计算
          calculatePositionAndShow()

          return true
        } catch (error) {
          console.error('Error parsing drop data:', error)
          return false
        }
      },

      handlePaste: (view, event) => {
        // 检查是否有剪贴板数据
        if (!event.clipboardData) return false
        if (props.readonly) return false

        // 获取粘贴的纯文本
        const plainText = event.clipboardData.getData('text/plain')

        // 检测是否为URL链接
        const urlRegex =
          /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z]{2,}\b(?:[-a-zA-Z0-9@:%._+~#=?!&\/]*)/i

        if (urlRegex.test(plainText.trim())) {
          // 阻止默认粘贴行为
          event.preventDefault()

          // 获取当前光标位置
          const { state } = view
          const { selection } = state
          const { from } = selection

          // 创建链接数据对象
          const linkData = {
            type: 'url' as const,
            url: plainText.trim(),
            text: plainText.trim()
          }

          // 存储链接数据和位置信息
          pendingDropData.value = {
            data: linkData,
            position: { x: 0, y: 0 }, // 粘贴时位置会在下面计算
            view,
            event: event as any // 临时类型转换
          }

          // 异步计算光标位置并显示菜单
          const calculatePositionAndShow = async () => {
            // 获取光标的DOM位置
            const coords = view.coordsAtPos(from)

            // 创建虚拟参考元素用于floating-ui计算
            const virtualReference = {
              getBoundingClientRect: () =>
                ({
                  width: 0,
                  height: 0,
                  x: coords.left,
                  y: coords.bottom,
                  top: coords.bottom,
                  left: coords.left,
                  right: coords.left,
                  bottom: coords.bottom,
                  toJSON: () => ({})
                }) as DOMRect
            }

            // 创建一个临时的菜单元素来计算位置
            const tempMenu = document.createElement('div')
            tempMenu.style.position = 'fixed'
            tempMenu.style.visibility = 'hidden'
            tempMenu.style.width = '256px' // min-w-64 = 16rem = 256px
            tempMenu.style.height = '200px' // 估算菜单高度
            document.body.appendChild(tempMenu)

            try {
              // 使用 floating-ui 计算最佳位置
              const { x, y } = await computePosition(virtualReference, tempMenu, {
                placement: 'bottom-start',
                middleware: [
                  offset(5), // 距离参考点5px
                  flip(), // 自动翻转避免边界溢出
                  shift({ padding: 8 }) // 保持8px的边距
                ],
                strategy: 'fixed'
              })

              // 显示菜单，传递计算好的位置
              insertChoiceMenu.value?.show(linkData, { x, y })
            } catch (error) {
              console.error('Error computing position:', error)
              // 降级到光标位置
              insertChoiceMenu.value?.show(linkData, { x: coords.left, y: coords.bottom })
            } finally {
              // 清理临时元素
              document.body.removeChild(tempMenu)
            }
          }

          // 立即执行异步计算
          calculatePositionAndShow()

          return true
        }

        // 不是URL链接，让其他处理器处理
        return false
      },

      handleKeyDown(view, event) {
        if (event.key === 'f' && (event.ctrlKey || event.metaKey)) {
          event.preventDefault()

          // 获取当前选中的文本
          let selectedText = ''
          const { state } = view
          const { selection } = state

          // 只有当选择不为空时才获取选中的文本
          if (!selection.empty) {
            const { from, to } = selection
            selectedText = state.doc.textBetween(from, to, '')
          }

          // 使用选中的文本打开搜索对话框
          const { showDialog } = useSearchDialog(editor.value!)
          showDialog(selectedText)
        }
      }
    }
  })

  editor.value.on('transaction', (tr) => {
    const meta = tr.transaction.getMeta(slashPluginKey)
    if (!meta || meta.shouldLock === undefined) return
    canScroll.value = !meta.shouldLock
  })

  // 修改事件监听器
  editor.value?.on('focus', handleFocus)
  editor.value?.on('blur', handleBlur)
})

onBeforeUnmount(() => {
  // 清理搜索对话框状态
  if (editor.value) {
    const { cleanup } = useSearchDialog(editor.value)
    cleanup()
  }

  editor.value?.destroy()

  // 修改事件监听器移除
  editor.value?.off('focus', handleFocus)
  editor.value?.off('blur', handleBlur)
})

const extractTextContent = (node: JSONContent) => {
  if (node.type != 'text') {
  }
  let textContent = ''

  if (node.type === 'text') {
    textContent += node.text
  }
  if (node.content) {
    node.content.forEach((childNode) => {
      textContent += extractTextContent(childNode)
    })
  }
  return textContent
}

// 处理插入AI解析知识块
const handleInsertAIKnowledgeBlock = (dropData: any) => {
  if (!editor.value || !pendingDropData.value) return
  if (props.readonly) return

  const { annotations, fromText, uuid } = dropData

  // 发送事件通知PDF
  mitter.emit(uuid, { annotations })

  // 获取源文件信息
  const nodeStore = useNodeStore()
  const sourceNode = nodeStore.getNoteByUUID(uuid)
  const sourceTitle = sourceNode?.title || 'Unknown Source'

  // 创建AI解析知识块
  const knowledgeBlockData = {
    type: 'ai-parsed', // AI解析类型
    source: uuid,
    title: sourceTitle,
    extra: `页面 ${annotations[0]?.sortIndex || ''} - AI解析`,
    contents: fromText
  }

  editor.value.commands.setKnowledgeBlock(knowledgeBlockData)

  // 清理待处理数据
  pendingDropData.value = null
}

// 处理插入手动构建知识块
const handleInsertManualKnowledgeBlock = (dropData: any) => {
  if (!editor.value || !pendingDropData.value) return
  if (props.readonly) return

  const { annotations, fromText, uuid } = dropData

  // 发送事件通知PDF
  mitter.emit(uuid, { annotations })

  // 获取源文件信息
  const nodeStore = useNodeStore()
  const sourceNode = nodeStore.getNoteByUUID(uuid)
  const sourceTitle = sourceNode?.title || 'Unknown Source'

  // 创建手动构建知识块
  const knowledgeBlockData = {
    type: 'manual', // 手动构建类型
    source: uuid,
    title: sourceTitle,
    extra: `页面 ${annotations[0]?.sortIndex || ''} - 手动构建`,
    contents: fromText
  }

  editor.value.commands.setKnowledgeBlock(knowledgeBlockData)

  // 清理待处理数据
  pendingDropData.value = null
}

// 处理插入链接（原有逻辑）
const handleInsertAnchorLink = (dropData: any) => {
  if (!editor.value || !pendingDropData.value) return
  if (props.readonly) return

  const { annotations, fromText, uuid } = dropData

  // 发送事件通知PDF
  mitter.emit(uuid, { annotations })

  // 创建block anchor（原有逻辑）
  const blockAnchor = {
    type: 'text',
    text: fromText,
    marks: [
      {
        type: 'blockAnchor',
        attrs: {
          symbolId: v4(),
          id: uuid,
          sourceId: uuid,
          sortIndex: annotations[0].sortIndex
        }
      }
    ]
  }

  editor.value.commands.insertContent(blockAnchor)

  // 清理待处理数据
  pendingDropData.value = null
}

// 处理插入普通链接
const handleInsertPlainLink = (linkData: { type: 'url'; url: string; text: string }) => {
  if (!editor.value || !pendingDropData.value) return
  if (props.readonly) return

  // 插入普通链接
  const linkContent = {
    type: 'text',
    text: linkData.text,
    marks: [
      {
        type: 'link',
        attrs: {
          href: linkData.url,
          target: '_blank'
        }
      }
    ]
  }

  editor.value.commands.insertContent(linkContent)

  // 清理待处理数据
  pendingDropData.value = null
}

// 处理插入书签
const handleInsertBookmark = (linkData: { type: 'url'; url: string; text: string }) => {
  if (!editor.value || !pendingDropData.value) return
  if (props.readonly) return

  // 创建书签样式的HTML内容
  const bookmarkHtml = `
    <div class="bookmark-card border border-base-300 rounded-lg p-4 my-2 bg-base-200/50">
      <div class="flex items-center gap-3">
        <div class="w-12 h-12 bg-accent/20 rounded-lg flex items-center justify-center">
          <svg class="w-6 h-6 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.102m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
          </svg>
        </div>
        <div class="flex-1">
          <h3 class="font-medium text-base-content">网页链接</h3>
          <p class="text-sm text-base-content/60 truncate">${linkData.url}</p>
        </div>
        <a href="${linkData.url}" target="_blank" class="btn btn-sm btn-ghost">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
          </svg>
        </a>
      </div>
    </div>
  `

  editor.value.commands.insertContent(bookmarkHtml)

  // 清理待处理数据
  pendingDropData.value = null
}

// 处理插入嵌入内容
const handleInsertEmbed = (linkData: { type: 'url'; url: string; text: string }) => {
  if (!editor.value || !pendingDropData.value) return
  if (props.readonly) return

  // 使用 iframe 扩展的 setIframe 命令，支持自定义尺寸
  editor.value.commands.setIframe({
    src: linkData.url,
    width: '800px',
    height: '500px'
  })

  // 清理待处理数据
  pendingDropData.value = null
}

// 处理取消
const handleInsertCancel = () => {
  pendingDropData.value = null
}

// 添加保存动画逻辑
const showSavingAnimation = () => {
  isSaving.value = true
  setTimeout(() => {
    isSaving.value = false
  }, 400)
}

// 处理编辑器容器点击
const handleEditorContainerClick = (event: MouseEvent) => {
  // 如果当前存在有效的文本选区（非折叠），说明用户刚完成拖拽选中，忽略此次 click，避免破坏选区
  const browserSel = window.getSelection()
  if (browserSel && !browserSel.isCollapsed) return

  if (!editor.value) return
  if (props.readonly) return

  const proseEl = editorContainer.value?.querySelector('.ProseMirror') as HTMLElement | null
  if (!proseEl) return

  const rect = proseEl.getBoundingClientRect()

  // 1. 点击在文档下方空白处：插入空段落并聚焦到末尾
  if (event.clientY > rect.bottom) {
    const state = editor.value.state
    const lastNode = state.doc.lastChild
    const hasEmptyTail =
      lastNode && lastNode.type.name === 'paragraph' && lastNode.textContent === ''

    if (!hasEmptyTail) {
      const endPosBefore = state.doc.content.size
      // 直接在文档末尾插入空段落，避免使用当前 selection 造成内容错位
      editor.value.commands.insertContentAt(endPosBefore, { type: 'paragraph' })
    }

    // 无论是否插入，都把光标聚焦到文档末尾
    const newEnd = editor.value.state.doc.content.size
    const tr = editor.value.state.tr.setSelection(
      TextSelection.create(editor.value.state.doc, newEnd)
    )
    editor.value.view.dispatch(tr)
    editor.value.commands.focus('end')
    return
  }

  // 2. 点击在左右留白处：将横坐标夹在 ProseMirror 区域内
  let x = event.clientX
  let horizontalBlank = false
  if (x < rect.left) {
    x = rect.left + 5
    horizontalBlank = true
  }
  if (x > rect.right) {
    x = rect.right - 5
    horizontalBlank = true
  }

  const posInfo = editor.value.view.posAtCoords({ left: x, top: event.clientY })
  if (posInfo) {
    const { pos } = posInfo
    const state = editor.value.state
    const $pos = state.doc.resolve(pos)
    const parentNode = $pos.node($pos.depth)

    // 默认使用精确位置
    let targetPos = pos

    // 如果点击在左右留白（horizontalBlank）且所在节点为文本块，则跳到块尾
    if (horizontalBlank && parentNode.isTextblock) {
      targetPos = $pos.end($pos.depth)
      // 若文本块为空，保持 pos（即块开始）
      if (parentNode.content.size === 0) {
        targetPos = pos
      }
    }

    const tr = state.tr.setSelection(TextSelection.create(state.doc, targetPos))
    editor.value.view.dispatch(tr)
    editor.value.commands.focus()
  }
}

// 暴露编辑器实例和方法
defineExpose({
  editor,
  showSavingAnimation
})
</script>

<style scoped lang="scss">
@import 'tailwindcss';
@plugin 'tailwind-scrollbar' {
  nocompatible: true;
}
.scrollbar-gutter {
  scrollbar-gutter: stable;
}

@keyframes fast-spin {
  from {
    transform: rotate(180deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: fast-spin 0.4s ease-in-out infinite;
}

// 菜单动画
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.2s ease-out;
}

// iframe resize 样式
:deep(.iframe-resizable-wrapper) {
  // 确保 resize 手柄可见
  &:hover {
    border-color: #3b82f6;
  }

  // 优化 resize 手柄的样式
  &::-webkit-resizer {
    background: linear-gradient(
      -45deg,
      transparent 0px,
      transparent 2px,
      #94a3b8 2px,
      #94a3b8 4px,
      transparent 4px
    );
    border-radius: 0 0 4px 0;
    width: 16px;
    height: 16px;
  }

  // 确保iframe在resize时保持比例
  iframe {
    transition: none; // 移除过渡效果以获得更好的resize体验
    pointer-events: auto;
  }

  // 确保容器可以被resize
  box-sizing: border-box;

  // 确保不会超出父容器边界，但允许水平resize
  max-width: calc(100% - 32px); // 留出一些边距
}

// 为编辑器内的iframe添加全局样式
:deep(.ProseMirror) {
  .iframe-resizable-wrapper {
    // 确保在编辑器中正确显示
    display: block;

    // 添加选中状态的样式
    &.ProseMirror-selectednode {
      border-color: #3b82f6;
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
    }

    // 确保编辑器中的iframe可以被resize
    resize: both;
    overflow: auto;
  }
}
</style>