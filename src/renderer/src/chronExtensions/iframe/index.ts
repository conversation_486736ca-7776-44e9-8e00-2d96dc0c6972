import { Node } from '@tiptap/core'

export interface IframeOptions {
    allowFullscreen: boolean,
    HTMLAttributes: {
        [key: string]: any
    },
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        iframe: {
            /**
             * Add an iframe
             */
            setIframe: (options: { src: string; width?: string; height?: string }) => ReturnType,
        }
    }
}

export default Node.create<IframeOptions>({
    name: 'iframe',

    group: 'block',

    atom: true,

    addOptions() {
        return {
            allowFullscreen: true,
            HTMLAttributes: {
                class: 'iframe-resizable-wrapper',
            },
        }
    },

    addAttributes() {
        return {
            src: {
                default: null,
            },
            width: {
                default: '800px',
                parseHTML: element => element.getAttribute('width'),
                renderHTML: attributes => {
                    if (!attributes.width) {
                        return {}
                    }
                    return {
                        width: attributes.width,
                    }
                },
            },
            height: {
                default: '400px',
                parseHTML: element => element.getAttribute('height'),
                renderHTML: attributes => {
                    if (!attributes.height) {
                        return {}
                    }
                    return {
                        height: attributes.height,
                    }
                },
            },
            frameborder: {
                default: 0,
            },
            allowfullscreen: {
                default: this.options.allowFullscreen,
                parseHTML: () => this.options.allowFullscreen,
            },
        }
    },

    parseHTML() {
        return [{
            tag: 'div.iframe-resizable-wrapper',
            getAttrs: (element) => {
                const iframe = element.querySelector('iframe')
                if (!iframe) return false

                return {
                    src: iframe.getAttribute('src'),
                    width: element.style.width || '800px',
                    height: element.style.height || '400px',
                }
            }
        }]
    },

    renderHTML({ HTMLAttributes }) {
        const { src, width, height, ...iframeAttrs } = HTMLAttributes

        return [
            'div',
            {
                ...this.options.HTMLAttributes,
                style: `
                    width: ${width || '800px'}; 
                    height: ${height || '400px'}; 
                    resize: both; 
                    overflow: auto;
                    min-width: 200px;
                    min-height: 150px;
                    max-width: 100%;
                    border: 1px solid #e5e7eb;
                    position: relative;
                    margin: 16px 0;
                `
            },
            [
                'iframe',
                {
                    ...iframeAttrs,
                    src,
                    style: 'width: 100%; height: 100%; border: none;'
                }
            ]
        ]
    },

    addCommands() {
        return {
            setIframe: (options: { src: string; width?: string; height?: string }) => ({ tr, dispatch }) => {
                const { selection } = tr
                const node = this.type.create({
                    src: options.src,
                    width: options.width || '800px',
                    height: options.height || '400px'
                })

                if (dispatch) {
                    tr.replaceRangeWith(selection.from, selection.to, node)
                }

                return true
            },
        }
    },
})