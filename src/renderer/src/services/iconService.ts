/**
 * 图标服务 - 整合扫描、配置和搜索功能
 * 提供统一的图标管理接口
 */

import {
  iconScanner,
  type IconMetadata,
  type CategoryMetadata,
  type ScanResult
} from '../utils/iconScanner'
import {
  iconCategories,
  iconEnhancements,
  type IconCategory,
  type IconEnhancement
} from '../config/iconConfig'
import { getAllSvgIcons } from '../utils/svgIcons'

// 服务状态
export interface ServiceStatus {
  isInitialized: boolean
  isLoading: boolean
  lastUpdate: string
  iconCount: number
  categoryCount: number
  configLevel: string
  error?: string
}

// 图标服务选项
export interface IconServiceOptions {
  autoScan?: boolean
  forceRescan?: boolean
}

/**
 * 图标服务类 - 整合所有图标相关功能
 */
export class IconService {
  private static instance: IconService
  private status: ServiceStatus = {
    isInitialized: false,
    isLoading: false,
    lastUpdate: '',
    iconCount: 0,
    categoryCount: 0,
    configLevel: 'Minimal'
  }

  // 缓存的处理后数据
  private processedIcons: IconMetadata[] = []
  private processedCategories: CategoryMetadata[] = []

  // 缓存管理
  private cacheValidUntil: number = 0
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

  static getInstance(): IconService {
    if (!IconService.instance) {
      IconService.instance = new IconService()
    }
    return IconService.instance
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(): boolean {
    return Date.now() < this.cacheValidUntil && this.status.isInitialized
  }

  /**
   * 更新缓存时间戳
   */
  private updateCacheTimestamp(): void {
    this.cacheValidUntil = Date.now() + this.CACHE_DURATION
  }

  /**
   * 初始化图标服务
   */
  async initialize(options: IconServiceOptions = {}): Promise<void> {
    const { autoScan = true, forceRescan = false } = options

    // 如果缓存有效且不强制重新扫描，直接返回
    if (this.isCacheValid() && !forceRescan) {
      console.log('✅ 图标服务缓存有效，跳过初始化')
      return
    }

    this.status.isLoading = true
    this.status.error = undefined

    try {
      console.log('🚀 初始化图标服务...')

      let scanResult: ScanResult | null = null

      // 执行扫描
      if (autoScan) {
        scanResult = await this.performFullScan(forceRescan)
      }

      // 应用配置增强
      await this.applyConfigurations(scanResult)

      // 更新状态和缓存
      this.status.isInitialized = true
      this.status.lastUpdate = new Date().toISOString()
      this.status.iconCount = this.processedIcons.length
      this.status.categoryCount = this.processedCategories.length
      this.updateCacheTimestamp()

      // 获取配置状态
      this.status.configLevel = 'Basic' // 简化配置等级

      console.log(
        `✅ 图标服务初始化完成: ${this.status.iconCount}个图标, ${this.status.categoryCount}个分类, 配置等级: ${this.status.configLevel}`
      )
    } catch (error) {
      this.status.error = error instanceof Error ? error.message : 'Unknown error'
      console.error('❌ 图标服务初始化失败:', error)
      throw error
    } finally {
      this.status.isLoading = false
    }
  }

  /**
   * 执行完整扫描
   */
  private async performFullScan(forceRescan: boolean): Promise<ScanResult> {
    console.log('🔍 开始图标扫描...')

    const scanResult = await iconScanner.scanAllIcons(forceRescan)

    if (scanResult.newCategories.length > 0 || scanResult.newIcons.length > 0) {
      console.log(
        `📈 发现新内容: ${scanResult.newCategories.length}个新分类, ${scanResult.newIcons.length}个新图标`
      )
    }

    return scanResult
  }

  /**
   * 应用配置增强
   */
  private async applyConfigurations(scanResult: ScanResult | null = null): Promise<void> {
    console.log('⚙️ 应用配置增强...')

    // 如果没有传入扫描结果，则执行扫描
    if (!scanResult) {
      scanResult = await iconScanner.scanAllIcons(false)
    }

    // 应用配置增强 - 使用新的配置结构
    const categories = scanResult.categories.map((cat) => {
      const configCat = iconCategories.find((c) => c.id === cat.id)
      return configCat
        ? {
            ...cat,
            labelZh: configCat.labelZh,
            labelEn: configCat.labelEn,
            label: configCat.labelZh || cat.label
          }
        : cat
    })

    const icons = scanResult.icons.map((icon) => {
      const enhancement = iconEnhancements.find((e) => e.id === icon.id)
      return enhancement
        ? {
            ...icon,
            displayName: enhancement.displayName || icon.displayName,
            aliases: [...new Set([...icon.aliases, ...enhancement.aliases])],
            tags: [...new Set([...icon.tags, ...enhancement.tags])],
            autoGenerated: false
          }
        : icon
    })

    this.processedCategories = categories
    this.processedIcons = icons

    console.log(`✨ 配置增强完成: ${categories.length}个分类, ${icons.length}个图标`)
  }

  /**
   * 简单搜索图标
   */
  searchIcons(query: string, category?: string): IconMetadata[] {
    if (!this.status.isInitialized) {
      console.warn('⚠️ 图标服务未初始化，返回空结果')
      return []
    }

    let filtered = this.processedIcons

    // 分类过滤
    if (category) {
      filtered = filtered.filter((icon) => icon.category === category)
    }

    // 搜索过滤
    if (query.trim()) {
      const normalizedQuery = query.toLowerCase()
      filtered = filtered.filter((icon) => {
        const searchTexts = [
          icon.name,
          icon.displayName,
          icon.categoryLabel,
          ...icon.aliases,
          ...icon.tags
        ]

        // 如果有拼音，也加入搜索
        if (icon.pinyin) searchTexts.push(icon.pinyin)
        if (icon.pinyinShort) searchTexts.push(icon.pinyinShort)

        // 检查是否有任何文本包含查询词
        return searchTexts.some((text) => text && text.toLowerCase().includes(normalizedQuery))
      })
    }

    return filtered
  }

  /**
   * 获取所有图标
   */
  getAllIcons(categoryFilter?: string): IconMetadata[] {
    if (!this.status.isInitialized) {
      return []
    }

    if (categoryFilter) {
      return this.processedIcons.filter((icon) => icon.category === categoryFilter)
    }

    return [...this.processedIcons]
  }

  /**
   * 获取所有分类
   */
  getAllCategories(): CategoryMetadata[] {
    if (!this.status.isInitialized) {
      return []
    }

    return [...this.processedCategories]
  }

  /**
   * 获取特定图标
   */
  getIcon(iconId: string): IconMetadata | undefined {
    return this.processedIcons.find((icon) => icon.id === iconId)
  }

  /**
   * 获取分类信息
   */
  getCategory(categoryId: string): CategoryMetadata | undefined {
    return this.processedCategories.find((category) => category.id === categoryId)
  }

  /**
   * 重新扫描图标
   */
  async rescan(forceRescan = true): Promise<void> {
    console.log('🔄 重新扫描图标...')

    this.status.isLoading = true
    try {
      const scanResult = await this.performFullScan(forceRescan)
      await this.applyConfigurations(scanResult)

      this.status.lastUpdate = new Date().toISOString()
      this.status.iconCount = this.processedIcons.length
      this.status.categoryCount = this.processedCategories.length
      this.updateCacheTimestamp()

      console.log('✅ 重新扫描完成')
    } finally {
      this.status.isLoading = false
    }
  }

  /**
   * 获取服务状态
   */
  getStatus(): ServiceStatus {
    return { ...this.status }
  }

  /**
   * 获取统计信息
   */
  getStats(): {
    service: ServiceStatus
    config: {
      categoriesConfigured: number
      iconsEnhanced: number
      totalCategories: number
      configurationLevel: string
    }
    scan: ReturnType<typeof iconScanner.getScanStats>
  } {
    return {
      service: this.getStatus(),
      config: {
        categoriesConfigured: iconCategories.length,
        iconsEnhanced: iconEnhancements.length,
        totalCategories: iconCategories.length,
        configurationLevel: 'Basic'
      },
      scan: iconScanner.getScanStats()
    }
  }

  /**
   * 获取热门图标（简单返回前几个）
   */
  getPopularIcons(limit = 10): IconMetadata[] {
    return this.processedIcons.slice(0, limit)
  }

  /**
   * 获取推荐图标（基于查询）
   */
  getRecommendedIcons(query: string, limit = 5): IconMetadata[] {
    if (!query.trim()) {
      return this.getPopularIcons(limit)
    }

    const searchResults = this.searchIcons(query)
    return searchResults.slice(0, limit)
  }

  /**
   * 清理缓存
   */
  clearCache(): void {
    console.log('🗑️ 清理缓存...')

    // 清理扫描缓存
    iconScanner.clearCache()

    // 重置处理后的数据
    this.processedIcons = []
    this.processedCategories = []

    // 重置状态和缓存
    this.status.isInitialized = false
    this.status.lastUpdate = ''
    this.status.iconCount = 0
    this.status.categoryCount = 0
    this.cacheValidUntil = 0

    console.log('✅ 缓存清理完成')
  }

  /**
   * 检查是否需要更新
   */
  needsUpdate(): boolean {
    if (!this.status.isInitialized) {
      return true
    }

    // 使用缓存有效性检查
    return !this.isCacheValid()
  }

  /**
   * 预热服务（预加载关键数据）
   */
  async warmup(): Promise<void> {
    if (this.status.isInitialized) {
      return
    }

    console.log('🔥 预热图标服务...')

    try {
      // 快速加载基础数据
      const svgIcons = getAllSvgIcons()
      console.log(`📦 预加载 ${svgIcons.length} 个SVG图标`)

      // 加载配置
      console.log(`⚙️ 加载配置: ${iconCategories.length} 个分类已配置`)

      console.log('🔥 预热完成')
    } catch (error) {
      console.warn('预热失败:', error)
    }
  }
}

// 导出单例实例
export const iconService = IconService.getInstance()

// 便捷方法导出
export const initializeIconService = (options?: IconServiceOptions) =>
  iconService.initialize(options)

export const searchIcons = (query: string, category?: string) =>
  iconService.searchIcons(query, category)

export const getAllIcons = (categoryFilter?: string) => iconService.getAllIcons(categoryFilter)

export const getAllCategories = () => iconService.getAllCategories()

export const getIconServiceStats = () => iconService.getStats()
