<template>
  <EditorContent
    :editor="editor"
    class="w-full h-full px-0 -ml-2 todo-title whitespace-pre-wrap"
    style="padding-inline: 0 !important"
  />
</template>

<script setup lang="ts">
import { TextSelection } from '@tiptap/pm/state'
import { Editor, EditorContent } from '@tiptap/vue-3'
import { onBeforeUnmount, onMounted, ref, watch } from 'vue'

import { todoTitle } from '@renderer/editor/documentStructure/todoTitle'
import { MentionNode } from '@renderer/editor/plugins/function/mention/InlineMention'
import mention from '@renderer/editor/plugins/function/mention/mention'
import { Text } from '@tiptap/extension-text'
import { v4 } from 'uuid'

const props = defineProps<{
  modelValue?: string
  readonly?: boolean
  autoFocus?: boolean
  placeholder?: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'enter'): void
}>()

const editor = ref<Editor>()

// 检查编辑器是否为空并更新 placeholder 类
const updatePlaceholderClass = () => {
  if (!editor.value) return

  const isEmpty = editor.value.isEmpty
  const proseMirrorElement = editor.value.view.dom as HTMLElement

  if (isEmpty) {
    proseMirrorElement.classList.add('is-empty')
  } else {
    proseMirrorElement.classList.remove('is-empty')
  }
}

onMounted(async () => {
  const content = props.modelValue || ''

  // 兼容性处理：如果是纯文本，自动添加p标签包裹
  let processedContent = content
  if (content && !content.includes('<') && !content.includes('>')) {
    processedContent = `<p>${content}</p>`
  }

  editor.value = new Editor({
    extensions: [mention, todoTitle, Text, MentionNode],
    content: processedContent,
    editable: !props.readonly,

    onDestroy: () => {
      // Clean up will be handled in onBeforeUnmount
    },

    onCreate: async () => {
      // 只有在非只读模式且允许自动聚焦时才自动聚焦
      if (!props.readonly && props.autoFocus !== false) {
        editor.value!.commands.focus('end')
      }
      updatePlaceholderClass()
    },

    onUpdate: ({ editor }) => {
      // 只有在非只读模式时才更新
      if (!props.readonly) {
        const content = editor.getHTML()
        emit('update:modelValue', content)
      }
      updatePlaceholderClass()
    },

    editorProps: {
      attributes: {
        class: 'px-0 single-line',
        'data-placeholder': props.placeholder || '输入标题...'
      },

      handleKeyDown: (_, event) => {
        // 阻止回车键创建新行
        if (event.key === 'Enter') {
          emit('enter')
          event.preventDefault()
          return true
        }

        // 阻止其他可能创建换行的按键
        if ((event.ctrlKey && event.key === 'Enter') || (event.shiftKey && event.key === 'Enter')) {
          event.preventDefault()
          return true
        }

        return false
      },

      handleDrop: (view, event) => {
        if (!event.dataTransfer) return false

        const data = event.dataTransfer.getData('application/json')
        if (!data) return false

        const { annotations, fromText, uuid } = JSON.parse(data)

        // Get position from drop coordinates
        const dropPos = view.posAtCoords({ left: event.clientX, top: event.clientY })
        if (dropPos) {
          // Set cursor to the drop position
          const { pos } = dropPos
          const tr = view.state.tr.setSelection(TextSelection.create(view.state.doc, pos))
          tr.scrollIntoView()
          view.dispatch(tr)
        }

        const blockAnchor = {
          type: 'text',
          text: fromText,
          marks: [
            {
              type: 'blockAnchor',
              attrs: {
                symbolId: v4(),
                id: uuid,
                sourceId: uuid,
                sortIndex: annotations[0].sortIndex
              }
            }
          ]
        }
        editor.value!.commands.insertContent(blockAnchor)

        return true
      }
    }
  })
})

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (!editor.value) return

    const currentContent = editor.value.getHTML()
    if (currentContent !== newValue) {
      editor.value.commands.setContent(newValue || '', { emitUpdate: false })
      updatePlaceholderClass()
    }
  }
)

// 监听 readonly 属性变化
watch(
  () => props.readonly,
  (newReadonly) => {
    if (editor.value) {
      editor.value.setEditable(!newReadonly)
    }
  }
)

onBeforeUnmount(() => {
  editor.value?.destroy()
})
</script>

<style scoped lang="scss">
.scrollbar-gutter {
  scrollbar-gutter: stable;
}

// 确保编辑器为单行显示
:deep(.ProseMirror) {
  white-space: pre-wrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  line-height: 1.5 !important;
  min-height: auto !important;

  // 移除默认的段落间距
  p {
    margin: 0 !important;
    padding: 0 !important;
    display: inline !important;
  }

  // 确保没有换行
  br {
    display: none !important;
  }
}

.todo-title {
  .single-line {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

:deep(.ProseMirror.is-empty::before) {
  content: attr(data-placeholder);
  color: #9ca3af;
  pointer-events: none;
  position: absolute;
  opacity: 0.6;
}
</style>
