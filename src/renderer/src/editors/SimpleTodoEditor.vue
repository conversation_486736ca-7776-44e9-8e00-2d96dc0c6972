<template>
  <EditorContent :editor="editor" class="todo" />
</template>

<script setup lang="ts">
import { Editor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import { Placeholder } from '@tiptap/extensions'
import { onBeforeUnmount, onMounted, ref, watch, nextTick } from 'vue'
import { MentionNode } from '@renderer/editor/plugins/function/mention/InlineMention'
import mention from '@renderer/editor/plugins/function/mention/mention'
import { TextSelection } from '@tiptap/pm/state'
import { v4 } from 'uuid'
import { CustomFileHandlePlugin } from '@renderer/editor/plugins/function/fileHandler'
import { customImg } from '@renderer/editor/plugins/node/image'

const props = withDefaults(
  defineProps<{
    modelValue?: string
    placeholder?: string
    readonly?: boolean
    autoFocus?: boolean
  }>(),
  {
    placeholder: '键入任务的详细内容，可以使用@来引用其他资源',
    autoFocus: false
  }
)

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'drop', data: { annotations: any; uuid: string }): void
}>()

const editor = ref<Editor>()

// 添加一个方法来设置焦点
const focus = () => {
  nextTick(() => {
    editor.value?.commands.focus('start')
  })
}

// 对外暴露方法
defineExpose({
  focus
})

watch(
  () => props.modelValue,
  (value) => {
    if (!editor.value) return

    // HTML
    const isSame = editor.value.getHTML() === value

    if (isSame) {
      return
    }

    editor.value.commands.setContent(value || '', { emitUpdate: false })
  }
)

// 监听 readonly 属性变化
watch(
  () => props.readonly,
  (newReadonly) => {
    if (editor.value) {
      editor.value.setEditable(!newReadonly)
    }
  }
)

onMounted(() => {
  editor.value = new Editor({
    content: props.modelValue || '',
    extensions: [
      StarterKit.configure({
        trailingNode: false,
        heading: false,
        link: false
      }),
      mention,
      MentionNode,
      customImg,
      CustomFileHandlePlugin,
      Placeholder.configure({
        placeholder: ({ node, editor }) => {
          if (node.type.name === 'paragraph' && editor.isEmpty) {
            return props.placeholder
          }
          return ''
        }
      })
    ],
    editable: !props.readonly,

    onCreate: () => {
      // 如果需要自动聚焦
      if (props.autoFocus && !props.readonly) {
        nextTick(() => {
          editor.value?.commands.focus('start')
        })
      }
    },

    onUpdate: () => {
      if (editor.value && !props.readonly) {
        emit('update:modelValue', editor.value.getHTML())
      }
    },
    editorProps: {
      attributes: {
        class: 'px-0'
      },
      handleDrop: (view, event) => {
        if (!event.dataTransfer) return false

        const data = event.dataTransfer.getData('application/json')
        if (!data) return false

        const { annotations, fromText, uuid } = JSON.parse(data)

        // 发出 drop 事件，让父组件处理业务逻辑
        emit('drop', { annotations, uuid })

        // Get position from drop coordinates
        const dropPos = view.posAtCoords({ left: event.clientX, top: event.clientY })
        if (dropPos) {
          // Set cursor to the drop position
          const { pos } = dropPos
          const tr = view.state.tr.setSelection(TextSelection.create(view.state.doc, pos))
          tr.scrollIntoView()
          view.dispatch(tr)
        }

        const blockAnchor = {
          type: 'text',
          text: fromText,
          marks: [
            {
              type: 'blockAnchor',
              attrs: {
                symbolId: v4(),
                id: uuid,
                sourceId: uuid,
                sortIndex: annotations[0].sortIndex
              }
            }
          ]
        }
        editor.value!.commands.insertContent(blockAnchor)

        return true
      }
    }
  })
})

onBeforeUnmount(() => {
  editor.value?.destroy()
})
</script>

<style scoped lang="scss">
.scrollbar-gutter {
  scrollbar-gutter: stable;
}

.todo {
  border-radius: 0.125rem;
  margin-left: -6px;
}
</style>
