import { ref, readonly, App, createApp, h } from 'vue'
import { useIntroStore } from '@renderer/stores/introStore'
import FeatureIntroModal, { type FeatureIntroConfig } from '@renderer/components/ui/FeatureIntroModal.vue'

// 全局状态管理
const isModalOpen = ref(false)
const currentConfig = ref<FeatureIntroConfig | null>(null)
let modalApp: App | null = null
let modalContainer: HTMLElement | null = null

/**
 * 功能介绍弹窗 Composable
 * 提供命令式调用的 API
 */
export function useFeatureIntro() {
  const introStore = useIntroStore()

  /**
   * 显示功能介绍弹窗
   * @param config 配置选项
   * @returns Promise<boolean> 返回是否实际显示了弹窗（如果已确认过则返回 false）
   */
  const show = async (config: FeatureIntroConfig): Promise<boolean> => {
    // 检查是否已经确认过该介绍
    if (introStore.isAcknowledged(config.id)) {
      console.log(`[FeatureIntro] 介绍 "${config.id}" 已被确认过，跳过显示`)
      return false
    }

    // 如果已经有弹窗打开，先关闭
    if (isModalOpen.value) {
      await hide()
    }

    // 设置配置并显示弹窗
    currentConfig.value = config
    isModalOpen.value = true

    // 创建模态框实例
    await createModalInstance()
    
    return true
  }

  /**
   * 隐藏功能介绍弹窗
   */
  const hide = async (): Promise<void> => {
    isModalOpen.value = false
    currentConfig.value = null
    
    // 销毁模态框实例
    await destroyModalInstance()
  }

  /**
   * 确认介绍并隐藏弹窗
   * @param id 介绍 ID
   */
  const acknowledge = async (id: string): Promise<void> => {
    // 标记为已确认
    introStore.acknowledge(id)
    console.log(`[FeatureIntro] 介绍 "${id}" 已被确认`)
    
    // 隐藏弹窗
    await hide()
  }

  /**
   * 创建模态框实例
   */
  const createModalInstance = async (): Promise<void> => {
    // 如果已存在实例，先销毁
    if (modalApp || modalContainer) {
      await destroyModalInstance()
    }

    // 创建容器元素
    modalContainer = document.createElement('div')
    modalContainer.id = 'feature-intro-modal-container'
    document.body.appendChild(modalContainer)

    // 创建 Vue 应用实例
    modalApp = createApp({
      render() {
        return h(FeatureIntroModal, {
          isOpen: isModalOpen.value,
          config: currentConfig.value,
          onClose: hide,
          onAcknowledge: acknowledge
        })
      }
    })

    // 挂载应用
    modalApp.mount(modalContainer)
  }

  /**
   * 销毁模态框实例
   */
  const destroyModalInstance = async (): Promise<void> => {
    if (modalApp) {
      modalApp.unmount()
      modalApp = null
    }

    if (modalContainer) {
      document.body.removeChild(modalContainer)
      modalContainer = null
    }
  }

  /**
   * 检查指定 ID 的介绍是否已确认
   * @param id 介绍 ID
   * @returns 是否已确认
   */
  const isAcknowledged = (id: string): boolean => {
    return introStore.isAcknowledged(id)
  }

  /**
   * 重置指定介绍的确认状态（用于开发调试）
   * @param id 介绍 ID，不提供则重置所有
   */
  const resetAcknowledgment = (id?: string): void => {
    introStore.resetAcknowledgment(id)
    console.log(`[FeatureIntro] 重置确认状态: ${id || '全部'}`)
  }

  /**
   * 获取所有已确认的介绍 ID 列表
   * @returns 已确认的 ID 数组
   */
  const getAcknowledgedList = (): readonly string[] => {
    return introStore.getAcknowledgedList()
  }

  return {
    // 核心方法
    show,
    hide,
    acknowledge,
    
    // 状态查询
    isAcknowledged,
    getAcknowledgedList,
    
    // 调试工具
    resetAcknowledgment,
    
    // 响应式状态（只读）
    isModalOpen: readonly(isModalOpen),
    currentConfig: readonly(currentConfig)
  }
}

// 类型导出
export type { FeatureIntroConfig } from '@renderer/components/ui/FeatureIntroModal.vue'