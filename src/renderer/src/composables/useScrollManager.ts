/**
 * useScrollManager - 智能滚动行为管理
 */

import { ref, nextTick } from 'vue'
import type { ScrollOptions } from '@renderer/types/chronEngine'
import { SCROLL_CONFIG } from '@renderer/constants/chronEngine'

export function useScrollManager(options: ScrollOptions = { container: null }) {
  const isUserScrolling = ref(false)
  const lastScrollTop = ref(0)
  const container = ref(options.container)
  const threshold = options.threshold ?? SCROLL_CONFIG.THRESHOLD
  const autoScroll = options.autoScroll ?? true

  /**
   * 设置滚动容器
   */
  const setContainer = (element: HTMLElement | null) => {
    container.value = element
  }

  /**
   * 滚动到底部
   */
  const scrollToBottom = async () => {
    await nextTick()
    if (container.value && (!isUserScrolling.value || !autoScroll)) {
      container.value.scrollTop = container.value.scrollHeight
    }
  }

  /**
   * 强制滚动到底部（忽略用户滚动状态）
   */
  const forceScrollToBottom = async () => {
    await nextTick()
    if (container.value) {
      container.value.scrollTop = container.value.scrollHeight
      isUserScrolling.value = false
    }
  }

  /**
   * 检查是否在底部附近
   */
  const isNearBottom = (): boolean => {
    if (!container.value) return false
    
    const { scrollTop, scrollHeight, clientHeight } = container.value
    const distanceFromBottom = scrollHeight - scrollTop - clientHeight
    return distanceFromBottom <= threshold
  }

  /**
   * 检查是否向上滚动
   */
  const isScrollingUp = (): boolean => {
    if (!container.value) return false
    
    const currentScrollTop = container.value.scrollTop
    const scrollingUp = currentScrollTop < lastScrollTop.value
    lastScrollTop.value = currentScrollTop
    return scrollingUp
  }

  /**
   * 处理用户滚动事件
   */
  const handleScroll = () => {
    if (!container.value) return

    const { scrollTop, scrollHeight, clientHeight } = container.value
    const scrollingUp = scrollTop < lastScrollTop.value
    const notAtBottom = scrollHeight - scrollTop - clientHeight > threshold

    // 如果用户向上滚动或距离底部较远，标记为用户滚动
    isUserScrolling.value = scrollingUp || notAtBottom

    // 如果滚动到底部附近，重置用户滚动标记
    if (!notAtBottom) {
      isUserScrolling.value = false
    }

    lastScrollTop.value = scrollTop
  }

  /**
   * 平滑滚动到指定位置
   */
  const smoothScrollTo = (position: number) => {
    if (container.value) {
      container.value.scrollTo({
        top: position,
        behavior: 'smooth'
      })
    }
  }

  /**
   * 滚动到顶部
   */
  const scrollToTop = () => {
    smoothScrollTo(0)
    isUserScrolling.value = false
  }

  /**
   * 滚动到指定元素
   */
  const scrollToElement = (element: HTMLElement, behavior: ScrollBehavior = 'smooth') => {
    if (container.value) {
      element.scrollIntoView({ 
        behavior,
        block: 'nearest',
        inline: 'nearest'
      })
    }
  }

  /**
   * 添加滚动事件监听器
   */
  const addScrollListener = () => {
    if (container.value) {
      container.value.addEventListener('scroll', handleScroll)
    }
  }

  /**
   * 移除滚动事件监听器
   */
  const removeScrollListener = () => {
    if (container.value) {
      container.value.removeEventListener('scroll', handleScroll)
    }
  }

  /**
   * 重置滚动状态
   */
  const resetScrollState = () => {
    isUserScrolling.value = false
    lastScrollTop.value = 0
  }

  /**
   * 获取滚动信息
   */
  const getScrollInfo = () => {
    if (!container.value) {
      return {
        scrollTop: 0,
        scrollHeight: 0,
        clientHeight: 0,
        scrollPercentage: 0,
        isAtTop: false,
        isAtBottom: false
      }
    }

    const { scrollTop, scrollHeight, clientHeight } = container.value
    const scrollPercentage = scrollHeight > clientHeight 
      ? (scrollTop / (scrollHeight - clientHeight)) * 100 
      : 100

    return {
      scrollTop,
      scrollHeight,
      clientHeight,
      scrollPercentage,
      isAtTop: scrollTop === 0,
      isAtBottom: isNearBottom()
    }
  }

  /**
   * 设置自动滚动延迟
   */
  const delayedScrollToBottom = (delay: number = SCROLL_CONFIG.AUTO_SCROLL_DELAY) => {
    setTimeout(() => {
      scrollToBottom()
    }, delay)
  }

  return {
    // 状态
    isUserScrolling,
    lastScrollTop,
    container,

    // 方法
    setContainer,
    scrollToBottom,
    forceScrollToBottom,
    isNearBottom,
    isScrollingUp,
    handleScroll,
    smoothScrollTo,
    scrollToTop,
    scrollToElement,
    addScrollListener,
    removeScrollListener,
    resetScrollState,
    getScrollInfo,
    delayedScrollToBottom
  }
}