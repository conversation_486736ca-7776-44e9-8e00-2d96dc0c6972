/**
 * useImageUpload - 图片上传和Coze文件ID管理
 */

import { ref } from 'vue'
import type { CozeFileManager, ImageFile } from '@renderer/types/chronEngine'
import { IMAGE_CONFIG } from '@renderer/constants/chronEngine'

export function useImageUpload() {
  // 本地存储上传的图片与Coze文件ID的映射关系
  const imageCozeFileIdMap = ref(new Map<string, string>())

  // Coze文件管理器
  const cozeFileManager: CozeFileManager = {
    set: (imageUrl: string, cozeFileId: string) => {
      imageCozeFileIdMap.value.set(imageUrl, cozeFileId)
      console.log('图片已关联Coze文件ID:', { imageUrl, cozeFileId })
    },

    get: (imageUrl: string) => {
      return imageCozeFileIdMap.value.get(imageUrl)
    },

    clear: () => {
      imageCozeFileIdMap.value.clear()
      console.log('已清空文件ID映射')
    },

    getAllFileIds: () => {
      return Array.from(imageCozeFileIdMap.value.values())
    }
  }

  /**
   * 获取图片URL对应的Coze文件ID
   */
  const getCozeFileIdForImage = (imageUrl: string): string | undefined => {
    return cozeFileManager.get(imageUrl)
  }

  /**
   * 获取编辑器内容中所有图片的Coze文件ID
   */
  const getAllCozeFileIds = (): string[] => {
    return cozeFileManager.getAllFileIds()
  }

  /**
   * 处理图片并创建文件对象
   */
  const processImageForUpload = (imageUrl: string): ImageFile => {
    const cozeFileId = getCozeFileIdForImage(imageUrl)
    
    // 如果有Coze文件ID，记录在控制台
    if (cozeFileId) {
      console.log(`Image ${imageUrl} has Coze file ID: ${cozeFileId}`)
    }

    return {
      type: 'image',
      transfer_method: 'remote_url',
      url: imageUrl
    }
  }

  /**
   * 检查图片URL是否为支持的协议
   */
  const isSupportedImageUrl = (url: string): boolean => {
    return IMAGE_CONFIG.SUPPORTED_PROTOCOLS.some(protocol => 
      url.startsWith(protocol)
    )
  }

  /**
   * 获取图片并创建本地Blob URL
   */
  const fetchImageAsBlob = async (imageUrl: string): Promise<string> => {
    try {
      const response = await fetch(imageUrl)
      const blob = await response.blob()
      return URL.createObjectURL(blob)
    } catch (error) {
      console.error('无法获取图片:', error)
      throw error
    }
  }

  /**
   * 处理图片URL，返回可用的本地URL
   */
  const processImageUrl = async (imageUrl: string): Promise<string> => {
    // 对于远程URL或Chron协议URL，尝试获取图片并创建本地Blob URL
    if (isSupportedImageUrl(imageUrl) && imageUrl.startsWith('http')) {
      try {
        return await fetchImageAsBlob(imageUrl)
      } catch (error) {
        console.error('获取图片失败，使用原始URL:', error)
        return imageUrl
      }
    }
    
    // 对于已经是本地URL的情况，直接使用
    return imageUrl
  }

  /**
   * 清理Blob URL
   */
  const revokeBlobUrl = (url: string) => {
    if (url.startsWith('blob:')) {
      URL.revokeObjectURL(url)
    }
  }

  /**
   * 重置图片上传状态
   */
  const resetImageUpload = () => {
    cozeFileManager.clear()
  }

  return {
    // 状态
    imageCozeFileIdMap: imageCozeFileIdMap.value,
    
    // 方法
    cozeFileManager,
    getCozeFileIdForImage,
    getAllCozeFileIds,
    processImageForUpload,
    isSupportedImageUrl,
    fetchImageAsBlob,
    processImageUrl,
    revokeBlobUrl,
    resetImageUpload
  }
}