/**
 * useMessageActions - 消息操作管理
 */

import { type Ref } from 'vue'
import type { AIMessage, MessageActionHandlers } from '@renderer/types/chronEngine'
import { TOAST_CONFIG } from '@renderer/constants/chronEngine'
import { useBlockCreation } from '@renderer/composables/useBlockCreation'
import { useBlockService } from '@renderer/composables/useBlockService'
import { showToast } from '@renderer/utils/toast'

export function useMessageActions(aiConversation: Ref<AIMessage[]>) {
  /**
   * 复制消息到剪贴板
   */
  const copyMessage = (index: number) => {
    const message = aiConversation.value[index]
    if (message) {
      navigator.clipboard.writeText(message.content)
      showToast(TOAST_CONFIG.SUCCESS_TITLE, TOAST_CONFIG.COPY_MESSAGE, TOAST_CONFIG.DEFAULT_DURATION)
    }
  }

  /**
   * 复制AI消息到剪贴板
   */
  const copyAiMessage = (content: string) => {
    navigator.clipboard.writeText(content)
    showToast(TOAST_CONFIG.SUCCESS_TITLE, TOAST_CONFIG.COPY_MESSAGE, TOAST_CONFIG.DEFAULT_DURATION)
  }

  /**
   * 删除AI消息
   */
  const deleteAiMessage = (index: number) => {
    // 如果是最后一对对话，则删除整个对话
    if (
      index === aiConversation.value.length - 1 &&
      index > 0 &&
      aiConversation.value[index].role === 'assistant' &&
      aiConversation.value[index - 1].role === 'user'
    ) {
      aiConversation.value.splice(index - 1, 2)
    } else {
      aiConversation.value.splice(index, 1)
    }
  }

  /**
   * 保存AI消息到笔记
   */
  const saveAiMessageToNote = async (content: string) => {
    try {
      const { createBlock } = useBlockCreation()
      const newNote = await createBlock('note')

      if (newNote) {
        const blockService = useBlockService()
        await blockService.updateBlock({
          uuid: newNote.uuid,
          title: '来自Chron Engine的回答',
          contents: content,
          updatedAt: Date.now()
        })
        showToast(TOAST_CONFIG.SUCCESS_TITLE, TOAST_CONFIG.SAVE_MESSAGE, TOAST_CONFIG.DEFAULT_DURATION)
      }
    } catch (error) {
      console.error('保存到笔记失败:', error)
      showToast(TOAST_CONFIG.ERROR_TITLE, '保存失败，请稍后重试', TOAST_CONFIG.DEFAULT_DURATION)
    }
  }

  /**
   * 分享AI消息
   */
  const shareAiMessage = (content: string) => {
    navigator.clipboard.writeText(content)
    showToast(TOAST_CONFIG.SUCCESS_TITLE, TOAST_CONFIG.SHARE_MESSAGE, TOAST_CONFIG.DEFAULT_DURATION)
  }

  /**
   * 批量删除消息
   */
  const batchDeleteMessages = (indices: number[]) => {
    // 按降序排列索引，从后往前删除
    const sortedIndices = indices.sort((a, b) => b - a)
    sortedIndices.forEach(index => {
      if (index >= 0 && index < aiConversation.value.length) {
        aiConversation.value.splice(index, 1)
      }
    })
  }

  /**
   * 清空所有消息
   */
  const clearAllMessages = () => {
    aiConversation.value.splice(0)
  }

  /**
   * 获取消息统计信息
   */
  const getMessageStats = () => {
    const total = aiConversation.value.length
    const userMessages = aiConversation.value.filter(msg => msg.role === 'user').length
    const assistantMessages = aiConversation.value.filter(msg => msg.role === 'assistant').length
    
    return {
      total,
      userMessages,
      assistantMessages
    }
  }

  /**
   * 查找消息
   */
  const searchMessages = (query: string, role?: 'user' | 'assistant') => {
    return aiConversation.value
      .map((msg, index) => ({ ...msg, index }))
      .filter(msg => {
        const matchesRole = !role || msg.role === role
        const matchesContent = msg.content.toLowerCase().includes(query.toLowerCase())
        return matchesRole && matchesContent
      })
  }

  /**
   * 导出对话历史
   */
  const exportConversation = (format: 'json' | 'text' = 'text') => {
    if (format === 'json') {
      const data = JSON.stringify(aiConversation.value, null, 2)
      return data
    } else {
      const text = aiConversation.value
        .map(msg => `${msg.role === 'user' ? '用户' : 'AI'}: ${msg.content}`)
        .join('\n\n')
      return text
    }
  }

  const messageActionHandlers: MessageActionHandlers = {
    copyMessage,
    copyAiMessage,
    deleteAiMessage,
    saveAiMessageToNote,
    shareAiMessage
  }

  return {
    // 基础操作
    ...messageActionHandlers,
    
    // 扩展操作
    batchDeleteMessages,
    clearAllMessages,
    getMessageStats,
    searchMessages,
    exportConversation
  }
}