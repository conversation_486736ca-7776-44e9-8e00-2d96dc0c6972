import { useDockviewStore } from '@renderer/stores/dockviewStore'
import { useFileTreeStore } from '@renderer/stores/fileTreeStore'
import { track } from '@renderer/utils/tracks'

/**
 * 处理面板关闭的 composable
 * 确保只有当节点不再在任何其他面板中打开时，才从 selectedNodeIds 中移除
 */
export function usePanelClose() {
  const dockviewStore = useDockviewStore()
  const fileTreeStore = useFileTreeStore()

  /**
   * 关闭面板的通用方法
   * @param nodeUuid 节点的 UUID
   * @param panelId 当前面板的 ID
   */
  const handleClose = (nodeUuid: string, panelId: string) => {
    // 在关闭面板之前检查该节点是否还在其他面板中打开
    const allPanels = dockviewStore.getAllPanels()

    // 检查是否还有其他面板（除了当前要关闭的面板）包含这个节点
    const hasOtherPanelsWithSameNode = allPanels.some((panel) => {
      const params = panel.params as any
      return panel.id !== panelId && params?.node?.uuid === nodeUuid
    })

    // 关闭当前面板
    dockviewStore.closeActivePanel()

    // 只有当该节点不再在任何其他面板中打开时，才从 selectedNodeIds 中移除
    if (!hasOtherPanelsWithSameNode) {
      const index = fileTreeStore.selectedNodeIds.indexOf(nodeUuid)
      if (index !== -1) {
        fileTreeStore.selectedNodeIds.splice(index, 1)
      }
    }

    // 记录用户行为
    track({
      action_type: 'Close Panel'
    })
  }

  /**
   * 使用 API 直接关闭指定面板的方法
   * @param nodeUuid 节点的 UUID
   * @param panelId 要关闭的面板 ID
   */
  const handleClosePanel = (nodeUuid: string, panelId: string) => {
    const api = dockviewStore.getApi()
    if (!api) return

    // 在关闭面板之前检查该节点是否还在其他面板中打开
    const allPanels = dockviewStore.getAllPanels()

    // 检查是否还有其他面板（除了当前要关闭的面板）包含这个节点
    const hasOtherPanelsWithSameNode = allPanels.some((panel) => {
      const params = panel.params as any
      return panel.id !== panelId && params?.node?.uuid === nodeUuid
    })

    // 关闭指定面板
    const panel = api.getPanel(panelId)
    if (panel) {
      api.removePanel(panel)
    }

    // 只有当该节点不再在任何其他面板中打开时，才从 selectedNodeIds 中移除
    if (!hasOtherPanelsWithSameNode) {
      const index = fileTreeStore.selectedNodeIds.indexOf(nodeUuid)
      if (index !== -1) {
        fileTreeStore.selectedNodeIds.splice(index, 1)
      }
    }

    // 记录用户行为
    track({
      action_type: 'Close Panel'
    })
  }

  return {
    handleClose,
    handleClosePanel
  }
}
