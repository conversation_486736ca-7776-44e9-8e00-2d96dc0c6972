<template>
  <div class="p-4">
    <h1 class="text-xl font-bold mb-4">回收站永久删除功能测试</h1>

    <div class="space-y-4">
      <!-- 测试按钮 -->
      <div class="flex gap-2">
        <button
          @click="addTestItem"
          class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          添加测试项目到回收站
        </button>

        <button
          @click="openTrash"
          class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
        >
          打开回收站 ({{ trashStore.trashCount }})
        </button>

        <button
          @click="testPermanentDelete"
          class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          :disabled="trashStore.isEmpty"
        >
          测试永久删除第一个项目
        </button>

        <button
          @click="testClearTrash"
          class="px-4 py-2 bg-red-700 text-white rounded hover:bg-red-800"
          :disabled="trashStore.isEmpty"
        >
          测试清空回收站
        </button>
      </div>

      <!-- 回收站状态 -->
      <div class="p-4 bg-gray-100 rounded">
        <h2 class="font-semibold mb-2">回收站状态:</h2>
        <p>项目数量: {{ trashStore.trashCount }}</p>
        <p>是否为空: {{ trashStore.isEmpty ? '是' : '否' }}</p>
        <p>回收站是否打开: {{ trashStore.isTrashOpen ? '是' : '否' }}</p>
      </div>

      <!-- 节点存储状态 -->
      <div class="p-4 bg-blue-100 rounded">
        <h2 class="font-semibold mb-2">节点存储状态:</h2>
        <p>内存节点数量: {{ nodeStore.memoryNodes.length }}</p>
        <p>可见节点数量: {{ nodeStore.visibleNodes.length }}</p>
      </div>

      <!-- 回收站项目列表 -->
      <div class="p-4 bg-gray-100 rounded">
        <h2 class="font-semibold mb-2">回收站项目:</h2>
        <div v-if="trashStore.isEmpty" class="text-gray-500">
          回收站为空
        </div>
        <div v-else class="space-y-2">
          <div
            v-for="item in trashStore.sortedTrashItems"
            :key="item.uuid"
            class="p-2 bg-white rounded border flex justify-between items-center"
          >
            <div>
              <span class="font-medium">{{ item.originalNode.title }}</span>
              <span class="text-sm text-gray-500 ml-2">({{ item.originalNode.type }})</span>
              <span class="text-xs text-gray-400 ml-2">UUID: {{ item.uuid.substring(0, 8) }}...</span>
            </div>
            <div class="flex gap-2">
              <button
                @click="restoreItem(item.uuid)"
                class="px-2 py-1 bg-green-500 text-white text-sm rounded hover:bg-green-600"
              >
                恢复
              </button>
              <button
                @click="permanentDeleteItem(item.uuid)"
                class="px-2 py-1 bg-red-500 text-white text-sm rounded hover:bg-red-600"
              >
                永久删除
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 测试日志 -->
      <div class="p-4 bg-yellow-100 rounded">
        <h2 class="font-semibold mb-2">测试日志:</h2>
        <div class="text-sm space-y-1 max-h-40 overflow-y-auto">
          <div v-for="(log, index) in testLogs" :key="index" class="text-gray-700">
            {{ log }}
          </div>
        </div>
        <button
          @click="clearLogs"
          class="mt-2 px-2 py-1 bg-yellow-500 text-white text-sm rounded hover:bg-yellow-600"
        >
          清空日志
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useTrashStore } from '../stores/trashStore'
import { useNodeStore } from '../stores/nodeStore'
import { showToast } from '../utils/toast'

const trashStore = useTrashStore()
const nodeStore = useNodeStore()
const testLogs = ref<string[]>([])

const addLog = (message: string) => {
  const timestamp = new Date().toLocaleTimeString()
  testLogs.value.push(`[${timestamp}] ${message}`)
}

const clearLogs = () => {
  testLogs.value = []
}

// 添加测试项目到回收站
const addTestItem = async () => {
  const testNode = {
    uuid: 'test-' + Date.now(),
    title: '测试项目 ' + new Date().toLocaleTimeString(),
    type: 'note',
    children: [],
    parent: null,
    meta: [],
    createdAt: Date.now(),
    updatedAt: Date.now()
  } as any

  await trashStore.addToTrash(testNode)
  addLog(`添加测试项目到回收站: ${testNode.title}`)
  showToast('成功', '已添加测试项目到回收站', 2000)
}

// 打开回收站
const openTrash = () => {
  trashStore.openTrash()
  addLog('打开回收站')
}

// 恢复项目
const restoreItem = async (uuid: string) => {
  try {
    const restoredItem = await trashStore.restoreItem(uuid)
    if (restoredItem) {
      addLog(`恢复项目: ${restoredItem.originalNode.title}`)
      showToast('成功', `已恢复 '${restoredItem.originalNode.title}'`, 2000)
    }
  } catch (error) {
    addLog(`恢复项目失败: ${error}`)
    showToast('错误', '恢复项目失败', 3000)
  }
}

// 永久删除单个项目
const permanentDeleteItem = async (uuid: string) => {
  try {
    const item = trashStore.getTrashItem(uuid)
    const nodeExistsBefore = !!nodeStore.getNodeById(uuid)
    
    addLog(`开始永久删除项目: ${item?.originalNode.title || uuid}`)
    addLog(`删除前节点是否存在: ${nodeExistsBefore}`)
    
    const success = await trashStore.permanentlyDelete(uuid)
    
    const nodeExistsAfter = !!nodeStore.getNodeById(uuid)
    addLog(`删除后节点是否存在: ${nodeExistsAfter}`)
    
    if (success) {
      addLog(`永久删除成功: ${item?.originalNode.title || uuid}`)
      showToast('成功', `已永久删除 '${item?.originalNode.title || uuid}'`, 2000)
    } else {
      addLog(`永久删除失败: ${uuid}`)
      showToast('错误', '永久删除失败', 3000)
    }
  } catch (error) {
    addLog(`永久删除异常: ${error}`)
    showToast('错误', '永久删除异常', 3000)
  }
}

// 测试永久删除第一个项目
const testPermanentDelete = async () => {
  if (trashStore.isEmpty) return
  
  const firstItem = trashStore.sortedTrashItems[0]
  await permanentDeleteItem(firstItem.uuid)
}

// 测试清空回收站
const testClearTrash = async () => {
  try {
    const itemCount = trashStore.trashCount
    addLog(`开始清空回收站，共 ${itemCount} 个项目`)
    
    await trashStore.clearTrash()
    
    addLog(`清空回收站完成，剩余项目: ${trashStore.trashCount}`)
    showToast('成功', '回收站已清空', 2000)
  } catch (error) {
    addLog(`清空回收站失败: ${error}`)
    showToast('错误', '清空回收站失败', 3000)
  }
}
</script>
