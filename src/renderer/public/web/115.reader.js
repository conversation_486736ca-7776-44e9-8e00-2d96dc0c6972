!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("reader",[],t):"object"==typeof exports?exports.reader=t():e.reader=t()}(self,(()=>(()=>{"use strict";function e(e,t){let r=0,n=e.length-1;for(;r<=n;){let a=Math.floor((r+n)/2);if(e[a].start<=t&&t<=e[a].end)return e[a];t<e[a].start?n=a-1:r=a+1}return null}return onmessage=async t=>{let{context:r,term:n,options:a}=t.data;postMessage(function(t,r,n){if(!r)return[];let{text:a,internalCharDataRanges:o}=t,s=[],d=(i=r,i.replace(/[\u2018\u2019]/g,"'").replace(/[\u201C\u201D]/g,'"')).replace(/[.*+?^${}()|[\]\\]/g,"\\$&");var i;n.entireWord&&(d="\\b"+d+"\\b");let l,u=new RegExp(d,"g"+(n.caseSensitive?"":"i"));for(;l=u.exec(a);){let[t]=l,{charDataID:r,start:n}=e(o,l.index),{charDataID:a,start:d}=e(o,l.index+t.length);s.push({startCharDataID:r,startIndex:l.index-n,endCharDataID:a,endIndex:l.index+t.length-d})}return s}(r,n,a))},{}})()));