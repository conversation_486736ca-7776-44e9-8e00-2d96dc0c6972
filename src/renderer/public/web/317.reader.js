"use strict";(self.webpackChunkreader=self.webpackChunkreader||[]).push([[317],{9317:(e,t,a)=>{a.r(t),a.d(t,{renderMathInternal:()=>s});var n=a(353),o=a(1039),r=a(4928),d=a(1909),u=a(4090),l=a(2519);let p=!1;function s(e){var t;p||((0,n.J)(new l.HTMLAdaptor(null!==(t=e.defaultView)&&void 0!==t?t:window)),p=!0);let a=o.mathjax.document(e,{InputJax:new r.TeX({packages:d.B}),OutputJax:new u.CHTML({fontURL:new URL("mathjax-fonts",document.location.href).toString()})});a.render();for(let e of a.math)e.typesetRoot.nodeType===Node.ELEMENT_NODE&&(e.typesetRoot.dataset.tex=e.math)}}}]);