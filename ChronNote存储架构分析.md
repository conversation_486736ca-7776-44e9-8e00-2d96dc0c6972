# ChronNote 存储架构分析文档

## 概览

ChronNote 是一个现代化的多平台笔记应用，采用以用户ID命名的分布式存储架构。本文档详细分析其存储设计理念、实现细节和架构优势。

## 🏗️ 整体架构设计

### 多平台统一存储策略

ChronNote 支持三个主要平台，每个平台采用不同的存储后端：

```
┌─────────────────┬──────────────────┬─────────────────────┐
│ 平台            │ 存储后端          │ 特点                │
├─────────────────┼──────────────────┼─────────────────────┤
│ Electron (桌面) │ SQLite + 文件系统 │ 性能最佳，功能完整   │
│ Web (浏览器)    │ IndexedDB + 本地存储│ 跨浏览器兼容       │
│ iOS (移动)      │ SQLite + Capacitor│ 原生性能，移动优化   │
└─────────────────┴──────────────────┴─────────────────────┘
```

### 核心存储结构

```
basePath/
├── example/                           # 用户数据根目录 (workspacePath)
│   ├── users/                         # 用户隔离目录
│   │   ├── {userA}/                   # 用户A专属目录
│   │   │   ├── chron.db               # 简化的数据库文件名
│   │   │   ├── blocks.json            # 简化的块数据文件名
│   │   │   ├── img/                   # 用户A专属图片目录
│   │   │   │   ├── avatar.jpg
│   │   │   │   └── screenshot.png
│   │   │   └── pdf/                   # 用户A专属PDF目录
│   │   │       ├── document1.pdf
│   │   │       └── manual.pdf
│   │   ├── {userB}/                   # 用户B专属目录
│   │   │   ├── chron.db
│   │   │   ├── blocks.json
│   │   │   ├── img/
│   │   │   └── pdf/
│   │   └── ...
│   └── note/                          # 保留的兼容性目录
└── backup/                            # 用户隔离的备份目录
    ├── 2024-01-15T10-30-00/          # 时间戳备份文件夹
    │   ├── {userA}/                   # 用户A的备份
    │   └── {userB}/                   # 用户B的备份
    └── ...
```

## 🔑 用户ID命名体系

### 用户标识设计

```typescript
// 用户信息接口定义
export interface UserInfo {
  uuid: string    // 核心用户标识 (UUID v4)
  email: string   // 用户邮箱
}

// 数据库路径生成逻辑
export const getDbPath = (userId: string) => {
  const config = useConfig()
  return `${config.workspacePath}/chron_${userId}`
}
```

### 实际命名示例

```bash
# 新架构文件示例
basePath/example/users/3492ed08-b2dc-45bd-abed-20090a1954e5/
├── chron.db                             # 简化命名，无冗余userId
├── blocks.json                          # 简化命名，无冗余userId
├── img/
└── pdf/

# 命名模式优化
旧架构: example/chron_{完整UUID}.db              # 冗余的userId命名
新架构: example/users/{完整UUID}/chron.db        # 通过目录隔离，文件名简化
```

## 📊 数据库表结构设计

### 1. 核心Block表 - 统一资源管理

```sql
CREATE TABLE IF NOT EXISTS Block (
    uuid TEXT PRIMARY KEY,                -- 唯一标识符
    type TEXT NOT NULL,                   -- 资源类型：note/todo/xingliu/chat等
    title TEXT NOT NULL,                  -- 资源标题
    createdAt INTEGER NOT NULL,           -- 创建时间戳
    updatedAt INTEGER NOT NULL,           -- 更新时间戳
    contents TEXT,                        -- 内容数据(JSON格式)
    annotations TEXT,                     -- 注释数据(JSON数组)
    meta TEXT,                           -- 元数据(JSON数组)
    selection INTEGER                     -- 用户阅读进度指针
);
```

**设计特点：**
- ✅ **类型灵活**：`type`字段不限制具体值，支持未来扩展
- ✅ **JSON存储**：`contents`、`annotations`、`meta`使用JSON格式存储复杂数据
- ✅ **时间追踪**：完整的创建和更新时间记录
- ✅ **用户体验**：`selection`字段记录用户阅读进度

### 2. 专门的Todo表 - 任务管理

```sql
CREATE TABLE IF NOT EXISTS todos (
    uuid TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    dueDate TEXT,                         -- 截止日期(ISO 8601格式)
    isCompleted INTEGER DEFAULT 0,        -- 完成状态(0=未完成，1=完成)
    priority INTEGER DEFAULT 0,           -- 优先级(0=低，1=中，2=高)
    createdAt TEXT NOT NULL,
    updatedAt TEXT,
    tags TEXT,                           -- 标签(逗号分隔或JSON)
    notes TEXT,                          -- 备注内容
    children TEXT                        -- 子任务列表(JSON数组)
);
```

**设计特点：**
- ✅ **层级支持**：`children`字段支持嵌套todo结构
- ✅ **状态管理**：完整的任务状态和优先级系统
- ✅ **时间管理**：支持截止日期和时间追踪

### 3. YJS协作更新表 - 实时协作支持

```sql
CREATE TABLE IF NOT EXISTS updates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    uuid TEXT NOT NULL,                   -- 关联的文档UUID
    dataUpdate BLOB,                      -- YJS二进制更新数据
    updateId TEXT,                        -- 更新操作ID
    timestamp INTEGER NOT NULL            -- 更新时间戳
);
```

**设计特点：**
- ✅ **增量更新**：存储YJS的二进制增量更新
- ✅ **版本控制**：完整的文档变更历史
- ✅ **实时协作**：支持多用户实时编辑

## 🔧 平台适配实现

### 统一数据库管理器

```typescript
export class DatabaseManager {
  private db: SQLiteDBConnection | webDB.IndexedDBStore | null = null
  private platform: Platform
  
  constructor(dbPath: string) {
    this.platform = getPlatform()
    this.dbPath = this.platform === 'electron' ? `${dbPath}.db` : dbPath
  }
  
  // 平台自适应初始化
  private async init(): Promise<void> {
    if (this.platform === 'electron') {
      await window.electronDB.initSqlite(this.dbPath!)
    } else if (this.platform === 'ios') {
      this.sqlite = await iOSDB.initSqlite()
      this.db = await iOSDB.connectSqlite(this.sqlite, this.dbPath!)
    } else if (this.platform === 'web') {
      this.db = await webDB.initSqlite()
    }
  }
}
```

### 路径配置系统

```typescript
class ConfigHelper {
  private async initialize() {
    // 根据平台和环境确定基础路径
    let basePath: string
    if (this.platform === 'web') {
      basePath = this.mode === 'development' ? '/dev-workspace' : '/prod-workspace'
    } else {
      basePath = this.mode === 'development' ? cwd : appDataPath
    }
    
    // 新架构：只设置基础路径，用户专属路径通过方法获取
    this.workspacePath = `${basePath}`  // 指向根目录
    this.backupPath = `${basePath}/backup`
  }

  // 新的用户专属路径方法
  getUserWorkspace(userId: string): string {
    return `${this.workspacePath}/users/${userId}`
  }

  getUserDbPath(userId: string): string {
    return `${this.getUserWorkspace(userId)}/chron`
  }

  getUserImgPath(userId: string): string {
    return `${this.getUserWorkspace(userId)}/img`
  }

  getUserPdfPath(userId: string): string {
    return `${this.getUserWorkspace(userId)}/pdf`
  }
}
```

## 🛡️ 数据安全与隔离

### 用户数据隔离

```typescript
// 每个服务都基于用户ID初始化
export class TodoService {
  constructor() {
    const userStore = useUserStore()
    if (!userStore.userInfo) {
      throw new Error('User must be logged in to initialize TodoService')
    }
    const userId = userStore.userInfo.uuid
    this.dbManager = new DatabaseManager(getDbPath(userId))
  }
}
```

**安全特性：**
- ✅ **强制认证**：所有数据操作都要求用户登录
- ✅ **数据隔离**：每个用户拥有独立的数据库文件
- ✅ **UUID命名**：避免用户名冲突和路径遍历攻击
- ✅ **JWT认证**：基于令牌的身份验证系统

## 🔄 备份与恢复系统

### 自动备份策略

```typescript
export class BackupService {
  // 默认备份配置
  private static DEFAULT_BACKUP_SETTINGS = {
    maxBackups: 5,                    // 最大备份数量
    backupIntervalDays: 1,            // 备份间隔天数
    pollingIntervalMinutes: 30        // 检查间隔分钟
  }
  
  // 创建备份
  private async createBackup(): Promise<void> {
    const userId = useUserStore().userInfo?.uuid || 'default'
    const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\..+/, '')
    const backupDir = `${this.backupPath}/${timestamp}`
    
    // 复制整个工作区
    await this.fs.copy(config.workspacePath, backupDir)
  }
}
```

**备份特性：**
- ✅ **自动化**：基于时间间隔的自动备份
- ✅ **版本管理**：保留多个历史版本
- ✅ **空间控制**：自动清理超出限制的旧备份
- ✅ **完整性**：整个工作区的完整备份

## 📈 架构优势分析

### 1. 扩展性优势

```
┌─────────────────┬─────────────────────────────────────┐
│ 设计特点        │ 扩展能力                            │
├─────────────────┼─────────────────────────────────────┤
│ 类型系统灵活     │ 轻松添加新的资源类型(图表、白板等)    │
│ JSON存储        │ 支持复杂数据结构的演进              │
│ 平台抽象        │ 快速适配新平台(Android、桌面等)      │
│ 微服务架构      │ 各功能模块独立，便于团队协作开发     │
└─────────────────┴─────────────────────────────────────┘
```

### 2. 性能优势

```
┌─────────────────┬─────────────────────────────────────┐
│ 优化策略        │ 性能提升                            │
├─────────────────┼─────────────────────────────────────┤
│ 用户数据分片     │ 避免大型数据库，提升查询速度         │
│ 平台原生存储     │ 利用各平台最佳存储方案              │
│ 增量更新        │ YJS提供高效的实时协作同步           │
│ 懒加载设计      │ 按需创建表和加载数据                │
└─────────────────┴─────────────────────────────────────┘
```

### 3. 维护性优势

```
┌─────────────────┬─────────────────────────────────────┐
│ 设计模式        │ 维护优势                            │
├─────────────────┼─────────────────────────────────────┤
│ 统一API抽象     │ 平台差异对业务层透明                │
│ 服务化架构      │ 功能模块边界清晰，便于测试和调试     │
│ 配置驱动        │ 环境差异通过配置管理                │
│ 错误处理完善     │ 详细的错误日志和异常恢复机制         │
└─────────────────┴─────────────────────────────────────┘
```

## 🎯 设计理念总结

ChronNote的存储架构体现了以下核心设计理念：

### 🔸 **多租户隔离**
每个用户拥有完全独立的数据存储空间，确保数据安全和隐私保护。

### 🔸 **跨平台一致性** 通过统一的API抽象层，在不同平台上提供一致的用户体验。

### 🔸 **实时协作支持**
基于YJS的增量更新机制，支持多用户实时协作编辑。

### 🔸 **数据完整性**
完善的备份恢复系统和版本控制，保障数据的安全性和可恢复性。

### 🔸 **扩展友好**
灵活的类型系统和JSON存储，为未来功能扩展预留充分空间。

### 🔸 **性能优化**
基于用户分片的存储策略，避免大型数据库带来的性能问题。

这套架构设计充分考虑了现代知识管理应用的核心需求：**多用户、多平台、实时协作、数据安全**，是一个相当成熟和前瞻性的企业级存储解决方案。