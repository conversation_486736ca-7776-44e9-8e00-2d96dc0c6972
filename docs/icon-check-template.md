# 图标新增检测模板

## 🔍 快速检测新增内容

### 基础检测命令
```bash
# 进入项目目录
cd /Users/<USER>/projects/chronnote/chronnoteV6

# 1. 检查分类总数变化
CURRENT_CATEGORIES=$(ls src/renderer/src/assets/svg/color/ | wc -l)
echo "当前分类数: $CURRENT_CATEGORIES (基准: 26)"

# 2. 检查图标总数变化  
TOTAL_ICONS=0
for dir in src/renderer/src/assets/svg/color/*/; do
  count=$(ls "$dir" | wc -l)
  TOTAL_ICONS=$((TOTAL_ICONS + count))
done
echo "当前图标总数: $TOTAL_ICONS (基准: 953)"

# 3. 列出所有分类并对比
echo "=== 当前所有分类 ==="
ls src/renderer/src/assets/svg/color/
```

### 详细检测脚本
```bash
#!/bin/bash
# icon-detect-changes.sh - 检测图标变更脚本

echo "🔍 图标变更检测报告"
echo "==================="
echo "检测时间: $(date)"
echo ""

# 基准数据
BASELINE_CATEGORIES=26
BASELINE_ICONS=953
BASELINE_CATEGORIES_LIST="alphhabetAndNumbers bubbleTea cinema city commercial emojis food fruits furniture graduation halloween herbs history information insect languageLearning magic music nature ocean plants Playground study SweetCandy transportation weather"

# 当前数据
CURRENT_DIR="src/renderer/src/assets/svg/color"
CURRENT_CATEGORIES=$(ls $CURRENT_DIR | wc -l | tr -d ' ')
CURRENT_CATEGORIES_LIST=$(ls $CURRENT_DIR | tr '\n' ' ')

# 计算图标总数
TOTAL_ICONS=0
for dir in $CURRENT_DIR/*/; do
  if [ -d "$dir" ]; then
    count=$(ls "$dir" | wc -l 2>/dev/null || echo 0)
    TOTAL_ICONS=$((TOTAL_ICONS + count))
  fi
done

echo "📊 数量对比:"
echo "分类数: $CURRENT_CATEGORIES (基准: $BASELINE_CATEGORIES)"
echo "图标数: $TOTAL_ICONS (基准: $BASELINE_ICONS)"
echo ""

# 检测新增分类
echo "🆕 分类变更检测:"
if [ $CURRENT_CATEGORIES -gt $BASELINE_CATEGORIES ]; then
  echo "✅ 发现新增分类 (+$((CURRENT_CATEGORIES - BASELINE_CATEGORIES))个)"
  
  # 找出新增的分类
  for category in $(ls $CURRENT_DIR); do
    if ! echo "$BASELINE_CATEGORIES_LIST" | grep -q "$category"; then
      echo "  🔸 新增: $category ($(ls $CURRENT_DIR/$category | wc -l | tr -d ' ')个图标)"
    fi
  done
elif [ $CURRENT_CATEGORIES -lt $BASELINE_CATEGORIES ]; then
  echo "⚠️  检测到分类减少 (-$((BASELINE_CATEGORIES - CURRENT_CATEGORIES))个)"
else
  echo "➡️  分类数量无变化"
fi

echo ""

# 检测图标数量变化
echo "📈 图标数量变化:"
if [ $TOTAL_ICONS -gt $BASELINE_ICONS ]; then
  echo "✅ 新增图标 (+$((TOTAL_ICONS - BASELINE_ICONS))个)"
elif [ $TOTAL_ICONS -lt $BASELINE_ICONS ]; then
  echo "⚠️  图标减少 (-$((BASELINE_ICONS - TOTAL_ICONS))个)"
else
  echo "➡️  图标总数无变化"
fi

echo ""

# 检查中文文件名分类状态
echo "🇨🇳 中文文件名分类检查:"
CHINESE_CATEGORIES=("commercial" "emojis" "information" "fruits")

for category in "${CHINESE_CATEGORIES[@]}"; do
  if [ -d "$CURRENT_DIR/$category" ]; then
    count=$(ls "$CURRENT_DIR/$category" | wc -l | tr -d ' ')
    echo "  📁 $category: $count个文件"
    
    # 检查是否还有中文文件名
    chinese_files=$(ls "$CURRENT_DIR/$category" | grep -E '[\u4e00-\u9fa5]' | wc -l 2>/dev/null || echo 0)
    if [ $chinese_files -gt 0 ]; then
      echo "    ❌ 仍有 $chinese_files 个中文文件名需要翻译"
    else
      echo "    ✅ 所有文件已使用英文命名"
    fi
  else
    echo "  ❓ $category 分类不存在"
  fi
done

echo ""
echo "📋 下一步建议:"
if [ $CURRENT_CATEGORIES -gt $BASELINE_CATEGORIES ] || [ $TOTAL_ICONS -gt $BASELINE_ICONS ]; then
  echo "1. 更新 docs/icon-configuration-status.md 中的基准数据"
  echo "2. 为新增分类确定配置优先级"
  echo "3. 如有中文文件名，优先进行英文翻译"
else
  echo "1. 继续现有配置工作计划"
  echo "2. 重点处理中文文件名翻译"
fi

echo ""
echo "🔗 相关文件:"
echo "- 状态文档: docs/icon-configuration-status.md"
echo "- 检测模板: docs/icon-check-template.md"
```

## 📝 手动检测步骤

### 1. 快速概览检测
```bash
# 分类数量对比
ls src/renderer/src/assets/svg/color/ | wc -l
# 期望: 26

# 各分类图标数量
for dir in src/renderer/src/assets/svg/color/*/; do 
  echo "$(basename "$dir"): $(ls "$dir" | wc -l | tr -d ' ') files"
done | sort
```

### 2. 新增分类识别
```bash
# 对比基准分类列表
echo "基准分类:"
echo "alphhabetAndNumbers bubbleTea cinema city commercial emojis food fruits furniture graduation halloween herbs history information insect languageLearning magic music nature ocean plants Playground study SweetCandy transportation weather" | tr ' ' '\n' | sort

echo -e "\n当前分类:"
ls src/renderer/src/assets/svg/color/ | sort

echo -e "\n差异对比:"
diff <(echo "alphhabetAndNumbers bubbleTea cinema city commercial emojis food fruits furniture graduation halloween herbs history information insect languageLearning magic music nature ocean plants Playground study SweetCandy transportation weather" | tr ' ' '\n' | sort) <(ls src/renderer/src/assets/svg/color/ | sort)
```

### 3. 中文文件名检测
```bash
# 检查需要翻译的分类
echo "=== 中文文件名分类状态 ==="

echo "Commercial (22个文件):"
ls src/renderer/src/assets/svg/color/commercial/ | head -5

echo -e "\nEmojis (13个文件):"
ls src/renderer/src/assets/svg/color/emojis/ | head -5

echo -e "\nInformation (5个文件):"
ls src/renderer/src/assets/svg/color/information/

echo -e "\nFruits (1个文件):"
ls src/renderer/src/assets/svg/color/fruits/
```

## 🔄 使用场景

### 场景1: 每周例行检查
1. 运行快速检测命令
2. 如无变化，继续现有工作计划
3. 如有变化，更新状态文档

### 场景2: 发现新增内容
1. 使用详细检测脚本分析变化
2. 记录新增分类和图标数量
3. 评估配置优先级
4. 更新工作计划

### 场景3: 配置前后对比
1. 配置前运行检测获取基准
2. 完成配置后再次检测
3. 确认配置效果和完整性

## 📊 检测结果解读

### 正常情况
- 分类数量 = 26
- 图标总数 = 953
- 中文文件名分类状态稳定

### 需要注意的情况
- 分类数量增加: 有新增分类，需要配置
- 图标总数增加: 有新增图标，需要检查
- 中文文件名消失: 可能已完成翻译配置

### 异常情况  
- 分类数量减少: 可能有分类被删除或重命名
- 图标总数减少: 可能有图标被删除
- 应立即调查并更新文档