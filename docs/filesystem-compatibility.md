# 文件系统跨平台兼容性指南

## 概述

ChronNote 支持多平台运行（Electron、Web、iOS），每个平台的文件系统 API 返回的数据格式可能不同。本文档描述了跨平台文件系统操作的最佳实践。

## 问题背景

### 不同平台的 fs.stat() 返回格式

在使用 `fs.stat()` 获取文件信息时，不同平台返回的对象格式存在差异：

#### Node.js (Electron)
```typescript
{
  dev: 16777229,
  mode: 16877,  // 包含文件类型和权限信息
  nlink: 6,
  uid: 501,
  gid: 20,
  isDirectory: function() { return true }, // 函数形式
  isFile: function() { return false },
  // ... 其他属性
}
```

#### Web/Browser 环境
```typescript
{
  mode: 16877,
  isDirectory: true,  // 布尔属性形式
  isFile: false,
  // ... 其他属性可能缺失
}
```

#### 某些特殊环境
```typescript
{
  mode: 16877,  // 只有 mode 字段，需要通过位运算判断
  // 缺少 isDirectory/isFile 属性或方法
}
```

## 解决方案

### 统一的文件类型检测工具

我们创建了 `src/renderer/src/utils/fsUtils.ts` 来处理跨平台兼容性：

```typescript
import { isDirectory, isFile, getFileType } from '@renderer/utils/fsUtils'

// 使用示例
const fs = new FileSystemManager()
const stats = await fs.stat('/some/path')

if (isDirectory(stats)) {
  console.log('这是一个目录')
}

if (isFile(stats)) {
  console.log('这是一个普通文件')
}

console.log('文件类型:', getFileType(stats))
```

### 检测逻辑

`isDirectory()` 函数使用三层检测机制：

1. **方法检测**: 检查 `stats.isDirectory` 是否为函数并调用
2. **属性检测**: 检查 `stats.isDirectory` 是否为布尔值
3. **位运算检测**: 通过 `stats.mode` 字段的位运算判断文件类型

```typescript
export function isDirectory(stats: any): boolean {
  // 方法1: 标准Node.js Stats对象
  if (typeof stats.isDirectory === 'function') {
    return stats.isDirectory()
  }
  
  // 方法2: 布尔属性形式
  if (typeof stats.isDirectory === 'boolean') {
    return stats.isDirectory
  }
  
  // 方法3: 位运算判断 (兜底方案)
  if (typeof stats.mode === 'number') {
    return (stats.mode & 0o170000) === 0o040000
  }
  
  return false
}
```

### 文件模式位说明

Unix 文件系统使用 mode 字段的高位来标识文件类型：

| 文件类型 | 八进制值 | 说明 |
|---------|---------|------|
| S_IFDIR | 0o040000 | 目录 |
| S_IFREG | 0o100000 | 普通文件 |
| S_IFLNK | 0o120000 | 符号链接 |
| S_IFCHR | 0o020000 | 字符设备 |
| S_IFBLK | 0o060000 | 块设备 |
| S_IFIFO | 0o010000 | FIFO (命名管道) |
| S_IFSOCK | 0o140000 | Socket |

使用 `0o170000` 作为掩码提取文件类型信息。

## 最佳实践

### 1. 始终使用工具函数

❌ **错误做法**:
```typescript
const stats = await fs.stat(path)
if (stats.isDirectory()) {  // 可能在某些平台失败
  // ...
}
```

✅ **正确做法**:
```typescript
import { isDirectory } from '@renderer/utils/fsUtils'

const stats = await fs.stat(path)
if (isDirectory(stats)) {  // 跨平台兼容
  // ...
}
```

### 2. 处理边界情况

始终在 try-catch 块中使用文件系统操作：

```typescript
try {
  const stats = await fs.stat(path)
  if (isDirectory(stats)) {
    // 目录处理逻辑
  } else if (isFile(stats)) {
    // 文件处理逻辑
  }
} catch (error) {
  console.error('文件系统操作失败:', error)
  // 错误处理逻辑
}
```

### 3. 调试和日志

使用 `getFileType()` 函数获取可读的文件类型信息，便于调试：

```typescript
const stats = await fs.stat(path)
console.log(`文件 ${path} 的类型: ${getFileType(stats)}`)
```

## 已知问题和解决方案

### 问题1: `isDirectory is not a function`

**原因**: 在某些平台上，`stats.isDirectory` 不是函数而是布尔值或不存在。

**解决**: 使用 `fsUtils.isDirectory()` 工具函数。

### 问题2: 文件类型检测不准确

**原因**: 不同平台的 `fs.stat()` 实现可能存在差异。

**解决**: 依次尝试多种检测方法，最后使用位运算作为兜底。

### 问题3: 性能考虑

**注意**: 位运算检测是最快的方法，但需要确保 `mode` 字段存在且格式正确。

## 相关文件

- `src/renderer/src/utils/fsUtils.ts` - 跨平台文件系统工具函数
- `src/renderer/src/utils/migrationService.ts` - 使用示例（存储迁移服务）
- `src/renderer/src/utils/storageCompatibilityAdapter.ts` - 使用示例（存储兼容性适配器）

## 更新记录

- **2025-07-06**: 创建文档，抽离 `fsUtils.ts` 工具函数
- **2025-07-06**: 修复 `migrationService.ts` 中的 `isDirectory` 兼容性问题