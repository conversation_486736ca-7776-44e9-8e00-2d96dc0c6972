# 图标管理系统

## 概述

这是一个自动化的图标配置管理系统，可以扫描 `src/renderer/src/assets/svg/color` 目录下的所有图标文件夹，并自动生成图标配置文件。

## 目录结构

```
src/renderer/src/assets/svg/color/
├── commercial/           # 商业金融 (22个图标)
├── emojis/              # 表情符号 (13个图标)
├── food/                # 食物 (14个图标)
├── transportation/      # 交通工具 (32个图标)
├── weather/             # 天气 (21个图标)
└── ...                  # 更多分类
```

## 使用方法

### 命令列表

```bash
# 扫描图标并生成配置（增量检测）
npm run icon

# 等同于上面的命令
npm run icon:scan

# 强制重新扫描所有图标（忽略缓存）
npm run icon:force
```

### 工作流程

1. **扫描文件夹**: 自动扫描 `svg/color` 目录下的所有子文件夹
2. **增量检测**: 对比上次扫描结果，检测新增、删除或修改的文件夹
3. **生成配置**: 为每个图标自动生成别名和标签
4. **更新配置文件**: 生成新的 `iconConfig.ts` 配置文件

### 增量检测功能

系统会自动检测以下变化：

- 🆕 **新增文件夹**: 发现新的图标分类
- 🗑️ **删除文件夹**: 检测已移除的分类
- 📝 **文件夹变化**: 检测新增或删除的图标文件
- ✅ **无变化**: 如果没有任何变化会显示确认信息

## 配置文件结构

生成的 `iconConfig.ts` 包含：

### 分类配置 (IconCategory)

```typescript
{
  id: "commercial",              // 文件夹名称
  labelZh: "商业金融",           // 中文标签
  labelEn: "Business",           // 英文标签
  iconCount: 22                  // 图标数量
}
```

### 图标配置 (IconEnhancement)

```typescript
{
  id: "commercial-books",        // 唯一标识
  category: "commercial",        // 所属分类
  filename: "书籍资料.svg",      // 文件名
  displayName: "书籍资料",       // 显示名称
  aliases: [                     // 搜索别名
    "书籍资料",
    "书籍", "资料", "文档",
    "books", "documents", "book"
  ],
  tags: [                        // 标签
    "商业", "办公", "business", "office"
  ]
}
```

## 别名生成规则

系统会自动为每个图标生成多种别名：

1. **原始文件名**: 包含完整文件名
2. **清理后名称**: 移除编号前缀 (001-, 002- 等)
3. **格式变体**: 处理空格、连字符等
4. **分类别名**: 添加分类相关的别名
5. **关键词匹配**: 根据文件名匹配预设的关键词别名
6. **中文支持**: 特别处理中文图标名

## 状态管理

- **状态文件**: `.icon-state.json` (在项目根目录)
- **内容**: 记录上次扫描的完整状态
- **用途**: 实现增量检测，避免重复处理

## 输出示例

### 首次扫描
```
🚀 图标配置生成器启动
🔍 开始扫描图标文件夹...
✅ 扫描完成: 26 个文件夹, 858 个图标
🆕 首次扫描，所有文件夹都是新的

📊 扫描报告
🆕 新增文件夹:
   • commercial (22 个图标)
   • emojis (13 个图标)
   • food (14 个图标)
   ...

📈 总计: 26 个文件夹, 858 个图标
🎉 图标配置生成完成!
```

### 增量扫描 (无变化)
```
📊 扫描报告
✅ 无变化 - 所有文件夹都是最新的

📈 总计: 26 个文件夹, 858 个图标
🆕 本次新增: 0 个图标
```

### 增量扫描 (有变化)
```
📊 扫描报告
🆕 新增文件夹:
   • newCategory (5 个图标)
     路径: /path/to/newCategory

📝 有变化的文件夹:
   • commercial: 22 → 25 个图标
     新增图标: new-icon1.svg, new-icon2.svg

📈 总计: 27 个文件夹, 863 个图标
🆕 本次新增: 8 个图标
```

## 使用建议

1. **定期运行**: 添加新图标后运行 `npm run icon`
2. **CI/CD 集成**: 可以集成到构建流程中
3. **版本控制**: 将生成的配置文件提交到 Git
4. **强制更新**: 如果配置异常，使用 `npm run icon:force`

## 文件位置

- **脚本**: `scripts/icon-config-generator.js`
- **图标目录**: `src/renderer/src/assets/svg/color/`
- **配置文件**: `src/renderer/src/config/iconConfig.ts`
- **状态文件**: `.icon-state.json`