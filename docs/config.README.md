# 配置管理系统 - 前端接口封装

本文档介绍了配置管理系统的前端接口封装，包括API请求封装、Vue组合式函数和组件使用示例。

## 📁 文件结构

```
src/renderer/src/
├── api/uniform/
│   ├── config.ts              # 配置管理API接口封装
│   ├── config.example.ts      # API使用示例
│   └── config.README.md       # 本文档
├── composables/
│   └── useConfig.ts           # Vue配置管理组合式函数
└── components/
    └── ConfigManager.vue      # 配置管理组件示例
```

## 🚀 快速开始

### 1. 基本API使用

```typescript
import { 
  getAllConfigs, 
  getConfig, 
  setConfig, 
  batchSetConfigs,
  deleteConfig 
} from '@renderer/api/uniform/config'

// 获取所有配置
const allConfigs = await getAllConfigs()
console.log(allConfigs.data.configs)

// 获取单个配置
const appName = await getConfig('app.name')
console.log(appName.data.value)

// 设置配置
await setConfig('app.name', 'ChronNote V6')

// 批量设置配置
await batchSetConfigs({
  'app.version': '6.0.0',
  'app.debug': true
})

// 删除配置
await deleteConfig('temp.config')
```

### 2. 类型安全的配置访问

```typescript
import { ConfigAccessor } from '@renderer/api/uniform/config'

// 类型安全的getter方法
const appName = await ConfigAccessor.getString('app.name', 'Default App')
const port = await ConfigAccessor.getNumber('server.port', 3000)
const debug = await ConfigAccessor.getBoolean('app.debug', false)
const config = await ConfigAccessor.getObject('database.config', {})
const features = await ConfigAccessor.getArray('app.features', [])

// 类型安全的setter方法
await ConfigAccessor.setString('app.name', 'ChronNote')
await ConfigAccessor.setNumber('server.port', 4000)
await ConfigAccessor.setBoolean('app.debug', true)
await ConfigAccessor.setObject('database.config', { host: 'localhost' })
await ConfigAccessor.setArray('app.features', ['feature1', 'feature2'])
```

### 3. 配置管理工具

```typescript
import { ConfigManager } from '@renderer/api/uniform/config'

// 检查配置是否存在
const exists = await ConfigManager.hasConfig('app.name')

// 获取配置，不存在则设置默认值
const language = await ConfigManager.getOrSetDefault('app.language', 'zh-CN')

// 批量获取配置
const configs = await ConfigManager.batchGetConfigs([
  'app.name', 'app.version', 'app.debug'
])

// 复制配置
await ConfigManager.copyConfig('app.name', 'app.displayName')

// 移动配置（重命名）
await ConfigManager.moveConfig('app.displayName', 'app.title')
```

## 🎯 Vue组合式函数使用

### 1. 基本配置管理

```vue
<script setup lang="ts">
import { useConfig } from '@renderer/composables/useConfig'

const { 
  configs, 
  loading, 
  hasError, 
  errorMessage,
  loadConfigs,
  getConfigValue,
  setConfigValue,
  setBatchConfigs,
  removeConfig,
  hasConfig,
  resetConfig,
  addChangeListener,
  clearError
} = useConfig()

// 组件挂载时加载配置
onMounted(async () => {
  await loadConfigs()
})

// 监听配置变更
const removeListener = addChangeListener((key, newValue, oldValue) => {
  console.log(`配置 ${key} 从 ${oldValue} 变更为 ${newValue}`)
})

// 组件卸载时移除监听器
onUnmounted(() => {
  removeListener()
})
</script>
```

### 2. 类型安全的配置访问

```vue
<script setup lang="ts">
import { useTypeSafeConfig } from '@renderer/composables/useConfig'

const { 
  getString, 
  getNumber, 
  getBoolean, 
  getObject, 
  getArray,
  setString, 
  setNumber, 
  setBoolean, 
  setObject, 
  setArray 
} = useTypeSafeConfig()

// 响应式配置值
const appName = computed(() => getString('app.name', 'Default App'))
const serverPort = computed(() => getNumber('server.port', 3000))
const debugMode = computed(() => getBoolean('app.debug', false))

// 更新配置
const updateAppName = async (name: string) => {
  await setString('app.name', name)
}
</script>
```

### 3. 应用配置管理

```vue
<script setup lang="ts">
import { useAppConfig } from '@renderer/composables/useConfig'

const { 
  appName, 
  appVersion, 
  appDebug, 
  appLanguage, 
  appTheme,
  setAppName,
  setAppVersion,
  setAppDebug,
  setAppLanguage,
  setAppTheme
} = useAppConfig()

// 直接使用响应式配置值
console.log('当前应用名称:', appName.value)
console.log('当前应用版本:', appVersion.value)

// 更新配置
const toggleDebugMode = async () => {
  await setAppDebug(!appDebug.value)
}
</script>
```

## 🎨 组件使用示例

### 配置管理组件

```vue
<template>
  <ConfigManager />
</template>

<script setup lang="ts">
import ConfigManager from '@renderer/components/ConfigManager.vue'
</script>
```

### 自定义配置组件

```vue
<template>
  <div class="config-panel">
    <h3>应用设置</h3>
    
    <!-- 应用名称 -->
    <div class="form-control">
      <label>应用名称</label>
      <input 
        v-model="localAppName" 
        @blur="updateAppName"
        type="text" 
        class="input input-bordered"
      />
    </div>
    
    <!-- 调试模式 -->
    <div class="form-control">
      <label class="label cursor-pointer">
        <span>调试模式</span>
        <input 
          v-model="localDebugMode" 
          @change="updateDebugMode"
          type="checkbox" 
          class="checkbox"
        />
      </label>
    </div>
    
    <!-- 主题选择 -->
    <div class="form-control">
      <label>主题</label>
      <select 
        v-model="localTheme" 
        @change="updateTheme"
        class="select select-bordered"
      >
        <option value="light">浅色</option>
        <option value="dark">深色</option>
        <option value="auto">自动</option>
      </select>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { useAppConfig } from '@renderer/composables/useConfig'

const { 
  appName, 
  appDebug, 
  appTheme,
  setAppName,
  setAppDebug,
  setAppTheme
} = useAppConfig()

// 本地状态
const localAppName = ref('')
const localDebugMode = ref(false)
const localTheme = ref('')

// 监听配置变化，同步到本地状态
watch([appName, appDebug, appTheme], () => {
  localAppName.value = appName.value
  localDebugMode.value = appDebug.value
  localTheme.value = appTheme.value
}, { immediate: true })

// 更新方法
const updateAppName = async () => {
  if (localAppName.value !== appName.value) {
    await setAppName(localAppName.value)
  }
}

const updateDebugMode = async () => {
  if (localDebugMode.value !== appDebug.value) {
    await setAppDebug(localDebugMode.value)
  }
}

const updateTheme = async () => {
  if (localTheme.value !== appTheme.value) {
    await setAppTheme(localTheme.value)
  }
}
</script>
```

## 📋 配置键常量

系统提供了预定义的配置键常量，方便使用：

```typescript
import { CONFIG_KEYS, DEFAULT_CONFIG_VALUES } from '@renderer/api/uniform/config'

// 应用配置
CONFIG_KEYS.APP_NAME          // 'app.name'
CONFIG_KEYS.APP_VERSION       // 'app.version'
CONFIG_KEYS.APP_DEBUG         // 'app.debug'
CONFIG_KEYS.APP_LANGUAGE      // 'app.language'
CONFIG_KEYS.APP_THEME         // 'app.theme'

// 服务器配置
CONFIG_KEYS.SERVER_PORT       // 'server.port'
CONFIG_KEYS.SERVER_HOST       // 'server.host'

// 功能开关
CONFIG_KEYS.FEATURES_ENABLED  // 'features.enabled'
CONFIG_KEYS.FEATURES_LIST     // 'features.list'

// 用户偏好
CONFIG_KEYS.USER_PREFERENCES  // 'user.preferences'
CONFIG_KEYS.USER_SETTINGS     // 'user.settings'

// 默认值
DEFAULT_CONFIG_VALUES[CONFIG_KEYS.APP_NAME]     // 'ChronNote'
DEFAULT_CONFIG_VALUES[CONFIG_KEYS.APP_VERSION]  // '1.0.0'
DEFAULT_CONFIG_VALUES[CONFIG_KEYS.APP_DEBUG]    // false
```

## 🔧 错误处理

所有API调用都包含了完善的错误处理：

```typescript
try {
  const config = await getConfig('app.name')
  console.log('配置获取成功:', config.data.value)
} catch (error) {
  console.error('配置获取失败:', error)
  // 处理错误逻辑
}
```

在Vue组件中使用组合式函数时，错误状态会自动管理：

```vue
<script setup lang="ts">
const { hasError, errorMessage, clearError } = useConfig()

// 在模板中显示错误
</script>

<template>
  <div v-if="hasError" class="alert alert-error">
    {{ errorMessage }}
    <button @click="clearError">清除错误</button>
  </div>
</template>
```

## 📝 最佳实践

1. **使用类型安全的访问器**：优先使用 `ConfigAccessor` 类的方法进行类型安全的配置访问
2. **配置键常量**：使用预定义的 `CONFIG_KEYS` 常量，避免硬编码字符串
3. **错误处理**：始终处理可能的错误情况，提供用户友好的错误提示
4. **响应式更新**：在Vue组件中使用组合式函数获得响应式的配置管理
5. **批量操作**：对于多个配置的更新，使用批量设置方法提高性能
6. **配置验证**：在设置配置前进行必要的验证，确保数据的有效性

## 🔗 相关文件

- `src/renderer/src/api/uniform/config.ts` - 配置管理API接口
- `src/renderer/src/api/uniform/config.example.ts` - API使用示例
- `src/renderer/src/composables/useConfig.ts` - Vue组合式函数
- `src/renderer/src/components/ConfigManager.vue` - 配置管理组件示例
