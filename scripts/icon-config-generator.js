#!/usr/bin/env node

/**
 * 图标配置生成器
 * 扫描 SVG 文件夹并生成基于文件夹的图标配置
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

// 配置路径
const SVG_DIR = path.join(__dirname, '../src/renderer/src/assets/svg/color')
const CONFIG_FILE = path.join(__dirname, '../src/renderer/src/config/iconConfig.ts')
const STATE_FILE = path.join(__dirname, '../.icon-state.json')

// 分类标签映射
const CATEGORY_LABELS = {
  'alphhabetAndNumbers': { zh: '字母数字', en: 'Alphabet & Numbers' },
  'bubbleTea': { zh: '奶茶饮品', en: 'Bubble Tea' },
  'cinema': { zh: '电影院', en: 'Cinema' },
  'city': { zh: '城市建筑', en: 'City' },
  'commercial': { zh: '商业金融', en: 'Business' },
  'emojis': { zh: '表情符号', en: 'Emojis' },
  'food': { zh: '食物', en: 'Food' },
  'fruits': { zh: '水果', en: 'Fruits' },
  'furniture': { zh: '家具', en: 'Furniture' },
  'graduation': { zh: '毕业典礼', en: 'Graduation' },
  'halloween': { zh: '万圣节', en: 'Halloween' },
  'herbs': { zh: '香草香料', en: 'Herbs' },
  'history': { zh: '历史文物', en: 'History' },
  'information': { zh: '信息图表', en: 'Information' },
  'insect': { zh: '昆虫', en: 'Insects' },
  'languageLearning': { zh: '语言学习', en: 'Language Learning' },
  'magic': { zh: '魔法奇幻', en: 'Magic' },
  'music': { zh: '音乐', en: 'Music' },
  'nature': { zh: '自然', en: 'Nature' },
  'ocean': { zh: '海洋', en: 'Ocean' },
  'plants': { zh: '植物', en: 'Plants' },
  'Playground': { zh: '游乐场', en: 'Playground' },
  'study': { zh: '学习', en: 'Study' },
  'SweetCandy': { zh: '糖果甜品', en: 'Sweet & Candy' },
  'transportation': { zh: '交通工具', en: 'Transportation' },
  'weather': { zh: '天气', en: 'Weather' }
}

// 基础别名映射
const BASE_ALIASES = {
  // 商业相关
  'books': ['书籍', '资料', '文档', 'books', 'documents', 'book', '图书'],
  'company': ['公司', '企业', 'company', 'business', '机构', 'organization'],
  'chart': ['图表', '统计', 'chart', 'graph', '数据', 'data', '分析'],
  'money': ['金钱', '钱', '货币', 'money', 'cash', 'currency', '财富'],
  
  // 表情相关
  'like': ['喜欢', '点赞', 'like', 'love', '爱', '赞', 'thumb'],
  'cry': ['哭', '悲伤', 'cry', 'sad', '伤心', '难过'],
  'happy': ['开心', '高兴', 'happy', 'smile', '快乐', 'joy', '笑'],
  'angry': ['生气', '愤怒', 'angry', 'mad', '火', 'rage'],
  
  // 食物相关
  'hamburger': ['汉堡', '汉堡包', 'hamburger', 'burger', '快餐'],
  'pizza': ['披萨', '比萨', 'pizza', '意式'],
  'coffee': ['咖啡', '拿铁', 'coffee', 'latte', '饮料', 'drink'],
  'cake': ['蛋糕', '糕点', 'cake', 'dessert', '甜品', 'sweet'],
  
  // 交通工具相关
  'car': ['汽车', '车', '轿车', 'car', 'vehicle', '小车', 'auto'],
  'bike': ['自行车', '单车', 'bike', 'bicycle', '脚踏车', 'cycle'],
  'plane': ['飞机', '航班', 'plane', 'airplane', '客机', 'flight'],
  'train': ['火车', '列车', 'train', 'railway', '铁路', 'rail'],
  
  // 天气相关
  'sun': ['太阳', '晴天', 'sun', 'sunny', '阳光', 'sunshine'],
  'rain': ['下雨', '雨天', 'rain', 'rainy', '降雨', 'wet'],
  'cloud': ['云朵', '云', 'cloud', 'cloudy', '阴天', 'overcast'],
  'snow': ['下雪', '雪花', 'snow', 'snowy', '雪', 'snowflake']
}

/**
 * 扫描文件夹获取图标信息
 */
function scanIconFolders() {
  console.log('🔍 开始扫描图标文件夹...')
  
  const folders = fs.readdirSync(SVG_DIR, { withFileTypes: true })
    .filter(dirent => dirent.isDirectory())
    .map(dirent => dirent.name)
    .sort()
  
  const result = {
    folders: {},
    totalFolders: folders.length,
    totalIcons: 0,
    scannedAt: new Date().toISOString()
  }
  
  folders.forEach(folderName => {
    const folderPath = path.join(SVG_DIR, folderName)
    
    try {
      const icons = fs.readdirSync(folderPath)
        .filter(file => file.endsWith('.svg'))
        .map(file => {
          const filePath = path.join(folderPath, file)
          // 验证文件是否真实存在
          if (!fs.existsSync(filePath)) {
            console.warn(`⚠️ 文件不存在: ${filePath}`)
            return null
          }
          return {
            filename: file,
            name: path.basename(file, '.svg'),
            path: filePath
          }
        })
        .filter(Boolean) // 过滤掉 null 值
      
      result.folders[folderName] = {
        path: folderPath,
        icons: icons,
        count: icons.length
      }
      
      result.totalIcons += icons.length
    } catch (error) {
      console.warn(`⚠️ 读取文件夹失败: ${folderPath}, 错误: ${error.message}`)
    }
  })
  
  console.log(`✅ 扫描完成: ${result.totalFolders} 个文件夹, ${result.totalIcons} 个图标`)
  return result
}

/**
 * 加载上次的状态
 */
function loadPreviousState() {
  if (!fs.existsSync(STATE_FILE)) {
    return null
  }
  
  try {
    const content = fs.readFileSync(STATE_FILE, 'utf8')
    return JSON.parse(content)
  } catch (error) {
    console.warn('⚠️ 无法读取上次状态文件:', error.message)
    return null
  }
}

/**
 * 保存当前状态
 */
function saveCurrentState(state) {
  try {
    fs.writeFileSync(STATE_FILE, JSON.stringify(state, null, 2))
    console.log('💾 状态已保存')
  } catch (error) {
    console.error('❌ 保存状态失败:', error.message)
  }
}

/**
 * 检测变化
 */
function detectChanges(currentScan, previousState) {
  if (!previousState) {
    console.log('🆕 首次扫描，所有文件夹都是新的')
    return {
      newFolders: Object.keys(currentScan.folders),
      removedFolders: [],
      changedFolders: [],
      newIcons: currentScan.totalIcons,
      summary: `首次扫描: ${currentScan.totalFolders} 个文件夹, ${currentScan.totalIcons} 个图标`
    }
  }
  
  const currentFolders = new Set(Object.keys(currentScan.folders))
  const previousFolders = new Set(Object.keys(previousState.folders))
  
  const newFolders = [...currentFolders].filter(folder => !previousFolders.has(folder))
  const removedFolders = [...previousFolders].filter(folder => !currentFolders.has(folder))
  const changedFolders = []
  
  let newIconsCount = 0
  
  // 检查每个文件夹的变化
  for (const folderName of currentFolders) {
    if (!previousFolders.has(folderName)) {
      continue // 新文件夹已经在 newFolders 中处理
    }
    
    const currentIcons = currentScan.folders[folderName].icons.map(icon => icon.filename)
    const previousIcons = previousState.folders[folderName]?.icons.map(icon => icon.filename) || []
    
    if (currentIcons.length !== previousIcons.length || 
        !currentIcons.every(icon => previousIcons.includes(icon))) {
      const newIconsInFolder = currentIcons.filter(icon => !previousIcons.includes(icon))
      changedFolders.push({
        name: folderName,
        newIcons: newIconsInFolder,
        totalIcons: currentIcons.length,
        previousTotal: previousIcons.length
      })
      newIconsCount += newIconsInFolder.length
    }
  }
  
  return {
    newFolders,
    removedFolders,
    changedFolders,
    newIcons: newIconsCount,
    summary: generateChangeSummary(newFolders, removedFolders, changedFolders, newIconsCount)
  }
}

/**
 * 生成变化摘要
 */
function generateChangeSummary(newFolders, removedFolders, changedFolders, newIcons) {
  const changes = []
  
  if (newFolders.length > 0) {
    changes.push(`${newFolders.length} 个新文件夹`)
  }
  
  if (removedFolders.length > 0) {
    changes.push(`${removedFolders.length} 个已删除文件夹`)
  }
  
  if (changedFolders.length > 0) {
    changes.push(`${changedFolders.length} 个文件夹有变化`)
  }
  
  if (newIcons > 0) {
    changes.push(`${newIcons} 个新图标`)
  }
  
  if (changes.length === 0) {
    return '无变化'
  }
  
  return changes.join(', ')
}

/**
 * 生成图标别名
 */
function generateIconAliases(iconName, categoryName) {
  const aliases = new Set()
  
  // 添加原始文件名
  aliases.add(iconName)
  
  // 移除编号前缀 (001-, 002- 等)
  const cleanName = iconName.replace(/^\d+-/, '')
  aliases.add(cleanName)
  
  // 处理空格和连字符
  aliases.add(cleanName.replace(/[-\s]+/g, ' '))
  aliases.add(cleanName.replace(/[-\s]+/g, ''))
  
  // 添加分类相关别名
  const categoryInfo = CATEGORY_LABELS[categoryName]
  if (categoryInfo) {
    aliases.add(categoryInfo.zh)
    aliases.add(categoryInfo.en.toLowerCase())
  }
  
  // 根据关键词匹配基础别名
  for (const [keyword, keywordAliases] of Object.entries(BASE_ALIASES)) {
    if (cleanName.toLowerCase().includes(keyword.toLowerCase())) {
      keywordAliases.forEach(alias => aliases.add(alias))
    }
  }
  
  // 特殊处理中文图标名
  if (/[\u4e00-\u9fa5]/.test(iconName)) {
    aliases.add(iconName)
  }
  
  return [...aliases].filter(alias => alias && alias.trim())
}

/**
 * 生成标签
 */
function generateTags(iconName, categoryName) {
  const tags = new Set()
  
  // 添加分类标签
  const categoryInfo = CATEGORY_LABELS[categoryName]
  if (categoryInfo) {
    tags.add(categoryInfo.zh)
    tags.add(categoryInfo.en.toLowerCase())
  }
  
  // 根据分类添加通用标签
  const categoryTags = {
    'commercial': ['商业', '办公', 'business', 'office'],
    'emojis': ['表情', '情感', 'emotion', 'feeling'],
    'food': ['食物', '餐饮', 'food', 'dining'],
    'transportation': ['交通', '出行', 'transport', 'vehicle'],
    'weather': ['天气', '气象', 'weather', 'climate'],
    'study': ['学习', '教育', 'study', 'education'],
    'music': ['音乐', '娱乐', 'music', 'entertainment']
  }
  
  if (categoryTags[categoryName]) {
    categoryTags[categoryName].forEach(tag => tags.add(tag))
  }
  
  return [...tags]
}

/**
 * 生成配置文件内容
 */
function generateConfigFile(scanResult) {
  console.log('📝 生成配置文件...')
  
  const categories = Object.keys(scanResult.folders).map(folderName => {
    const categoryInfo = CATEGORY_LABELS[folderName] || { zh: folderName, en: folderName }
    return {
      id: folderName,
      labelZh: categoryInfo.zh,
      labelEn: categoryInfo.en,
      iconCount: scanResult.folders[folderName].count
    }
  })
  
  const iconEnhancements = []
  
  Object.entries(scanResult.folders).forEach(([folderName, folderData]) => {
    folderData.icons.forEach(icon => {
      const aliases = generateIconAliases(icon.name, folderName)
      const tags = generateTags(icon.name, folderName)
      
      iconEnhancements.push({
        id: `${folderName}-${icon.name}`,
        category: folderName,
        filename: icon.filename,
        displayName: icon.name.replace(/^\d+-/, '').replace(/[-_]/g, ' '),
        aliases: aliases,
        tags: tags
      })
    })
  })
  
  return `/**
 * 图标配置文件
 * 自动生成于: ${new Date().toISOString()}
 * 总计: ${categories.length} 个分类, ${iconEnhancements.length} 个图标
 */

export interface IconCategory {
  id: string
  labelZh: string
  labelEn: string
  iconCount: number
}

export interface IconEnhancement {
  id: string
  category: string
  filename: string
  displayName: string
  aliases: string[]
  tags: string[]
}

// 分类配置
export const iconCategories: IconCategory[] = ${JSON.stringify(categories, null, 2)}

// 图标增强配置
export const iconEnhancements: IconEnhancement[] = ${JSON.stringify(iconEnhancements, null, 2)}

// 配置统计
export const configStats = {
  totalCategories: ${categories.length},
  totalIcons: ${iconEnhancements.length},
  generatedAt: '${new Date().toISOString()}'
}

// 便捷查找函数
export function findIconsByCategory(categoryId: string): IconEnhancement[] {
  return iconEnhancements.filter(icon => icon.category === categoryId)
}

export function findIconById(iconId: string): IconEnhancement | undefined {
  return iconEnhancements.find(icon => icon.id === iconId)
}

export function searchIcons(query: string): IconEnhancement[] {
  const normalizedQuery = query.toLowerCase()
  return iconEnhancements.filter(icon => {
    const searchTexts = [
      icon.displayName,
      icon.filename,
      ...icon.aliases,
      ...icon.tags
    ]
    return searchTexts.some(text => 
      text.toLowerCase().includes(normalizedQuery)
    )
  })
}
`
}

/**
 * 显示变化报告
 */
function displayChangeReport(changes, scanResult) {
  console.log('\n📊 扫描报告')
  console.log('='.repeat(50))
  
  if (changes.newFolders.length > 0) {
    console.log('🆕 新增文件夹:')
    changes.newFolders.forEach(folder => {
      const iconCount = scanResult.folders[folder].count
      console.log(`   • ${folder} (${iconCount} 个图标)`)
      console.log(`     路径: ${scanResult.folders[folder].path}`)
    })
  }
  
  if (changes.removedFolders.length > 0) {
    console.log('🗑️ 已删除文件夹:')
    changes.removedFolders.forEach(folder => {
      console.log(`   • ${folder}`)
    })
  }
  
  if (changes.changedFolders.length > 0) {
    console.log('📝 有变化的文件夹:')
    changes.changedFolders.forEach(folder => {
      console.log(`   • ${folder.name}: ${folder.previousTotal} → ${folder.totalIcons} 个图标`)
      if (folder.newIcons.length > 0) {
        console.log(`     新增图标: ${folder.newIcons.join(', ')}`)
      }
    })
  }
  
  if (changes.newFolders.length === 0 && changes.removedFolders.length === 0 && changes.changedFolders.length === 0) {
    console.log('✅ 无变化 - 所有文件夹都是最新的')
  }
  
  console.log(`\n📈 总计: ${scanResult.totalFolders} 个文件夹, ${scanResult.totalIcons} 个图标`)
  console.log(`🆕 本次新增: ${changes.newIcons} 个图标`)
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 图标配置生成器启动')
  console.log('='.repeat(50))
  
  try {
    // 扫描当前图标
    const currentScan = scanIconFolders()
    
    // 加载上次状态
    const previousState = loadPreviousState()
    
    // 检测变化
    const changes = detectChanges(currentScan, previousState)
    
    // 显示变化报告
    displayChangeReport(changes, currentScan)
    
    // 生成配置文件
    const configContent = generateConfigFile(currentScan)
    fs.writeFileSync(CONFIG_FILE, configContent)
    console.log(`\n✅ 配置文件已更新: ${CONFIG_FILE}`)
    
    // 保存当前状态
    saveCurrentState(currentScan)
    
    console.log('\n🎉 图标配置生成完成!')
    
  } catch (error) {
    console.error('❌ 生成失败:', error.message)
    console.error(error.stack)
    process.exit(1)
  }
}

// 运行主函数
if (require.main === module) {
  main()
}

module.exports = {
  scanIconFolders,
  detectChanges,
  generateConfigFile
}