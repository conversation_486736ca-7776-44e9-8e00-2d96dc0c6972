#!/usr/bin/env node

/**
 * 存储兼容性适配器演示程序
 * 使用真实的backup数据测试兼容性功能
 */

const fs = require('fs').promises
const path = require('path')

// 模拟必要的全局对象
global.window = {
  electron: {
    getPath: () => '/mock/path',
    getCwd: () => process.cwd()
  }
}

// 模拟Vue的ref函数
global.ref = (value) => ({ value })

class MockFileSystemManager {
  async stat(filePath) {
    try {
      const stats = await fs.stat(filePath)
      return {
        isFile: () => stats.isFile(),
        isDirectory: () => stats.isDirectory(),
        size: stats.size
      }
    } catch (error) {
      throw error
    }
  }

  async readdir(dirPath) {
    return await fs.readdir(dirPath)
  }

  async readFile(filePath) {
    return await fs.readFile(filePath, 'utf-8')
  }

  async writeFile(filePath, content) {
    await fs.writeFile(filePath, content)
  }

  async mkdir(dirPath) {
    await fs.mkdir(dirPath, { recursive: true })
  }

  async copy(src, dest) {
    const stats = await fs.stat(src)
    if (stats.isDirectory()) {
      await fs.mkdir(dest, { recursive: true })
      const items = await fs.readdir(src)
      for (const item of items) {
        await this.copy(path.join(src, item), path.join(dest, item))
      }
    } else {
      await fs.copyFile(src, dest)
    }
  }

  async remove(filePath) {
    await fs.rm(filePath, { recursive: true, force: true })
  }
}

// 模拟配置
const mockConfig = {
  workspacePath: path.resolve(__dirname, '../test-workspace')
}

// 模拟useConfig
global.useConfig = () => mockConfig

// 模拟平台检测
global.getPlatform = () => 'electron'

async function setupTestEnvironment() {
  console.log('🔧 设置测试环境...')
  
  // 清理并创建测试工作空间
  await fs.rm(mockConfig.workspacePath, { recursive: true, force: true })
  await fs.mkdir(mockConfig.workspacePath, { recursive: true })
  
  console.log(`✅ 测试工作空间创建: ${mockConfig.workspacePath}`)
}

async function testLegacyArchitecture() {
  console.log('\n📦 测试老架构识别...')
  
  const userId = '3492ed08-b2dc-45bd-abed-20090a1954e5'
  const examplePath = `${mockConfig.workspacePath}/example`
  
  // 创建老架构结构
  await fs.mkdir(examplePath, { recursive: true })
  await fs.mkdir(`${examplePath}/img`, { recursive: true })
  await fs.mkdir(`${examplePath}/pdf`, { recursive: true })
  await fs.writeFile(`${examplePath}/chron_${userId}.db`, 'mock database content')
  await fs.writeFile(`${examplePath}/blocks_${userId}.json`, '[]')
  
  // 动态导入适配器
  const { StorageCompatibilityAdapter } = await import('../dist/renderer/utils/storageCompatibilityAdapter.js')
  const adapter = new StorageCompatibilityAdapter()
  
  const mode = await adapter.detectStorageMode(userId)
  console.log(`✅ 检测到存储模式: ${mode}`)
  
  if (mode === 'legacy') {
    const dbPath = await adapter.getAdaptiveDbPath(userId)
    const imgPath = await adapter.getAdaptiveImgPath(userId)
    const pdfPath = await adapter.getAdaptivePdfPath(userId)
    const blocksPath = await adapter.getAdaptiveBlocksPath(userId)
    
    console.log(`📍 数据库路径: ${dbPath}`)
    console.log(`🖼️  图片路径: ${imgPath}`)
    console.log(`📄 PDF路径: ${pdfPath}`)
    console.log(`🧱 Blocks路径: ${blocksPath}`)
    
    // 验证路径正确性
    const expectedDbPath = `${examplePath}/chron_${userId}`
    const expectedImgPath = `${examplePath}/img`
    const expectedPdfPath = `${examplePath}/pdf`
    const expectedBlocksPath = `${examplePath}/blocks_${userId}.json`
    
    console.log(`✅ 数据库路径正确: ${dbPath === expectedDbPath}`)
    console.log(`✅ 图片路径正确: ${imgPath === expectedImgPath}`)
    console.log(`✅ PDF路径正确: ${pdfPath === expectedPdfPath}`)
    console.log(`✅ Blocks路径正确: ${blocksPath === expectedBlocksPath}`)
  } else {
    console.log('❌ 应该检测到legacy模式，但检测到了modern模式')
  }
}

async function testModernArchitecture() {
  console.log('\n🏗️ 测试新架构识别...')
  
  const userId = '3492ed08-b2dc-45bd-abed-20090a1954e5'
  const usersPath = `${mockConfig.workspacePath}/users`
  const userPath = `${usersPath}/${userId}`
  
  // 创建新架构结构
  await fs.mkdir(userPath, { recursive: true })
  await fs.mkdir(`${userPath}/img`, { recursive: true })
  await fs.mkdir(`${userPath}/pdf`, { recursive: true })
  await fs.writeFile(`${userPath}/chron.db`, 'mock database content')
  await fs.writeFile(`${userPath}/blocks.json`, '[]')
  
  // 动态导入适配器
  const { StorageCompatibilityAdapter } = await import('../dist/renderer/utils/storageCompatibilityAdapter.js')
  const adapter = new StorageCompatibilityAdapter()
  
  // 清除缓存以重新检测
  adapter.clearCache()
  
  const mode = await adapter.detectStorageMode(userId)
  console.log(`✅ 检测到存储模式: ${mode}`)
  
  if (mode === 'modern') {
    const dbPath = await adapter.getAdaptiveDbPath(userId)
    const imgPath = await adapter.getAdaptiveImgPath(userId)
    const pdfPath = await adapter.getAdaptivePdfPath(userId)
    const blocksPath = await adapter.getAdaptiveBlocksPath(userId)
    
    console.log(`📍 数据库路径: ${dbPath}`)
    console.log(`🖼️  图片路径: ${imgPath}`)
    console.log(`📄 PDF路径: ${pdfPath}`)
    console.log(`🧱 Blocks路径: ${blocksPath}`)
    
    // 验证路径正确性
    const expectedDbPath = `${userPath}/chron`
    const expectedImgPath = `${userPath}/img`
    const expectedPdfPath = `${userPath}/pdf`
    const expectedBlocksPath = `${userPath}/blocks.json`
    
    console.log(`✅ 数据库路径正确: ${dbPath === expectedDbPath}`)
    console.log(`✅ 图片路径正确: ${imgPath === expectedImgPath}`)
    console.log(`✅ PDF路径正确: ${pdfPath === expectedPdfPath}`)
    console.log(`✅ Blocks路径正确: ${blocksPath === expectedBlocksPath}`)
  } else {
    console.log('❌ 应该检测到modern模式，但检测到了legacy模式')
  }
}

async function testRealBackupData() {
  console.log('\n📂 测试真实backup数据...')
  
  const backupPath = path.resolve(__dirname, '../backup/2025-07-05T18-37-50')
  const testExamplePath = `${mockConfig.workspacePath}/example`
  
  try {
    // 检查backup目录是否存在
    await fs.stat(backupPath)
    
    // 复制backup数据到测试环境
    const mockFS = new MockFileSystemManager()
    await mockFS.copy(backupPath, testExamplePath)
    
    console.log(`✅ 复制backup数据完成: ${backupPath} -> ${testExamplePath}`)
    
    // 列出复制的文件
    const files = await fs.readdir(testExamplePath)
    console.log(`📁 复制的文件: ${files.join(', ')}`)
    
    // 查找用户ID
    const dbFiles = files.filter(file => file.startsWith('chron_') && file.endsWith('.db'))
    if (dbFiles.length > 0) {
      const userId = dbFiles[0].replace('chron_', '').replace('.db', '')
      console.log(`👤 发现用户ID: ${userId}`)
      
      // 测试适配器
      const { StorageCompatibilityAdapter } = await import('../dist/renderer/utils/storageCompatibilityAdapter.js')
      const adapter = new StorageCompatibilityAdapter()
      adapter.clearCache()
      
      const mode = await adapter.detectStorageMode(userId)
      console.log(`✅ 真实数据存储模式: ${mode}`)
      
      if (mode === 'legacy') {
        const validation = await adapter.validateStorageIntegrity(userId)
        console.log(`✅ 存储完整性验证: ${validation.isValid}`)
        if (!validation.isValid) {
          console.log(`❌ 缺失文件: ${validation.missingFiles.join(', ')}`)
          console.log(`⚠️ 问题: ${validation.issues.join(', ')}`)
        }
        
        const stats = await adapter.getStorageStats(userId)
        console.log(`📊 存储统计:`)
        console.log(`   - 总大小: ${stats.totalSize} bytes`)
        console.log(`   - 数据库: ${stats.dbSize} bytes`)
        console.log(`   - 图片: ${stats.imgSize} bytes`)
        console.log(`   - PDF: ${stats.pdfSize} bytes`)
        console.log(`   - Blocks: ${stats.blocksSize} bytes`)
      }
    } else {
      console.log('❌ 在backup数据中未找到数据库文件')
    }
  } catch (error) {
    console.log(`⚠️ 无法测试真实backup数据: ${error.message}`)
  }
}

async function testMigrationStatus() {
  console.log('\n⚙️ 测试迁移状态管理...')
  
  const { StorageCompatibilityAdapter } = await import('../dist/renderer/utils/storageCompatibilityAdapter.js')
  const adapter = new StorageCompatibilityAdapter()
  
  // 设置迁移状态
  await adapter.setMigrationStatus({
    version: 'v2.0',
    migrated: true,
    rollbackAvailable: true,
    backupPath: '/some/backup/path'
  })
  
  console.log('✅ 迁移状态设置完成')
  
  // 读取迁移状态
  const status = await adapter.getMigrationStatus()
  console.log(`📋 迁移状态:`)
  console.log(`   - 版本: ${status.version}`)
  console.log(`   - 已迁移: ${status.migrated}`)
  console.log(`   - 可回滚: ${status.rollbackAvailable}`)
  console.log(`   - 备份路径: ${status.backupPath}`)
  console.log(`   - 迁移日期: ${status.migrationDate}`)
}

async function cleanup() {
  console.log('\n🧹 清理测试环境...')
  await fs.rm(mockConfig.workspacePath, { recursive: true, force: true })
  console.log('✅ 清理完成')
}

async function main() {
  console.log('🚀 存储兼容性适配器测试')
  console.log('=' * 50)
  
  try {
    // 先构建项目
    console.log('📦 构建项目...')
    const { spawn } = require('child_process')
    
    await new Promise((resolve, reject) => {
      const build = spawn('pnpm', ['build:web'], { stdio: 'inherit', cwd: path.resolve(__dirname, '..') })
      build.on('close', (code) => {
        if (code === 0) resolve()
        else reject(new Error(`构建失败，退出码: ${code}`))
      })
    })
    
    await setupTestEnvironment()
    await testLegacyArchitecture()
    await testModernArchitecture()
    await testRealBackupData()
    await testMigrationStatus()
    
    console.log('\n🎉 所有测试完成！')
  } catch (error) {
    console.error('❌ 测试失败:', error)
  } finally {
    await cleanup()
  }
}

// 运行主程序
if (require.main === module) {
  main().catch(console.error)
}